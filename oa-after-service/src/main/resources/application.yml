server:
  port: 11007
  servlet:
    context-path: /afterservice

eureka:
  client:
    enabled: false

spring:
  profiles:
    active: jiuji
  application:
    name: afterservice
  messages:
    basename: i18n/url,i18n/constants,i18n/logContent
    basePackages: com.jiuji.oa.afterservice.common.source
  main:
    allow-bean-definition-overriding: true
  cloud:
    inetutils:
      ignored-interfaces:
        - docker0
        - veth.*
      timeout-seconds: 5
    consul:
      enabled: true
      discovery:
        register: true #注册到consul
        prefer-ip-address: true
        instance-group: sim
        service-name: ${spring.application.name}
        instance-id: ${spring.application.name}-${spring.cloud.consul.discovery.instance-zone}-${spring.cloud.consul.discovery.instance-group}-${spring.cloud.client.hostname}-${server.port}
        #健康检查失败多久强制取消服务注册
        health-check-critical-timeout: 120s
        #heartbeat:
        #  enabled: true
        tags: traefik.frontend.rule=Host:oa-afterservice
        health-check-url: http://${spring.cloud.consul.discovery.ip-address}:${server.port}/${server.servlet.context-path}/actuator/info
        instance-zone: ${instance.zone}
        default-query-tag: zone=${spring.cloud.consul.discovery.instance-zone}
      host: ${spring.cloud.client.hostname}
      #      host: ${consul.host}
      port: ${consul.port}

  datasource:
    hikari:
      maximum-pool-size: 60
      minimum-idle: 5
    default:
      url: jdbc:sqlserver://${sqlserver.after_write.host}:${sqlserver.after_write.port};databaseName=${sqlserver.after_write.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.after_write.username}
      password: ${sqlserver.after_write.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      data-source-type: com.jiuji.oa.afterservice.common.config.db.MyDynamicDataSource
  redis:
    url: redis://${redis.oa.url}/
    host: ${redis.oa.host}
    port: ${redis.oa.port}
    #    password: ${redis.oa.password}
    jedis:
      pool:
        max-active: 128
        max-idle: 16
        min-idle: 0
    timeout: 10s

sqlserver:
  datasource:
    driver-class-name: com.microsoft.sqlserver.jdbc.SQLServerDriver
    type: com.zaxxer.hikari.HikariDataSource
mysql:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.alibaba.druid.pool.DruidDataSource

# mybatis-plus配置
mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  typeAliasesPackage: com.jiuji.oa.afterservice.**.po
  #    typeEnumsPackage: com.ch999.web.jiuji.entity.enums
  global-config:
    db-config:
      id-type: none
      field-strategy: not_empty
      table-underline: true #新项目用true好点，由于历史原因这里用false
      logic-delete-value: 1
      logic-not-delete-value: 0
    sql-parser-cache: true #这个要开启，否则@SqlParser无效
  configuration:
    map-underscore-to-camel-case: true #新项目用true好点，由于历史原因这里用false
    cache-enabled: false

# 数据库配置
dynamic:
  datasource:
    web999:
      url: jdbc:sqlserver://${sqlserver.web999.host}:${sqlserver.web999.port};databaseName=${sqlserver.web999.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.web999.username}
      password: ${sqlserver.web999.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    office:
      url: jdbc:sqlserver://${sqlserver.office.host}:${sqlserver.office.port};databaseName=${sqlserver.office.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.office.username}
      password: ${sqlserver.office.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}

    office2:
      url: jdbc:sqlserver://${sqlserver.office2.host:}:${sqlserver.office2.port:};databaseName=${sqlserver.office2.dbname:};applicationName=java_${spring.application.name}
      username: ${sqlserver.office2.username:}
      password: ${sqlserver.office2.password:}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      is-enable: ${sqlserver.office2.is-enable:false}
    oacore:
      url: jdbc:mysql://${mysql.oa_core.url}/oa_core?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      username: ${mysql.oa_core.username}
      password: ${mysql.oa_core.password}
      type: ${mysql.datasource.type}
      driver-class-name: ${mysql.datasource.driver-class-name}
    oa_nc:
      url: jdbc:mysql://${mysql.oa_nc.url}/${mysql.oa_nc.dbname}?useUnicode=true&characterEncoding=utf8&serverTimezone=Asia/Shanghai&autoReconnect=true&autoReconnectForPools=true&rewriteBatchedStatements=true
      username: ${mysql.oa_nc.dbname}
      password: ${mysql.oa_nc.password}
      type: ${mysql.datasource.type}
      driver-class-name: ${mysql.datasource.driver-class-name}
    oa_log:
      url: jdbc:mysql://${mysql.oa_log.url}/${mysql.oa_log.dbname}?characterEncoding=utf8&useSSL=false&serverTimezone=Asia/Shanghai
      username: ${mysql.oa_log.username}
      password: ${mysql.oa_log.password}
      type: ${mysql.datasource.type}
      driver-class-name: ${mysql.datasource.driver-class-name}

    ch999oanew:
      url: jdbc:sqlserver://${sqlserver.ch999oanew.host}:${sqlserver.ch999oanew.port};databaseName=${sqlserver.ch999oanew.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.ch999oanew.username}
      password: ${sqlserver.ch999oanew.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}

    ch999oanew2:
      url: jdbc:sqlserver://${sqlserver.ch999oanew2.host:}:${sqlserver.ch999oanew2.port:};databaseName=${sqlserver.ch999oanew2.dbname:};applicationName=java_${spring.application.name}
      username: ${sqlserver.ch999oanew2.username:}
      password: ${sqlserver.ch999oanew2.password:}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
      is-enable: ${sqlserver.ch999oanew2.is-enable:false}
    ch999oanewReport:
      url: jdbc:sqlserver://${sqlserver.ch999oanewReport.host}:${sqlserver.ch999oanewReport.port};databaseName=${sqlserver.ch999oanewReport.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.ch999oanewReport.username}
      password: ${sqlserver.ch999oanewReport.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    oanew_his:
      url: jdbc:sqlserver://${sqlserver.ch999oanewHis.host}:${sqlserver.ch999oanewHis.port};databaseName=${sqlserver.ch999oanewHis.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.ch999oanewHis.username}
      password: ${sqlserver.ch999oanewHis.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
    ipaddress:
      url: jdbc:sqlserver://${sqlserver.ipaddress.host}:${sqlserver.ipaddress.port};databaseName=${sqlserver.ipaddress.dbname};applicationName=java_${spring.application.name}
      username: ${sqlserver.ipaddress.username}
      password: ${sqlserver.ipaddress.password}
      type: ${sqlserver.datasource.type}
      driver-class-name: ${sqlserver.datasource.driver-class-name}
# jetcache缓存配置
jetcache:
  statIntervalMinutes: 60
  areaInCacheName: false
  hiddenPackages: com.jiuji
  local:
    default:
      type: caffeine
      limit: 100
      keyConvertor: fastjson
      expireAfterWriteInMillis: 3600000
  remote:
    default:
      type: redis.lettuce
      keyConvertor: fastjson
      valueEncoder: kryo
      valueDecoder: kryo
      expireAfterWriteInMillis: 21600000
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      uri: redis://${redis.oa.url}/

# mongodb
mongodb:
  url:
    ch999oa: ${mongodb.url1}

# 日志配置，默认使用dev
logging:
  config: ${logging.config.path}
#9机短信接口地址
sms:
  url: http://sms.ch999.com.cn/?test=
  sendEmailUrl: http://sms.ch999.com.cn/email/email.aspx
  #短信平台地址
  platformUrl: https://www.9xun.com
#图片服务器地址
image:
  uploadImgUrl: ${image.upload.url}
  delImgUrl: ${image.del.url}
  selectImgUrl: ${image.select.url}
rabbitmq:
  multiple:
    oaAsync:
      host: ${rabbitmq.oaAsync.url}
      port: ${rabbitmq.oaAsync.port}
      username: ${rabbitmq.oaAsync.username}
      password: ${rabbitmq.oaAsync.password}
      virtual-host: ${rabbitmq.oaAsync.vhost}
    oa:
      host: ${rabbitmq.oa.url:${rabbitmq.oaAsync.url}}
      port: ${rabbitmq.oa.port:${rabbitmq.oaAsync.port}}
      username: ${rabbitmq.oa.username:${rabbitmq.oaAsync.username}}
      password: ${rabbitmq.oa.password:${rabbitmq.oaAsync.password}}
      virtual-host: ${rabbitmq.oa.vhost:${rabbitmq.oaAsync.vhost}}

#jvm监控数据上报配置
management:
  endpoints:
    web:
      exposure:
        include:
          - 'info'
          - 'health'
  metrics:
    tags:
      application: ${spring.application.name}

## 查询imei接口 暂时放这里 如果其他系统也用在配置到每个配置文件
imei:
  url: http://*************:7984/api/imei/queryInfo?imei=

#mqtt:
#  host: tcp://************:2883
#  topic: oa2/nc-segments
#  clientinid: nc-segments-${random.value}
#  qoslevel: 1
#  username: 9xunyun
#  password: rPvvDZM9
#  timeout: 10000
#  keepalive: 20
#  adminUrl: http://************:8081/api/v4
#  adminUser: admin
#  adminPassword: public

feign:
  hystrix:
    enabled: true
    exclude-client-names: com.jiuji.oa.finance.BeDirectPayConfigCloud
  client:
    config:
        default:
            connectTimeout: 120000
            readTimeout: 120000

hystrix:
  command:
    default:
      execution:
        timeoutInMilliseconds: 120000
        isolation:
          strategy: SEMAPHORE
          maxConcurrentRequests: 2000
          thread:
            timeoutInMilliseconds: 120000

#延迟队列配置 生产环境
lmstfy:
  host: ${after.lmstfy.host}
  namespace:
  token:
  # 重试次数
  retryTimes: 2
  # 重试时间间隔（单位：毫秒）
  retryIntervalMilliseconds: 1000
  # 读 超时时间（单位：秒）
  readTimeoutSecond: 5
  # 写 超时时间（单位：秒）
  writeTimeoutSecond: 5
  # 连接 超时时间（单位：秒）
  connectTimeoutSecond: 5
  mult:
    first-lmstfy-client:
      namespace: oa_after
      token: ${after.lmstfy.token}
      #(延迟)处理方式为预约到店且订单类型为小件预约单
      smallPiecesQueue: SmallPieces_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #(延迟)处理方式为预约到店且订单类型为大件预约单
      largeSizedObjectQueue: LargeSizedObject_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #(延迟)处理方式为上门取件的预约单
      doorToDoorPickupQueue: DoorToDoorPickup_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #(延迟)处理方式为邮寄送修的预约单
      mailAppointmentFormQueue: MailAppointmentForm_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #(堆积0点到9点30的订单队列)预约到店且订单类型为小件预约单
      smallPiecesAccumulationQueue: SmallPiecesAccumulation_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #(堆积0点到9点30的订单队列)预约到店且订单类型为大件预约单
      largeSizedObjectAccumulationQueue: LargeSizedObjectAccumulation_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #(堆积0点到9点30的订单队列)上门取件的预约单
      doorToDoorPickupAccumulationQueue: DoorToDoorPickupAccumulation_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #(堆积0点到9点30的订单队列)邮寄送修的预约单
      mailAppointmentFormAccumulationQueue: MailAppointmentFormAccumulation_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #复购到期前7天,消息提醒推送
      repurchaseBuyExpireQueue: RepurchaseBuyExpire_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #软件维修单的评价信息推送统一调整为和销售单的推送逻辑一致（30分钟后推送）
      sendMsoftNoticeQueue: SendMsoftNotice_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}
      #预约单确认之后 到店时间开始的前1个小时 消息通知
      yuyueConfirmPushMsgQueue: YuyueConfirmPushMsg_${lmstfy.mult.first-lmstfy-client.namespace}_${tenantId:${instance-zone}}


mqtt:
  host: ${mqtt.host}
  clientinid: oa-afterservice-${random.value}
  username: ${mqtt.username}
  password: ${mqtt.password}
  timeout: 1000
  keepalive: 20
  topic: afterservice-default-topic

app:
  id: oa-afterService
apollo:
  meta: ${apollo.url:}
  bootstrap:
    enabled: true
    namespaces: ${apollo.file:}


