<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.SmallReasonConfigMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.smallpro.po.SmallReasonConfig">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
            <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
            <result property="source" column="source" jdbcType="INTEGER"/>
            <result property="reason" column="reason" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,category_id,category_name,
        source,reason,create_time
    </sql>
    <select id="getSmallReasonConfigList"
            resultType="com.jiuji.oa.afterservice.smallpro.vo.res.SmallReasonConfigListRes">
        select id, reason from small_reason_config s
        <where>
            <if test="req.source != null">
                and s.source = #{req.source}
            </if>
            <if test="req.cids != null and req.cids.size > 0">
                and s.category_id in
                <foreach collection="req.cids" item="cid" index="index" separator="," open="(" close=")">
                    #{cid}
                </foreach>
            </if>
        </where>
        order by id desc
        <if test="req.size != null and req.size != 0">
            limit #{req.size}
        </if>
    </select>
</mapper>
