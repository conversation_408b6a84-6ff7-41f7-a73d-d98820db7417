<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.NetpayRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.smallpro.po.NetpayRecord">
        <id column="id" property="id"/>
        <result column="trade_no" property="tradeNo"/>
        <result column="sub_number" property="subNumber"/>
        <result column="money" property="money"/>
        <result column="dtime" property="dtime"/>
        <result column="type" property="type"/>
        <result column="batch_no" property="batchNo"/>
        <result column="payWay" property="payWay"/>
        <result column="refundPrice" property="refundPrice"/>
        <result column="fenQiNum" property="fenQiNum"/>
        <result column="userid" property="userid"/>
        <result column="midplatform" property="midplatform"/>
        <result column="platformUserid" property="platformUserid"/>
        <result column="isFree" property="isFree"/>
    </resultMap>

    <select id="getReturnWayInstallmentInfo" resultType="java.lang.String">
        SELECT DISTINCT
        CASE
        WHEN
        payWay = '微信APP' THEN
        '微信' ELSE payWay
        END payWay
        FROM
        dbo.netpay_record with(nolock)
        WHERE
        datediff( MONTH, dtime, getdate( ) ) &lt;= 3
        AND money - isnull( refundPrice, 0 ) >0
        <if test="type == 3 || type == 4 || type == 6 || type == 7">
            AND sub_number IN
            <foreach collection="subNumberList" index="index" item="subNumber" separator="," open="(" close=")">
                #{subNumber}
            </foreach>
            AND type = 1
        </if>
        <if test="type == 5 || type == 10 || type ==11">
            AND sub_number = #{subId}
            AND type IN ( 2, 10 )
        </if>
        <if test=" type == 8 || type == 99">
            and sub_number =  #{subId}
            and type=3
        </if>
    </select>

    <select id="getNetPayInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.NetPayModelBo">
        select id,money-isnull(refundPrice,0) leftPrice from dbo.netpay_record with(nolock)
        where sub_number=#{subId} and type=#{type} and money-isnull(refundPrice,0)>0 and payWay='库分期'
    </select>

    <update id="updateRefundPrice">
        update dbo.netpay_record set refundPrice=isnull(refundPrice,0)+ #{price} where id=#{netRecordId} and money>=isnull(refundPrice,0)+ #{price}
    </update>

    <!-- getNetpayRecordByTypeAndId -->
    <select id="getNetpayRecordByTypeAndId" resultMap="BaseResultMap">
        select id,money,refundPrice,payWay,trade_no from dbo.netpay_record where type = #{type} and id = #{id}
    </select>

</mapper>
