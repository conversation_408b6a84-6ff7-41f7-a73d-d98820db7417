<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.DiyTimeCardMapper">

    <select id="selectLogByBasketId" resultType= "com.jiuji.oa.afterservice.smallpro.po.diy.DiyTimeCardPo">
        select diy.*
        from dbo.DIYTimeCard diy WITH (nolock)
         left join dbo.basket b WITH (nolock) on b.basket_id = diy.basket_id
        where isnull(diy.isdel,0)=0 and isnull(b.isdel,0)=0 and diy.basket_id is not null
          and (diy.purchase_basket_id = #{basketId} or diy.basket_id = #{basketId})
          and b.basket_date between #{startTime} and #{endTime}
    </select>

</mapper>
