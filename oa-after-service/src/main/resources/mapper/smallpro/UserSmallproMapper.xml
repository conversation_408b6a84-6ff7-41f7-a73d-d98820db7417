<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.UserSmallproMapper">
    <select id="listPage" resultType="com.jiuji.oa.afterservice.smallpro.bo.user.UserSmallproResVo">
        SELECT s.sub_id,s.tradeDate1 buyDate,b.ppriceid,isnull(p.product_name,'') productName,isnull(p.product_color,'') productColor
             ,b.price price,b.basket_id basketId,s.userid,p.bpic productImg FROM basket b with(nolock)
                    left join productinfo p with(nolock) on p.ppriceid = b.ppriceid
                    left join dbo.sub s with(nolock) on b.sub_id=s.sub_id
                --购物袋 485分类不显示，其余小件订单均需展示。
            where
                --贴膜不展示在该功能中
               (b.[type] in (85,86)
                    <if test="query.cids != null">
                        <foreach collection="query.tieMoCids" item="cid" open="or p.cid not in(" separator="," close=")">
                            #{cid}
                        </foreach>
                    </if>

                )
                --订单完成的
              and isnull(b.isdel,0)=0 and s.sub_check=3 and s.userid=#{query.userId}
              <if test="query.subId != null">
                  and s.sub_id=#{query.subId}
              </if>
              <if test="query.cids != null">
                  <foreach collection="query.cids" item="cid" open="and p.cid in (" separator="," close=")">
                      #{cid}
                  </foreach>
              </if>

                --限定为小件
              and isnull(b.ismobile,0)=0
    </select>
    <select id="queryProKcItem" resultType="com.jiuji.oa.afterservice.smallpro.bo.user.ProKcItem">
        select p.ppriceid as ppriceId,p.count,p.type,p.areaid from (
        select ppriceid ,areaid,leftCount count,1 as type from product_kc p with(nolock) where ppriceid IN
        <foreach collection="priceidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
        and p.areaid = #{areaId}
        union
        select b.ppriceid,p.areaid,SUM(lcount) as count,2 as type from caigou_basket b with(nolock) left join caigou_sub
        p with(nolock) on b.sub_id=p.id where p.stats in(0,1,2) and b.ppriceid IN
        <foreach collection="priceidList" item="pid" open="(" separator="," close=")">
            #{pid}
        </foreach>
        and p.areaid = #{areaId} group by areaid,ppriceid
        ) p
    </select>
    <select id="getRanks" resultType="java.lang.String">
        SELECT cr.ranks from ch999Ranks cr with(nolock) where cr.areaid = #{areaId} and cr.ch999_id = #{ch999Id}
    </select>
    <select id="existsAreaRanks" resultType="java.lang.Boolean">
        select case when exists(SELECT cr.ranks from ch999Ranks cr with(nolock) where cr.areaid = #{areaId} and cr.ch999_id = #{ch999Id}) then 1 else 0 end
    </select>
</mapper>