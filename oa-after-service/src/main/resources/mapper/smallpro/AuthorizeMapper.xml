<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.AuthorizeMapper">


    <select id="querySmallCategoryArea" resultType="java.lang.Integer">
        select info.id
        from departInfo depart with (nolock)
         right join areainfo info with (nolock) on info.depart_id = depart.id
        where info.ispass ='true' and info.id in (${sql})
    </select>
</mapper>
