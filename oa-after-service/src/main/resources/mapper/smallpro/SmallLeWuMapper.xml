<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.SmallLeWuMapper">

    <select id="countExchange" resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.BasketSmallCountBo">
        select sb.basket_id,sb.ppriceid,count(distinct sp.id) smallCount
        from SmallproBill sb with(nolock)
        inner join Smallpro sp with(nolock) on sp.id = sb.smallproID
        where sp.Stats = 1 and sp.Kind = 2 and sb.basket_id in
           <foreach collection="basketIds" item="basketId" open="(" close=")" separator=",">
               #{basketId}
           </foreach>
        group by sb.basket_id,sb.ppriceid
    </select>
</mapper>