<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.logxxl.dao.SmallProByXxlMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.smallpro.logxxl.bo.ProductKclogsBO">
        <result column="count" property="count" />
        <result column="lastcount" property="lastcount" />
        <result column="inprice" property="inprice" />
        <result column="area" property="area" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="insource" property="insource" />
        <result column="check1" property="check1" />
        <result column="check1dtime" property="check1dtime" />
        <result column="check2" property="check2" />
        <result column="check2dtime" property="check2dtime" />
        <result column="comment" property="comment" />
        <result column="basket_id" property="basketId" />
        <result column="ppriceid" property="ppriceid" />
        <result column="shouhou_id" property="shouhouId" />
        <result column="areaid" property="areaid" />
    </resultMap>
    <select id="getSmallProByXxl" resultType="java.lang.Integer">
        SELECT count(1)
        FROM
            (
                SELECT
                    *,SUM([count]) OVER ( partition BY ppriceid, areaid, basket_id ORDER BY id ) AS rank_
                FROM
                    dbo.product_kclogs WITH ( nolock )
                WHERE dtime > DATEADD(YEAR,DATEDIFF(YEAR,0,GETDATE()),0)
                  and comment LIKE '售后小件退换转现%'
            ) k
        WHERE
            k.rank_>1

    </select>
    <select id="getProductKc" resultType="java.lang.Integer">
        if exists (SELECT top 1 ppriceid from product_kc pk with(nolock) where pk.orderCount &lt; 0) select '1' else select '0'
    </select>
    
    <select id="getShouhouTuiHuanList" resultType="com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo">
        select d.*
        from  dbo.shouhou_tuihuan d with (nolock)
                 left join dbo.batch_return s with (nolock) on d.shouhou_id = s.id
        where s.status=3 and  isnull(d.isdel,0)=0   and d.tuihuan_kind =12   order by s.id desc
    </select>
</mapper>
