<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.SmallproSafeShellMapper">

<select id="getCidByBasketId" resultType="java.lang.Integer">
    select pf.cid FROM dbo.basket b with(nolock)
        INNER JOIN dbo.productinfo pf with(nolock) ON pf.ppriceid = b.ppriceid
    WHERE b.basket_id = #{basketId} AND ISNULL(b.isdel,0) = 0 AND b.price >= 0
    </select>
</mapper>
