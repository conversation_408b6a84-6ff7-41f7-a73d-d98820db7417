<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.SmallproWxMapper">

    <insert id="saveBatch" parameterType="java.util.List" useGeneratedKeys="false">
        INSERT INTO smallpro_wx(ppid, type, sub_id, basket_id,price,from_areaid, op_time,dTime,ch999_user_id,verified_flag) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.ppid},#{item.type},#{item.subId},#{item.basketId},#{item.price},#{item.fromAreaId},#{item.opTime},GETDATE(),#{item.ch999UserId},#{item.verifiedFlag})
        </foreach>
    </insert>

    <update id="updateInfo">
        update smallpro_wx set
        update_time = GETDATE(),
        insource=#{inSource},
        baoxiu=#{baoxiu},
        status=#{status},
        ch999_user_id=#{ch999UserId},
        remark=#{remark},
        verified_flag=#{verifiedFlag}
        where id = #{id}
    </update>
    <sql id="pageQuery">
        from smallpro_wx wx with(nolock)
            left join productinfo p with(nolock) on wx.ppid = p.ppriceid
            left join dbo.category ca with(nolock ) on p.cid=ca.id
            left join Ok3w_qudao c with(nolock) on wx.insource = c.id
            left join areainfo a with(nolock) on wx.from_areaid = a.id
            LEFT JOIN product_xtenant_info pxi with(nolock) on pxi.ppriceid = p.ppriceid and  pxi.xTenant = a.xtenant
            left join departInfo d with(nolock) on d.code=a.areaCode
            left join departInfo f WITH(NOLOCK)  on f.id=dbo.getDepartTypeId(d.id,3)
            left join ch999_user u with(nolock) on wx.ch999_user_id = u.ch999_id
            left join shouhou sh with(nolock) on sh.id = wx.sub_id
            where 1=1
        <if test="req.cids != null and req.cids.size>0">
            and p.cid in
           <foreach collection="req.cids" open="(" item="cid" close=")" separator=",">
               #{cid}
           </foreach>
        </if>
        <if test="req.bids != null and req.bids.size>0">
            and p.brandid in
            <foreach collection="req.bids" open="(" item="bid" close=")" separator=",">
                #{bid}
            </foreach>
        </if>
        <if test="req.areaIds != null and req.areaIds.size>0">
            and a.id in
            <foreach collection="req.areaIds" open="(" item="areaId" close=")" separator=",">
                #{areaId}
            </foreach>
        </if>
        <choose>
            <when test="req.searchType != null and req.searchType == 1 and req.searchKey != null and req.searchKey != ''">
                and p.product_name like concat('%',#{req.searchKey},'%')
            </when>
            <when test="req.searchType != null and req.searchType == 2 and req.searchKeyId != null and req.searchKeyId != 0">
                and wx.ppid = #{req.searchKeyId}
            </when>
            <when test="req.searchType != null and req.searchType == 3 and req.searchKey != null and req.searchKey != ''">
                and u.ch999_name =#{req.searchKey}
            </when>
            <when test="req.searchType != null and req.searchType == 4 and req.searchKeyId != null and req.searchKeyId != 0 ">
                and wx.id=#{req.searchKeyId}
            </when>
            <when test="req.searchType != null and req.searchType == 5 and req.searchKeyId != null and req.searchKeyId != 0 ">
                and wx.sub_id=#{req.searchKeyId} and wx.type=1
            </when>
            <when test="req.searchType != null and req.searchType == 6 and req.searchKeyId != null and req.searchKeyId != 0 ">
                and wx.sub_id=#{req.searchKeyId} and wx.type=2
            </when>
            <otherwise></otherwise>
        </choose>
        <if test="req.type != null and req.type != 0">
            and wx.type = #{req.type}
        </if>
        <if test="req.baoxiu != null and req.baoxiu != 0">
            and wx.baoxiu = #{req.baoxiu}
        </if>
        <if test="req.status != null and req.status != 0">
            and wx.status = #{req.status}
        </if>
        <if test="req.qudao != null">
            and c.id = #{req.qudao}
        </if>
        <if test="req.tag != null">
            and isnull(pxi.product_label,0) = #{req.tag}
        </if>
        <if test="req.verifiedFlag != null">
            and wx.verified_flag = #{req.verifiedFlag}
        </if>
        <if test="req.isGaoji != null and req.isGaoji=true">
            <if test="req.startTime != null">
              and wx.op_time >= #{req.startTime}
            </if>
            <if test="req.endTime != null">
              and wx.op_time &lt;= #{req.endTime}
            </if>
        </if>
    </sql>

    <select id="countWx" resultType="java.lang.Integer">
        select count(1)
        <include refid="pageQuery"></include>
    </select>

    <select id="list" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.SmallproWxInfoRes">
        select
            wx.id as id,
            wx.ppid as ppid,
            p.product_name as productName,
            ca.Name        as categoryName,
            p.product_color as productColor,
            isnull(pxi.product_label,0) as tag,
            wx.type as type,
            wx.baoxiu as baoxiu,
            wx.sub_id as subId,
            wx.basket_id as basketId,
            wx.from_areaid as fromAreaId,
            a.area as fromArea,
            d.name as cityName,
            f.name as regionName,
            wx.price as price,
            wx.op_time as opTime,
            wx.dTime as dTime,
            wx.update_time as updateTime,
            wx.insource as inSource,
            c.company_jc as inSourceName,
            wx.status as status,
            wx.ch999_user_id as ch999UserId,
            u.ch999_name as ch999UserName,
            wx.remark as remark,
            sh.problem,
            sh.weixiuren,
            wx.verified_flag verifiedFlag
        <include refid="pageQuery"></include>
        order by wx.op_time desc
        OFFSET #{req.startRows} ROWS FETCH NEXT #{req.size} ROWS ONLY
    </select>
    <select id="countWxNew" resultType="java.lang.Integer">
        select count(1)
        <include refid="pageQueryNew"></include>
    </select>
    <select id="listNew" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.SmallproWxInfoRes">
        select
        wx.id as id,
        wx.ppid as ppid,
        p.product_name as productName,
        ca.Name        as categoryName,
        p.product_color as productColor,
        isnull(pxi.product_label,0) as tag,
        wx.type as type,
        wx.baoxiu as baoxiu,
        wx.sub_id as subId,
        wx.basket_id as basketId,
        wx.from_areaid as fromAreaId,
        a.area as fromArea,
        d.name as cityName,
        f.name as regionName,
        wx.price as price,
        wx.op_time as opTime,
        wx.dTime as dTime,
        wx.update_time as updateTime,
        wx.insource as inSource,
        c.company_jc as inSourceName,
        wx.status as status,
        wx.ch999_user_id as ch999UserId,
        u.ch999_name as ch999UserName,
        wx.remark as remark,
        sh.problem,
        sh.weixiuren,
        wx.row_num,
        wx.verified_flag verifiedFlag
        <include refid="pageQueryNew"></include>
        order by wx.op_time desc
        OFFSET #{req.startRows} ROWS FETCH NEXT #{req.size} ROWS ONLY
    </select>
    <select id="selectTransfer" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.TransferUserRes">
        select d.id as transferId, u.ch999_name as ch999UserName, u.ch999_id as ch999UserId
        from dbo.diaobo_sub d with (nolock)
         left join dbo.ch999_user u with (nolock) on u.ch999_name = d.inuser
        where 1=1
        <if test="transferIdList != null and transferIdList.size > 0">
             and  d.id in
            <foreach collection="transferIdList" index="index" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="selectUserNameList" resultType="com.jiuji.oa.afterservice.smallpro.vo.res.TransferUserRes">
        select ch999_name as ch999UserName, ch999_id as ch999UserId,area1id as areaId
        from dbo.ch999_user with(nolock ) where 1=1
        <if test="userNameList != null and userNameList.size > 0">
            and  ch999_name in
            <foreach collection="userNameList" index="index" item="name" open="(" separator="," close=")">
                #{name}
            </foreach>
        </if>
    </select>


    <sql id="pageQueryNew">
        from(
        SELECT sm.id ,
        sm.ppid ,
        sm.type ,
        sm.baoxiu ,
        sm.sub_id ,
        sm.basket_id ,
        sm.from_areaid,
        sm.price,
        sm.op_time ,
        sm.dTime ,
        sm.update_time ,
        sm.insource ,
        sm.status ,
        sm.ch999_user_id ,
        sm.remark ,
        sm.verified_flag ,
        ROW_NUMBER() OVER (PARTITION BY sm.sub_id, sm.ppid ORDER BY sm.id) AS row_num
        FROM dbo.smallpro_wx sm WITH (NOLOCK)
        ) AS wx
        left join productinfo p with(nolock) on wx.ppid = p.ppriceid
        left join dbo.category ca with(nolock ) on p.cid=ca.id
        left join Ok3w_qudao c with(nolock) on wx.insource = c.id
        left join areainfo a with(nolock) on wx.from_areaid = a.id
        LEFT JOIN product_xtenant_info pxi with(nolock) on pxi.ppriceid = p.ppriceid and  pxi.xTenant = a.xtenant
        left join departInfo d with(nolock) on  d.id = dbo.getDepartTypeId(a.depart_id,4)
        left join departInfo f WITH(NOLOCK)  on f.id=dbo.getDepartTypeId(d.id,3)
        left join ch999_user u with(nolock) on wx.ch999_user_id = u.ch999_id
        left join shouhou sh with(nolock) on sh.id = wx.sub_id
        where 1=1 and row_num=1
        <if test="req.cids != null and req.cids.size>0">
            and p.cid in
            <foreach collection="req.cids" open="(" item="cid" close=")" separator=",">
                #{cid}
            </foreach>
        </if>
        <if test="req.bids != null and req.bids.size>0">
            and p.brandid in
            <foreach collection="req.bids" open="(" item="bid" close=")" separator=",">
                #{bid}
            </foreach>
        </if>
        <if test="req.areaIds != null and req.areaIds.size>0">
            and a.id in
            <foreach collection="req.areaIds" open="(" item="areaId" close=")" separator=",">
                #{areaId}
            </foreach>
        </if>
        <choose>
            <when test="req.searchType != null and req.searchType == 1 and req.searchKey != null and req.searchKey != ''">
                and p.product_name like concat('%',#{req.searchKey},'%')
            </when>
            <when test="req.searchType != null and req.searchType == 2 and req.searchKeyId != null and req.searchKeyId != 0">
                and wx.ppid = #{req.searchKeyId}
            </when>
            <when test="req.searchType != null and req.searchType == 3 and req.searchKey != null and req.searchKey != ''">
                and u.ch999_name =#{req.searchKey}
            </when>
            <when test="req.searchType != null and req.searchType == 4 and req.searchKeyId != null and req.searchKeyId != 0 ">
                and wx.id=#{req.searchKeyId}
            </when>
            <when test="req.searchType != null and req.searchType == 5 and req.searchKeyId != null and req.searchKeyId != 0 ">
                and wx.sub_id=#{req.searchKeyId} and wx.type=1
            </when>
            <when test="req.searchType != null and req.searchType == 6 and req.searchKeyId != null and req.searchKeyId != 0 ">
                and wx.sub_id=#{req.searchKeyId} and wx.type=2
            </when>
            <otherwise></otherwise>
        </choose>
        <if test="req.type != null and req.type != 0">
            and wx.type = #{req.type}
        </if>
        <if test="req.baoxiu != null and req.baoxiu != 0">
            and wx.baoxiu = #{req.baoxiu}
        </if>
        <if test="req.status != null and req.status != 0">
            and wx.status = #{req.status}
        </if>
        <if test="req.qudao != null">
            and c.id = #{req.qudao}
        </if>
        <if test="req.tag != null">
            and isnull(pxi.product_label,0) = #{req.tag}
        </if>
        <if test="req.verifiedFlag != null">
            and wx.verified_flag = #{req.verifiedFlag}
        </if>
        <if test="req.isGaoji != null and req.isGaoji=true">
            <if test="req.startTime != null">
                and wx.op_time >= #{req.startTime}
            </if>
            <if test="req.endTime != null">
                and wx.op_time &lt;= #{req.endTime}
            </if>
        </if>
    </sql>
</mapper>
