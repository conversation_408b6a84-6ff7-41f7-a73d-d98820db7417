<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.TiemoCardUserLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.smallpro.po.TiemoCardUserLog">
        <id column="id" property="id" />
        <result column="cardId" property="cardId" />
        <result column="basket_id" property="basketId" />
        <result column="dtime" property="dtime" />
        <result column="inuser" property="inuser" />
        <result column="smallProId" property="smallProId" />
    </resultMap>
    <select id="selectLogByBasketId" resultType= "com.jiuji.oa.afterservice.smallpro.po.TiemoCardUserLog">
        SELECT tcl.*
        FROM dbo.tiemoCardUserLog tcl with(nolock)
         LEFT JOIN  dbo.tiemoCard tc
        with (nolock)
        ON tc.id= tcl.cardId
            LEFT JOIN dbo.basket b
        with (nolock)
        ON tc.basketid= b.basket_id
         WHERE (basket_idBind = #{basketId} OR basketid = #{basketId})
          and isnull(tc.isdel, 0)=0
          and tc.dtime between #{startTime} and #{endTime}
    </select>

</mapper>
