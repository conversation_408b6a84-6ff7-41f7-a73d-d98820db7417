<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.TiemoCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.smallpro.po.TiemoCard">
        <id column="id" property="id"/>
        <result column="userid" property="userid"/>
        <result column="sub_id" property="subId"/>
        <result column="areaid" property="areaid"/>
        <result column="basketid" property="basketid"/>
        <result column="basket_idBind" property="basketIdbind"/>
        <result column="price" property="price"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="useCount" property="useCount"/>
        <result column="allCount" property="allCount"/>
        <result column="inuser" property="inuser"/>
        <result column="dtime" property="dtime"/>
        <result column="etime" property="etime"/>
        <result column="isdel" property="isdel"/>
        <result column="type_" property="type"/>
        <result column="stime" property="stime"/>
    </resultMap>

    <select id="checkValidFilm" resultType="java.lang.Integer">
        SELECT count(1) from dbo.basketBindRecord b with(nolock)
        left join tiemoCard t with(nolock) on b.basket_id = t.basket_idBind
        where b.imei = #{imei} and t.etime > GETDATE()
    </select>
    <select id="listUsedTiemoCard" resultType="com.jiuji.oa.afterservice.smallpro.bo.UsedTiemoCardBO">
        SELECT
            tc.id,
            tc.userid as userId,
            b.ppriceid as servicePpid,
            p.cidFamily,
            tc.etime endTime,
            tc.basketid basketId,
            tc.basket_idBind basketIdBind,
            u.xtenant,
            u.mobile,
            tc.areaid areaId,
            tcul.dtime lastUserTime
        from tiemoCard tc with(nolock)
        left join (SELECT cardId,dtime,ROW_NUMBER() OVER(PARTITION BY cardId ORDER BY id DESC) rn FROM tiemoCardUserLog with(nolock) ) tcul on tcul.cardId = tc.id and tcul.rn = 1
        left join basket b with(nolock) on tc.basketid = b.basket_id
        left join productinfo p with(nolock) on p.ppriceid = tc.ppriceid
        left join BBSXP_Users u with(nolock) on u.ID = tc.userid
        where tc.useCount >= tc.allCount
        and DATEDIFF(day, CONVERT(date, tcul.dtime), CONVERT(date, GETDATE())) = #{days}
    </select>
    <select id="checkRepurchaseBuy" resultType="java.lang.Integer">
        SELECT
            count(1)
        from tiemoCard tc with(nolock)
        where tc.id &gt; #{vo.id} and tc.userid = #{vo.userId} and tc.basket_idBind = #{vo.basketIdBind}
    </select>

</mapper>
