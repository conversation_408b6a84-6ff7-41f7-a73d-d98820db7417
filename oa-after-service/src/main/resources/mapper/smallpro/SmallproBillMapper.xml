<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.smallpro.dao.SmallproBillMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.smallpro.po.SmallproBill">
        <id column="id" property="id"/>
        <result column="smallproID" property="smallproID"/>
        <result column="basket_id" property="basketId"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="count" property="count"/>
        <result column="inprice" property="inprice"/>
    </resultMap>
    <select id="existLockStock" resultType="java.lang.Boolean">
        select count(1) from SmallproBill sb with(nolock) where sb.smallproID = #{smallproId}
        and exists(select 1 from product_kc_lock_info pkli with(nolock)
                            where pkli.basket_id  = sb.id and pkli.basket_type = 2 and pkli.is_del = 0
                              <if test="isDone != null">
                                  and pkli.isDone = #{isDone}
                              </if>
                )
    </select>

</mapper>
