<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.statistics.dao.BrandCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.statistics.po.BrandCategory">
        <id column="id1" property="id1" />
        <result column="brandID" property="brandID" />
        <result column="categoryID" property="categoryID" />
        <result column="Rank" property="Rank" />
    </resultMap>

    <select id="getBrandCategoryList" resultType="com.jiuji.oa.afterservice.bigpro.statistics.bo.BrandCategoryBo">
        select id,name,c.categoryID as categoryId,c.Rank as rank from brandCategory c with(nolock) left join brand b with(nolock) on c.brandID=b.id where  b.id is not null
    </select>
</mapper>
