<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.statistics.dao.StoreRentStatisticsMapper">
    <sql id="item_query_sub">
        select ss.yifuM,ss.yingfuM,ss.coinM,ss.youhui1M,ss.jidianM,ss.feeM,p.productid,p.product_name,b.price,b.basket_count,b.ismobile,ss.sub_id,ss.tradeDate1,b.seller,ss.sub_check,ss.areaid
        from dbo.sub ss with(nolock)
        left join dbo.basket b with(nolock,index=IX_basket_sub_id) on b.sub_id = ss.sub_id
        left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
        <where>
            and isnull(b.isdel,0)=0 and isnull(ss.yifuM,0.0)>0
            <if test="orderIds == null || orderIds.isEmpty()">
                <!--不是精确查询才考虑订单状态 因为收银方式存在多种 订单完成是包含pos收银的 统计的时候 pos机收银 需要考虑时间范围-->
                and ((ss.sub_check in (3,9)
                        <if test="startTime != null and endTime != null">
                            and ss.tradeDate1 between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20)
                        </if>
                    ) or
                     <!--pos机收银-->
                    (ss.sub_check &lt;&gt;4 and exists(select 1 from dbo.shouying qsy with(nolock)
                        where qsy.shouying_type in ('交易','订金') and qsy.sub_id = ss.sub_id and qsy.sub_pay07>0
                        <if test="startTime != null and endTime != null">
                            and qsy.dtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20)
                        </if>
                    ))
                    )
            </if>
            <if test="areaId != null">
                and ss.areaid=#{areaId}
            </if>
            <if test="orderIds != null">
                <foreach collection="orderIds" item="orderId" open="and ss.sub_id in (" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </where>
    </sql>
    <select id="listSubPayment" resultType="com.jiuji.oa.afterservice.statistics.vo.rent.StoreRentOrderVo">
        <!--订单查询-->
        select isnull(z.price,0.0) as alipayPayment,isnull(s.yifuM,0.0) as amountPaid,isnull(y.sub_pay01,0.0) as cashPayment
             ,SUM(s.price * isnull(s.basket_count,1)) OVER (partition by s.sub_id ORDER BY s.price asc) as totalPrice
             ,isnull(y.sub_pay05,0.0) as voucherPayment,isnull(w.price,0.0) as wechatPayment
             ,isnull(s.yingfuM,0.0) +isnull(s.coinM,0.0)+isnull(s.youhui1M,0.0)+isnull(s.jidianM,0.0)-isnull(w.price,0.0)+isnull(y.dtime_sub_pay07,0.0)
                  -isnull(z.price,0.0)-isnull(y.sub_pay01,0.0)-isnull(y.sub_pay05,0.0)-isnull(y.sub_pay07,0.0)-isnull(s.feeM,0.0) as otherPayment
             ,isnull(s.basket_count,1) as commodityCount,s.productid as commodityId,s.product_name as commodityName,s.price as commodityPrice,isnull(s.ismobile,0)^1 as type,s.sub_id as orderId,1 as orderType
             ,isnull(y.dtime,s.tradeDate1) as paymentTime,y.dtime_market_pos as posCardPayment,s.seller as salesmenName
            <!--0 未确认 1 已确认 2 已出库 3 已完成 4 已删除 5 等待确认 6 欠款 7 待处理 8 退订 9 退款-->
             ,CASE s.sub_check WHEN 0 THEN '未确认' WHEN 1 THEN '已确认' WHEN 2 THEN '已出库' WHEN 3 THEN '已完成' WHEN 4 THEN '已删除'
                 WHEN 5 THEN '等待确认' WHEN 6 THEN '欠款' WHEN 7 THEN '待处理' WHEN 8 THEN '退订' WHEN 9 THEN '退款' ELSE '无' END status
             ,case when exists (
                select 1
                from dbo.cardAccounting c with(nolock)
                inner join dbo.shouying sy with(nolock) on sy.id = c.otherid
                inner join dbo.posInfo p with(nolock) on p.posID = c.posid
                where sy.sub_id = s.sub_id
                and p.posPay = '商场专用' and userWay = 1 and type_ = 2
                <if test="startTime != null and endTime != null">
                    and sy.dtime between convert(datetime, #{startTime}, 20) and convert(datetime, #{endTime}, 20)
                </if>
                and sy.shouying_type in ('交易','订金')
                ) then 1 end as marketPosFlag
             ,case when exists (
                select 1
                from shouying sy with(NOLOCK)
                inner join shouyin_other so with(NOLOCK) on so.shouyinid = sy.id
                where sy.sub_id = s.sub_id
                and so.type_ in (
                select cast(c.value as int)
                from dbo.sysConfig c with(NOLOCK)
                inner join dbo.payment_config pc with(NOLOCK) on pc.sys_config_id = c.id
                where code = 37 and pc.activity_type = '商场活动')
                <if test="startTime != null and endTime != null">
                    and sy.dtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20)
                </if>
                and sy.shouying_type in ('交易','订金')
                ) then 1 end as marketVoucherFlag
             ,s.areaid as storeId,a.area as storeNo
        from (<include refid="item_query_sub"></include>) s
        left join (
            select n.sub_number,sum(n.money) as price from dbo.netpay_record n with(nolock)
            where n.type=1
                and exists(select 1 from (<include refid="item_query_sub"></include>) s where n.sub_number = cast(s.sub_id as nvarchar(20)) )
                and n.payWay like '%微信%'
            group by n.sub_number
        ) w on w.sub_number = s.sub_id
        left join (
            select n.sub_number,sum(n.money) as price from dbo.netpay_record n with(nolock)
            where n.type=1
                and exists(select 1 from (<include refid="item_query_sub"></include>) s where n.sub_number = cast(s.sub_id as nvarchar(20)) )
                and n.payWay like '%支付宝%'
            group by n.sub_number
        ) z on z.sub_number = s.sub_id
        left join (
            select s.sub_id,sum(s.sub_pay01) as sub_pay01,sum(s.sub_pay05) as sub_pay05,sum(s.sub_pay07) sub_pay07
                 ,sum(iif(s.dtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20) and not (cat.type_=2 AND cat.userWay=1),s.sub_pay07,0)) as dtime_sub_pay07
                 ,sum(iif(s.dtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20) and cat.type_=2 AND cat.userWay=1,cat.accounting,0)) as dtime_market_pos
                 ,max(s.dtime) as dtime
            from dbo.shouying s  with(nolock)
            left join dbo.cardAccounting cat with(nolock) on cat.otherid = s.id
        where s.shouying_type in ('交易','订金')
                and exists(select 1 from (<include refid="item_query_sub"></include>) ss where s.sub_id = ss.sub_id )
            group by s.sub_id
        ) y on y.sub_id = s.sub_id
        left join dbo.areainfo a with(nolock) on s.areaid=a.id
        where isnull(y.dtime,s.tradeDate1) is not null
        order by orderId desc,type asc,commodityPrice desc
    </select>
    <sql id="item_query_shouhou">
        select ss.yifum,ss.feiyong,ss.youhuifeiyong,p.productid,p.product_name,wxc.price,ss.id,ss.shouyingdate,wxc.inuser,ss.stats,ss.areaid
        from dbo.shouhou ss with(nolock)
        left join wxkcoutput wxc with(nolock) on wxc.wxid = ss.id
        left join dbo.productinfo p with(nolock) on wxc.ppriceid = p.ppriceid
        <where>
            isnull(ss.xianshi,1)=1 and isnull(ss.yifum,0)>0
            <if test="orderIds == null || orderIds.isEmpty()">
                <!--不是精确查询才考虑订单状态 因为收银方式存在多种 订单完成是包含pos收银的 统计的时候 pos机收银 需要考虑时间范围-->
                and ((isnull(ss.isquji,0) = 1
                <if test="startTime != null and endTime != null">
                    and ss.modidtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20)
                </if>
                ) or
                <!--pos机收银-->
                (exists(select 1 from dbo.shouying qsy with(nolock)
                where qsy.shouying_type = '售后' and qsy.sub_id = ss.id and qsy.sub_pay07>0
                <if test="startTime != null and endTime != null">
                    and qsy.dtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20)
                </if>
                ))
                )
            </if>
            <if test="startTime != null and endTime != null">
                and ss.modidtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20)
            </if>
            <if test="areaId != null">
                and ss.areaid=#{areaId}
            </if>
            <if test="orderIds != null">
                <foreach collection="orderIds" item="orderId" open="and ss.id in (" close=")" separator=",">
                    #{orderId}
                </foreach>
            </if>
        </where>
    </sql>
    <select id="listShouhouPayment" resultType="com.jiuji.oa.afterservice.statistics.vo.rent.StoreRentOrderVo">
        <!--维修单查询-->
        select isnull(z.price,0.0) as alipayPayment,isnull(s.yifum,0.0) as amountPaid,isnull(y.sub_pay01,0.0) as cashPayment
             ,SUM(s.price) OVER (partition by s.id ORDER BY s.price asc) as totalPrice
        ,isnull(y.sub_pay05,0.0) as voucherPayment,isnull(w.price,0.0) as wechatPayment
        ,isnull(s.feiyong,0.0)+isnull(s.youhuifeiyong,0.0)+isnull(y.dtime_sub_pay07,0.0)
             -isnull(w.price,0.0)-isnull(z.price,0.0)-isnull(y.sub_pay01,0.0)-isnull(y.sub_pay05,0.0)-isnull(y.sub_pay07,0.0) as otherPayment
        ,1 as commodityCount,s.productid as commodityId,s.product_name as commodityName,s.price as commodityPrice,2 as type,s.id as orderId,2 as orderType
        ,isnull(y.dtime,s.shouyingdate) as paymentTime, y.dtime_market_pos as posCardPayment,s.inuser as salesmenName
        <!--0处理中，1已修好，3修不好-->
        ,CASE s.stats WHEN 0 THEN '处理中' WHEN 1 THEN '已修好' WHEN 3 THEN '修不好' ELSE '无' END status
        ,case when exists (
        select 1
        from dbo.cardAccounting c with(nolock)
        inner join dbo.shouying sy with(nolock) on sy.id = c.otherid
        inner join dbo.posInfo p with(nolock) on p.posID = c.posid
        where sy.sub_id = s.id
        and p.posPay = '商场专用' and userWay = 1 and type_ = 2
        <if test="startTime != null and endTime != null">
            and sy.dtime between convert(datetime, #{startTime}, 20) and convert(datetime, #{endTime}, 20)
        </if>
        and sy.shouying_type = '售后'
        ) then 1 end as marketPosFlag
        ,case when exists (
        select 1
        from shouying sy with(NOLOCK)
        inner join shouyin_other so with(NOLOCK) on so.shouyinid = sy.id
        where sy.sub_id = s.id
        and so.type_ in (
        select cast(c.value as int)
        from dbo.sysConfig c with(NOLOCK)
        inner join dbo.payment_config pc with(NOLOCK) on pc.sys_config_id = c.id
        where code = 37 and pc.activity_type = '商场活动')
        <if test="startTime != null and endTime != null">
            and sy.dtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20)
        </if>
        and sy.shouying_type = '售后'
        ) then 1 end as marketVoucherFlag
        ,s.areaid as storeId,a.area as storeNo
        from (<include refid="item_query_shouhou"></include>) s
        left join (
            select n.sub_number,sum(n.money) as price from dbo.netpay_record n with(nolock)
            where
                n.type=2
                and exists(select 1 from (<include refid="item_query_shouhou"></include>) s where n.sub_number = cast(s.id as nvarchar(20)) )
                and n.payWay like '%微信%'
            group by n.sub_number
        ) w on w.sub_number = s.id
        left join (
            select n.sub_number,sum(n.money) as price from dbo.netpay_record n with(nolock)
            where n.type=2
                and n.payWay like '%支付宝%'
                and exists(select 1 from (<include refid="item_query_shouhou"></include>) s where n.sub_number = cast(s.id as nvarchar(20)) )
            group by n.sub_number
        ) z on z.sub_number = s.id
        left join (
            select s.sub_id,sum(s.sub_pay01) as sub_pay01,sum(s.sub_pay05) as sub_pay05,sum(s.sub_pay07) as sub_pay07
                 ,sum(iif(s.dtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20) and not (cat.type_=2 AND cat.userWay=1),s.sub_pay07,0)) as dtime_sub_pay07
                 ,sum(iif(s.dtime between CONVERT(datetime,#{startTime},20) and CONVERT(datetime,#{endTime},20) and cat.type_=2 AND cat.userWay=1,cat.accounting,0)) as dtime_market_pos
                 ,max(s.dtime) as dtime
            from dbo.shouying s  with(nolock)
            left join dbo.cardAccounting cat with(nolock) on cat.otherid = s.id
            where
                s.shouying_type = '售后'
                and exists(select 1 from (<include refid="item_query_shouhou"></include>) ss where s.sub_id = ss.id )
            group by s.sub_id
        ) y on y.sub_id = s.id
        left join dbo.areainfo a with(nolock) on s.areaid=a.id
        where isnull(y.dtime,s.shouyingdate) is not null
        order by orderId desc,type asc,commodityPrice desc
    </select>
</mapper>
