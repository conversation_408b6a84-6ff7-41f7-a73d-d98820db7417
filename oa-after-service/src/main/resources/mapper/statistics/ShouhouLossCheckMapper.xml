<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.statistics.dao.ShouhouLossCheckMapper">

    <select id="getShouhouLossList" resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.ShouhouLossListRes">
        select s.name,s.areaid,s.inuser shInuser,s.modidate,isnull(s.feiyong,0) as feiyong,isnull(s.costprice,0) as costprice,
        hx.hexiao_total,hs.hsprice,isnull(s.servicetype,0) servicetype,q.*,serversOutDtime,s.imei,fp.fid fuwupic
        from shouhou_qujishenhe q with(nolock)
        LEFT JOIN dbo.shouhou_fuwupic fp with(nolock) ON fp.shouhouid=q.shouhouid
        left join dbo.shouhou s with(nolock) on q.shouhouid = s.id
        LEFT JOIN(
            SELECT x.wxid, SUM(ISNULL(x.inprice,0)) hexiao_total
            FROM wxkcoutput x  WITH(NOLOCK) INNER JOIN shouhou_hexiao h  WITH(NOLOCK) on h.Kc_logsId=x.id
            WHERE  h.ishexiao IS NOT NULL
            GROUP BY x.wxid
        ) hx ON hx.wxid=s.id
        LEFT JOIN ( select shouhou_id,SUM(ISNULL(price,0)) hsprice from shouhou_huishou WITH(NOLOCK) WHERE ISNULL(isdel,0)=0 GROUP BY shouhou_id ) hs ON hs.shouhou_id=s.id
        where 1=1

        <if test='req.ranks.contains("6e1") and !req.ranks.contains("6e2") and !req.ranks.contains("6e3") and !req.ranks.contains("6e4")'>
           and (q.checkUser1='未审核' or q.checkUser1&lt;&gt; '未审核' )
        </if>
        <if test='req.ranks.contains("6e2") and !req.ranks.contains("6e1") and !req.ranks.contains("6e3") and !req.ranks.contains("6e4")'>
            and  q.dtime1 is not null and   (q.checkUser2='未审核' or q.checkUser2&lt;&gt; '未审核' )
        </if>
        <if test='req.ranks.contains("6e3") and !req.ranks.contains("6e1") and !req.ranks.contains("6e2") and !req.ranks.contains("6e4")'>
            and  q.dtime1 is not null and q.dtime2 is not null and   (q.checkUser3='未审核' or q.checkUser3&lt;&gt; '未审核' )
        </if>
        <if test='req.ranks.contains("6e4") and !req.ranks.contains("6e1") and !req.ranks.contains("6e2") and !req.ranks.contains("6e3")'>
            and  q.dtime1 is not null and q.dtime2 is not null and q.dtime3 is not null and (q.checkUser4='未审核' or q.checkUser4&lt;&gt; '未审核' )
        </if>

        <if test="req.areaIds != null and req.areaIds.size > 0">
            and s.areaid in
            <foreach collection="req.areaIds" item="areaId" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>

        <if test="req.jiujian != null">
            <choose>
                <!--已返回-->
                <when test="req.jiujian == 1">
                    AND EXISTS(SELECT 1 FROM dbo.shouhou_huishou hs WITH(NOLOCK) INNER JOIN dbo.wxkcoutput wxk WITH(NOLOCK) ON wxk.id=hs.wxkcid WHERE hs.shouhou_id=s.id AND ISNULL(hs.isfan,0)=1)
                </when>
                <!--已换货-->
                <when test="req.jiujian == 2">
                    AND EXISTS(SELECT 1 FROM dbo.shouhou_huishou hs WITH(NOLOCK) INNER JOIN dbo.wxkcoutput wxk WITH(NOLOCK) ON wxk.id=hs.wxkcid WHERE hs.shouhou_id=s.id AND ISNULL(hs.ishuan,0)=1
                </when>
                <!--已回收-->
                <when test="req.jiujian == 3">
                    AND EXISTS(SELECT 1 FROM dbo.shouhou_huishou hs WITH(NOLOCK) INNER JOIN dbo.wxkcoutput wxk WITH(NOLOCK) ON wxk.id=hs.wxkcid WHERE hs.shouhou_id=s.id)
                </when>
            </choose>
        </if>

        <if test="req.ywb != null and req.ywb != 0">
            <choose>
                <when test="req.ywb == 999>">
                    and IsNull(s.servicetype, 0)= 0
                </when>
                <otherwise>
                    and IsNull(s.servicetype, 0) =#{req.ywb}
                </otherwise>
            </choose>
        </if>

        <if test="req.actionName == null or req.actionName == ''">
            <if test='req.shenhe != null'>
                <choose>
                    <when test="req.shenhe == 1">
                        and q.dtime1 is null and checkUser1='未审核'
                    </when>
                    <when test="req.shenhe == 2">
                        and (q.dtime1 is not null or checkUser1&lt;&gt;'未审核' ) and q.dtime2 is null and checkUser2='未审核'
                    </when>
                    <when test="req.shenhe == 3">
                        and (q.dtime1 is not null or checkUser1&lt;&gt;'未审核' ) and (q.dtime2 is not null or checkUser2&lt;&gt;'未审核') and q.dtime3 is null and checkUser3='未审核'
                    </when>
                    <when test="req.shenhe == 4">
                        and (q.dtime1 is not null or checkUser1&lt;&gt;'未审核' ) and (q.dtime2 is not null or checkUser2&lt;&gt;'未审核') and (q.dtime3 is not null or checkUser3&lt;&gt;'未审核') and q.dtime4 is null and checkUser4='未审核'
                    </when>
                    <when test="req.shenhe == 5">
                        and ((q.dtime1 is null and checkUser1='未审核' ) or ( q.dtime2 is null and checkUser2='未审核') or (q.dtime3 is null and checkUser3='未审核') or (q.dtime4 is null and checkUser4='未审核') )
                    </when>
                    <when test="req.shenhe == 6">
                        and (q.dtime1 is not null or checkUser1&lt;&gt;'未审核' ) and (q.dtime2 is not null or checkUser2&lt;&gt;'未审核') and (q.dtime3 is not null or checkUser3&lt;&gt;'未审核') and (q.dtime4 is not null or checkUser4&lt;&gt;'未审核')
                    </when>
                </choose>
            </if>
        </if>
        <if test="req.startTime != null and req.startTime != ''">
            and q.dtime &gt;= #{req.startTime}
        </if>
        <if test="req.endTime != null and req.endTime != ''">
            and q.dtime &lt;= #{req.endTime}
        </if>

    </select>

    <select id="getWxKcInfo" resultType="com.jiuji.oa.afterservice.bigpro.statistics.bo.WxKcInfoBo">
        SELECT k.wxid as wxId,k.name,k.ppriceid as ppid,
        CASE WHEN ISNULL(h.isfan,0)=1 THEN '已返回' WHEN  ISNULL(h.ishuanhuo,0)=1 THEN '已换货' WHEN h.id IS NOT NULL THEN '已回收' ELSE '' END AS hsStats
        FROM dbo.wxkcoutput k WITH(NOLOCK)
        LEFT JOIN dbo.shouhou_huishou h WITH(NOLOCK) ON h.wxkcid=k.id
        WHERE
        k.wxid in
        <foreach collection="req.shouhouIds" index="index" item="shouhouId" open="(" separator="," close=")">
            #{shouhouId}
        </foreach>
        and stats != 3
    </select>
</mapper>
