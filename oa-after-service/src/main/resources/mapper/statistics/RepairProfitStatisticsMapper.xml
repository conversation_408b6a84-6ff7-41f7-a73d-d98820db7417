<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.statistics.dao.RepairProfitStatisticsMapper">
    <resultMap id="statisticsMap" type="com.jiuji.oa.afterservice.statistics.vo.res.RepairProfitStatisticsVO">
        <result column="area" property="area"/>
        <result column="areaid" property="areaId"/>
        <result column="有毛配件量" property="hasProfitNum"/>
        <result column="无毛配件量" property="noProfitNum"/>
        <result column="毛利" property="profit"/>
        <result column="departRank" property="departRank"/>
        <result column="code" property="areaLevel1Code"/>
        <result column="areaCode" property="areaLevel2Code"/>
    </resultMap>

    <select id="profit" resultMap="statisticsMap">
        SELECT s.areaid,
        SUM(CASE WHEN (ISNULL(k.price, 0) - ISNULL(k.inprice, 0) &lt;= 0) THEN 1 ELSE 0 END) AS 无毛配件量,
        SUM(CASE WHEN (ISNULL(k.price, 0) - ISNULL(k.inprice, 0) > 0) THEN 1 ELSE 0 END) AS 有毛配件量,
        SUM(CASE
        WHEN ISNULL(k.price, 0) - ISNULL(k.inprice, 0) > 0 - ISNULL(k.youhuifeiyong,0) then ISNULL(k.price, 0) - ISNULL(k.inprice, 0) - ISNULL(k.youhuifeiyong,0)
        else 0 end) AS 毛利
        FROM dbo.wxkcoutput k WITH (nolock)
        LEFT JOIN productinfo p WITH (nolock) ON p.ppriceid = k.ppriceid
        LEFT JOIN shouhou s WITH (NOLOCK) ON s.id = k.wxid
        WHERE ISNULL(s.isquji, 0) = 1
        AND k.ppriceid &lt;&gt; 0
        <choose>
            <when test="cid == 728">
                and p.cid in (728)
            </when>
            <otherwise>
                and p.cid = #{cid}
            </otherwise>
        </choose>
        <if test="req.start != null">
            AND s.offtime >= #{req.start}
        </if>
        <if test="req.end != null">
            AND s.offtime &lt;= #{req.end}
        </if>
        <if test="req.brands !=null and req.brands.size > 0">
            and p.brandid in
            <foreach collection="req.brands" index="index" item="brandId" open="(" close=")" separator=",">
                #{brandId}
            </foreach>
        </if>
        <if test="req.areaIds !=null and req.areaIds.size > 0">
            and s.areaid in
            <foreach collection="req.areaIds" index="index" item="areaId" open="(" close=")" separator=",">
                #{areaId}
            </foreach>
        </if>
        GROUP BY s.areaid
    </select>

    <select id="areaSelect" resultMap="statisticsMap">
        select ai.area,ai.id as areaid, dbo.getDepartTypeId(ai.depart_id,4) as areaCode,dbo.getDepartTypeId(ai.depart_id,3) as code,ISNULL(departRank,99) departRank
        FROM areainfo ai with(nolock)
        LEFT JOIN dbo.departInfo d WITH(NOLOCK) ON d.id=ai.depart_id
        WHERE ai.ispass = 1
        <if test="req.areaKind != null">
            AND ai.kind1 = #{req.areaKind}
        </if>
        <if test="req.areaIds !=null and req.areaIds.size > 0">
            and ai.id in
            <foreach collection="req.areaIds" index="index" item="areaId" open="(" close=")" separator=",">
                #{areaId}
            </foreach>
        </if>
        <if test="req.authPart != null and req.authPart == true">
            and ai.ispass=1 and ai.authorizeid=#{req.authorizeId}
        </if>
    </select>
</mapper>
