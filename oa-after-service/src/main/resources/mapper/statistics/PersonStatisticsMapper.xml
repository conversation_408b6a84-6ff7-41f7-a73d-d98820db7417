<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.statistics.dao.PersonStatisticsMapper">
    <resultMap id="statisticsMap" type="com.jiuji.oa.afterservice.statistics.vo.res.PersonStatisticsVO">
        <result column="员工" property="yuanGong"/>
        <result column="地区" property="diQu"/>
        <result column="软件评价总量" property="ruanJianPingJiaZongLiang"/>
        <result column="软件评价平均分" property="ruanJianPingJiaPingJunFen"/>
        <result column="软件评价差评量" property="ruanJianPingJiaChaPingLiang"/>
        <result column="售后维修评价总量" property="shouHouWeiXiuPingJiaZongLiang"/>
        <result column="售后评价平均分" property="shouHouWeiXiuPingJiaPingJunFen"/>
        <result column="售后维修评价差评量" property="shouHouWeiXiuPingJiaChaPingLiang"/>
        <result column="售后技术客服评价总量" property="shouHouJiShuKeFuPingJiaZongLiang"/>
        <result column="售后技术客服评价平均分" property="shouHouJiShuKeFuPingJiaPingJunFen"/>
        <result column="售后技术客服评价差评量" property="shouHouJiShuKeFuPingJiaChaPingLiang"/>
        <result column="总接件量" property="zongJieJianLiang"/>
        <result column="在保量" property="zaiBaoLiang"/>
        <result column="不在保量" property="buZaiBaoLiang"/>
        <result column="外修量" property="waiXiuLiang"/>
        <result column="九机服务量" property="jiuJiFuWuLiang"/>
        <result column="上门安装量" property="shangMenAnZhuangLiang"/>
        <result column="上门安装毛利" property="shangMenAnZhuangMaoLi"/>
        <result column="接件人维修毛利" property="jieJianRenWeiXiuMaoLi"/>
        <result column="接件人旧件毛利" property="jieJianRenJiuJianMaoLi"/>
        <result column="接件人置换旧件毛利" property="jieJianRenZhiHuanJiuJianMaoLi"/>
        <result column="接件人置换量" property="jieJianRenZhiHuanLiang"/>
        <result column="软件安装量" property="ruanJianAnZhuangLiang"/>
        <result column="软件接件量" property="ruanJianJieJianLiang"/>
        <result column="个人手机销量" property="geRengShouJiXiaoLiang"/>
        <result column="软件外修毛利" property="ruanJianWaiXiuMaoLi"/>
        <result column="总修好量" property="zongXiuHaoLiang"/>
        <result column="置换量" property="zhiHuanLiang"/>
        <result column="上门量" property="shangMenLiang"/>
        <result column="硬盘量" property="yingPanLiang"/>
        <result column="外送修好量" property="waiSongXiuHaoLiang"/>
        <result column="更配修好量" property="gengPeiXiuHaoLiang"/>
        <result column="芯片修好量" property="xinPianXiuHaoLiang"/>
        <result column="返修量" property="fanXiuLiang"/>
        <result column="修不好量" property="xiuBuHaoLiang"/>
        <result column="维修毛利" property="weiXiuMaoLi"/>
        <result column="自接自修毛利" property="ziJieZiXiuMaoLi"/>
        <result column="维修人旧件毛利" property="weiXiuRenJiuJianMaoLi"/>
        <result column="维修人置换旧件毛利" property="weiXiuRenZhiHuanJiuJianMaoLi"/>
        <result column="测试总量" property="ceShiZongLiang"/>
        <result column="测试不通过量" property="ceShiBuTongGuoLiang"/>
        <result column="无法测试量" property="wuFaCeShiLiang"/>
        <result column="修不好率" property="xiuBuHaoLv"/>
        <result column="返修率" property="fanXiuLv"/>
        <result column="无毛利芯片修好量" property="wuMaoLiXinPianXiuHaoLiang"/>
        <result column="无毛利更配修好量" property="wuMaoLiGengPeiXiuHaoLiang"/>
        <result column="无毛利硬盘修好量" property="wuMaoLiYingPanXiuHaoLiang"/>
        <result column="非保外送及换货" property="feiBaoWaiSongJiHuanHuoLiang"/>
        <result column="预约接件量" property="yuYueJieJianLiang"/>
        <result column="售后电池险量" property="shouHouDianChiXianLiang"/>
        <result column="售后电池险金额" property="shouHouDianChiXianJinE"/>
        <result column="售后电池出库量" property="shouHouDianChiChuKuLiang"/>
        <result column="售后电池险出险成本" property="shouHouDianChiXianChuXianChengBen"/>
        <result column="售后碎屏险量" property="shouHouSuiPingXianLiang"/>
        <result column="售后碎屏险金额" property="shouHouSuiPingXianJinE"/>
        <result column="售后屏幕出库量" property="shouHouPingMuChuKuLiang"/>
        <result column="售后碎屏险出险成本" property="shouHouSuiPingXianChuXianChengBen"/>
        <result column="换外屏量" property="huanWaiPingLiang"/>
        <result column="换外屏返修量" property="huanWaiPingFanXiuLiang"/>
        <result column="换外屏毛利" property="huanWaiPingMaoLi"/>
        <result column="iszaizhi" property="isZaizhi"/>
    </resultMap>

    <sql id="shouHou">
        SELECT id
             , inuser
             , s.weixiuren
             , ISNULL(baoxiu, 0)      baoxiu
             , ISNULL(s.stats, 0)     stats
             , ISNULL(s.wxkind, 0)    wxkind
             , feiyong
             , costprice
             , ISNULL(isfan, 0)       isfan
             , ISNULL(ServiceType, 0) ServiceType
             , ISNULL(istui, 0)       istui
             , yuyueid
             , weixiuzuid
             , RepairLevel
             , webtype2
             , ProcessConfirmStats
        FROM dbo.shouhou s WITH (NOLOCK)
        WHERE s.isquji = 1
          AND s.offtime BETWEEN #{req.start} AND #{req.end}
          AND ISNULL(s.issoft, 0) = 0
          and ISNULL(s.xianshi, 0) = 1
        <if test="req.authPart != null and req.authPart == true">
            and exists(select 1 from areainfo a with(nolock) where a.ispass=1 and a.authorizeid=#{req.authorizeId} and a.id=s.areaid )
        </if>

    </sql>

    <sql id="queryWhere">
        <if test="req.areaIds != null and req.areaIds.size > 0">
            and c.area1id in
            <foreach collection="req.areaIds" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        <if test="req.areaKind != null and req.areaKind > 0">
            and a.kind1 = #{req.areaKind}
        </if>
    </sql>

    <select id="selectWithNoJoin1" resultMap="statisticsMap">
        <include refid="sonSelectSql-1"/>
    </select>
    <select id="selectWithNoJoin2" resultMap="statisticsMap">
        <include refid="sonSelectSql-2"/>
    </select>
    <select id="selectWithNoJoin3" resultMap="statisticsMap">
        <include refid="sonSelectSql-3"/>
    </select>
    <select id="selectWithNoJoin4" resultMap="statisticsMap">
        <include refid="sonSelectSql-4"/>
    </select>
    <select id="selectWithNoJoin5" resultMap="statisticsMap">
        <include refid="sonSelectSql-5"/>
    </select>
    <select id="selectWithNoJoin6" resultMap="statisticsMap">
        <include refid="sonSelectSql-6"/>
    </select>
    <select id="selectWithNoJoin7" resultMap="statisticsMap">
        <include refid="sonSelectSql-7"/>
    </select>
    <select id="selectWithNoJoin8" resultMap="statisticsMap">
        <include refid="sonSelectSql-8"/>
    </select>
    <select id="selectWithNoJoin9" resultMap="statisticsMap">
        <include refid="sonSelectSql-9"/>
    </select>
    <select id="selectWithNoJoin10" resultMap="statisticsMap">
        <include refid="sonSelectSql-10"/>
    </select>
    <select id="selectWithNoJoin11" resultMap="statisticsMap">
        <include refid="sonSelectSql-11"/>
    </select>
    <select id="selectWithNoJoin12" resultMap="statisticsMap">
        <include refid="sonSelectSql-12"/>
    </select>
    <select id="selectWithNoJoin13" resultMap="statisticsMap">
        <include refid="sonSelectSql-13"/>
    </select>
    <select id="selectWithNoJoin14" resultMap="statisticsMap">
        <include refid="sonSelectSql-14"/>
    </select>
    <select id="selectWithNoJoin15" resultMap="statisticsMap">
        <include refid="sonSelectSql-15"/>
    </select>
    <select id="selectWithNoJoin16" resultMap="statisticsMap">
        <include refid="sonSelectSql-16"/>
    </select>
    <select id="selectWithNoJoin17" resultMap="statisticsMap">
        <include refid="sonSelectSql-17"/>
    </select>
    <select id="selectWithNoJoin18" resultMap="statisticsMap">
        <include refid="sonSelectSql-18"/>
    </select>
    <select id="selectWithNoJoin19" resultMap="statisticsMap">
        <include refid="sonSelectSql-19"/>
    </select>
    <select id="selectWithNoJoin20" resultMap="statisticsMap">
        <include refid="sonSelectSql-20"/>
    </select>
    <select id="selectWithNoJoin21" resultMap="statisticsMap">
        <include refid="sonSelectSql-21"/>
    </select>
    <select id="selectWithNoJoin22" resultMap="statisticsMap">
        <include refid="sonSelectSql-22"/>
    </select>
    <select id="selectWithNoJoin23" resultMap="statisticsMap">
        <include refid="sonSelectSql-23"/>
    </select>
    <select id="selectWithNoJoin24" resultMap="statisticsMap">
        <include refid="sonSelectSql-24"/>
    </select>
    <select id="selectWithNoJoin25" resultMap="statisticsMap">
        <include refid="sonSelectSql-25"/>
    </select>
    <select id="selectWithNoJoin26" resultMap="statisticsMap">
        <include refid="sonSelectSql-26"/>
    </select>
    <select id="selectWithNoJoin27" resultMap="statisticsMap">
        <include refid="sonSelectSql-27"/>
    </select>
    <select id="selectWithNoJoin28" resultMap="statisticsMap">
        <include refid="sonSelectSql-28"/>
    </select>
    <select id="selectWithNoJoin29" resultMap="statisticsMap">
        <include refid="sonSelectSql-29"/>
    </select>

    <sql id="isBindCh999User">
        exists(select 1 from ch999UserBind cub with(nolock) where cub.ch999Id = c.ch999_id and cub.bindCh999Id is not null)
    </sql>
    <sql id="sonSelectSql-1">
        select aa.员工, 地区,bb.个人手机销量,aa.iszaizhi from

        (SELECT ch999_name AS 员工, a.area AS 地区,a.ispass,a.authorizeid,isnull(c.iszaizhi,0) iszaizhi
        FROM ch999_user c with (nolock)
        LEFT JOIN areainfo a with (nolock) ON c.area1id = a.id
        WHERE 1=1 and not <include refid="isBindCh999User"></include>
        <include refid="queryWhere"/>
        ) aa

        left join

        (SELECT
        a.area,
        SUM ( b.basket_count ) 个人手机销量
        FROM
        dbo.basket b WITH ( nolock )
        LEFT JOIN dbo.sub s WITH ( nolock ) ON b.sub_id= s.sub_id
        LEFT JOIN dbo.productinfo p WITH ( nolock ) ON b.ppriceid= p.ppriceid
        LEFT JOIN dbo.areainfo a WITH ( nolock ) ON a.id= s.areaid
        WHERE
        isnull( b.isdel, 0 ) = 0
        AND s.sub_check in (3,9)
        AND s.tradeDate1 BETWEEN #{req.start} AND #{req.end}
        AND p.ismobile1 = 1
        group by a.area
        ) bb

        on aa.地区=bb.area
        <if test="req.authPart != null and req.authPart == true">
            and aa.ispass=1 and aa.authorizeid=#{req.authorizeId}
        </if>
        where 1=1
        order by aa.地区


    </sql>
    <sql id="sonSelectSql-2">
        SELECT c.ch999_name                      AS 员工,
               SUM(CASE
                       WHEN (c.ch999_id = es.relateCh999id) AND ISNULL(es.job, 0) = 13 AND ISNULL(type_, 0) = 6 AND
                            Score IS NOT NULL THEN 1
                       ELSE 0 END)               AS 软件评价总量,
               avg(CASE
                       WHEN (c.ch999_id = es.relateCh999id) AND ISNULL(es.job, 0) = 13 AND ISNULL(type_, 0) = 6 THEN
                           ISNULL(score, 0) END) AS 软件评价平均分,
               SUM(CASE
                       WHEN (c.ch999_id = es.relateCh999id) AND ISNULL(es.job, 0) = 13 AND ISNULL(type_, 0) = 6 AND
                            ISNULL(score, 0) &lt;= 2 AND Score IS NOT NULL THEN 1
                       ELSE 0 END)               AS 软件评价差评量,
               SUM(CASE
                       WHEN (c.ch999_id = es.relateCh999id) AND ISNULL(es.job, 0) = 8 AND ISNULL(type_, 0) = 3 AND
                            Score IS NOT NULL THEN 1
                       ELSE 0 END)               AS 售后维修评价总量,
               avg(CASE
                       WHEN (c.ch999_id = es.relateCh999id) AND ISNULL(es.job, 0) = 8 AND ISNULL(type_, 0) = 3 THEN
                           ISNULL(score, 0) END) AS 售后评价平均分,
               SUM(CASE
                       WHEN (c.ch999_id = es.relateCh999id) AND ISNULL(es.job, 0) = 8 AND ISNULL(type_, 0) = 3 AND
                            ISNULL(score, 0) &lt;= 2 AND Score IS NOT NULL THEN 1
                       ELSE 0 END)               AS 售后维修评价差评量,
               SUM(CASE
                       WHEN (c.ch999_id = es.relateCh999id) AND ISNULL(es.job, 0) = 7 AND ISNULL(type_, 0) = 3 AND
                            Score IS NOT NULL THEN 1
                       ELSE 0 END)               AS 售后技术客服评价总量,
               avg(CASE
                       WHEN (c.ch999_id = es.relateCh999id) AND ISNULL(es.job, 0) = 7 AND ISNULL(type_, 0) = 3 THEN
                           ISNULL(score, 0) END) AS 售后技术客服评价平均分,
               SUM(CASE
                       WHEN (c.ch999_id = es.relateCh999id) AND ISNULL(es.job, 0) = 7 AND ISNULL(type_, 0) = 3 AND
                            ISNULL(score, 0) &lt;= 2 AND Score IS NOT NULL THEN 1
                       ELSE 0 END)               AS 售后技术客服差评量
        FROM ${officeName}..EvaluateScore es with (nolock)
                 LEFT JOIN ch999_user c with (nolock) ON c.ch999_id = es.relateCh999id
        WHERE es.dtime BETWEEN #{req.start} AND #{req.end} and not <include refid="isBindCh999User"></include>
        GROUP BY c.ch999_name
    </sql>
    <sql id="sonSelectSql-3">
        SELECT COUNT(1) AS 总接件量, inuser AS 员工
        FROM (<include refid="shouHou"/>) s
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-4">
        SELECT COUNT(1) AS 在保量,s.inuser AS 员工 FROM (<include refid="shouHou"/>) s
        LEFT JOIN shouhou_qudao sq with(nolock) ON s.id = sq.shouhouid
        WHERE s.baoxiu=1
        AND (s.isfan=0 OR (s.isfan=1 AND s.feiyong+s.costprice>0)) AND s.ServiceType=0
        AND (sq.shouhouid IS NOT NULL OR (
        sq.shouhouid IS NULL AND ( s.costprice&lt;&gt;0 OR
        (s.costprice=0 AND ISNULL(s.istui,0)=0 AND EXISTS(SELECT 1 FROM dbo.shouhou_tuihuan with(nolock) WHERE
        shouhou_id=s.id AND ISNULL(isdel,0)=0)) )
        ))
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-5">
        SELECT COUNT(1) AS 不在保量, inuser AS 员工
        FROM (<include refid="shouHou"/>) s
        WHERE s.baoxiu = 0
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-6">
        SELECT COUNT(1) AS 外修量, inuser AS 员工
        FROM (<include refid="shouHou"/>) s
        WHERE s.baoxiu = 2
        AND (s.feiyong > 0 or s.costprice > 0)
        GROUP BY inuser
    </sql>
    <sql id="sonSelectSql-7">
        SELECT COUNT(1) AS 九机服务量,s.inuser as 员工 FROM (<include refid="shouHou"/>) s
        WHERE s.ServiceType&lt;&gt;0
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-8">
        SELECT SUM(CASE WHEN (s.wxkind=5 and p.cid =31) THEN 0 ELSE ISNULL(k.wxtongjitotal,0)-k.inprice END) AS
        接件人维修毛利,s.inuser AS 员工
        FROM dbo.wxkcoutput k with (nolock) INNER JOIN (<include refid="shouHou"/>) s ON k.wxid=s.id
        left join productinfo p with(nolock) on p.ppriceid = k.ppriceid
        WHERE s.stats=1 AND s.baoxiu&lt;&gt;1 AND s.istui=0
        and s.inuser
        &lt;&gt;
        s.weixiuren AND k.[stats]&lt;&gt;3
        AND (s.ServiceType=0 OR (s.ServiceType&lt;&gt;0 AND ISNULL(k.wxtongjitotal,0)>0) )
        AND NOT EXISTS(SELECT 1 FROM shouhou_huishou sh WITH(NOLOCK) where ISNULL(ishuanhuo,0) = 1 and sh.shouhou_id =
        s.id AND sh.wxkcid=k.id)
        AND NOT EXISTS(SELECT 1 FROM dbo.wxkcoutput wk WITH(NOLOCK) WHERE wk.wxid=s.id AND ((s.ServiceType=9 AND
        p.cid=31) OR (s.ServiceType=10 AND p.cid=393) ))
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-9">
        SELECT s.inuser              AS 员工,
        sum((isnull(sh.saleprice,0)-isnull(sh.price,0))/2.0)  AS 接件人旧件毛利
        FROM shouhou s with (nolock)
        left JOIN shouhou_huishou sh with (nolock) ON s.id = sh.shouhou_id
        LEFT JOIN ch999_user c with (nolock) ON c.ch999_name = s.inuser
        WHERE ISNULL(s.wxkind, 0)  &lt;&gt; 5
        AND ISNULL(s.issoft, 0) = 0
        AND sh.sdtime BETWEEN #{req.start} AND #{req.end} AND s.isquji=1
        AND sh.issale = 1 and
        sh.isfan = 0 and not <include refid="isBindCh999User"></include>
        <if test="req.authPart != null and req.authPart == true">
            and exists(select 1 from areainfo a with(nolock) where a.ispass=1 and a.authorizeid=#{req.authorizeId} and a.id=s.areaid )
        </if>
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-10">
        SELECT s.inuser                   AS 员工,
               SUM((k.price - k.inprice) +
                   (ISNULL(CASE WHEN sh.toareaid =  #{req.yaPingAreaId} THEN sh.inprice ELSE sh.saleprice END, 0) -
                    ISNULL(sh.price, 0))) AS 接件人置换旧件毛利
        FROM shouhou s with (nolock)
                 INNER JOIN shouhou_huishou sh with (nolock) ON s.id = sh.shouhou_id
                 INNER JOIN dbo.wxkcoutput k WITH (NOLOCK) ON k.wxid = s.id
                 INNER JOIN dbo.productinfo p WITH (NOLOCK) ON p.ppriceid = k.ppriceid
        WHERE ISNULL(s.wxkind, 0) = 5
          AND ISNULL(s.issoft, 0) = 0
          AND s.wxkind = 5
          AND p.cid = 31
          AND ((sh.sdtime BETWEEN #{req.start} AND #{req.end} AND ISNULL(sh.toareaid, 0) =  #{req.yaPingAreaId})
            OR (ISNULL(sh.toareaid, 0) !=  #{req.yaPingAreaId} AND sh.saledate BETWEEN #{req.start} AND #{req.end} AND sh.issale = 1 and
                sh.isfan = 0))
        <if test="req.authPart != null and req.authPart == true">
            and exists(select 1 from areainfo a with(nolock) where a.ispass=1 and a.authorizeid=#{req.authorizeId} and a.id=s.areaid )
        </if>

        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-11">
        SELECT inuser                                                                         AS 员工,
               COUNT(1)                                                                       AS 软件接件量,
               SUM(CASE WHEN ISNULL(s.baoxiu, 0) = 2 THEN s.feiyong - s.costprice ELSE 0 END) AS 软件外修毛利
        FROM shouhou s with (nolock)
        WHERE s.stats = 1
          AND ISNULL(s.isquji, 0) = 1
          AND ISNULL(s.xianshi, 0) = 1
          AND ISNULL(s.issoft, 0) = 1
          AND s.offtime BETWEEN #{req.start} AND #{req.end}
        <if test="req.authPart != null and req.authPart == true">
            and exists(select 1 from areainfo a with(nolock) where a.ispass=1 and a.authorizeid=#{req.authorizeId} and a.id=s.areaid )
        </if>

        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-12">
        SELECT weixiuren AS 员工,
        COUNT(1) AS 总修好量,
        SUM(CASE WHEN ISNULL(s.wxkind, 0) = 5 THEN 1 ELSE 0 END) AS 置换量,
        SUM(CASE WHEN ISNULL(s.wxkind, 0) IN (5, 6) AND ISNULL(s.webtype2, 0) = 4 THEN 1 ELSE 0 END) AS 上门量,
        SUM(CASE WHEN ISNULL(s.RepairLevel, 0) = 4 THEN 1 ELSE 0 END) AS 硬盘量
        FROM (<include refid="shouHou"/>) s
        WHERE s.stats = 1
        AND (s.costprice > 0 OR s.feiyong > 0)
        GROUP BY s.weixiuren
    </sql>
    <sql id="sonSelectSql-13">
        SELECT weixiuren AS 员工, COUNT(1) AS 外送修好量
        FROM (<include refid="shouHou"/>) s
        INNER JOIN shouhou_qudao sq with (nolock) ON s.id = sq.shouhouid
        WHERE s.stats = 1
        GROUP BY s.weixiuren
    </sql>
    <sql id="sonSelectSql-14">
        SELECT weixiuren AS 员工,
        SUM(CASE WHEN ISNULL(RepairLevel, 0) = 1 THEN 1 ELSE 0 END) AS 更配修好量,
        SUM(CASE WHEN ISNULL(RepairLevel, 0) = 2 THEN 1 ELSE 0 END) AS 芯片修好量,
        sum(CASE WHEN ISNULL(s.isfan, 0) = 1 THEN 1 ELSE 0 end) AS 返修量
        FROM (<include refid="shouHou"/>) s
        WHERE s.stats = 1
        AND (s.feiyong > 0 OR s.costprice > 0)
        AND NOT EXISTS(SELECT 1 FROM dbo.shouhou_qudao sq WITH (NOLOCK) WHERE sq.shouhouid = s.id)
        GROUP BY s.weixiuren
    </sql>
    <sql id="sonSelectSql-15">
        SELECT weixiuren AS 员工, COUNT(1) AS 修不好量
        FROM (<include refid="shouHou"/>) s
        WHERE s.stats = 3
        GROUP BY s.weixiuren
    </sql>
    <sql id="sonSelectSql-16">
        SELECT weixiuren AS 员工,
        SUM(CASE WHEN (s.wxkind=5 and p.cid=31) OR (s.inuser=s.weixiuren) THEN 0 ELSE ISNULL(k.wxtongjitotal,0) -
        k.inprice END) AS 维修毛利
        ,SUM(CASE WHEN (s.wxkind&lt;&gt;5 OR p.cid&lt;&gt;31) AND s.inuser=s.weixiuren THEN ISNULL(k.wxtongjitotal,0) -
        k.inprice
        ELSE 0 END ) 自接自修毛利
        FROM (<include refid="shouHou"/>) s INNER JOIN dbo.wxkcoutput k WITH(NOLOCK) ON s.id=k.wxid
        left join productinfo p with(nolock) on p.ppriceid = k.ppriceid
        WHERE s.stats=1 AND s.baoxiu&lt;&gt;1 AND s.istui=0
        AND (s.ServiceType=0 OR (s.ServiceType&lt;&gt;0 AND ISNULL(k.wxtongjitotal,0)>0) )
        and not exists(select id from shouhou_huishou sh with(nolock) where ISNULL(ishuanhuo,0) = 1 and sh.shouhou_id =
        s.id AND sh.wxkcid=k.id )
        AND ISNULL(k.stats,0)&lt;&gt;3
        AND NOT EXISTS(SELECT 1 FROM dbo.wxkcoutput wk WITH(NOLOCK) WHERE wk.wxid=s.id AND ((s.ServiceType=9 AND
        p.cid=31) OR (s.ServiceType=10 AND p.cid=393) ))
        GROUP BY s.weixiuren ORDER BY s.weixiuren
    </sql>
    <sql id="sonSelectSql-17">
        SELECT s.weixiuren              AS 员工,
        sum((isnull(sh.saleprice,0)-isnull(sh.price,0))/2.0)  AS 维修人旧件毛利
        FROM shouhou s with (nolock)
                 left JOIN shouhou_huishou sh with (nolock) ON s.id = sh.shouhou_id
                 LEFT JOIN ch999_user c with (nolock) ON c.ch999_name = s.inuser
        WHERE ISNULL(s.wxkind, 0)  &lt;&gt; 5
          AND ISNULL(s.issoft, 0) = 0
          AND sh.sdtime BETWEEN #{req.start} AND #{req.end} AND s.isquji=1
          AND sh.issale = 1 and
          sh.isfan = 0 and not <include refid="isBindCh999User"></include>
        <if test="req.authPart != null and req.authPart == true">
            and exists(select 1 from areainfo a with(nolock) where a.ispass=1 and a.authorizeid=#{req.authorizeId} and a.id=s.areaid )
        </if>
        GROUP BY s.weixiuren
    </sql>
    <sql id="sonSelectSql-18">
        SELECT s.weixiuren                AS 员工,
               SUM((k.price - k.inprice) +
                   (ISNULL(CASE WHEN sh.toareaid =  #{req.yaPingAreaId} THEN sh.inprice ELSE sh.saleprice END, 0) -
                    ISNULL(sh.price, 0))) AS 维修人置换旧件毛利
        FROM shouhou s with (nolock)
                 INNER JOIN shouhou_huishou sh with (nolock) ON s.id = sh.shouhou_id
                 INNER JOIN dbo.wxkcoutput k WITH (NOLOCK) ON k.wxid = s.id
                 INNER JOIN dbo.productinfo p WITH (NOLOCK) ON p.ppriceid = k.ppriceid
        WHERE ISNULL(s.wxkind, 0) = 5
          AND ISNULL(s.issoft, 0) = 0
          AND s.wxkind = 5
          AND p.cid = 31
          AND ((sh.sdtime BETWEEN #{req.start} AND #{req.end} AND ISNULL(sh.toareaid, 0) =  #{req.yaPingAreaId})
            OR (ISNULL(sh.toareaid, 0) !=  #{req.yaPingAreaId} AND sh.saledate BETWEEN #{req.start} AND #{req.start} AND
                sh.issale = 1 and
                sh.isfan = 0))
        <if test="req.authPart != null and req.authPart == true">
            and exists(select 1 from areainfo a with(nolock) where a.ispass=1 and a.authorizeid=#{req.authorizeId} and a.id=s.areaid )
        </if>

        GROUP BY s.weixiuren
    </sql>
    <sql id="sonSelectSql-19">
        SELECT t.inuser as 员工
        , SUM(CASE WHEN t.testResult = 1 THEN 1 ELSE 0 END) AS 测试总量
        , SUM(CASE WHEN t.testResult = 0 THEN 1 ELSE 0 END) AS 测试不通过量
        , SUM(CASE WHEN t.processConfirmStats = '4' THEN 1 ELSE 0 END) 无法测试量
        FROM (
        SELECT ts.inuser,
        ISNULL(ts.testResult, 0) testResult,
        s.ProcessConfirmStats,
        ROW_NUMBER() OVER (PARTITION BY s.id ORDER BY ts.id DESC) r
        FROM (<include refid="shouHou"/>) s
        LEFT JOIN dbo.shouhoutestInfo ts WITH (NOLOCK) ON s.id = ts.shouhou_id
        WHERE s.stats = 1
        ) t
        WHERE t.r = 1
        GROUP BY t.inuser
    </sql>
    <sql id="sonSelectSql-20">
        SELECT weixiuren AS 员工,
        SUM(CASE WHEN ISNULL(RepairLevel,0) = 2 THEN 1 ELSE 0 END ) AS 无毛利芯片修好量,
        SUM(CASE WHEN ISNULL(RepairLevel,0) = 1 THEN 1 ELSE 0 END ) AS 无毛利更配修好量,
        SUM(CASE WHEN ISNULL(RepairLevel,0) = 4 THEN 1 ELSE 0 END ) AS 无毛利硬盘修好量
        FROM (<include refid="shouHou"/>) s
        WHERE s.stats=1 AND s.feiyong+s.costprice>0
        AND s.feiyong&lt;= s.costprice
        AND s.isfan= 0
        AND NOT EXISTS(SELECT 1 FROM dbo.shouhou_qudao sq WITH(NOLOCK) WHERE sq.shouhouid=s.id)
        GROUP BY s.weixiuren
    </sql>
    <sql id="sonSelectSql-21">
        SELECT s.inuser AS 员工, COUNT(s.id) 接件人置换量
        FROM (<include refid="shouHou"/>) s
        WHERE s.stats = 1
        AND s.wxkind = 5
        AND s.feiyong + s.costprice > 0
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-22">
        SELECT COUNT(1) AS 非保外送及换货,s.inuser AS 员工 FROM (<include refid="shouHou"/>) s
        LEFT JOIN shouhou_qudao sq with(nolock) ON s.id = sq.shouhouid
        WHERE s.stats=1 AND s.baoxiu&lt;&gt;1 AND s.feiyong+s.costprice>0 AND s.ServiceType=0
        and (exists(select id from shouhou_huishou sh with(nolock) where ISNULL(ishuanhuo,0) = 1
        and sh.shouhou_id = s.id) or sq.shouhouid is not null)
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-23">
        SELECT COUNT(1) as 预约接件量, y.inuser as 员工
        FROM (<include refid="shouHou"/>) s
        INNER JOIN dbo.shouhou_yuyue y WITH (NOLOCK) ON y.id = s.yuyueid
        WHERE y.stats IN (3, 6, 7)
        AND s.stats = 1
        AND s.baoxiu IN (0, 2)
        AND s.feiyong + s.costprice > 0
        AND y.fchecktime BETWEEN #{req.start} and #{req.end}
        group by y.inuser
    </sql>
    <sql id="sonSelectSql-24">
        SELECT s.inuser 员工,COUNT( DISTINCT s.id) 上门安装量,SUM(ISNULL(k.wxtongjitotal,0)-ISNULL(k.inprice,0)) 上门安装毛利
        FROM (<include refid="shouHou"/>) s INNER JOIN dbo.shouhou_yuyue y WITH(NOLOCK) ON y.id=s.yuyueid
        LEFT JOIN dbo.wxkcoutput k WITH(NOLOCK) ON k.wxid=s.id AND k.stats&lt;&gt;3
        WHERE y.stype=6 AND s.stats=1 AND s.feiyong+s.costprice>0
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-25">
        SELECT s.inuser as 员工
        , SUM(CASE WHEN wk.ppriceid=81683 THEN 1 ELSE 0 END) 售后电池险量
        , SUM(CASE WHEN wk.ppriceid=81683 THEN wk.price ELSE 0 END)售后电池险金额
        , SUM(CASE WHEN wk.ppriceid IN
        (51626,44001,62793,61811,61811,61811,44001,51626,51626,44001,51626,52216,47026,52216,61811,52216,62320,54643,84936,84935,58904,56910,58904,62320,43280,54643,54643,61824,61824,61824,61824,61824,25783,25938,34781,34782,57331,57334,57330,57332,44903,46484,62557,59610,62594,62595,76555,76554,63822)
        THEN 1 ELSE 0 END) 售后电池出库量
        , SUM(CASE WHEN ISNULL(s.ServiceType,0)=10 AND p.cid=393 AND wk.price=0 THEN wk.inprice ELSE 0 END) 售后电池险出险成本
        , SUM(CASE WHEN wk.ppriceid=81682 THEN 1 ELSE 0 END) 售后碎屏险量
        , SUM(CASE WHEN wk.ppriceid=81682 THEN wk.price ELSE 0 END) 售后碎屏险金额
        , SUM(CASE WHEN wk.ppriceid
        IN(31629,31634,34481,34483,74892,74914,83341,83340,83342,44902,71966,44909,58881,58880,83343,83344,83345,83346,72594,72596,72592,72590,25080,25088,34477,34482,74891,74908,44901,71964,44908,58878,58879,72593,72595,72591,72589,59989,71790,85840,85791,70933,70932,82219,72933,82110,83349,83347,83350,83351)
        THEN 1 ELSE 0 END) 售后屏幕出库量
        , SUM(CASE WHEN ISNULL(s.ServiceType,0)=9 AND p.cid=31 THEN wk.inprice ELSE 0 END) 售后碎屏险出险成本
        FROM (<include refid="shouHou"/>) s INNER JOIN dbo.wxkcoutput wk WITH(NOLOCK) ON wk.wxid=s.id LEFT JOIN
        dbo.productinfo p WITH(NOLOCK) ON
        p.ppriceid=wk.ppriceid
        WHERE s.stats=1 AND ISNULL(wk.stats,0)&lt;&gt;3
        GROUP BY s.inuser
    </sql>
    <sql id="sonSelectSql-26">
        SELECT a.inuser 员工,换外屏量,换外屏返修量,换外屏毛利 FROM (
        SELECT s.inuser,COUNT(1) 换外屏量 ,SUM(CASE WHEN s.isfan=1 THEN 1 ELSE 0 END ) 换外屏返修量
        FROM (<include refid="shouHou"/>) s WHERE s.stats=1 AND s.weixiuzuid=96
        AND s.istui=0 AND s.feiyong+s.costprice>0
        GROUP BY s.inuser
        ) a LEFT JOIN (
        SELECT s.inuser ,SUM(wk.wxtongjitotal-wk.inprice) 换外屏毛利
        FROM (<include refid="shouHou"/>) s INNER JOIN dbo.wxkcoutput wk WITH(NOLOCK) ON wk.wxid=s.id
        WHERE s.stats=1 AND s.weixiuzuid=96 AND wk.ppriceid=0
        AND s.istui=0 AND ISNULL(wk.stats,0)&lt;&gt;3
        AND NOT EXISTS(SELECT 1 FROM dbo.shouhou_huishou hs WITH(NOLOCK) WHERE hs.wxkcid=wk.id AND hs.ishuanhuo=1)
        GROUP BY s.inuser
        ) b ON a.inuser=b.inuser
    </sql>
    <sql id="sonSelectSql-27">
        SELECT inuser 员工, COUNT(id) 软件安装量
        FROM msoft with (nolock)
        WHERE isticheng = 1
          AND modidate BETWEEN #{req.start} AND #{req.end}
        group by inuser
    </sql>
    <sql id="sonSelectSql-28">
	    SELECT
	    s.Inuser 员工,
	    SUM ( b.basket_count ) 个人手机销量
        FROM
	    dbo.basket b WITH ( nolock )
	    LEFT JOIN dbo.sub s WITH ( nolock ) ON b.sub_id= s.sub_id
	    LEFT JOIN dbo.productinfo p WITH ( nolock ) ON b.ppriceid= p.ppriceid
        WHERE
	    isnull( b.isdel, 0 ) = 0
	    AND s.sub_check= 3
	    AND s.tradeDate1 BETWEEN #{req.start} AND #{req.end}
	    AND p.ismobile1 = 1
        GROUP BY
	    s.Inuser
    </sql>
    <sql id="sonSelectSql-29">
       SELECT
       	a.area,
       	SUM ( b.basket_count ) 个人手机销量
       FROM
       	dbo.basket b WITH ( nolock )
       	LEFT JOIN dbo.sub s WITH ( nolock ) ON b.sub_id= s.sub_id
       	LEFT JOIN dbo.productinfo p WITH ( nolock ) ON b.ppriceid= p.ppriceid
       	LEFT JOIN dbo.areainfo a WITH ( nolock ) ON a.id= s.areaid
       WHERE
       	isnull( b.isdel, 0 ) = 0
       	AND s.sub_check in (3,9)
       	AND s.tradeDate1 BETWEEN #{req.start} AND #{req.end}
       	AND p.ismobile1 = 1
       GROUP BY
       	a.area
    </sql>

    <select id="selectUserDepartInfo" resultType="com.jiuji.oa.afterservice.statistics.bo.person.UserDepartInfoBO">
        SELECT
            ch999_name userName,
            a.id areaId,
            a.area area,
            a.kind1,
            a.level1 areaLevel,
            d.name smallArea,
            d1.name bigArea
        FROM
            ch999_user c WITH (nolock)
        LEFT JOIN areainfo a WITH (nolock) ON c.area1id = a.id
            LEFT JOIN departInfo d WITH (nolock) ON d.id = dbo.getDepartTypeId(a.depart_id,3)
            LEFT JOIN departInfo d1 WITH (nolock) ON d1.id = dbo.getDepartTypeId(a.depart_id,4)
        WHERE isnull(c.iszaizhi,0) = 1
        AND a.ispass = 1
    </select>

</mapper>
