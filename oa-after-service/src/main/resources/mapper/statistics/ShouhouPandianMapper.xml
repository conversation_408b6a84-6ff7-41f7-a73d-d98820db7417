<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.statistics.dao.ShouhouPandianMapper">

    <!-- 通用查询映射结果 -->
    <!--<resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.WxPriceQueryRes">-->
        <!--<id column="id" property="id" />-->
        <!--<result column="shouhou_id" property="shouhouId" />-->
        <!--<result column="comment" property="comment" />-->
        <!--<result column="inuser" property="inuser" />-->
        <!--<result column="dtime" property="dtime" />-->
        <!--<result column="isweb" property="isweb" />-->
    <!--</resultMap>-->

    <update id="panDianAllByAreaIdAndStats">
        update shouhou set pandian=0 where isnull(toareaid,areaid)= #{req.areaId} and isnull(isquji,0)=0 and isnull(pandianinuser,'') != '已核对'
        <if test="req.weixiuzuids">
          and  weixiuzuid in
            <foreach collection="req.weixiuzuids" index="index" item="weixiuzuid" open="(" separator="," close=")">
                #{weixiuzuid}
            </foreach>
        </if>
        <if test="req.stats != null">
        <choose>
            <when test="req.stats == 'all' ">
                and stats in(0,1,3)
            </when>
            <otherwise>
                and stats= #{req.stats}
            </otherwise>
        </choose>
        </if>
    </update>

    <select id="getPandianListPage" resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.PandianListRes">
        select * from shouhou with(nolock) where xianshi=1 and isnull(pandianinuser,'') !='已核对'
        and isnull(isquji,0)=0
        <if test="req.pandian != null">
            and pandian = #{req.pandian}
        </if>
        <choose>
            <when test="req.newMachine != null and req.newMachine = 1">
                and userid = 76783
            </when>
            <otherwise>
                and userid &lt;&gt; 76783
            </otherwise>
        </choose>
        <if test="req.weixiuzuids != null and req.weixiuzuids.size > 0 ">
            and weixiuzuid in
            <foreach collection="req.weixiuzuids" index="index" item="weixiuzuid" separator="," open="(" close=")">
                #{weixiuzuid}
            </foreach>
        </if>
        <choose>
            <when test="req.stats != null and req.stats = 'all' or req.stats = 'ALL'">
                and stats in(0,1,3)
            </when>
            <otherwise>
                and stats = #{req.stats}
            </otherwise>
        </choose>

        <choose>
            <when test="req.isToArea != null and req.isToArea == 1">
                and toareaid in
                <foreach collection="req.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
            </when>
            <when test="req.isToArea != null and req.isToArea == 0">
                and toareaid in
                <foreach collection="req.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
                and ISNULL(toareaid,0)=0
            </when>
            <otherwise>
                and (( areaid in(
                    <foreach collection="req.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                        #{areaId}
                    </foreach>
                ) and ISNULL(toareaid,0)=0) or toareaid in (
                <foreach collection="req.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                    #{areaId}
                </foreach>
                ))
            </otherwise>
        </choose>
        AND (EXISTS(SELECT id FROM shouhou_yuyue with(nolock) WHERE isnull(isdel,0) =0 AND id = shouhou.yuyueid) OR ISNULL(shouhou.yuyueid,0) = 0)
    </select>

    <select id="getPdtjData" resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.PandianSumListRes">
        select t1.*,isnull(t2.dyjPandianOver,0) dyjPandianOver,isnull(t2.dyjNotPandian,0) as dyjNotPandian,a.area,a.areaCode,di.name as smallArea,di1.name as bigArea  from
        (
        select isnull(s.toareaid,s.areaid) as area,
        sum(case when s.userid = 76783 AND ISNULL(k.kc_check,0)=6 AND s.pandiandate IS NOT NULL AND s.pandiandate &gt;= #{req.pdDate} then 1 else 0 end) newMachinePandianOver,
        sum(case when s.userid = 76783 AND ISNULL(k.kc_check,0)=6 AND (s.pandiandate IS NULL OR s.pandiandate &lt; #{req.pdDate}) then 1 else 0 end) newMachineNotPandian,
        sum(case when s.userid &lt;&gt; 76783 AND s.pandiandate IS NOT NULL AND s.pandiandate &gt;= #{req.pdDate} then 1 else 0 end)  customerMachinePandianOver,
        sum(case when s.userid &lt;&gt; 76783 AND ( s.pandiandate IS NULL OR s.pandiandate &lt; #{req.pdDate} )  then 1 else 0 end) customerMachineNotPandian
        from shouhou s with(nolock) LEFT JOIN dbo.product_mkc k WITH(NOLOCK) ON s.mkc_id=k.id
        where s.xianshi=1 and isnull(s.pandianinuser,'') &lt;&gt; '已核对'
        and ISNULL(s.stats,0) in(0,1,3)
        and (EXISTS(SELECT id FROM shouhou_yuyue with(nolock) WHERE isnull(isdel,0) =0 AND id = s.yuyueid) OR ISNULL(s.yuyueid,0) = 0)
        and isnull(s.isquji,0) = 0
        group by isnull(s.toareaid,s.areaid)
        ) t1
        left join
        (
        SELECT
        areaid,
        sum(case when pandiandate IS NOT NULL AND pandiandate &gt;= #{req.pdDate} then 1 else 0 end)  dyjPandianOver,
        sum(case when (pandiandate IS NULL OR pandiandate &lt;#{req.pdDate} ) then 1 else 0 end) dyjNotPandian
        from daiyongji d with(nolock)
        WHERE d.[stats]=1 and isnull(d.isdel,0)=0
        group by areaid
        )t2 on t1.area = t2.areaid
        left join areainfo a with(nolock) on a.id = t1.area
        left join dbo.departInfo di with(nolock) on di.code = left(a.areaCode,8)
        left join dbo.departInfo di1 with(nolock) on di1.code = left(a.areaCode,6)
        WHERE 1=1

        <if test="req.areaIds !+ null and req.areaIds.size > 0 ">
            and a.id in
            <foreach collection="req.areaIds" index="index" item="areaId" separator="," open="(" close=")">
                #{areaId}
            </foreach>
        </if>
    </select>

</mapper>
