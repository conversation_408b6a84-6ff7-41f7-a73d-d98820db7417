<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.statistics.dao.ShouhouBatchToAreaMapper">

   <select id="getShouhouBasicInfo" resultType="com.jiuji.oa.afterservice.bigpro.statistics.bo.ShouhouBasicInfoBo">
         select areaid as areaId,toareaid as toAreaId,isweixiu as isWeiXiu,weixiudtime as weiXiuDime,pandian,modidate,q.shqd2id as shqd2Id,q.dtime
         ,(SELECT TOP 1 pushtime FROM dbo.shouhou_msgrecord WITH(NOLOCK)
         WHERE shouhouid=s.id AND msgid IN(8,9,10,11,12,13,14,15,16,32,33) ) qudaoTplTime
         FROM shouhou s WITH(NOLOCK)
         LEFT JOIN dbo.shouhou_qudao q WITH(NOLOCK) ON s.id=q.shouhouid
         WHERE s.id=#{shouhouId}
   </select>

    <update id="updateWuliuStatsById">
        update wuliu set stats=4,ctime=getdate() where id=#{wuliuId} and ISNULL(stats,0)!=4
    </update>

    <select id="getWuliuIdByCompleteType" resultType="java.lang.Long">
        <if test="completeType != null">
            <!--配件调拨-->
            <if test="completeType == 1">
                select distinct wuliuid from dbo.diaobo_sub with(nolock) where id in
                <foreach collection="upIds" index="index" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

            <!--大件调拨-->
            <if test="completeType == 2">
                select distinct wuliuid from dbo.mkc_toarea with(nolock) where id in
                <foreach collection="upIds" index="index" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

            <!--良品调拨-->
            <if test="completeType == 3">
                select distinct wuliuid from dbo.recover_toarea with(nolock) where id in
                <foreach collection="upIds" index="index" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>

            <!--维修机调拨-->
            <if test="completeType == 4">
                select distinct wuliuid from dbo.shouhou_toarea with(nolock) where id in
                <foreach collection="upIds" index="index" item="id" open="(" close=")" separator=",">
                    #{id}
                </foreach>
            </if>
        </if>

    </select>

    <select id="checkGoodsNotRecviced" resultType="java.lang.Integer">
        <if test="completeType != null">
            <!--配件调拨-->
            <if test="completeType == 1">
                select count(1) from dbo.diaobo_sub with(nolock) where  stats =3 and wuliuid=#{wuliuId}
            </if>

            <!--大件调拨-->
            <if test="completeType == 2">
                elect count(1) from dbo.mkc_toarea with(nolock) where stats != 3 and wuliuid=#{wuliuId}
            </if>

            <!--良品调拨-->
            <if test="completeType == 3">
                select count(1) from dbo.recover_toarea with(nolock) where stats != 3 and wuliuid=#{wuliuId}
            </if>

            <!--维修机调拨-->
            <if test="completeType == 4">
                select count(1) from dbo.shouhou_toarea with(nolock) where isnull([check],0)=0 and wuliuid=#{wuliuId}
            </if>
        </if>
    </select>

    <select id="getToAreaList" resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.ShouhouToAreaListRes">
        select t.id,t.shouhou_id as shouhouId,t.areaid as areaId,t.toareaid as toAreaId,t.dtime,t.inuser,t.checkinuser as checkInUser,
        t.checkdtime as checkDtime,t.[check],t.shibian as shibieMa,s.name as productName,s.baoxiu,s.problem,s.imei,s.userid
        from shouhou_toarea t with(nolock) left join shouhou s with(nolock) on t.shouhou_id=s.id where 1=1
        <if test="req.areaId != null and req.areaId != 0">
            <if test="req.toAreaKind != null and req.toAreaKind == 1">
                and t.areaid=#{req.areaId}
            </if>
        </if>
        <if test="req.key != null">
            <!--售后单-->
            <if test="req.keyKind == 1">
                and s.id = #{req.key}
            </if>
            <!--识别码-->
            <if test="req.keyKind == 2">
                and t.shibian=#{req.key}
            </if>
            <!--商品信息-->
            <if test="req.keyKind == 3">
                and (s.name like CONCAT('%',#{req.key},'%')  )
            </if>
            <!--申请人-->
            <if test="req.keyKind == 4">
                and t.inuser = #{req.key}
            </if>
            <!--串号-->
            <if test="req.keyKind == 5">
                and imei like  CONCAT('%',#{req.key},'%')
            </if>
        </if>
        <!--新机-->
        <if test="req.newj != null ">
            <if test="req.newj == true">
                and s.userid=76783
            </if>
            <if test="req.newj == false">
                and s.userid != 76783
            </if>
        </if>
        <!--接收情况-->
        <if test="req.check != null">
            <if test="req.check == false">
                and t.[check]= 0
            </if>
            <if test="req.check == true">
                and t.[check]= 1
            </if>

        </if>

        <!--查询时间类别-->
        <if test="req.isGaoji != null and req.isGaoji == true">
            <!--提交时间-->
            <if test="req.dateKind == 1">
                and t.dtime between #{req.startTime} and #{req.endTime}
            </if>
            <!--接收时间-->
            <if test="req.dateKind == 2">
                and t.checkdtime between #{req.startTime} and #{req.endTime}
            </if>
        </if>

    </select>
</mapper>
