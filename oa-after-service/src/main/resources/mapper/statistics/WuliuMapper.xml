<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.statistics.dao.WuliuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.statistics.po.Wuliu">
        <id column="id" property="id" />
        <result column="sname" property="sname" />
        <result column="smobile" property="smobile" />
        <result column="saddress" property="saddress" />
        <result column="sarea" property="sarea" />
        <result column="scityid" property="scityid" />
        <result column="rname" property="rname" />
        <result column="rmobile" property="rmobile" />
        <result column="raddress" property="raddress" />
        <result column="rarea" property="rarea" />
        <result column="rcityid" property="rcityid" />
        <result column="area" property="area" />
        <result column="dtime" property="dtime" />
        <result column="ctime" property="ctime" />
        <result column="price" property="price" />
        <result column="inprice" property="inprice" />
        <result column="shoujianren" property="shoujianren" />
        <result column="paijianren" property="paijianren" />
        <result column="stats" property="stats" />
        <result column="danhaobind" property="danhaobind" />
        <result column="wutype" property="wutype" />
        <result column="comment" property="comment" />
        <result column="com" property="com" />
        <result column="nu" property="nu" />
        <result column="weight" property="weight" />
        <result column="inuser" property="inuser" />
        <result column="linktype" property="linktype" />
        <result column="sendtime" property="sendtime" />
        <result column="areaid" property="areaid" />
        <result column="sareaid" property="sareaid" />
        <result column="rareaid" property="rareaid" />
        <result column="receiveUser" property="receiveUser" />
        <result column="receiveTime" property="receiveTime" />
        <result column="notifyType" property="notifyType" />
        <result column="pay_method" property="payMethod" />
        <result column="subKinds" property="subKinds" />
        <result column="wpid" property="wpid" />
        <result column="LastRouteTime" property="LastRouteTime" />
        <result column="EstimatedArrivalTime" property="EstimatedArrivalTime" />
        <result column="wCateId" property="wCateId" />
    </resultMap>

</mapper>
