<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.statistics.dao.ShouhouStatisticsMapper">


    <sql id="wxPriceCondition">
        exists(select 1 from dbo.f_category_children(23) f where f.ID=p.cid) AND p.isdel=0 and p.que &lt;&gt; 2
        <choose>
            <when test="req.ranks != null and req.ranks.size > 0 and req.ranks.contains('2d5')">
                <if test="req.show != null">
                    AND ISNULL(cfg.display,1)=#{req.show}
                </if>
            </when>
            <otherwise>
                AND ISNULL(cfg.display,1)=1
            </otherwise>
        </choose>
        <if test="req.wxpjProductIds != null and req.wxpjProductIds.size>0">
            and p.productid in
            <foreach collection="req.wxpjProductIds" index="index" item="wxpjProductId" open="(" close=")" separator=",">
                #{wxpjProductId}
            </foreach>
        </if>
        <if test="req.wxpjPpId != null">
            and p.ppriceid = #{req.wxpjPpId}
        </if>
        <if test="req.plabel != null">
            and isnull(pxi.product_label,0) = #{req.plabel}
        </if>
        <if test="req.cids != null and req.cids.size>0">
            and p.cid in
            <foreach collection="req.cids" index="index" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
        </if>

        <if test="req.brandIds != null and req.brandIds.size > 0">
            and p.brandID in
            <foreach collection="req.brandIds" index="index" item="brandId" open="(" close=")" separator=",">
                #{brandId}
            </foreach>
        </if>
        <if test="req.serviceType != null">
            and ssc.service_type = #{req.serviceType}
        </if>
        <choose>
            <when test="req.wxpjText != null and req.wxpjTextNum != null">
                AND (replace(isnull(p.product_name,'')+ isnull(p.product_color,'')+'('+isnull(str(p.ppriceid),'')+')',' ','')
                like CONCAT('%',replace(#{req.wxpjText},' ',''),'%') or p.ppriceid=#{req.wxpjTextNum} or p.productid = #{req.wxpjTextNum})
            </when>
            <when test="req.wxpjText != null and req.wxpjText != ''">
                AND replace(isnull(p.product_name,'')+ isnull(p.product_color,'')+'('+isnull(str(p.ppriceid),'')+')',' ','')
                like CONCAT('%',replace(#{req.wxpjText},' ',''),'%')
            </when>
            <when test="req.wxpjTextNum != null">
                and (p.ppriceid=#{req.wxpjTextNum} or p.productid = #{req.wxpjTextNum})
            </when>
        </choose>
    </sql>

    <select id="countWxPrice" resultType="java.lang.Long">
        SELECT count(p.ppriceid)
        FROM dbo.productinfo p with(nolock)
        LEFT JOIN product_xtenant_info pxi with(nolock) on pxi.ppriceid = p.ppriceid and xTenant = #{xTenant}
        LEFT JOIN wxproductconfig cfg with(nolock) ON p.ppriceid=cfg.ppriceid
        LEFT JOIN dbo.shouhou_service_config ssc with(nolock) ON p.ppriceid=ssc.ppid AND ssc.is_del=0 AND ssc.disable=1
        WHERE <include refid="wxPriceCondition"></include>
    </select>

    <select id="queryWxPrice" resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.WxPriceQueryRes">
        SELECT distinct p.ppriceid as ppid,p.product_name productName,isnull(pxi.product_label,0) as pLabel,p.memberprice as memberPrice
             ,p.product_color as productColor
             ,p.memberprice as wxInsurancePrice,ssc.service_ppid as servicePpid
             , ISNULL(cfg.display,1) AS show,cfg.id as wxId,cfg.fid,cfg.descripion,k.lcount,k.orderCount,k.leftCount leftCount
             ,ISNULL(wxcp_count.sales,0) as sales
        FROM dbo.productinfo p with(nolock)
        LEFT JOIN product_xtenant_info pxi with(nolock) on pxi.ppriceid = p.ppriceid and xTenant = #{xTenant}
        LEFT JOIN wxproductconfig cfg with(nolock) ON p.ppriceid=cfg.ppriceid
        LEFT JOIN dbo.product_kc k with(nolock) ON k.ppriceid=p.ppriceid AND k.areaid=#{req.currAreaId}
        LEFT JOIN dbo.shouhou_service_config ssc with(nolock) ON p.ppriceid=ssc.ppid AND ssc.is_del=0 AND ssc.disable=1
        left join (SELECT  COUNT(wxcp.id) as sales,wxcp.ppriceid FROM dbo.wxkcoutput wxcp with(nolock)
            where wxcp.dtime > CONVERT(datetime,#{req.salesStartDate},20) group by wxcp.ppriceid) wxcp_count
        on wxcp_count.ppriceid = p.ppriceid
        WHERE <include refid="wxPriceCondition"></include>
        order by sales desc,p.ppriceid DESC offset ${req.current > 0 ? (req.current - 1) * req.size : 0} row fetch next #{req.size} row only
    </select>

    <sql id="shouhouSonQuery">
        SELECT distinct s.id,
                        s.areaid,
                        s.userid,
                        s.mobile,
                        ISNULL(s.stats, 0)       stats
                ,
                        ISNULL(s.baoxiu, 0)      baoxiu,
                        ISNULL(s.ServiceType, 0) serviceType,
                        s.wxkind,
                        ishuishou,
                        ISNULL(s.isquji, 0)      isquji,
                        s.feiyong,
                        s.costprice,
                        ISNULL(s.istui, 0)       istui,
                        ISNULL(s.isfan, 0)       isfan,
                        s.isXcMkc,
                        s.modidate,
                        s.modidtime,
                        s.weixiuzuid,
                        s.qujitongzhitime,
                        s.offtime,
                        s.yuyueid
        FROM dbo.shouhou s with (nolock)
        WHERE ISNULL(xianshi, 0) = 1
          and ISNULL(s.issoft, 0) = 0
          AND s.offtime between #{req.start} and #{req.end}
    </sql>


    <sql id="areaCode">
        and ai.id in
        <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </sql>

    <sql id="areaKind">
        and ai.kind1 = #{req.areaKind}
    </sql>


    <!--    接件量 tb1  userid#-->
    <sql id="jiejianliang">
        SELECT s.areaid,
        sum((case when(baoxiu = 1 and ISNULL(s.ServiceType, 0) = 0) and isquji = 1 then 1 else 0 end)) as 在保接件量,
        sum(case when(baoxiu = 0 and s.feiyong+s.costprice> 0 and isquji=1 and isfan=0 AND s.istui=0) then 1 else 0 end)
        as 不在保接件量,
        sum(CASE WHEN baoxiu != 1 AND s.feiyong-s.costprice>0 THEN 1 ELSE 0 END) AS 非保接件量,
        sum(case when(baoxiu = 2 and s.feiyong+s.costprice>0 and isquji= 1 and isfan= 0 AND s.istui=0) then 1 else 0
        end)
        as 外修接件量,
        sum((case when s.ServiceType>0 AND s.stats=1 AND isquji= 1 then 1 else 0 end)) as 九机服务接件量
        from (
        <include refid="shouhouSonQuery"/>
        )
        s where s.userid != 76783
        group by s.areaid
    </sql>
    <!--    换机量 退款量 tb2-->
    <sql id="huanJiLiangTuiKuanLiang">
        select s.areaid,
               sum(case when st.tuihuan_kind = 3 then 1 else 0 end)                        as 退款量,
               sum(case when st.tuihuan_kind = 4 or st.tuihuan_kind = 1 then 1 else 0 end) as 换机量
        from shouhou s with (nolock)
                 inner join shouhou_tuihuan st with (nolock) on s.id = shouhou_id
        WHERE ISNULL(s.isquji, 0) = 1
          AND st.tuihuan_kind IN (1, 3, 4)
          AND isnull(st.check3, 0) = 1
          and check3dtime between #{req.start} and #{req.end}
        group by s.areaid
    </sql>
    <!--    售后周期 tb3  userid# -->
    <sql id="shouHouZhouQi">
        SELECT s.areaid,
        avg(DATEDIFF(mi, s.modidate, isnull(s.modidtime , offtime))) as 平均处理周期,
        avg(case when sq.shouhouid is not NULL AND s.isquji=1 and isnull(s.stats,0)=1 then DATEDIFF(mi, s.modidate, isnull(s.modidtime ,
        offtime)) end) as 平均外送周期,
        avg(case when sq.shouhouid is null AND s.isquji=1 then DATEDIFF(mi,
        s.modidate,ISNULL(sqs.dtime,ISNULL(s.modidtime, offtime))) end) as 平均自修周期,
        SUM(case when sq.shouhouid is null AND s.isquji=1 AND s.stats=1 then 1 ELSE 0 end) as 周期自修修好量,
        SUM(case when sq.shouhouid is null AND s.isquji=1 AND s.stats=1 AND s.baoxiu IN(0,2)
        AND DATEDIFF(MINUTE,s.modidate,ISNULL(s.modidtime,offtime)) BETWEEN 9 AND 60 then 1 ELSE 0 end) as
        一小时自修修好量,
        SUM(case when sq.shouhouid is null AND s.isquji=1 AND s.stats=1 AND s.baoxiu IN(0,2)
        AND DATEDIFF(MINUTE,s.modidate,ISNULL(s.modidtime,offtime))&lt;=8 then 1 ELSE 0 end) as 八分钟自修修好量,
        SUM(case when sq.shouhouid is null AND s.isquji=1 AND s.stats=1 AND s.baoxiu IN(0,2)
        AND DATEDIFF(DAY,s.modidate,ISNULL(s.modidtime,offtime))>3 then 1 ELSE 0 end) as 超过3天自修修好量
        from (
        <include refid="shouhouSonQuery"/>
        ) s
        LEFT JOIN shouhou_qudao sq with(nolock) on s.id = sq.shouhouid
        LEFT JOIN shouhou_qujishenhe sqs with(nolock) ON sqs.shouhouid = s.id
        WHERE s.userid != 76783 AND isnull(s.weixiuzuid,0) != 9
        group by s.areaid
    </sql>
    <!--    todo 维修周期查询  userid#-->
    <sql id="weiXiuDanZhouQi">
        SELECT s.areaid,
        case when sq.shouhouid is not null and (s.isquji is null or s.isquji=0)
        and DATEDIFF(DAY, s.modidate, isnull(s.qujitongzhitime,offtime))>1
        and
        from (
        <include refid="shouhouSonQuery"/>
        ) s
        LEFT JOIN shouhou_qudao sq with(nolock) on s.id = sq.shouhouid
        LEFT JOIN shouhou_qujishenhe sqs with(nolock) ON sqs.shouhouid = s.id
        WHERE s.userid != 76783 AND isnull(s.weixiuzuid,0) != 9
        group by s.areaid
    </sql>

    <!--    维修量 tb4-->
    <sql id="weiXiuLiang">
        SELECT s.areaid,
        sum(case when s.stats=1 and s.istui=0 and ( ( s.feiyong +s.costprice > 0 and sq.shouhouid is null ) or
        (sq.shouhouid is not null) ) then 1 else 0 end) as 总修好量,
        sum(case when s.stats=1 and s.istui=0 and sq.shouhouid is not null then 1 else 0 end) as 外送修好量,
        sum(case when s.stats=1 and s.istui=0 and s.feiyong+s.costprice > 0 and sq.shouhouid is null then 1 else 0 end)
        as 自修修好量,
        sum(case when s.stats=1 and ISNULL(s.ServiceType, 0) > 0 AND sq.shouhouid is not null then 1 else 0 end) as
        九机服务修好量,
        sum(case when s.stats=3 then 1 else 0 end) as 修不好量,
        sum(case when s.stats=1 and(s.feiyong > 0 or s.costprice > 0) and yuyueid is not null then 1 else 0 end) as
        预约修好量
        from (
        <include refid="shouhouSonQuery"/>
        ) s
        left join shouhou_qudao sq with(nolock) on s.id = sq.shouhouid
        where 1=1
        group by s.areaid
    </sql>

    <!--    售后毛利 外修毛利 tb5-->
    <sql id="shouHouWaiXiuMaoLi">
        SELECT s.areaid, SUM(CASE WHEN (ISNULL(k.wxtongjitotal,0)-k.inprice)
        &lt;
        0 and s.ServiceType=9 AND ( p.cid=31 OR
        k.ppriceid=0 ) then 0 else ISNULL(k.wxtongjitotal,0)-k.inprice END) AS 维修毛利,
        SUM(CASE WHEN s.baoxiu=2 then ISNULL(k.wxtongjitotal,0)-k.inprice else 0 end) as 外修毛利
        from (
        <include refid="shouhouSonQuery"/>
        ) s INNER JOIN dbo.wxkcoutput k with(nolock) ON s.id=k.wxid
        left JOIN dbo.productinfo p with(nolock) ON p.ppriceid=k.ppriceid
        WHERE s.stats=1 AND s.baoxiu!=1 AND s.istui=0
        AND (isnull(k.service_type,0)=0 OR (k.service_type >0 AND ISNULL(k.wxtongjitotal,0)-k.inprice>0))
        AND (ISNULL(p.cid,0)!=31 OR s.wxkind!=5) AND k.stats!=3
        and not exists(select id from shouhou_huishou sh with(nolock) where ISNULL(ishuanhuo,0) = 1 and sh.shouhou_id =
        s.id AND sh.wxkcid=k.id)
        GROUP by s.areaid
    </sql>

    <!--    旧件毛利 tb6-->
    <sql id="jiuJianMaoLi">
        SELECT s.areaid,
               sum(case
                       when isnull(s.stats, 0) = 1 and ISNULL(s.wxkind, 0) != 5 then (saleprice - price)
                       else 0 end) as 旧件毛利
        from shouhou_huishou sh with (nolock)
                 LEFT JOIN shouhou s with (nolock) ON s.id = sh.shouhou_id
        where ISNULL(s.isquji, 0) = 1
          and ISNULL(xianshi, 0) = 1
          and ISNULL(s.issoft, 0) = 0
          and sh.saledate between #{req.start} and #{req.end}
          and ISNULL(wxkind, 0) != 5
        group by s.areaid
    </sql>

    <!--    九机服务亏损 特殊质保成本 tb7 userid#-->
    <sql id="fuWuKuiSunZhiBaoChengBen">
        select s.areaid,
        sum(case WHEN s.stats=1 AND s.ServiceType>0 AND wx.price=0 AND wx.inprice>0 AND wx.ppriceid>0 then
        wx.price-wx.inprice else 0 end) as 九机服务亏损,
        sum(case WHEN s.baoxiu=1 AND ISNULL(s.wxkind,0)!=5 AND s.ServiceType=0 AND ISNULL(s.ishuishou,0)=0 AND
        ISNULL(s.isXcMkc,0)=0 then wx.inprice else 0 end) as 特殊质保成本
        from (
        <include refid="shouhouSonQuery"/>
        ) s INNER JOIN dbo.wxkcoutput wx with(nolock) ON wx.wxid=s.id
        where s.stats=1 AND s.userid!=76783
        AND NOT EXISTS(SELECT 1 FROM dbo.shouhou_huishou hs with(nolock) WHERE hs.shouhou_id=s.id AND
        ISNULL(hs.ishuanhuo,0)=1)
        group by s.areaid
    </sql>

    <!--    维修配件 tb8-->
    <sql id="weiXiuPeiJian">
        select s.areaid, sum(w.inprice) as 返修换货成本
        from (
        <include refid="shouhouSonQuery"/>
        ) s
        left join wxkcoutput w with(nolock) on w.wxid = s.id
        left join shouhou_huishou sh with(nolock) on sh.shouhou_id = w.wxid
        where s.stats=1 and ISNULL(sh.ishuanhuo, 0)=1
        group by s.areaid
    </sql>

    <!--    售后转出量 tb9-->
    <sql id="shouHouZhuanChuLiang">
        select m.areaid, count(1) as 售后转出量
        from mkc_dellogs m with (nolock)
        where kinds = 'h1'
          and ISNULL(m.check2, 0) = 1
          and m.check2dtime between #{req.start} and #{req.end}
        group by m.areaid
    </sql>

    <!--    实时新机库存 tb10-->
    <sql id="shiShiXinJiKuCun">
        select k.areaid, count(1) as 实时新机库存
        from product_mkc k with (nolock)
        where kc_check = 6
        group by k.areaid
    </sql>

    <!--    unuse 软件安装提成量 tb11-->
    <sql id="ruanJianAnZhuangTiChengLiang">
        SELECT areaid, COUNT(1) AS 软件安装提成量 FROM dbo.msoft with(nolock) WHERE isticheng=1
        <if test="req.areaCodes.size > 0">
            and areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        AND modidate between #{req.start} and #{req.end} GROUP BY areaid
    </sql>

    <!--    unuse 门店手机销量 tb12 basket# ppid#-->
    <sql id="menDianShouJiXiaoLiang">
        SELECT areaid,SUM(b.basket_count)  门店手机销量 FROM sub  with(nolock)
        left join basket b with(nolock) on sub.sub_id = b.sub_id
        WHERE sub.sub_check=3 AND ISNULL(b.isdel,0)=0
        and b.ppriceid in (select p.ppriceid from dbo.productinfo p with(nolock) where p.cid in(select f.ID from dbo.f_category_children('2,20,22,201,21') f))
        and sub.subtype != 10 and  ((sub.yingfuM>0 and sub.jidianM>0) or sub.jidianM=0  )  and isnull(b.ischu,0) = 1
        and isnull(b.type,0)!=22 and b.ppriceid not in (60142,59948)
        <if test="req.areaCodes.size > 0">
            and areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>

        AND sub.tradeDate1 BETWEEN #{req.start} and #{req.end} GROUP BY sub.areaid
    </sql>

    <!--    unuse 店面调往h1的维修配件 tb13 cid# areaid#done-->
    <sql id="dianMianDiaoWangH1WeiXiuPeiJian">
        SELECT s.areaid, SUM(b.lcount) 店面调往h1的维修配件
        FROM dbo.diaobo_sub s with(nolock) INNER JOIN dbo.diaobo_basket b with(nolock) ON s.id=b.sub_id
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid=b.ppriceid AND p.cidFamily LIKE '%,23,%' AND p.cid NOT
        IN(53,68,70,311)
        WHERE s.stats=4 AND EXISTS(SELECT 1 FROM dbo.authorize a WHERE a.H1AreaId = s.toareaid)
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        AND dtime between #{req.start} and #{req.end}
        GROUP BY s.areaid
    </sql>

    <!--    unuse 调往门店的维修配件 tb14  cid#-->
    <sql id="diaoWangMenDianWeiXiuPeiJian">
        SELECT s.toareaid areaid, SUM(b.lcount) 调往门店的维修配件
        FROM dbo.diaobo_sub s with(nolock) INNER JOIN dbo.diaobo_basket b with(nolock) ON s.id=b.sub_id
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid=b.ppriceid AND p.cidFamily LIKE '%,23,%' AND p.cid NOT
        IN(53,68,70,311)
        WHERE s.stats=4
        <if test="req.areaCodes.size > 0">
            and s.toareaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        AND dtime between #{req.start} and #{req.end}
        GROUP BY s.toareaid
    </sql>

    <!--    unuse 店面调往dc的维修配件 tb15 cid# areaid#done-->
    <sql id="dianMianDiaoWangDCWeiXiuPeiJian">
        SELECT s.areaid, SUM(b.lcount) 店面调往dc的维修配件
        FROM dbo.diaobo_sub s with(nolock) INNER JOIN dbo.diaobo_basket b with(nolock) ON s.id=b.sub_id
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid=b.ppriceid AND p.cidFamily LIKE '%,23,%' AND p.cid NOT
        IN(53,68,70,311)
        WHERE s.stats=4 AND EXISTS(SELECT 1 FROM dbo.authorize a WHERE a.dcAreaId = s.toareaid)
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        AND dtime between #{req.start} and #{req.end}
        GROUP BY s.areaid
    </sql>

    <!--    unuse 优品比 tb16-->
    <sql id="youPingBi">
        SELECT aa.areaid,aa.xcnumber as 优品库存量,bb.xsnumber as 上月手机销量 FROM
        (
        SELECT COUNT(1) AS xcnumber,k.areaid AS areaid FROM dbo.product_mkc k with(nolock)
        LEFT JOIN dbo.xc_mkc x with(nolock) ON x.mkc_id=k.id WHERE x.isLock=1 AND k.kc_check = 3 GROUP BY k.areaid
        ) aa LEFT JOIN
        (
        SELECT SUM(b.basket_count) AS xsnumber,s.areaid AS areaid FROM basket b WITH(nolock) LEFT JOIN sub s
        with(nolock) ON b.sub_id
        = s.sub_id WHERE
        b.ismobile = 1 AND s.sub_check = 3
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        AND s.tradeDate1 BETWEEN DATEADD(MONTH,-1,#{req.start}) AND DATEADD(MONTH,-1,#{req.end})
        GROUP BY s.areaid
        )bb ON aa.areaid = bb.areaid
    </sql>

    <!--    优惠码使用数量及总金额 tb17-->
    <sql id="youHuiMaShuLiangJinE">
        SELECT cd.areaid,
               COUNT(1)      AS 优惠码使用量,
               SUM(nc.Total) AS 优惠码使用总金额
        FROM dbo.cardLogs cd WITH (nolock)
                 LEFT JOIN shouhou s WITH (nolock) ON s.id = cd.sub_id
                 LEFT JOIN dbo.NumberCard nc WITH (nolock) ON nc.ID = cd.cardid
        WHERE cd.pushtime BETWEEN #{req.start} and #{req.end}
          AND ISNULL(s.baoxiu, 0) != 1
          AND ISNULL(s.isquji, 0) = 1
          AND ISNULL(s.stats, 0) = 1
          AND cd.useType = 1
        GROUP BY cd.areaid
    </sql>

    <!--    改价优惠单量及高价优惠总金额 tb18-->
    <sql id="gaiJiaYouHuiDanGaoJieYouHuiZongJinE">
        SELECT s.areaid, COUNT(1) AS 改价单量,
        SUM(w.price1-w.price) AS 改价金额
        FROM (
        <include refid="shouhouSonQuery"/>
        ) s INNER JOIN dbo.wxkcoutput w with(nolock) ON w.wxid = s.id
        WHERE (w.price1 - w.price) > 0 AND isnull(s.ServiceType,0)=0 AND w.ppriceid>0 AND ISNULL(s.wxkind,0)!=5
        AND s.stats= 1 AND s.baoxiu!= 1
        <!-- 需要排除退款的 -->
        and isnull(s.feiyong,0) > 0
        AND NOT EXISTS(select id from shouhou_huishou sh WITH(NOLOCK) where ISNULL(ishuanhuo,0)=1 and sh.shouhou_id =
        s.id)
        <!-- 剔除内部员工 -->
        and s.mobile not in ( select mobile FROM ch999_user WITH (nolock) WHERE iszaizhi= 1 AND ch999_id > 1 and mobile is not null )
        <!-- 剔除总部改价 -->
        AND s.areaid not in (
        SELECT distinct(dcAreaId) from authorize with(nolock) where dcAreaId is not null union
        SELECT distinct(D1AreaId) from authorize with(nolock) where D1AreaId is not null union
        SELECT distinct(H1AreaId) from authorize with(nolock) where H1AreaId is not null union
        SELECT DISTINCT (a.HQAreaId) FROM(
        select cast(f.split_value as int) AS HQAreaId  from dbo.authorize a with(nolock) cross apply dbo.F_SPLIT(a.HQAreaId,',') f where isnumeric(f.split_value)=1
        ) a where a.HQAreaId is not null
        )
        GROUP BY s.areaid
    </sql>

    <!--    改价优惠单量及高价优惠总金额 tb18-->
    <sql id="gaiJiaYouHuiDanGaoJieYouHuiZongJinEJiuJi">
        SELECT s.areaid, COUNT(1) AS 改价单量,
        SUM(w.price1-w.price) AS 改价金额
        FROM (
        <include refid="shouhouSonQuery"/>
        ) s INNER JOIN dbo.wxkcoutput w with(nolock) ON w.wxid = s.id
        WHERE (w.price1 - w.price) > 0 AND isnull(s.ServiceType,0)=0 AND w.ppriceid>0 AND ISNULL(s.wxkind,0)!=5
        AND s.stats= 1 AND s.baoxiu!= 1
        <!-- 需要排除退款的 -->
        and isnull(s.feiyong,0) > 0
        AND NOT EXISTS(select id from shouhou_huishou sh WITH(NOLOCK) where ISNULL(ishuanhuo,0)=1 and sh.shouhou_id =
        s.id)
        <!-- 剔除内部员工 -->
        and s.mobile not in ( select mobile FROM ch999_user WITH (nolock) WHERE iszaizhi= 1 AND ch999_id > 1 and mobile is not null )
        <!-- 剔除总部改价 -->
        AND s.areaid not in (
        SELECT distinct(dcAreaId) from authorize with(nolock) where dcAreaId is not null union
        SELECT distinct(D1AreaId) from authorize with(nolock) where D1AreaId is not null union
        SELECT distinct(H1AreaId) from authorize with(nolock) where H1AreaId is not null union
        SELECT DISTINCT (a.HQAreaId) FROM(
        select cast(f.split_value as int) AS HQAreaId  from dbo.authorize a with(nolock) cross apply dbo.F_SPLIT(a.HQAreaId,',') f where isnumeric(f.split_value)=1
        ) a where a.HQAreaId is not null
        )
        and s.areaid != 360
        GROUP BY s.areaid
    </sql>


    <!--    门店置换旧件毛利 tb19 areaid#done cid#-->
    <sql id="menDianZhiHuanJiuJianMaoLi">
        SELECT s.areaid,
               SUM(k.price - k.inprice + CAST(ISNULL(sh.inprice, 0.0) AS DECIMAL(18, 2)) -
                   ISNULL(sh.price, 0.00)) AS 门店置换旧件毛利
        FROM shouhou s with (nolock)
                 INNER JOIN shouhou_huishou sh with (nolock) ON s.id = sh.shouhou_id
                 INNER JOIN dbo.wxkcoutput k WITH (NOLOCK) ON k.id = sh.wxkcid
                 INNER JOIN dbo.productinfo p WITH (NOLOCK) ON p.ppriceid = k.ppriceid
        WHERE ISNULL(s.wxkind, 0) = 5
          AND ISNULL(s.issoft, 0) = 0
          AND s.wxkind = 5
          AND p.cid = 31
          AND sh.sdtime BETWEEN #{req.start} and #{req.end}
          AND EXISTS(SELECT 1 FROM dbo.authorize a WHERE a.dcAreaId = sh.toareaid)
        GROUP BY s.areaid
        union
        SELECT s.areaid,
               SUM(k.price - k.inprice + CAST(ISNULL(sh.saleprice, 0.0) AS DECIMAL(18, 2)) -
                   ISNULL(sh.price, 0.00)) AS 门店置换旧件毛利
        FROM shouhou s with (nolock)
                 INNER JOIN shouhou_huishou sh with (nolock) ON s.id = sh.shouhou_id
                 INNER JOIN dbo.wxkcoutput k WITH (NOLOCK) ON k.id = sh.wxkcid
                 INNER JOIN dbo.productinfo p WITH (NOLOCK) ON p.ppriceid = k.ppriceid
        WHERE ISNULL(s.wxkind, 0) = 5
          AND ISNULL(s.issoft, 0) = 0
          AND s.wxkind = 5
          AND p.cid = 31
          AND (sh.saledate BETWEEN #{req.start} and #{req.end} AND sh.issale = 1 and sh.isfan = 0 AND
               NOT EXISTS(SELECT 1 FROM dbo.authorize a WHERE a.dcAreaId = sh.toareaid))
        GROUP BY s.areaid
    </sql>

    <!--    总接件量 tb20 userid# -->
    <sql id="zongJieJianLiang">
        SELECT s.areaid, count(1) as 总接件量
        from shouhou s with (nolock)
        where ISNULL(xianshi, 0) = 1
          and ISNULL(s.issoft, 0) = 0
          and s.modidate between #{req.start} and #{req.end}
          and s.userid != 76783
        group by s.areaid
    </sql>

    <!--    返修总量 自修返修量，外送返修量 tb21-->
    <sql id="fanXiuZiXiuFanXiuWaiSongFanXiu">
        select s.areaid,
        sum(case when isfan=1 and s.stats=1 then 1 else 0 end) as 返修总量,
        sum(case when isfan=1 and sq.shouhouid is null and s.stats=1 then 1 else 0 end) as 自修返修量,
        sum(case when isfan=1 and sq.shouhouid is not null and s.stats=1 then 1 else 0 end) as 外送返修量
        from (
        <include refid="shouhouSonQuery"/>
        ) s
        left join shouhou_qudao sq with(nolock) on s.id = sq.shouhouid
        where not exists(select id from shouhou_tuihuan st with(nolock) where st.shouhou_id = s.id and st.tuihuan_kind
        in(3,4) and st.check3 = 1)
        group by s.areaid
    </sql>

    <!--    unuse 单台毛利比量 tb22-->
    <sql id="danTaiMaoLiBiLiang">
        SELECT s.areaid,count(1) AS 单台毛利比量
        FROM (
        <include refid="shouhouSonQuery"/>
        ) s LEFT JOIN shouhou_qudao sq WITH(NOLOCK) ON s.id = sq.shouhouid
        WHERE s.stats=1
        AND ( (s.feiyong>0) OR (
        baoxiu!=1
        AND s.ServiceType=0
        AND sq.shouhouid IS NULL
        AND ISNULL(s.isfan,0)=0
        AND ISNULL(s.wxkind,0)!=5
        AND NOT EXISTS(select id from shouhou_huishou sh with(nolock) where ISNULL(ishuanhuo,0) = 1 and sh.shouhou_id =
        s.id)
        ) )
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by s.areaid
    </sql>

    <!--    unuse 配件返还单量 tb23-->
    <sql id="peiJianFanHuanDanLiang">
        select s.areaid,
        COUNT(1) as 配件返还单量
        FROM (
        <include refid="shouhouSonQuery"/>
        ) s LEFT JOIN dbo.wxkcoutput wxk WITH(NOLOCK) ON s.id=wxk.wxid
        LEFT JOIN dbo.shouhou_huishou hs WITH(NOLOCK) ON hs.wxkcid=wxk.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON wxk.ppriceid=p.ppriceid
        where s.feiyong>=s.costprice AND p.cid=31 AND ISNULL(hs.isfan,0)=1
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="areaCode">
                #{areaCode}
            </foreach>
        </if>
        group by s.areaid
    </sql>

    <!--    unuse 屏幕更换维修单量 tb24 cid# -->
    <sql id="pinMuGengHuanWeiXiuDanLiang">
        select s.areaid,
        COUNT(1) as 屏幕更换维修单量
        FROM (
        <include refid="shouhouSonQuery"/>
        ) s LEFT JOIN dbo.wxkcoutput wxk WITH(NOLOCK) ON s.id=wxk.wxid
        LEFT JOIN dbo.shouhou_huishou hs WITH(NOLOCK) ON hs.wxkcid=wxk.id and hs.isdel = 0
        where s.feiyong>=s.costprice AND wxk.ppriceid in(select p.ppriceid from dbo.productinfo p WITH(NOLOCK) where p.cid=31)
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="areaCode">
                #{areaCode}
            </foreach>
        </if>
        group by s.areaid
    </sql>

    <!--    预约接件量 tb25-->
    <sql id="yuYueJieJianLiang">
        SELECT y.areaid, COUNT(1) 预约接件量
        FROM dbo.shouhou_yuyue y WITH (NOLOCK)
                 INNER JOIN dbo.shouhou s WITH (NOLOCK) ON y.id = s.yuyueid
        WHERE y.stats = 3
          AND s.feiyong + s.costprice > 0
          AND s.xianshi = 1
          AND y.fchecktime BETWEEN #{req.start} and #{req.end}
        group by y.areaid
    </sql>

    <!--    售后服务统计 tb26 ppid# cid#-->
    <sql id="shouHouFuWuTongJi">
        SELECT s.areaid
        , SUM(CASE WHEN wk.ppriceid=81683 THEN 1 ELSE 0 END) 售后电池险量
        , SUM(CASE WHEN wk.ppriceid=81683 THEN wk.price ELSE 0 END)售后电池险金额
        , SUM(CASE WHEN wk.ppriceid=182901 THEN 1 ELSE 0 END) 安心保销量
        , SUM(CASE WHEN wk.ppriceid=182901 THEN wk.price ELSE 0 END)安心保金额
        , SUM(CASE WHEN c.service_ppid = 81683 THEN 1 ELSE 0 END) 售后电池出库量
        , SUM(CASE WHEN s.ServiceType=10 AND p.cid=393 AND wk.price=0 THEN wk.inprice ELSE 0 END) 售后电池险出险成本

        , SUM(CASE WHEN wk.ppriceid=81682 THEN 1 ELSE 0 END) 售后碎屏险量
        , SUM(CASE WHEN wk.ppriceid=81682 THEN wk.price ELSE 0 END) 售后碎屏险金额
        , SUM(CASE WHEN c.service_ppid = 81682 THEN 1 ELSE 0 END) 售后屏幕出库量
        , SUM(CASE WHEN s.ServiceType=9 AND p.cid=31 THEN wk.inprice ELSE 0 END) 售后碎屏险出险成本

        , SUM(CASE WHEN wk.ppriceid=110939 THEN 1 ELSE 0 END) 售后后盖险量
        , SUM(CASE WHEN wk.ppriceid=110939 THEN wk.price ELSE 0 END) 售后后盖险金额
        , SUM(CASE WHEN c.service_ppid = 110939 THEN 1 ELSE 0 END) 售后后盖出库量
        , SUM(CASE WHEN s.ServiceType=11 THEN wk.inprice ELSE 0 END) 售后后盖险出险成本
        FROM (
        <include refid="shouhouSonQuery"/>
        ) s
        INNER JOIN dbo.wxkcoutput wk WITH(NOLOCK) ON wk.wxid=s.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=wk.ppriceid
        LEFT JOIN dbo.shouhou_service_config c WITH(NOLOCK) ON c.ppid = wk.ppriceid
        WHERE s.stats=1 AND ISNULL(wk.stats,0)!=3
        GROUP BY s.areaid
    </sql>

    <!--    软件安装量 tb27-->
    <sql id="ruanJianAnZhuangLiang">
        SELECT areaid, COUNT(1) 软件接件量
        FROM dbo.msoft soft WITH (NOLOCK)
        WHERE soft.isticheng = 1
          AND soft.modidate BETWEEN #{req.start} and #{req.end}
        group by areaid
    </sql>

    <!--    软件维修毛利 bt28-->
    <sql id="ruanJianWeiXiuMaoLi">
        SELECT s.areaid, SUM(s.feiyong - s.costprice) 软件维修毛利
        FROM dbo.shouhou s with (nolock)
        WHERE s.issoft = 1
          AND ISNULL(s.isquji, 0) = 1
          AND ISNULL(s.stats, 0) = 1
          AND ISNULL(s.istui, 0) = 0
          AND s.xianshi = 1
          AND s.offtime BETWEEN #{req.start} and #{req.end}
        GROUP BY s.areaid
    </sql>

    <!--    unuse 高容量电池总量 tb29 ppid# -->
    <sql id="gaoRongLiangDianChiZongLiang">
        SELECT s.areaid,COUNT(1) 电池分类总量, SUM(CASE WHEN wx.ppriceid
        IN(78146,76555,76554,68371,62595,62594,62592,57334,57332,57331,57330,19900,62593) THEN 1 ELSE 0 END) 高容量电池总量
        FROM (
        <include refid="shouhouSonQuery"/>
        ) s INNER JOIN dbo.wxkcoutput wx with(nolock) ON wx.wxid=s.id
        WHERE s.stats=1 AND ISNULL(wx.stats,0)!=3
        AND wx.ppriceid
        IN(78146,76555,76554,68371,62595,62594,62592,57334,57332,57331,57330,19900,62593,8812,8740,7714,82230,82228,82226,71791,70935,70934,63822,62557,59610,46484,44903,40249,34782,34781,25938,25783,16614,13154
        ,78146,76555,76554,68371,62595,62594,62592,57334,57332,57331,57330,19900,62593)
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY s.areaid
    </sql>

    <!--    优品成本，良品成本 tb30-->
    <sql id="youPingLiangPingChengBen">
        SELECT s.areaid, SUM( CASE WHEN ISNULL(s.isXcMkc,0)=1 THEN wx.inprice ELSE 0 END)良品成本, SUM(CASE WHEN
        ISNULL(s.ishuishou,0)=1 THEN wx.inprice ELSE 0 END) 优品成本
        FROM (
        <include refid="shouhouSonQuery"/>
        ) s INNER JOIN dbo.wxkcoutput wx with(nolock) ON s.id=wx.wxid
        WHERE s.stats=1 AND s.baoxiu=1
        AND wx.ppriceid>0 AND ISNULL(wx.stats,0)!=3 AND (ISNULL(s.isXcMkc,0)=1 OR ISNULL(s.ishuishou,0)=1)
        GROUP BY s.areaid
    </sql>

    <sql id="jieJianLiangSql">
        SELECT areaid,
        sum(总接件量) 总接件量,
        sum(在保接件量) 在保接件量,
        sum(不在保接件量) 不在保接件量,
        sum(非保接件量) 非保接件量,
        sum(外修接件量) 外修接件量,
        sum(九机服务接件量) 九机服务接件量,
        sum(软件接件量) 软件接件量,
        sum(预约接件量) 预约接件量
        FROM (
        select
        ISNULL(ISNULL(ISNULL(
        zongJieJianLiang.areaid,jiejianliang.areaid),
        ruanJianAnZhuangLiang.areaid),
        yuYueJieJianLiang.areaid)
        areaid ,
        zongJieJianLiang.总接件量,
        jiejianliang.在保接件量,
        jiejianliang.不在保接件量,
        jiejianliang.非保接件量,
        jiejianliang.外修接件量,
        jiejianliang.九机服务接件量,
        ruanJianAnZhuangLiang.软件接件量,
        yuYueJieJianLiang.预约接件量
        FROM
        (<include refid="zongJieJianLiang"/>)as zongJieJianLiang
        full join
        (<include refid="jiejianliang"/>)as jiejianliang
        on zongJieJianLiang.areaid=jiejianliang.areaid
        full join
        (<include refid="ruanJianAnZhuangLiang"/>)as ruanJianAnZhuangLiang
        on zongJieJianLiang.areaid=ruanJianAnZhuangLiang.areaid
        full join
        (<include refid="yuYueJieJianLiang"/>)as yuYueJieJianLiang
        on zongJieJianLiang.areaid=yuYueJieJianLiang.areaid
        where 1=1
        )
    </sql>


    <sql id="huanJiLiangTuiKuanLiangSql">
        SELECT areaid,
        sum(退款量) 退款量,
        sum(换机量) 换机量,
        sum(总接件量) 总接件量
        FROM(
        select
        ISNULL(zongJieJianLiang.areaid,huanJiLiangTuiKuanLiang.areaid) areaid,
        zongJieJianLiang.总接件量,
        huanJiLiangTuiKuanLiang.退款量,
        huanJiLiangTuiKuanLiang.换机量
        FROM
        (<include refid="zongJieJianLiang"/>)as zongJieJianLiang
        full join
        (<include refid="huanJiLiangTuiKuanLiang"/>) as huanJiLiangTuiKuanLiang
        on huanJiLiangTuiKuanLiang.areaid=zongJieJianLiang.areaid
        where 1=1
        )
    </sql>

    <sql id="shouHouZhouQiSql">
        SELECT areaid,
        sum(平均处理周期) 平均处理周期,
        sum(平均外送周期) 平均外送周期,
        sum(平均自修周期) 平均自修周期,
        sum(周期自修修好量) 周期自修修好量,
        sum(一小时自修修好量) 一小时自修修好量,
        sum(八分钟自修修好量) 八分钟自修修好量,
        sum(超过3天自修修好量) 超过3天自修修好量
        FROM (
        <include refid="shouHouZhouQi"/>
        )
    </sql>

    <sql id="weiXiuLiangSql">
        SELECT areaid,
        sum(总修好量) 总修好量,
        sum(外送修好量) 外送修好量,
        sum(自修修好量) 自修修好量,
        sum(九机服务修好量) 九机服务修好量,
        sum(修不好量) 修不好量,
        sum(预约修好量) 预约修好量,
        sum(返修总量) 返修总量,
        sum(自修返修量) 自修返修量,
        sum(外送返修量) 外送返修量
        FROM (
        select
        ISNULL(weiXiuLiang.areaid,fanXiuZiXiuFanXiuWaiSongFanXiu.areaid) areaid,
        weiXiuLiang.总修好量,
        weiXiuLiang.外送修好量,
        weiXiuLiang.自修修好量,
        weiXiuLiang.九机服务修好量,
        weiXiuLiang.修不好量,
        weiXiuLiang.预约修好量,
        fanXiuZiXiuFanXiuWaiSongFanXiu.返修总量,
        fanXiuZiXiuFanXiuWaiSongFanXiu.自修返修量,
        fanXiuZiXiuFanXiuWaiSongFanXiu.外送返修量
        FROM
        (<include refid="weiXiuLiang"/>) weiXiuLiang
        full join
        (<include refid="fanXiuZiXiuFanXiuWaiSongFanXiu"/>) fanXiuZiXiuFanXiuWaiSongFanXiu
        on weiXiuLiang.areaid=fanXiuZiXiuFanXiuWaiSongFanXiu.areaid
        where 1=1
        )
    </sql>


    <sql id="shouHouFuWuTongJiSql">
        SELECT areaid,
        sum(售后电池险量) 售后电池险量,
        sum(售后电池险金额) 售后电池险金额,
        sum(售后电池出库量) 售后电池出库量,
        sum(售后电池险出险成本) 售后电池险出险成本,
        sum(售后碎屏险量) 售后碎屏险量,
        sum(售后碎屏险金额) 售后碎屏险金额,
        sum(售后屏幕出库量) 售后屏幕出库量,
        sum(售后碎屏险出险成本) 售后碎屏险出险成本,
        sum(售后后盖险量) 售后后盖险量,
        sum(售后后盖险金额) 售后后盖险金额,
        sum(售后后盖出库量) 售后后盖出库量,
        sum(售后后盖险出险成本) 售后后盖险出险成本
        FROM (
        <include refid="shouHouFuWuTongJi"/>
        )
    </sql>

    <sql id="shouHouWaiXiuMaoLiSql">
        SELECT
        areaid,
        sum(维修毛利) 维修毛利,
        sum(外修毛利) 外修毛利,
        sum(软件维修毛利) 软件维修毛利,
        sum(门店置换旧件毛利) 门店置换旧件毛利
        FROM (
        select
        ISNULL(ISNULL(shouHouWaiXiuMaoLi.areaid,ruanJianWeiXiuMaoLi.areaid)
        ,menDianZhiHuanJiuJianMaoLi.areaid) areaid,
        shouHouWaiXiuMaoLi.维修毛利,
        shouHouWaiXiuMaoLi.外修毛利,
        ruanJianWeiXiuMaoLi.软件维修毛利,
        menDianZhiHuanJiuJianMaoLi.门店置换旧件毛利
        FROM
        (<include refid="shouHouWaiXiuMaoLi"/>)as shouHouWaiXiuMaoLi
        full join
        (<include refid="ruanJianWeiXiuMaoLi"/>)as ruanJianWeiXiuMaoLi
        on shouHouWaiXiuMaoLi.areaid=ruanJianWeiXiuMaoLi.areaid
        full join
        (<include refid="menDianZhiHuanJiuJianMaoLi"/>)as menDianZhiHuanJiuJianMaoLi
        on ruanJianWeiXiuMaoLi.areaid=menDianZhiHuanJiuJianMaoLi.areaid
        where 1=1
        )
    </sql>

    <sql id="jiuJianMaoLiSql">
        SELECT areaid,
        sum(旧件毛利) 旧件毛利
        FROM (
        <include refid="jiuJianMaoLi"/>
        )
    </sql>

    <sql id="shouHouChengBenSql">
        SELECT
        areaid,
        sum(九机服务亏损) 九机服务成本,
        sum(特殊质保成本) 特殊质保成本,
        sum(良品成本) 良品成本,
        sum(优品成本) 优品成本
        FROM (
        select
        ISNULL(fuWuKuiSunZhiBaoChengBen.areaid,youPingLiangPingChengBen.areaid) areaid,
        fuWuKuiSunZhiBaoChengBen.九机服务亏损,
        fuWuKuiSunZhiBaoChengBen.特殊质保成本,
        youPingLiangPingChengBen.优品成本,
        youPingLiangPingChengBen.良品成本
        FROM
        (<include refid="fuWuKuiSunZhiBaoChengBen"/>)as fuWuKuiSunZhiBaoChengBen
        full join
        (<include refid="youPingLiangPingChengBen"/>)as youPingLiangPingChengBen
        on fuWuKuiSunZhiBaoChengBen.areaid=youPingLiangPingChengBen.areaid
        where 1=1
        )
    </sql>

    <sql id="weiXiuPeiJianSql">
        SELECT areaid,
        sum(返修换货成本) 返修换货成本
        FROM (
        <include refid="weiXiuPeiJian"/>
        )
    </sql>

    <sql id="shouHouZhuanChuLiangSql">
        SELECT
        areaid,
        sum(售后转出量) 售后转出量,
        sum(实时新机库存) 实时新机库存
        FROM (
        select
        ISNULL(shiShiXinJiKuCun.areaid,shouHouZhuanChuLiang.areaid) areaid,
        shiShiXinJiKuCun.实时新机库存,
        shouHouZhuanChuLiang.售后转出量
        FROM
        (<include refid="shiShiXinJiKuCun"/>)as shiShiXinJiKuCun
        full join
        (<include refid="shouHouZhuanChuLiang"/>)as shouHouZhuanChuLiang
        on shiShiXinJiKuCun.areaid=shouHouZhuanChuLiang.areaid
        where 1=1
        )
    </sql>

    <sql id="youHuiShuJuSql">
        SELECT
        areaid,
        sum(优惠码使用量) 优惠码使用量,
        sum(优惠码使用总金额) 优惠码使用总金额,
        sum(改价单量) 改价单量,
        sum(改价金额) 改价金额
        FROM (
        select
        ISNULL(youHuiMaShuLiangJinE.areaid,gaiJiaYouHuiDanGaoJieYouHuiZongJinE.areaid) areaid,
        youHuiMaShuLiangJinE.优惠码使用量,
        youHuiMaShuLiangJinE.优惠码使用总金额,
        gaiJiaYouHuiDanGaoJieYouHuiZongJinE.改价单量,
        gaiJiaYouHuiDanGaoJieYouHuiZongJinE.改价金额
        FROM
        (<include refid="youHuiMaShuLiangJinE"/>)as youHuiMaShuLiangJinE
        full join
        (<include refid="gaiJiaYouHuiDanGaoJieYouHuiZongJinE"/>)as gaiJiaYouHuiDanGaoJieYouHuiZongJinE
        on youHuiMaShuLiangJinE.areaid=gaiJiaYouHuiDanGaoJieYouHuiZongJinE.areaid
        where 1=1
        )
    </sql>

    <sql id="youHuiShuJuSqlJiuJi">
        SELECT
        areaid,
        sum(优惠码使用量) 优惠码使用量,
        sum(优惠码使用总金额) 优惠码使用总金额,
        sum(改价单量) 改价单量,
        sum(改价金额) 改价金额
        FROM (
        select
        ISNULL(youHuiMaShuLiangJinE.areaid,gaiJiaYouHuiDanGaoJieYouHuiZongJinE.areaid) areaid,
        youHuiMaShuLiangJinE.优惠码使用量,
        youHuiMaShuLiangJinE.优惠码使用总金额,
        gaiJiaYouHuiDanGaoJieYouHuiZongJinE.改价单量,
        gaiJiaYouHuiDanGaoJieYouHuiZongJinE.改价金额
        FROM
        (<include refid="youHuiMaShuLiangJinE"/>)as youHuiMaShuLiangJinE
        full join
        (<include refid="gaiJiaYouHuiDanGaoJieYouHuiZongJinEJiuJi"/>)as gaiJiaYouHuiDanGaoJieYouHuiZongJinE
        on youHuiMaShuLiangJinE.areaid=gaiJiaYouHuiDanGaoJieYouHuiZongJinE.areaid
        where 1=1
        )
    </sql>

    <!--维修套餐  productid#-->
    <sql id="wxtaocanSQL">
        SELECT area.id                                                       areaid
             , tc.套餐类维修量
             , gj.裸机类维修单量
             , ROUND(CAST(tc.套餐类维修量 as FLOAT) / (tc.套餐类维修量 + gj.裸机类维修单量), 4) 套餐搭配率
        FROM dbo.areainfo area WITH (NOLOCK)
                 LEFT JOIN(
            SELECT h.areaid, COUNT(h.id) 套餐类维修量
            FROM shouhou h with (nolock)
            WHERE h.xianshi = 1
              AND ISNULL(h.stats, 0) = 1
              AND ISNULL(h.issoft, 0) = 0
              AND h.offtime between #{req.start} and #{req.end}
              AND EXISTS(SELECT 1
                         FROM dbo.wxkcoutput wxk WITH (NOLOCK)
                                  INNER JOIN dbo.productinfo wxp WITH (NOLOCK) ON wxk.ppriceid = wxp.ppriceid
                         WHERE wxk.wxid = h.id
                           AND wxp.cid IN (393, 410)
                           AND wxk.stats != 3)
              AND EXISTS(SELECT 1
                         FROM dbo.wxkcoutput wxk WITH (NOLOCK)
                                  INNER JOIN dbo.productinfo wxp WITH (NOLOCK) ON wxk.ppriceid = wxp.ppriceid
                         WHERE wxk.wxid = h.id
                           AND wxp.cid NOT IN (393, 410)
                           AND wxk.stats != 3
                           AND (wxk.ppriceid = 0 OR (wxk.ppriceid > 0 AND wxp.productid NOT IN
                                                                          (20737, 26386, 33980, 21413, 21411, 44364,
                                                                           44365, 38422, 38421, 38420, 26389, 33981,
                                                                           44366, 33982, 41602, 41601))))
            GROUP BY h.areaid
        ) tc ON tc.areaid = area.id
                 LEFT JOIN (
            SELECT h.areaid, COUNT(h.id) 裸机类维修单量
            FROM shouhou h with (nolock)
            WHERE h.xianshi = 1
              AND ISNULL(h.stats, 0) = 1
              AND ISNULL(h.issoft, 0) = 0
              AND h.offtime between #{req.start} and #{req.end}
              AND (SELECT count(id)
                   FROM dbo.wxkcoutput wxk WITH (NOLOCK)
                            INNER JOIN dbo.productinfo wxp WITH (NOLOCK) ON wxk.ppriceid = wxp.ppriceid
                   WHERE wxk.wxid = h.id
                     AND wxk.stats != 3
                     AND wxp.productid NOT IN
                         (20737, 26386, 33980, 21413, 21411, 44364, 44365, 38422, 38421, 38420, 26389, 33981, 44366,
                          33982, 41602, 41601)) = 1
            GROUP BY h.areaid
        ) gj on gj.areaid = area.id
    </sql>
    <!--换外屏 userid#-->
    <!--    no area code-->
    <sql id="huanwaipinSQL">
        SELECT a.areaid,换外屏量,换外屏返修量,换外屏毛利 FROM (
        SELECT s.areaid,COUNT(1) 换外屏量 ,SUM(CASE WHEN ISNULL(s.isfan,0)=1 THEN 1 ELSE 0 END ) 换外屏返修量
        FROM dbo.shouhou s with(nolock) WHERE ISNULL(s.isquji,0)=1 AND s.stats=1 AND s.weixiuzuid=96 AND s.issoft=0
        AND ISNULL(s.istui,0)=0 AND s.feiyong+s.costprice>0 AND s.offtime between #{req.start} and #{req.end}
        GROUP BY s.areaid
        ) a LEFT JOIN (
        SELECT s.areaid ,SUM(wk.wxtongjitotal-wk.inprice) 换外屏毛利
        FROM dbo.shouhou s with(nolock) INNER JOIN dbo.wxkcoutput wk with(nolock) ON wk.wxid=s.id
        WHERE ISNULL(s.isquji,0)=1 AND s.stats=1 AND s.weixiuzuid=96 AND s.issoft=0 AND wk.ppriceid=0
        AND ISNULL(s.istui,0)=0 AND ISNULL(wk.stats,0)!=3
        AND NOT EXISTS(SELECT 1 FROM dbo.shouhou_huishou hs with(nolock) WHERE hs.wxkcid=wk.id AND hs.ishuanhuo=1)
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        AND s.offtime between #{req.start} and #{req.end}
        GROUP BY s.areaid
        ) b ON a.areaid=b.areaid
    </sql>
    <sql id="huanwaipinNewSQL">
        SELECT ai.id as areaid,外送换外屏量,自压量,自压毛利 FROM dbo.areainfo ai WITH (NOLOCK)
        LEFT JOIN (
        select sh.areaid,COUNT(DISTINCT sh.id) 外送换外屏量
        from shouhou sh with(nolock)
        inner join wxkcoutput wk with(nolock) on wk.wxid=sh.id
        left join productinfo p with(nolock) on p.ppriceid=wk.ppriceid
        where  ISNULL(sh.istui,0)=0 and p.cid=522 and isnull(sh.isquji,0)=1  AND wk.stats &lt;&gt; 3
          and sh.stats=1 and  isnull(sh.issoft,0)=0 and sh.feiyong+sh.costPrice >0 and sh.offtime between #{req.start} and #{req.end}
        GROUP BY sh.areaid
        ) a on a.areaid = ai.id
        LEFT JOIN (
        select sh.areaid, COUNT(DISTINCT sh.id) 自压量
        ,sum(case when isnull(sh.ServiceType,0)=0 then wk.wxtongjitotal-wk.inprice else 0 end) 自压毛利
        from shouhou sh with(nolock)
        inner join wxkcoutput wk with(nolock) on wk.wxid=sh.id
        left join productinfo p with(nolock) on p.ppriceid=wk.ppriceid
        where  ISNULL(sh.istui,0)=0 and p.cid=603 and isnull(sh.isquji,0)=1  AND ISNULL(wk.stats,0) &lt;&gt; 3 and sh.stats=1 and  isnull(sh.issoft,0)=0
        and sh.feiyong+sh.costPrice >0 and p.memberprice>0  and sh.baoxiu &lt;&gt; 1 and sh.offtime between #{req.start} and #{req.end}
        group by sh.areaid
        ) b ON b.areaid = ai.id
        <where>
            <if test="req.areaKind != null and req.areaKind > 0">
                and ai.kind1 = #{req.areaKind}
            </if>
            <if test="req.areaCodes != null and req.areaCodes.size > 0">
                and ai.id in
                <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                    #{item}
                </foreach>
            </if>
        </where>

    </sql>
    <!--    未取机情况统计 userid#-->
    <!--    no area code-->
    <sql id="weiqujiSql">
        select ISNULL(s.toareaid,s.areaid) areaid,
        SUM(case when sq.shouhouid is null AND ISNULL(s.stats,0)=0 AND DATEDIFF(DAY,s.modidate,GETDATE())>5 then 1 ELSE
        0 end) as 处理中自修超5天量,
        SUM(case when sq.shouhouid IS NOT NULL AND ISNULL(s.stats,0)=0 AND DATEDIFF(DAY,s.modidate,GETDATE())>30 then 1
        ELSE 0 end) as 处理中外送超30天量,
        SUM(CASE WHEN ISNULL(s.stats,0)=1 then 1 ELSE 0 end) as 处理中已修好单,
        SUM(CASE WHEN ISNULL(s.stats,0)=3 AND DATEDIFF(DAY,s.modidate,GETDATE())>7 then 1 ELSE 0 end) as 修不好超7天,
        SUM(CASE WHEN ISNULL(s.stats,0)=1 AND DATEDIFF(DAY,s.modidate,GETDATE())>5 then 1 ELSE 0 end) as 已修好超5天,
        SUM(CASE WHEN ISNULL(s.stats,0)=1 AND DATEDIFF(DAY,s.modidate,GETDATE())>30 then 1 ELSE 0 end) as 已修好超30天
        from shouhou s with(nolock)
        left join shouhou_qudao sq with(nolock) on s.id = sq.shouhouid
        where ISNULL(s.isquji,0)=0 and ISNULL(xianshi, 0) = 1
        and ISNULL(s.issoft, 0) = 0 and s.userid!=76783
        <!--        <if test="req.areaCodes != null and req.areaCodes.size > 0">-->
        <!--            and s.areaid in-->
        <!--            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">-->
        <!--                #{item}-->
        <!--            </foreach>-->
        <!--        </if>-->
        group by ISNULL(s.toareaid,s.areaid)
    </sql>
    <!--    预约单-->
    <!--    no area code-->
    <sql id="yuyueSql">
        SELECT y.areaid,count(1)预约单量
        ,SUM(CASE WHEN stats=3 AND ISNULL(y.isdel,0)=0 THEN 1 ELSE 0 END)预约单完成量
        ,SUM(CASE WHEN (ISNULL(y.isdel,0)=1 OR y.stats=10) THEN 1 ELSE 0 END)预约单取消量
        FROM dbo.shouhou_yuyue y with(nolock) WHERE y.ismobile=1
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and y.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        AND y.dtime between #{req.start} and #{req.end}
        GROUP BY y.areaid
    </sql>
    <!--    评价：售后、技术服务 EvaluateScore.job#-->
    <sql id="pingjiaSql">
        SELECT e.AreaId
             , SUM(CASE WHEN e.EvaluateType = 3 AND CommendedScore != -1 THEN 1 ELSE 0 END)         售后评价单量
             , SUM(CASE WHEN e.EvaluateType = 3 AND CommendedScore IN (9, 10) THEN 1 ELSE 0 END)    售后评价推荐
             , SUM(CASE WHEN e.EvaluateType = 3 AND CommendedScore IN (0, 1, 2) THEN 1 ELSE 0 END)  售后评价不推荐
             , SUM(CASE WHEN e.EvaluateType != 3 THEN 1 ELSE 0 END)                                 技术评价单量
             , SUM(CASE WHEN e.EvaluateType != 3 AND CommendedScore IN (9, 10) THEN 1 ELSE 0 END)   技术评价推荐
             , SUM(CASE WHEN e.EvaluateType != 3 AND CommendedScore IN (0, 1, 2) THEN 1 ELSE 0 END) 技术评价不推荐
        FROM  ${officeName}.dbo.Evaluate e with (nolock)
        WHERE dtime between #{req.start} and #{req.end}
          AND (e.EvaluateType = 3
            OR (e.EvaluateType = 6
                and EXISTS(SELECT 1
                           FROM  ${officeName}.dbo.EvaluateScore es with (nolock)
                           WHERE es.EvaluateId = e.Id
                             AND es.Job = 13)))
        GROUP BY e.AreaId
    </sql>

    <sql id="pingjiaSql2">
        SELECT e.AreaId
             , SUM(CASE WHEN e.EvaluateType in (3, 6) THEN 1 ELSE 0 END)                                      售后及技术评价总量
             , SUM(CASE WHEN e.EvaluateType = 3 THEN 1 ELSE 0 END)                                            售后评价总量
             , SUM(CASE WHEN e.EvaluateType = 6 THEN 1 ELSE 0 END)                                            技术评价总量
             , SUM(CASE WHEN e.EvaluateType = 3 AND e.CommendedScore in (9, 10) THEN 1 ELSE 0 END)            售后910星评价量
             , SUM(
                CASE WHEN e.EvaluateType = 3 AND e.CommendedScore IN (0, 1, 2, 3, 4, 5, 6) THEN 1 ELSE 0 END) 售后06星评价量
             , SUM(CASE WHEN e.EvaluateType = 6 AND e.CommendedScore in (9, 10) THEN 1 ELSE 0 END)            技术910星评价量
             , SUM(
                CASE WHEN e.EvaluateType = 6 AND e.CommendedScore IN (0, 1, 2, 3, 4, 5, 6) THEN 1 ELSE 0 END) 技术06星评价量
             , SUM(CASE WHEN e.EvaluateType in (3, 6) AND e.CommendedScore in (9, 10) THEN 1 ELSE 0 END)      售后及技术910星评价量
             , SUM(CASE
                       WHEN e.EvaluateType in (3, 6) AND e.CommendedScore IN (0, 1, 2, 3, 4, 5, 6) THEN 1
                       ELSE 0 END)                                                                            售后及技术06星评价量

        FROM  ${officeName}.dbo.Evaluate e with (nolock)
        WHERE e.dtime between #{req.start} and #{req.end}
          AND e.EvaluateType in (3, 6)
          and e.CommendedScore &lt;&gt; -1
        GROUP BY e.AreaId
    </sql>

    <!--    拉新统计-->
    <!--    no area code-->
    <sql id="laxinSql">
        SELECT t.areaid,
        COUNT(1) 接件用户量,
        SUM(CASE WHEN t.baoxiu=2 AND ABS(DATEDIFF(HOUR, t.modidate,t.UserRegTime))&lt;=24
        THEN 1 ELSE 0 END) 拉新量
        FROM ( SELECT s.areaid,s.userid,s.baoxiu,s.modidate,u.UserRegTime,ROW_NUMBER() OVER(PARTITION BY s.areaid,
        s.userid ORDER BY s.modidate) r
        FROM dbo.shouhou s with(nolock) LEFT JOIN dbo.BBSXP_Users u with(nolock) ON u.id=s.userid WHERE
        s.userid!=76783 AND s.xianshi=1
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        ) t WHERE t.r=1
        GROUP BY t.areaid
    </sql>





    <resultMap id="statisticsMap" type="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        <result column="area" property="area"/>
        <result column="areaid" property="areaid"/>

        <result column="departRank" property="departRank"/>
        <result column="总接件量" property="totalJieJian"/>
        <result column="code" property="areaLevel1Code"/>
        <result column="areaCode" property="areaLevel2Code"/>
        <result column="在保接件量" property="zaiBaoJieJianLiang"/>
        <result column="不在保接件量" property="buZaiBaoJieJianLiang"/>
        <result column="外修接件量" property="waiXiuJieJianLiang"/>
        <result column="九机服务接件量" property="jiJiFuWuJieJianLiang"/>
        <result column="换机量" property="huanJiLiang"/>
        <result column="退款量" property="tuiKuanLiang"/>
        <result column="平均处理周期" property="pingJunChuLiZhouQi"/>
        <result column="平均外送周期" property="pingJunWaiSongZhouQi"/>
        <result column="平均自修周期" property="pingJunZiXiuZhouQi"/>
        <result column="周期自修修好量" property="zhouQiZiXiuHaoLiang"/>
        <result column="一小时自修修好量" property="yiXiaoShiZiXiuXiuHaoLiang"/>
        <result column="八分钟自修修好量" property="baFenZhongZiXiuXiuHaoLiang"/>
        <result column="超过3天自修修好量" property="chaoGuo3TianZiXiuXiuHaoLiang"/>
        <result column="总修好量" property="zongXiuHaoLiang"/>
        <result column="外送修好量" property="waiSongXiuHaoLiang"/>
        <result column="自修修好量" property="ziXiuXiuHaoLiang"/>
        <result column="九机服务修好量" property="jiuJiFuWuXiuHaoLiang"/>
        <result column="修不好量" property="xiuBuHaoLiang"/>
        <result column="预约修好量" property="yuYueXiuHaoLiang"/>
        <result column="维修毛利" property="weiXiuMaoLi"/>
        <result column="外修毛利" property="waiXiuMaoLi"/>
        <result column="旧件毛利" property="jiuJianMaoLi"/>
        <!--        <result column="九机服务亏损" property="jiuJiFuWuKuiSun"/>-->
        <result column="九机服务成本" property="jiuJiFuWuChengBen"/>
        <result column="特殊质保成本" property="teShuZhiBaoChengBen"/>
        <result column="返修换货成本" property="fanXiuHuanHuoChengBen"/>
        <result column="售后转出量" property="shouHouZhuanChuLiang"/>
        <result column="实时新机库存" property="shiShiXinJiKuCun"/>
        <result column="软件安装提成量" property="ruanJianAnZhuangTiChengLiang"/>
        <result column="门店手机销量" property="menDianShouJiXiaoLiang"/>
        <result column="店面调往h1的维修配件" property="dianMianDiaoWangH1DeWeiXiuPeiJian"/>
        <result column="调往门店的维修配件" property="diaoWangMenDianDeWeiXiuPeiJian"/>
        <result column="店面调往dc的维修配件" property="dianMianDiaoWangDCDeWeiXiuPeiJiaNa"/>
        <result column="优品库存量" property="youPinKuCunLiang"/>
        <result column="上月手机销量" property="shangGeYueShouJiXiaoShouLiang"/>
        <result column="优惠码使用量" property="youHuiMaShiYongLiang"/>
        <result column="优惠码使用总金额" property="youHuiMaShiYongZongJinE"/>
        <result column="改价单量" property="gaiJiaDanLiang"/>
        <result column="改价金额" property="gaiJiaJinE"/>
        <result column="门店置换旧件毛利" property="menDianZhiHuanJiuJianMaoLi"/>
        <result column="返修总量" property="fanXiuZongLiang"/>
        <result column="自修返修量" property="ziXiuFanXiuLiang"/>
        <result column="外送返修量" property="waiSongFanXiuLiang"/>
        <result column="单台毛利比量" property="danTaiMaoLiBiLiang"/>
        <result column="配件返还单量" property="peiJianFanHuaiDanLiang"/>
        <result column="屏幕更换维修单量" property="pingMuGengHuanWeiXiuDanLiang"/>
        <result column="预约接件量" property="yuYueJieJianLiang"/>
        <result column="非保接件量" property="feiBaoJieJianLiang"/>
        <result column="售后电池险量" property="shouHouDianChiXianLiang"/>
        <result column="售后电池险金额" property="shouHouDianChiXianJinE"/>
        <result column="售后电池出库量" property="shouHouDianChiChuKuLiang"/>
        <result column="售后电池险出险成本" property="shouHouDianChiXianChuXianChengBen"/>
        <result column="售后碎屏险量" property="shouHouSuiPingxianLiang"/>
        <result column="售后碎屏险金额" property="shouHouSuiPingXianJinE"/>
        <result column="售后屏幕出库量" property="shouHouPingMuChuKuLiang"/>
        <result column="售后碎屏险出险成本" property="shouHouSuiPingXianChuXianChengBen"/>

        <result column="售后后盖险量" property="shouHouHouGaiXianLiang"/>
        <result column="售后后盖险金额" property="shouHouHouGaiXianJinE"/>
        <result column="售后后盖出库量" property="shouHouHouGaiChuKuLiang"/>
        <result column="售后后盖险出险成本" property="shouHouHouGaiXianChuXianChengBen"/>

        <result column="软件接件量" property="ruanJianJieJianLiang"/>
        <result column="软件维修毛利" property="ruanJianWeiXiuMaoLi"/>
        <result column="电池分类总量" property="dianChiFenLeiZongLiang"/>
        <result column="高容量电池总量" property="gaoRongLiangDianChiZongLiang"/>
        <result column="套餐类维修量" property="taoCanLeiWeiXiuLiang"/>
        <result column="优品成本" property="youPinChengBen"/>
        <result column="良品成本" property="liangPinChengBen"/>
        <result column="裸机类维修单量" property="luoJiLeiWeiXiuDanLiang"/>
        <result column="套餐搭配率" property="taoCanDaPeiLv"/>
        <!--换外屏业务旧-->
        <result column="换外屏量" property="huanWaiPingLiang"/>
        <result column="换外屏返修量" property="huanWaiPingFanXiuLiang"/>
        <result column="换外屏毛利" property="huanWaiPingMaoLi"/>
        <!--换外屏业务新-->
        <result column="外送换外屏量" property="waiSongHuanWaiPingLiang"/>
        <result column="自压量" property="ziYaLiang"/>
        <result column="自压毛利" property="ziYaLiangMaoLi"/>

        <result column="处理中自修超5天量" property="chuLiZhongZiXiuChao5TianLiang"/>
        <result column="处理中外送超30天量" property="chuLiZhongWaiSongChao30TianLiang"/>
        <result column="处理中已修好单" property="chuLiZhongYiXiuHaoDan"/>
        <result column="修不好超7天" property="xiuBuHaoChao7Tian"/>
        <result column="已修好超5天" property="yiXiuHaoChao5Tian"/>
        <result column="已修好超30天" property="yiXiuHaoChao30Tian"/>
        <result column="预约单量" property="yuYueDanLiang"/>
        <result column="预约单完成量" property="yuYueDanWanChengLiang"/>
        <result column="预约单取消量" property="yuYueDanQuXiaoLiang"/>
        <result column="售后评价单量" property="shouHouPingJiaDanLiang"/>
        <result column="售后评价推荐" property="shouHouPingJiaTuiJian"/>
        <result column="售后评价不推荐" property="shouHouPingJiaBuTuiJian"/>
        <result column="技术评价单量" property="jiShuPingJiaDanLiang"/>
        <result column="技术评价推荐" property="jiShuPingJiaTuiJian"/>
        <result column="技术评价不推荐" property="jiShuPingJiaBuTuiJian"/>
        <result column="拉新量" property="laXinLiang"/>
        <result column="接件用户量" property="jieJianYongHuLiang"/>

        <result column="售后及技术评价总量" property="shouHouJiJiShuPingJiaZongLiang"/>
        <result column="售后评价总量" property="shouHouPingJiaZongLiang"/>
        <result column="技术评价总量" property="jiShuPingJiaZongLiang"/>
        <result column="售后910星评价量" property="shouHouJiuShiXingPingJiaLiang"/>
        <result column="售后06星评价量" property="shouHouLingLiuXingPingJiaLiang"/>
        <result column="技术910星评价量" property="jiShuJiuShiXingPingJiaLiang"/>
        <result column="技术06星评价量" property="jiShuLingLiuXingPingJiaLiang"/>
        <result column="售后及技术910星评价量" property="shouHouJiJiShuJiuShiXingPingJiaLiang"/>
        <result column="售后及技术06星评价量" property="shouHouJiJiShuLingLiuXingPingJiaLiang"/>

    </resultMap>

    <select id="areaInfoQuery" resultMap="statisticsMap"
            parameterType="com.jiuji.oa.afterservice.batchreturn.vo.req.ShouHouStatisticsQueryReq">
        select ai.area,ai.id as areaid,dbo.getDepartTypeId(ai.depart_id,4) as areaLevel2Code,
        dbo.getDepartTypeId(ai.depart_id,3) as areaLevel1Code,ISNULL(departRank,99) departRank
        FROM areainfo ai with(nolock)
        LEFT JOIN dbo.departInfo d with(nolock) ON d.id=ai.depart_id
        WHERE ai.ispass = 1
        <if test="req.areaKind != null and req.areaKind > 0">
            <include refid="areaKind"/>
        </if>
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            <include refid="areaCode"/>
        </if>
        <if test="req.authorizedId != null">
            and ai.authorizeid = #{req.authorizedId}
        </if>
    </select>

    <select id="tenantAreaQuery" resultType="java.lang.Integer">
        SELECT count(*)
        FROM dbo.sysConfig with (nolock)
        WHERE code = 26
          AND isdel = 0
          AND value = '1'
    </select>

    <select id="areaIdAuthorizedQuery" resultType="java.lang.Integer">
        SELECT authorizeid
        FROM dbo.areainfo with (nolock)
        WHERE id = #{areaId}
    </select>

    <sql id="outSideGroupStatistics">
        select ai.id as areaid, tmp.*
        FROM areainfo ai with(nolock)
        LEFT JOIN (
        <if test="req.shouHouStatisticsEnum.code == 0">
            <include refid="jieJianLiangSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 1">
            <include refid="huanJiLiangTuiKuanLiangSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 2">
            <include refid="shouHouZhouQiSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 3">
            <include refid="weiXiuLiangSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 5">
            <include refid="shouHouFuWuTongJiSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 6">
            <include refid="shouHouWaiXiuMaoLiSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 7">
            <include refid="jiuJianMaoLiSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 8">
            <include refid="shouHouChengBenSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 9">
            <include refid="weiXiuPeiJianSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 10">
            <include refid="shouHouZhuanChuLiangSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 13 and req.xtenant != null">
            <choose>
                <when test="req.xtenant >= 1000">
                    <include refid="youHuiShuJuSql"/>
                </when>
                <otherwise>
                    <include refid="youHuiShuJuSqlJiuJi"/>
                </otherwise>
            </choose>
        </if>

        tmasd3 GROUP BY tmasd3.areaid
        ) tmp ON tmp.areaid = ai.id
        WHERE 1=1
        <if test="req.areaKind != null and req.areaKind > 0">
            <include refid="areaKind"/>
        </if>
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            <include refid="areaCode"/>
        </if>
    </sql>

    <select id="anotherStatistics" resultMap="statisticsMap"
            parameterType="com.jiuji.oa.afterservice.batchreturn.vo.req.ShouHouStatisticsQueryReq">
        <if test="req.statisticsChildrenEnum.code == 0">
            <include refid="ruanJianAnZhuangTiChengLiang"/>
        </if>
        <if test="req.statisticsChildrenEnum.code == 1">
            <include refid="menDianShouJiXiaoLiang"/>
        </if>
        <if test="req.statisticsChildrenEnum.code == 2">
            <include refid="dianMianDiaoWangH1WeiXiuPeiJian"/>
        </if>
        <if test="req.statisticsChildrenEnum.code == 3">
            <include refid="diaoWangMenDianWeiXiuPeiJian"/>
        </if>
        <if test="req.statisticsChildrenEnum.code == 4">
            <include refid="dianMianDiaoWangDCWeiXiuPeiJian"/>
        </if>
        <if test="req.statisticsChildrenEnum.code == 5">
            <include refid="youPingBi"/>
        </if>
        <if test="req.statisticsChildrenEnum.code == 6">
            <include refid="danTaiMaoLiBiLiang"/>
        </if>
        <if test="req.statisticsChildrenEnum.code == 7">
            <include refid="peiJianFanHuanDanLiang"/>
        </if>
        <if test="req.statisticsChildrenEnum.code == 8">
            <include refid="pinMuGengHuanWeiXiuDanLiang"/>
        </if>
        <if test="req.statisticsChildrenEnum.code == 9">
            <include refid="gaoRongLiangDianChiZongLiang"/>
        </if>
    </select>

    <select id="allTypeStatisticsQuery"
            parameterType="com.jiuji.oa.afterservice.batchreturn.vo.req.ShouHouStatisticsQueryReq"
            resultMap="statisticsMap">

        <if test="req.shouHouStatisticsEnum.code == 4">
            <include refid="laxinSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 12">
            <include refid="wxtaocanSQL"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 14">
            <include refid="huanwaipinSQL"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 15">
            <include refid="weiqujiSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 16">
            <include refid="pingjiaSql2"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 17">
            <include refid="yuyueSql"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 19">
            <include refid="huanwaipinNewSQL"/>
        </if>

        <if test="req.shouHouStatisticsEnum.code == 0">
            <include refid="outSideGroupStatistics"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 1">
            <include refid="outSideGroupStatistics"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 2">
            <include refid="outSideGroupStatistics"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 3">
            <include refid="outSideGroupStatistics"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 5">
            <include refid="outSideGroupStatistics"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 6">
            <include refid="outSideGroupStatistics"/>
        </if>

        <if test="req.shouHouStatisticsEnum.code == 7">
            <include refid="outSideGroupStatistics"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 8">
            <include refid="outSideGroupStatistics"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 9">
            <include refid="outSideGroupStatistics"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 10">
            <include refid="outSideGroupStatistics"/>
        </if>
        <if test="req.shouHouStatisticsEnum.code == 13">
            <include refid="outSideGroupStatistics"/>
        </if>

    </select>

    <select id="getCurUserDaQuAreaCodes" resultType="java.lang.String">
        select DISTINCT id
        from areainfo with (nolock)
        WHERE depart_id = (SELECT top 1 depart_id from areainfo with (nolock) WHERE id = #{id})
    </select>


    <select id="menDianZiXiuChao1Tian" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid
        , sum(IIF(DATEDIFF(HOUR, modidate, GETDATE()) > 24 and toareaid IS NULL and sq.id is null
        and sa.id is null, 1, 0)) as menDianZiXiuChao1Tian
        FROM shouhou s WITH (nolock)
        left join shouhou_qudao sq WITH (nolock) on s.id = sq.shouhouid
        left join shouhou_apply sa WITH (nolock) on s.id = sa.wxid
        WHERE isnull(stats, 0) = 0
        AND isnull(isquji, 0) = 0
        AND offtime IS NULL
        and s.areaid is not null
        and isnull(xianshi, 0) = 1
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>


    <select id="menDianZiXiuChao3Tian" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid
        , sum(IIF(DATEDIFF(DAY, modidate, GETDATE()) > 3 and toareaid IS NULL
        and sa.kindstats &lt;&gt; 5, 1, 0)) as menDianZiXiuChao3Tian
        FROM shouhou s WITH (nolock)
        left join shouhou_qudao sq WITH (nolock) on s.id = sq.shouhouid
        left join shouhou_apply sa WITH (nolock) on s.id = sa.wxid
        WHERE isnull(stats, 0) = 0
        AND isnull(isquji, 0) = 0
        and isnull(xianshi, 0) = 1
        AND offtime IS NULL
        and s.areaid is not null
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>

    <select id="kuaDianZiXiuChao5Tian" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.toareaid areaid
        , sum(IIF(DATEDIFF(DAY, modidate, GETDATE()) > 5, 1, 0)) as kuaDianZiXiuChao5Tian
        FROM shouhou s WITH (nolock)
        WHERE s.id not in (SELECT shouhouid FROM shouhou_qudao WITH (nolock))
        AND isnull(stats, 0) = 0
        AND isnull(isquji, 0) = 0
        and isnull(xianshi, 0) = 1
        AND toareaid is not null
        AND offtime IS NULL
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.toareaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.toareaid
        order by s.toareaid
    </select>

    <select id="ziXiuChao7Tian" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid, sum(IIF(DATEDIFF(DAY, modidate, GETDATE()) > 7, 1, 0)) as ziXiuChao7Tian
        FROM shouhou s WITH (nolock)
        WHERE s.id not in (SELECT shouhouid FROM shouhou_qudao WITH (nolock))
        AND isnull(stats
        , 0) = 0
        AND isnull(isquji
        , 0) = 0
        and isnull(xianshi, 0) = 1
        AND offtime IS NULL
        and s.areaid is not null
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>


    <select id="anZhuoWaiSongChao10Tian" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid,
        sum(IIF(DATEDIFF(DAY, modidate, GETDATE()) > 10, 1, 0)) as
        anZhuoWaiSongChao10Tian
        FROM shouhou s WITH (nolock)
        left join productinfo p WITH (nolock) on p.ppriceid = s.ppriceid
        WHERE s.id in (SELECT shouhouid FROM shouhou_qudao WITH (nolock))
        AND isnull(stats
        , 0) = 0
        AND isnull(isquji
        , 0) = 0
        and isnull(xianshi, 0) = 1
        AND offtime IS NULL
        AND isnull(p.brandID, 0) &lt;&gt; 1
        and s.areaid is not null
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>


    <select id="pingGuoWaiSongChao25Tian"
            resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid,
        sum(IIF(DATEDIFF(DAY, modidate, GETDATE()) > 25, 1, 0)) as
        pingGuoWaiSongChao25Tian
        FROM shouhou s WITH (nolock)
        left join productinfo p WITH (nolock)on p.ppriceid=s.ppriceid
        WHERE s.id in (SELECT shouhouid FROM shouhou_qudao WITH (nolock))
        and isnull(xianshi, 0) = 1
        AND isnull(p.brandID, 0) = 1
        AND isnull(stats, 0) = 0
        AND isnull(isquji, 0) = 0
        AND isnull(wxKind, 0) = 4
        AND offtime IS NULL
        and s.areaid is not null
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>


    <select id="waiXiuWaiSongChao15Tian" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid, sum(IIF(DATEDIFF(DAY, modidate, GETDATE()) > 15, 1, 0)) as waiXiuWaiSongChao15Tian
        FROM shouhou s WITH (nolock)
        WHERE isnull(stats, 0) = 0
        AND isnull(isquji, 0) = 0
        AND isnull(baoxiu, 0) = 2
        AND isnull(wxKind, 0) = 4
        and isnull(xianshi, 0) = 1
        and s.id in (SELECT shouhouid FROM shouhou_qudao WITH (nolock))
        AND offtime IS NULL
        and s.areaid is not null
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>


    <select id="waiSongChao30Tian" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid, sum(IIF(DATEDIFF(DAY, modidate, GETDATE()) > 30, 1, 0)) as waiSongChao30Tian
        FROM shouhou s WITH (nolock)
        WHERE s.id in (SELECT shouhouid FROM shouhou_qudao WITH (nolock))
        and isnull(xianshi, 0) = 1
        AND isnull(stats, 0) = 0
        AND isnull(isquji, 0) = 0
        AND isnull(wxKind, 0) = 4
        AND offtime IS NULL
        and s.areaid is not null
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>


    <select id="zhouQiYiXiuHaoChao5Tian" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid, sum(IIF(DATEDIFF(DAY, modidtime, GETDATE()) > 5, 1, 0)) as zhouQiYiXiuHaoChao5Tian
        FROM shouhou s WITH (nolock)
        WHERE s.id in (SELECT shouhouid FROM shouhou_qudao WITH (nolock))
        and isnull(xianshi, 0) = 1
        AND isnull(isquji, 0) = 0
        and ISNULL(stats, 0) = 1
        AND offtime IS NULL
        and s.areaid is not null
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>


    <select id="zhouQiYiXiuHaoChao30Tian"
            resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid, sum(IIF(DATEDIFF(DAY, modidtime, GETDATE()) > 30, 1, 0)) as zhouQiYiXiuHaoChao30Tian
        FROM shouhou s WITH (nolock)
        WHERE s.id in (SELECT shouhouid FROM shouhou_qudao WITH (nolock))
        and isnull(xianshi, 0) = 1
        AND isnull(isquji, 0) = 0
        and ISNULL(stats, 0) = 1
        AND offtime IS NULL
        and s.areaid is not null
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>


    <select id="zhouQiYiXiuHaoChao60Tian"
            resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes">
        SELECT s.areaid, sum(IIF(DATEDIFF(DAY, modidtime, GETDATE()) > 60, 1, 0)) as zhouQiYiXiuHaoChao60Tian
        FROM shouhou s WITH (nolock)
        WHERE s.id in (SELECT shouhouid FROM shouhou_qudao WITH (nolock))
        and isnull(xianshi, 0) = 1
        AND isnull(isquji, 0) = 0
        and ISNULL(stats, 0) = 1
        AND offtime IS NULL
        and s.areaid is not null
        <if test="req.areaCodes != null and req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        and s.modidate BETWEEN #{req.start} and #{req.end}
        group by s.areaid
        order by s.areaid
    </select>


    <select id="getBigAndSmallAreaDepartId" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.BigAndSmallAreaInfo">
        select a.id as areaId,a.area,dbo.getDepartTypeId(a.depart_id,4) as smallDepartId,d.departRank,
        dbo.getDepartTypeId(a.depart_id,3) as bigDepartId,a.rank,a.leve,a.kind1,a.kind2
        FROM areainfo a with(nolock) left join departInfo d with(nolock) on a.depart_id = d.id
        WHERE a.ispass = 1 and isnull(d.isdel,0) = 0
        and a.id in
        <foreach collection="areaIds" index="index" open="(" close=")" separator="," item="areaId">
            #{areaId}
        </foreach>
        order by d.departRank,rank,leve,kind1,kind2
    </select>
    <select id="searchProduct" resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.SearchProductVO">
        select distinct p.product_id as pid,p.ppriceid as ppid,p.product_name as productName,p.product_color as productColor,isnull(bcs.sales,0) sales
        from productinfo p with(nolock)
        left join (
        SELECT p.product_id,SUM(isnull(b.basket_count,0)) as sales
        FROM dbo.productinfo p with(nolock)
        LEFT JOIN basket b with(nolock)
        ON p.ppriceid = b.ppriceid and ISNULL(b.isdel,0)=0 and isnull(b.ischu,0) = 1 and isnull(b.type,0)!=22
        WHERE
        exists(select 1 from sub with(nolock) where sub.sub_id = b.sub_id and sub.sub_check=3 and sub.subtype != 10 and ((sub.yingfuM>0 and sub.jidianM>0) or sub.jidianM=0 ) AND sub.tradeDate1 > CONVERT(datetime,#{salesStartDate},20))
        and p.cid in (select f.ID FROM dbo.f_category_children('2,20,22,201,21,155') f) GROUP BY p.product_id
        ) bcs on bcs.product_id = p.product_id
        <where>
            AND  p.cid in (select f.ID from dbo.f_category_children('2,20,22,201,21,155') f)
        </where>
        order by sales desc,p.product_id DESC
    </select>
    <select id="searchProductKc"
            resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.SearchProductKcVO">
        SELECT distinct p2.cid as cid,p.productid as pid,p.ppriceid as ppid,ssc.price as price,k.inprice as inprice,ISNULL(cfg.display,1) display
             ,k.lcount as lcount,p2.name as productName
             ,(SELECT  STRING_AGG(value, ' ') WITHIN GROUP (ORDER BY orderid ASC) AS product_color FROM VIEW_PRODUCT_ST AA WHERE AA.ppriceid = p.ppriceid AND AA.cid = p2.cid) as productColor
             ,0 as sales
        FROM dbo.productprice p with(nolock)
        inner join dbo.product p2 with(nolock) on p.productid = p2.id
        LEFT JOIN dbo.product_kc k with(nolock) ON k.ppriceid=p.ppriceid AND k.areaid=#{areaId}
        LEFT JOIN dbo.shouhou_service_config ssc with(nolock) ON p.ppriceid=ssc.ppid AND ssc.is_del=0 AND ssc.disable=1
        LEFT JOIN wxproductconfig cfg with(nolock) ON p.ppriceid=cfg.ppriceid
        <where>
            and p2.cid in (select c.ID from category c with(nolock) where c.ID not in (40,50,69,463,481,522,603,728))
            and exists(select 1 from dbo.f_category_children(23) f where f.ID=p2.cid) AND p.isdel=0
        </where>
    </select>
    <select id="listSalesByPpid" resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.SearchProductKcVO$PpidSales">
        SELECT  COUNT(wxcp.id) as sales,wxcp.ppriceid ppid FROM dbo.wxkcoutput wxcp with(nolock)
        where wxcp.dtime > CONVERT(datetime,#{salesStartDate},20) group by wxcp.ppriceid
    </select>

    <select id="getRepairModelByProductId" resultType="java.lang.Integer">
        SELECT productId FROM RepairModel t1 WITH(NOLOCK) WHERE isnull(isdel,0) = 0
    </select>


    <select id="externalStatisticsList" resultMap="statisticsMap"  parameterType="com.jiuji.oa.afterservice.batchreturn.vo.req.ExternalStatisticsReq">
        <if test="type == 1">
            <!-- 维修毛利-->
            <include refid="shouHouWaiXiuMaoLiByExternal"/>
        </if>
        <if test="type == 2">
            <!-- 计算维修毛利配比 -->
            select ISNULL(shouhoumaoli.areaid,mendianxiaoliang.areaid) areaid,
            shouhoumaoli.维修毛利,
            mendianxiaoliang.门店手机销量
            FROM
            (<include refid="shouHouWaiXiuMaoLiByExternal"/>)as shouhoumaoli
            full join
            (<include refid="menDianShouJiXiaoLiangByExternal"/>)as mendianxiaoliang
            on shouhoumaoli.areaid=mendianxiaoliang.areaid where 1=1
        </if>
        <if test="type == 3">
            <!-- 计算电池搭售率 -->
            <include refid="shouHouFuWuTongJiByExternal"/>
        </if>
        <if test="type == 4">
            <!-- 计算屏幕搭售率 -->
            <include refid="shouHouFuWuTongJiByExternal"/>
        </if>
        <if test="type == 5">
            <!-- 计算放弃维修率 -->
            select ISNULL(weixiuliang.areaid,zongjiejianliang.areaid) areaid,
            weixiuliang.修不好量,
            zongjiejianliang.总接件量
            FROM
            (<include refid="weiXiuLiangByExternal"/>)as weixiuliang
            full join
            (<include refid="zongJieJianLiangByExternal"/>)as zongjiejianliang
            on weixiuliang.areaid=zongjiejianliang.areaid where 1=1
        </if>
    </select>
    <select id="listOaUserAreaId" resultType="java.lang.Integer">
        SELECT cr.areaid FROM ch999Ranks cr with(nolock)
        where cr.ch999_id = #{userId}
          and #{rank} in (select fs.split_value from dbo.F_SPLIT(isnull(cr.ranks,''),',') fs)
    </select>
    <select id="queryServiceStatisticsListPageByArea"
            resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.ServiceStatisticsRes">
        SELECT s.areaid as areaId,a.area as areaCode, Round(ISNULL(sum(w.inprice),0),2) as serviceInprice,s.ServiceType,
            ISNULL(psr.ServiceType,s.ServiceType) as mainServiceType,COUNT(DISTINCT s.id)  as serviceWxCount
        from shouhou s with(nolock)
        left join wxkcoutput w with(nolock) on w.wxid = s.id and w.service_type >0
        left join ServiceRecord sr with(nolock) on sr.server_shouhou_id = s.id
        left join ServiceRecord psr with(nolock) on psr.id = sr.servicesTypeBindId
        left join areainfo a with(nolock) on a.id = s.areaid
        left join productinfo p with(nolock) on p.ppriceid = s.ppriceid
        <where>s.isquji = 1 and isnull(s.issoft,0) = 0 and s.stats = 1
            and s.xianshi = 1 and s.userid != 76783
            <!--门店-->
            <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
                and a.id in
                <foreach collection="req.searchAreaIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <!--商品名称-->
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and p.product_name like '%' + #{req.searchValue} + '%'
            </if>
            <!--商品id-->
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and p.productid = #{req.searchValue}
            </if>
            <!--skuid-->
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and p.ppriceid = #{req.searchValue}
            </if>
            <if test="req.startTime != null and req.endTime != null  ">
                <if test="req.searchTimeType!=null and req.searchTimeType==1">
                    and s.offtime between #{req.startTime} and #{req.endTime}
                </if>
                <if test="req.searchTimeType!=null and req.searchTimeType==2">
                    and s.tradedate between #{req.startTime} and #{req.endTime}
                </if>
            </if>
        </where>
        group by s.areaid,a.area,s.ServiceType,psr.ServiceType
        having  COUNT(DISTINCT s.id) !=0
        order by s.areaid
    </select>

    <select id="queryServiceStatisticsListPageByProduct"
            resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.ServiceStatisticsRes">
        SELECT isnull(p.product_id,0) as productId,isnull(p.product_name,'其他') as productName,Round(ISNULL(sum(w.inprice),0),2) as serviceInprice,
               s.ServiceType,ISNULL(psr.ServiceType,s.ServiceType) as mainServiceType,COUNT(DISTINCT s.id) as serviceWxCount
        from shouhou s with(nolock)
        left join wxkcoutput w with(nolock) on w.wxid = s.id and w.service_type >0
        left join areainfo a with(nolock) on a.id = s.areaid
        left join ServiceRecord sr with(nolock) on sr.server_shouhou_id = s.id
        left join ServiceRecord psr with(nolock) on psr.id = sr.servicesTypeBindId
        left join productinfo p with(nolock) on p.ppriceid = s.ppriceid
        <where>s.isquji = 1 and isnull(s.issoft,0) = 0 and s.stats = 1
            and s.xianshi = 1 and s.userid != 76783
            <!--门店-->
            <if test="req.searchAreaIdList != null and req.searchAreaIdList.size()>0 ">
                and a.id in
                <foreach collection="req.searchAreaIdList" item="it" separator="," open="(" close=")">
                    #{it}
                </foreach>
            </if>
            <!--商品名称-->
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==1">
                and p.product_name like '%' + #{req.searchValue} + '%'
            </if>
            <!--商品id-->
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==2">
                and p.productid = #{req.searchValue}
            </if>
            <!--skuid-->
            <if test="req.searchValue!=null and req.searchValue!='' and req.searchType==3">
                and p.ppriceid = #{req.searchValue}
            </if>
            <if test="req.startTime != null and req.endTime != null  ">
                <if test="req.searchTimeType!=null and req.searchTimeType==1">
                    and s.offtime between #{req.startTime} and #{req.endTime}
                </if>
                <if test="req.searchTimeType!=null and req.searchTimeType==2">
                    and s.tradedate between #{req.startTime} and #{req.endTime}
                </if>
            </if>
        </where>
        group by p.product_id,p.product_name,s.ServiceType,psr.ServiceType
        having  COUNT(DISTINCT s.id) !=0
        order by p.product_id
    </select>
    <select id="shouhouCountStatistics"
            resultType="com.jiuji.oa.afterservice.bigpro.statistics.bo.ShouhouProductCountBo">
        SELECT s.product_id productId, count(1) wxCount from shouhou s with(nolock)
        where s.xianshi = 1 and s.product_id is not null
        and modidate &gt; #{sixMoth}
        group by s.product_id
    </select>

    <!--    售后毛利 外修毛利 tb5-->
    <sql id="shouHouWaiXiuMaoLiByExternal">
        SELECT s.areaid, SUM(CASE WHEN (ISNULL(k.wxtongjitotal,0)-k.inprice)
        &lt;
        0 and s.ServiceType=9 AND ( p.cid=31 OR
        k.ppriceid=0 ) then 0 else ISNULL(k.wxtongjitotal,0)-k.inprice END) AS 维修毛利
        from (
        <include refid="shouhouSonQueryByExternal"/>
        ) s INNER JOIN dbo.wxkcoutput k with(nolock) ON s.id=k.wxid
        left JOIN dbo.productinfo p with(nolock) ON p.ppriceid=k.ppriceid
        WHERE s.stats=1 AND s.baoxiu!=1 AND s.istui=0
        AND (s.ServiceType=0 OR (s.ServiceType!=0 AND ISNULL(k.wxtongjitotal,0)>0) )
        AND (ISNULL(p.cid,0)!=31 OR s.wxkind!=5) AND k.stats!=3
        <if test="req.areaCodes.size > 0">
        and s.areaid in
        <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        </if>
        and not exists(select id from shouhou_huishou sh with(nolock) where ISNULL(ishuanhuo,0) = 1 and sh.shouhou_id =
        s.id AND sh.wxkcid=k.id)
        GROUP by s.areaid
    </sql>

    <!--    unuse 门店手机销量 tb12 basket# ppid#-->
    <sql id="menDianShouJiXiaoLiangByExternal">
        SELECT areaid,SUM(b.basket_count)  门店手机销量 FROM sub  with(nolock)
        left join basket b with(nolock) on sub.sub_id = b.sub_id
        WHERE sub.sub_check=3 AND ISNULL(b.isdel,0)=0
        and b.ppriceid in(select p.ppriceid from dbo.productinfo p with(nolock) where p.cid in (select f.ID from dbo.f_category_children('2,20,22,201,21') f))
        and sub.subtype != 10 and  ((sub.yingfuM>0 and sub.jidianM>0) or sub.jidianM=0  )  and isnull(b.ischu,0) = 1
        and isnull(b.type,0)!=22 and b.ppriceid not in (60142,59948)
        <if test="req.areaCodes.size > 0">
            and areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        AND sub.tradeDate1 BETWEEN #{req.startTime} and #{req.endTime}
        GROUP BY sub.areaid
    </sql>

    <!--    维修量 tb4-->
    <sql id="weiXiuLiangByExternal">
        SELECT s.areaid,
        sum(case when s.stats=3 then 1 else 0 end) as 修不好量
        from (
        <include refid="shouhouSonQueryByExternal"/>
        ) s
        left join shouhou_qudao sq with(nolock) on s.id = sq.shouhouid
        where 1=1
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by s.areaid
    </sql>

    <!--    总接件量 tb20 userid# -->
    <sql id="zongJieJianLiangByExternal">
        SELECT s.areaid, count(1) as 总接件量
        from shouhou s with (nolock)
        where ISNULL(xianshi, 0) = 1
        and ISNULL(s.issoft, 0) = 0
        and s.modidate between #{req.startTime} and #{req.endTime}
        and s.userid != 76783
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        group by s.areaid
    </sql>

    <!--    售后服务统计 tb26 ppid# cid#-->
    <sql id="shouHouFuWuTongJiByExternal">
        SELECT s.areaid
        , SUM(CASE WHEN wk.ppriceid=81683 THEN 1 ELSE 0 END) 售后电池险量
        , SUM(CASE WHEN wk.ppriceid=182901 THEN 1 ELSE 0 END) 安心保销量
        , SUM(CASE WHEN c.service_ppid = 81683 THEN 1 ELSE 0 END) 售后电池出库量
        , SUM(CASE WHEN wk.ppriceid=81682 THEN 1 ELSE 0 END) 售后碎屏险量
        , SUM(CASE WHEN c.service_ppid = 81682 THEN 1 ELSE 0 END) 售后屏幕出库量
        FROM (
        <include refid="shouhouSonQueryByExternal"/>
        ) s
        INNER JOIN dbo.wxkcoutput wk WITH(NOLOCK) ON wk.wxid=s.id
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=wk.ppriceid
        LEFT JOIN dbo.shouhou_service_config c WITH(NOLOCK) ON c.ppid = wk.ppriceid
        WHERE s.stats=1 AND ISNULL(wk.stats,0)!=3
        <if test="req.areaCodes.size > 0">
            and s.areaid in
            <foreach collection="req.areaCodes" open="(" separator="," close=")" item="item">
                #{item}
            </foreach>
        </if>
        GROUP BY s.areaid
    </sql>

    <sql id="shouhouSonQueryByExternal">
        SELECT distinct s.id,
        s.areaid,
        s.userid,
        s.mobile,
        ISNULL(s.stats, 0)       stats
        ,
        ISNULL(s.baoxiu, 0)      baoxiu,
        ISNULL(s.ServiceType, 0) serviceType,
        s.wxkind,
        ishuishou,
        ISNULL(s.isquji, 0)      isquji,
        s.feiyong,
        s.costprice,
        ISNULL(s.istui, 0)       istui,
        ISNULL(s.isfan, 0)       isfan,
        s.isXcMkc,
        s.modidate,
        s.modidtime,
        s.weixiuzuid,
        s.qujitongzhitime,
        s.offtime,
        s.yuyueid
        FROM dbo.shouhou s with (nolock)
        WHERE ISNULL(xianshi, 0) = 1
        and ISNULL(s.issoft, 0) = 0
        AND s.offtime between #{req.startTime} and #{req.endTime}
    </sql>


</mapper>
