<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.batchreturn.dao.BatchReturnMkcDao">

    <insert id="insertBatch">
        insert into batch_return_mkc(batch_return_id,mkc_id,original_price,price,ppid,imei,basket_id) values
        <foreach collection="entries" open=" " separator="," close=" " item="entry">
            (#{entry.batchReturnId},#{entry.mkcId},#{entry.originalPrice},#{entry.price},#{entry.ppid},#{entry.imei},#{entry.basketId})
        </foreach>
    </insert>
    <insert id="insertSecret">
        INSERT INTO secretCodeConfig(secret, version, code)
        select #{secret}, '1.0', #{code} where not exists(select 1 from secretCodeConfig where code = #{code});
    </insert>
    <update id="updatePriceBatch">
        <foreach collection="entries" item="item">
            update batch_return_mkc
            <set>
                price = #{item.price}
            </set>
            where id = #{item.id};
        </foreach>
    </update>

    <update id="updateToMkcBatch">
        <foreach collection="entries" item="item">
            update batch_return_mkc
            <set>
                to_mkc_id = #{item.toMkcId}
            </set>
            where id = #{item.id};
        </foreach>
    </update>

    <select id="listMkcVoByBatchReturnId" resultType="com.jiuji.oa.afterservice.batchreturn.vo.BatchReturnMkcVO">
         select
            mkc.id,
            mkc.mkc_id mkcId,
            mkc.imei,
            mkc.price,
            mkc.original_price originalPrice,
            mkc.ppid ppid,
            mkc.basket_id basketId,
            p.product_name productName,
            p.product_color productColor,
             m.inprice
        from dbo.batch_return_mkc mkc with(nolock)
            left join dbo.productinfo p with(nolock) on mkc.ppid = p.ppriceid
             left join dbo.product_mkc m with(nolock ) on m.id = mkc.mkc_id
        where mkc.batch_return_id = #{batchReturnId}
    </select>

    <select id="listToMkcIdByIdList" resultType="java.lang.Integer">
        select
            brm.to_mkc_id
        from batch_return_mkc brm with(nolock)
        inner join batch_return br with(nolock) on brm.batch_return_id = br.id and br.status = 2
        where brm.id in
        <foreach collection="idList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="getSecretByCode" resultType="java.lang.String">
      select secret from secretCodeConfig t with(nolock) where t.code=#{code}
    </select>


</mapper>
