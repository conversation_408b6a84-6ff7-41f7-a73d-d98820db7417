<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.customeraccount.dao.CustomerAccountMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.customeraccount.po.CustomerAccount">
        <id column="id" property="id" />
        <result column="customerId" property="customerId" />
        <result column="userId" property="userId" />
        <result column="sub_id" property="subId" />
        <result column="kinds" property="kinds" />
        <result column="kindsName" property="kindsName" />
        <result column="beginPrice" property="beginPrice" />
        <result column="addPrice" property="addPrice" />
        <result column="reducePrice" property="reducePrice" />
        <result column="endPrice" property="endPrice" />
        <result column="dtime" property="dtime" />
        <result column="inuser" property="inuser" />
        <result column="comment" property="comment" />
    </resultMap>
    <select id="getCustomerId" resultType="java.lang.Integer">
        SELECT
            id
        FROM
            dbo.zitidian with(nolock)
        WHERE
            userid = #{userId}
            AND shopType = 4
            AND ispass = 1
    </select>
    <select id="getEndPrice" resultType="java.math.BigDecimal">
        SELECT TOP
            1 endPrice
        FROM
            dbo.customerAccount with(nolock)
        WHERE
            customerId = #{customerId}
        ORDER BY
            dtime DESC
    </select>
    <select id="getCustomerKinds" resultType="java.lang.Integer">
        SELECT
            ztd.customerKinds
        FROM
            dbo.zitidian ztd with(nolock)
        WHERE
            ztd.userid = #{userId}
          AND ztd.ispass = 1
    </select>

</mapper>
