<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.batchreturn.dao.BatchReturnDao">

    <insert id="insert" parameterType="com.jiuji.oa.afterservice.batchreturn.po.BatchReturn" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO batch_return (sub_id, remark, take_ch999_id, take_way,area_id,user_name,user_tel,buy_time,return_way)
        VALUES (#{subId}, #{remark}, #{takeCh999Id}, #{takeWay},#{areaId},#{userName},#{userTel},#{buyTime},#{returnWay})
    </insert>
    <insert id="insertTuihuan"  keyColumn="id" useGeneratedKeys="true" keyProperty="batchReturn.tuihuanId">
        insert into shouhou_tuihuan(shouhou_id,tuihuan_kind,inuser,areaid,comment,tui_way,bankname,bankfuming,banknumber,tuikuanM,tuikuanM1
                                   ,zhejiaM,sub_id,basket_ids,inprice,coinM,smallproid,baitiaoM,kuBaiTiaoM,isValidt,dtime
                                   ,check1user,check1dtime,check1,check2user,check2dtime,check2)
        select #{batchReturn.id},#{tuihuanKind},#{inUser.userName},#{inUser.areaId},#{batchReturn.remark},#{batchReturn.returnWay},#{batchReturn.bankName}
             ,#{batchReturn.bankUserName},#{batchReturn.bankCardNum},#{refundPrice},#{refundPrice},0.00,#{batchReturn.subId},null,
               (SELECT isnull(sum(pm.inbeihuoprice),0) FROM  batch_return_mkc brm with(nolock)
                                                     left join product_mkc pm with(nolock) on pm.basket_id = brm.basket_id
                                                     where brm.batch_return_id = #{batchReturn.id}),0.00,#{batchReturn.id}
             ,null,0,0,getdate(),#{batchReturn.review1Ch999Id},#{batchReturn.review1Time},iif(#{batchReturn.review1Ch999Id} is null,null,1)
             ,#{batchReturn.review2Ch999Id},#{batchReturn.review2Time},iif(#{batchReturn.review2Ch999Id} is null,null,1)
        where not exists(SELECT 1 FROM shouhou_tuihuan st where isnull(st.isdel,0) = 0 and st.tuihuan_kind = #{tuihuanKind}
                                                            and st.shouhou_id = #{batchReturn.id} and st.check3 is null);
    </insert>
    <select id="getBatchReturnSubInfo" resultType="com.jiuji.oa.afterservice.batchreturn.bo.BatchReturnSubBO">
        select
            s.sub_id subId,
            b.basket_id basketId,
            b.basket_count basketCount,
            k.id mkcId,
            k.ppriceid ppid,
            k.imei imei,
            p.product_name productName,
            p.product_color productColor,
            s.tradeDate1 tradeDate1,
            s.sub_check subCheck,
            isnull(b.price_shouhou, b.price2) price2,
            s.userid userId,
            s.sub_to userName,
            s.sub_mobile userTel
        from dbo.basket b with(nolock)
            left join dbo.sub s with(nolock)on b.sub_id = s.sub_id
            left join dbo.product_mkc k with(nolock) on k.basket_id = b.basket_id
            left join dbo.productinfo p with(nolock) on b.ppriceid = p.ppriceid
        where isnull(b.isdel, 0) = 0
            and b.ismobile = 1
            and s.sub_check = 3
            and s.sub_id = #{subId}
           -- and s.subtype in(13,14)
    </select>

    <select id="getBatchReturnSubInfoAll" resultType="com.jiuji.oa.afterservice.batchreturn.bo.BatchReturnSubBO">
        select
            s.sub_id subId,
            s.userid userId,
            b.basket_id basketId,
            b.basket_count basketCount,
            b.youhuiPrice,
            b.jifenPrice,
            k.id mkcId,
            k.ppriceid ppid,
            k.imei imei,
            s.tradeDate1 tradeDate1,
            s.sub_check subCheck,
            isnull(b.price_shouhou, b.price2) price2,
            b.ismobile isMobile
        from dbo.basket b with(nolock)
            left join dbo.sub s with(nolock)on b.sub_id = s.sub_id
            left join dbo.product_mkc k with(nolock) on k.basket_id = b.basket_id
            left join dbo.productinfo p with(nolock) on b.ppriceid = p.ppriceid
        where isnull(b.isdel, 0) = 0
            and s.sub_check = 3
            and s.sub_id = #{subId}
           -- and s.subtype in(13,14)
    </select>

    <select id="getUserNames" resultType="com.jiuji.oa.afterservice.common.bo.OaUserBO">
        select
        ch999_id userId,
        ch999_name userName
        from ch999_user with(nolock)
        where ch999_id in
        <foreach collection="userIds" open="(" separator="," close=")" item="userId">
            #{userId}
        </foreach>
    </select>

    <select id="page" parameterType="com.jiuji.oa.afterservice.batchreturn.vo.req.BatchReturnQueryReq"
            resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.BatchReturnPageVO">
        select
        br.id id,
        br.sub_id subId,
        a.area area,
        br.user_tel userTel,
        p.product_name productName,
        p.product_color productColor,
        u.ch999_name takeCh999Name,
        br.take_time takeTime,
        br.status statusCode
        from batch_return br with(nolock)
        left join batch_return_mkc mkc with(nolock) on mkc.batch_return_id=br.id
        left join dbo.productinfo p with(nolock) on p.ppriceid = mkc.ppid
        left join dbo.ch999_user u with(nolock) on br.take_ch999_id=u.ch999_id
        left join areainfo a with(nolock) on a.id=br.area_id
        where 1=1
        and a.area is not null
        <if test="req.id!=null and req.id!=0">
            and br.id=#{req.id}
        </if>
        <if test="req.takeCh999UserName != null and req.takeCh999UserName!=''">
            and u.ch999_name like concat('%',#{req.takeCh999UserName},'%')
        </if>
        <if test="req.areaId != null and req.areaId!=0">
            and br.area_id = #{req.areaId}
        </if>
        <if test="req.subId!=null and req.subId!=0">
            and br.sub_id = #{req.subId}
        </if>
        <if test="req.userTel!=null and req.userTel!=''">
            and br.user_tel like concat('%',#{req.userTel},'%')
        </if>
        <if test="req.productName !=null and req.productName !=''">
            and p.product_name like concat('%',#{req.productName},'%')
        </if>
        <if test="req.ppid!=null and req.ppid!=0">
            and mkc.ppid=#{req.ppid}
        </if>
        <if test="req.timeType != null and req.timeType==1">
            <if test="req.startTime !=null">
                and br.take_time >= #{req.startTime}
            </if>
            <if test="req.endTime !=null">
                and br.take_time &lt;= #{req.endTime}
            </if>
        </if>
        <if test="req.status!=null">
            and br.status = #{req.status}
        </if>
        <if test="req.timeType != null and req.timeType==2">
            <if test="req.startTime !=null">
                and br.return_time >= #{req.startTime}
            </if>
            <if test="req.endTime !=null">
                and br.return_time &lt;= #{req.endTime}
            </if>
        </if>
        <if test="req.returnWay != null">
            and br.return_way = #{req.returnWay}
        </if>
    </select>

    <update id="updateSubToReturn">
        update sub
        set
            sub_check=9,
            returnDate=getdate()
        where sub_id=#{subId} and sub_check = 3
    </update>
    <update id="updateRollBackSubToReturn">
        update sub
        set
            sub_check=3,
            returnDate=null
        where sub_id=#{subId} and sub_check = 9
    </update>
    <select id="getReturnInfoByReturnId" resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.BatchReturnPageVO">
        select
        br.id id,
        br.sub_id subId,
        a.area area,
        br.user_tel userTel,
        p.product_name productName,
        p.product_color productColor,
        u.ch999_name takeCh999Name,
        br.take_time takeTime,
        br.status statusCode
        from batch_return br with(nolock)
        left join batch_return_mkc mkc with(nolock) on mkc.batch_return_id=br.id
        left join dbo.productinfo p with(nolock) on p.ppriceid = mkc.ppid
        left join dbo.ch999_user u with(nolock) on br.take_ch999_id=u.ch999_id
        left join areainfo a with(nolock) on a.id=br.area_id
        where 1=1
        and a.area is not null
		and br.id in
		<foreach collection="returnIds" open="(" close=")" separator="," item="returnId" index="index">
            #{returnId}
        </foreach>
    </select>
    <select id="getLastNotComplete" resultType="com.jiuji.oa.afterservice.batchreturn.po.BatchReturn">
        SELECT top 1 id, sub_id, status, remark, take_ch999_id, take_time, take_way, return_way, bank_card_num, bank_name, bank_user_name
             , review1_ch999_id, review1_time, review2_ch999_id, review2_time, return_ch999_id, return_time, to_sub_id, area_id, user_name
             , user_tel, buy_time, kemu, voucher_id FROM batch_return br with(nolock)
        where br.status = 1 and br.sub_id=#{subId}
        order by br.id desc
    </select>
    <select id="existsGtBuyPrice" resultType="java.lang.Boolean">
        SELECT iif(min(isnull(b.price2,0) - isnull(brm.price,0))&lt;0,1,0) from batch_return_mkc brm with(nolock)
        left join basket b with(nolock) on b.basket_id = brm.basket_id where brm.batch_return_id = #{batchReturnId}
    </select>
    <select id="listRefundDataBySubId" resultType="com.jiuji.oa.afterservice.refund.bo.RefundDataBo">
        SELECT isnull(b.price_shouhou, b.price2) price, isnull(s.coinM,0) coinM, isnull(s.yingfuM,0) yingfuM, isnull(s.yifuM,0) yifuMoney,
        b.basket_id,b.sub_id,s.userid, s.tradeDate1, b.type, b.product_peizhi, s.sub_check,s.areaid
        FROM basket b with(nolock)
        INNER JOIN sub s with(nolock) ON b.sub_id = s.sub_id
        WHERE s.sub_check = 3
        AND isnull(b.isdel, 0) = 0
        and b.basket_id in
        <foreach collection="basketIds" open="(" close=")" separator="," item="id" index="index">
            #{id}
        </foreach>

    </select>

</mapper>
