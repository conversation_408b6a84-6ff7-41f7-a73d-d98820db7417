<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ProductbarcodeMapper">

    <select id="listByPpids" resultType="com.jiuji.oa.afterservice.bigpro.entity.Productbarcode">
        SELECT a.id,
               a.ppriceid as ppriceid,
               a.barcode,
               a.isdefault as isDefault,
               a.operator,
               a.opxtenant opXtenant,
               isnull(a.addTime,'1900-01-01') as addTime,
               a.updatedate
        FROM productBarcode as a with(nolock)
        where a.ppriceid in
            <foreach collection="ppids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        order by a.ppriceid asc, a.isDefault desc, addTime desc
    </select>
</mapper>

