<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouHouCantFixReasonMapper">


    <select id="selectByShouHouIdList" resultType="com.jiuji.oa.afterservice.bigpro.po.ShouHouCantFixReason">
        select distinct shouhou_id,reason from (
            select shouhou_id,reason,ROW_NUMBER() over(PARTITION by shouhou_id,batch_number order by batch_number desc) rn
            from shouhou_cant_fix_reason s with(nolock)
            <where>
                <if test="shouHouIdList != null and shouHouIdList.size() > 0">
                    and shouhou_id in
                    <foreach collection="shouHouIdList" item="item" open="(" close=")" separator=",">
                        #{item}
                    </foreach>
                </if>
            </where>
        ) t where rn = 1
    </select>
</mapper>