<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.OperatorBusinessRefundMapper">
    <select id="selectOperatorBusiness" resultType="com.jiuji.oa.afterservice.refund.vo.res.BusinessRefundVo">
        SELECT c.ppriceid, c.business_cash_back ,ob.offsetMoney-ob.cash_back_amount  as businessCashBackMoney
        from basket b with (nolock)
         inner join sub s with (nolock) on s.sub_id = b.sub_id
            inner join OperatorBasket ob with (nolock) on b.basket_id = ob.basketId
            inner join OperatorBusinessConfig c  with (nolock) on c.id = ob.configId
            inner join shouying y with (nolock) on y.id = ob.shouying_id
            inner join shouyin_other o with (nolock) on o.shouyinid = y.id
        where b.sub_id = #{subId}
          and s.sub_check = 1
          and ob.status = 1
          and c.business_cash_back = 1
          and ob.offsetMoney > 0
          and s.yifuM > s.yingfuM
          and ob.offsetMoney-ob.cash_back_amount > 0
          and ob.isChecked = 1
          and y.hejim > isnull(o.refund_price,0)
          and EXISTS(SELECT 1
            FROM sysConfig sc with (nolock)
            inner join payment_config pc with (nolock) on pc.sys_config_id = sc.id
            where sc.code in (36, 37)
          and sc.value = cast(o.type_ as varchar(20))
          and pc.refund_type in (2, 3))
    </select>

    <select id="listOperatorShouyingId"  resultType="java.lang.Integer">
        SELECT ob.shouying_id FROM dbo.basket b WITH(NOLOCK)
                  INNER JOIN OperatorBasket ob WITH(NOLOCK) on b.basket_id = ob.basketId
        WHERE b.sub_id = #{subId} and ob.shouying_id is not null
    </select>
</mapper>
