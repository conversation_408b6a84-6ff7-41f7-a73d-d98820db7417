<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.way.BankPayTransferAccountsMapper">
    <insert id="batchInsert">
        insert into shouhou_tuihuan_detail
        (fk_tuihuan_id,refund_price,tui_way,tui_group,kemu_tui,bank_fuming,bank_number,bank_name,create_time,create_user,update_time,is_del,refund_business_type)
        values
        <foreach collection="tuiWayDetails" item="tuiWayDetail" separator=",">
            (#{tuihuanForm.tuihuanId},#{tuiWayDetail.refundPrice},#{tuiWayDetail.returnWayName},#{tuiWayDetail.groupCode},#{tuiWayDetail.kemuTui},
            #{tuiWayDetail.bankFuming},#{tuiWayDetail.bankNumber},#{tuiWayDetail.bankName},getdate(),#{tuihuanForm.currUser.userName},getdate(),0,#{tuiWayDetail.refundBusinessType})
        </foreach>
    </insert>
</mapper>