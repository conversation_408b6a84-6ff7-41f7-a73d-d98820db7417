<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.way.WechatAlipaySecondsRefundMapper">
    <insert id="batchInsert">
        insert into shouhou_tuihuan_detail
        (refund_business_type,fk_tuihuan_id,refund_price,tui_way,tui_group,is_validt,create_time,create_user,update_time,is_del,pay_open_type,bank_fuming)
        values
        <foreach collection="tuiWayDetails" item="tuiWayDetail" separator=",">
            (#{tuiWayDetail.refundBusinessType},#{tuihuanForm.tuihuanId},#{tuiWayDetail.refundPrice},#{tuiWayDetail.returnWayName},#{tuiWayDetail.groupCode},0,
            getdate(),#{tuihuanForm.currUser.userName},getdate(),0,#{tuiWayDetail.payOpenType},#{tuiWayDetail.bankFuming})
        </foreach>
    </insert>
    <select id="getWeixinPayDayTotal" resultType="java.math.BigDecimal">
        select sum(amount) from Pay_WXEnterprise with(nolock) where payment_no is not null and datediff(day,tradeDate,getdate())=0 and xtenant=#{xtenant}
    </select>
    <select id="getLastDaysPayCount" resultType="com.jiuji.oa.afterservice.refund.bo.PayCountBo">
        <choose>
            <when test="type == 1">
                select COUNT(1) payCount,SUM(amount) payTotal from Pay_WXEnterprise with(nolock) where payment_no is not null and openid=#{payOpenId} and tradeDate &gt;= DATEADD(day,#{days},GETDATE())
            </when>
            <when test="type == 2">
                select COUNT(1) payCount,SUM(amount) payTotal from alipayToAccountLog with(nolock) where order_id is not null and payee_account = #{payOpenId} and pay_date &gt;=DATEADD(day,#{days},GETDATE())
            </when>
        </choose>
    </select>
    <select id="getAliPayDayTotal" resultType="java.math.BigDecimal">
        select sum(amount) from alipayToAccountLog with(nolock) where order_id is not null and datediff(day,pay_date,getdate())=0
    </select>
</mapper>