<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.way.CardPayOriginWayMapper">
    <insert id="batchInsert">
        insert into shouhou_tuihuan_detail
        (refund_business_type,fk_tuihuan_id,refund_price,pay_kinds,record_id,tui_way,tui_group,kemu_tui,create_time,create_user,update_time,is_del,third_refund_type)
        values
        <foreach collection="tuiWayDetails" item="tuiWayDetail" separator=",">
            (#{tuiWayDetail.refundBusinessType},#{tuihuanForm.tuihuanId},#{tuiWayDetail.refundPrice},2,#{tuiWayDetail.posId},#{tuiWayDetail.returnWayName},#{tuiWayDetail.groupCode},null,
            getdate(),#{tuihuanForm.currUser.userName},getdate(),isnull(#{tuiWayDetail.isDel},0),
             <!--isDel true 其他方式退款的记录 2-->
             <choose>
                 <when test="tuiWayDetail.isDel">2</when>
                 <otherwise>null</otherwise>
             </choose>
            )
        </foreach>
    </insert>
    <sql id="andTuihuanMatchTradeSubIdSql">
        <choose>
            <!-- 5 退维修费 || 10 小件退维修费 11 退订金(维修费)-->
            <when test="tuihuanKind == 5 || tuihuanKind == 10 || tuihuanKind == 11">
                and st.shouhou_id=#{subId}
            </when>
            <!-- 9 小件换货 -->
            <when test="tuihuanKind == 9">
                and st.smallproid = #{subId}
            </when>
            <!-- 1 换机头 2 换主板 3 退款 4 换其它型号 6 退订金 7 退配件 8 退订金(良品) -->
            <otherwise>
                and (st.sub_id=#{subId} and st.tuihuan_kind &lt;&gt; 4 or st.tuihuan_kind = 4
                and exists(select 1 from shouhou s with(nolock) where s.id = st.shouhou_id and s.sub_id = #{subId}))
            </otherwise>

        </choose>
    </sql>
    <select id="sumRefundedPriceGroupPosId" resultType="cn.hutool.core.lang.Dict">
        select std.record_id posId,sum(std.refund_price) refundedPrice from dbo.shouhou_tuihuan_detail std with(nolock)
        where std.record_id in
        <foreach collection="posIds" open="(" close=")" item="posId" separator=",">
            #{posId}
        </foreach>
        and std.pay_kinds = 2 and (isnull(std.is_del,0) = 0 or <include refid="com.jiuji.oa.afterservice.refund.dao.RefundCommonMapper.stdOtherRefundSql"></include>)
        and exists(select 1 from dbo.shouhou_tuihuan st with(nolock) where isnull(st.isdel,0)=0
                and st.id = std.fk_tuihuan_id
                <foreach collection="subIds" open="and (" close=")" item="subId" separator="or">
                    <trim prefixOverrides="and">
                        <include refid="andTuihuanMatchTradeSubIdSql"></include>
                    </trim>
                </foreach>
        )
        group by std.record_id
    </select>
    <select id="listCardPayRecordInfo" resultType="com.jiuji.oa.afterservice.refund.vo.res.way.CardOriginRefundVo">
        select p.posPay returnWayName,sy.sub_id subId,p.posTerminal payTerminalNo,isnull(sum(sy.sub_pay07),0) refundPrice,
        <!-- refundedPrice 已退金额在程序中处理-->
        isnull(sum(sy.sub_pay07),0) actualPayPrice,0 refundedPrice,p.posID posId,min(sy.dtime) dtime
        from dbo.cardAccounting c with(nolock)
                      inner join dbo.shouying sy with(nolock) on sy.id = c.otherid
                      inner join dbo.posInfo p with(nolock) on p.posID=c.posid
                      inner JOIN dbo.areainfo a with(nolock) ON a.id=p.areaid
        where sy.sub_pay07>0 and sy.sub_id in
            <foreach collection="subIds" open="(" close=")" item="subId" separator=",">
                #{subId}
            </foreach>
            and sy.shouying_type in
            <foreach collection="shouyingTypes" open="(" close=")" item="shouyingType" separator=",">
                #{shouyingType}
            </foreach>
            <choose>
                <when test="posIds != null and !posIds.isEmpty()">
                    and p.posID in
                    <foreach collection="posIds" open="(" close=")" item="posId" separator=",">
                        #{posId}
                    </foreach>
                </when>
                <when test="originWays != null and !originWays.isEmpty()">
                    and p.posPay in
                    <foreach collection="originWays" open="(" close=")" item="originWay" separator=",">
                        #{originWay}
                    </foreach>
                </when>
            </choose>
            <if test="isOriginalRefund">
                and exists(
                    select 1 from dbo.posPayConfig ppc with(nolock) where ppc.name=p.posPay
                    <choose>
                    <when test="xtenant != null and xtenant >= 1000">
                        <!--输出才有授权隔离-->
                        AND ppc.authorizeid=a.authorizeid and isnull(ppc.isOriginalPathRefund,0)=1
                    </when>
                    <otherwise>
                        and not(isnull(p.isOnlinePay,0)=1 and isnull(p.isOriginalPathRefund,0)=1)
                    </otherwise>
                </choose>

                )
            </if>
        group by p.posID,p.posTerminal,p.posPay,sy.sub_id
    </select>
</mapper>
