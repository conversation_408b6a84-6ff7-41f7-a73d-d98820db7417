<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.way.OtherRefundMapper">
    <insert id="batchInsert">
        insert into shouhou_tuihuan_detail
        (refund_business_type,fk_tuihuan_id,refund_price,tui_way,tui_group,kemu_tui,create_time,create_user,update_time,is_del, third_refund_type)
        values
        <foreach collection="tuiWayDetails" item="tuiWayDetail" separator=",">
            (#{tuiWayDetail.refundBusinessType},#{tuihuanForm.tuihuanId},#{tuiWayDetail.refundPrice},#{tuiWayDetail.returnWayName},#{tuiWayDetail.groupCode},null,
             getdate(),#{tuihuanForm.currUser.userName},getdate(),0, #{tuiWayDetail.thirdRefundType})
        </foreach>
    </insert>

    <select id="countZiTiDianUser" resultType="java.lang.Integer">
        seLect count(1) from BBSXP_Users u with(nolock)
            inner join zitidian z with(nolock) on u.id=z.userid
            inner join sub s with(nolock) on s.zitidianid=z.id
        where z.id>0 and s.delivery=3  and s.sub_id=#{subId}
    </select>
    <select id="listPay03" resultType="java.math.BigDecimal">
        select s.sub_pay03 from dbo.shouying s where s.sub_id=#{subId}
    </select>
    <select id="listAllOther" resultType="com.jiuji.oa.afterservice.refund.vo.res.way.OtherRefundVo">
        select y.sub_id subId,y.sub_pay01 as actualPayPrice,0.0 refundedPrice, y.sub_pay01 refundPrice,y.id shouyingId
        ,'现金' returnWayName,y.dtime
        from dbo.shouying y with(nolock)
        where sub_pay01 > 0
        and y.sub_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        and y.shouying_type in
        <foreach collection="shouyingTypes" open="(" separator="," close=")" item="shouyingType">
            #{shouyingType}
        </foreach>
        union all
        select y.sub_id subId,y.sub_pay08 as actualPayPrice,0.0 refundedPrice, y.sub_pay08 refundPrice,y.id shouyingId
        ,'余额' returnWayName,y.dtime
        from dbo.shouying y with(nolock)
        where sub_pay08 > 0
        and y.sub_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        and y.shouying_type in
        <foreach collection="shouyingTypes" open="(" separator="," close=")" item="shouyingType">
            #{shouyingType}
        </foreach>
        union all
        select y.sub_id subId,y.sub_pay06 as actualPayPrice,0.0 refundedPrice, y.sub_pay06 refundPrice,y.id shouyingId
        ,y.inuser returnWayName,y.dtime
        from dbo.shouying y with(nolock)
        where sub_pay06 > 0
        and y.sub_id in
        <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
            #{orderId}
        </foreach>
        and y.shouying_type in
        <foreach collection="shouyingTypes" open="(" separator="," close=")" item="shouyingType">
            #{shouyingType}
        </foreach>
    </select>
</mapper>
