<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.ShouhouTuihuanGiftRelationMapper">

    <select id="selectInfoByTuihuanId" resultType="com.jiuji.oa.afterservice.refund.vo.res.TuiGiftBasketListVo">
        select r.basket_id, r.discount_amount as discountAmount, r.is_back
        FROM shouhou_tuihuan_gift_relation r with (nolock)
         inner join shouhou_tuihuan st with (nolock) on r.fk_tuihuan_id = st.id
            inner join shouhou s with (nolock) on s.id = st.shouhou_id
        where
            r.fk_tuihuan_id = #{tuihuanId}
          and s.ishuishou = 1
    </select>
    <select id="selectInfoByLpBasketId" resultType="com.jiuji.oa.afterservice.refund.vo.res.TuiGiftBasketListVo">

        SELECT b.ppriceid, b.basket_id, b.basket_count, p.product_name, p.product_color, p.memberprice as discountAmount
        FROM dbo.recover_marketSubInfo b WITH (NOLOCK)
        LEFT JOIN dbo.productinfo p WITH (NOLOCK) ON b.ppriceid = p.ppriceid
        WHERE b.giftid in
        <foreach collection="lpBasketIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
        AND b.[type] = 1
        AND ISNULL(b.isdel, 0) = 0
        and p.cid &lt;&gt; 485
     </select>
</mapper>
