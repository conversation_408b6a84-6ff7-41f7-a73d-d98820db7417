<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.RefundValidMemberMapper">

    <select id="getRefundValidBySub" resultType="com.jiuji.oa.afterservice.refund.bo.RefundValidBo">
        select s.tradeDate1, isnull(z.tel1,s.sub_mobile) mobile,isnull(z.userid,s.userid) userid,1 businessType from sub s with(nolock)
              left join dbo.zitidian z with(nolock) on s.zitidianID=z.id where sub_id=#{subId}
    </select>
    <select id="getRefundValidByLp" resultType="com.jiuji.oa.afterservice.refund.bo.RefundValidBo">
        select tradeDate1,sub_mobile mobile,userid,2 businessType from recover_marketinfo with(nolock)  where sub_id=#{subId}
    </select>
    <select id="getRefundValidByShouhou" resultType="com.jiuji.oa.afterservice.refund.bo.RefundValidBo">
        select mobile,userid,4 businessType from dbo.shouhou with(nolock) where id=#{subId}
    </select>
    <select id="getRefundValidBySubUserMobile" resultType="com.jiuji.oa.afterservice.refund.bo.RefundValidBo">
        select s.tradeDate1, isnull(z.tel1,u.mobile) mobile,isnull(z.userid,s.userid) userid,1 businessType from sub s with(nolock)
                          left join dbo.zitidian z with(nolock) on s.zitidianID=z.id
                          left join dbo.BBSXP_Users u with(nolock) on u.ID=s.userid where s.sub_id=#{subId}
    </select>
    <select id="getRefundValidByProductMkc" resultType="com.jiuji.oa.afterservice.refund.bo.RefundValidBo">
        SELECT s.tradeDate1,isnull(z.tel1,sub_mobile) mobile,isnull(z.userid,s.userid) userid,1 businessType
        FROM dbo.product_mkc k with(nolock)
            LEFT JOIN dbo.basket b WITH(nolock) ON b.basket_id = k.basket_id
            LEFT JOIN sub s with(nolock) ON b.sub_id = s.sub_id
            left join dbo.zitidian z with(nolock) on s.zitidianID=z.id
        WHERE k.id = #{mkcId}  AND ISNULL(b.isdel,0) = 0 AND k.kc_check = 5  AND s.sub_check =3
    </select>
    <select id="count15DayNoReason" resultType="java.lang.Integer">
        select COUNT(1) from dbo.shouhou_tuihuan t with(nolock)
        inner join dbo.shouhou s with(nolock) on t.shouhou_id = s.id
        where s.userid = #{userId}
        and s.modidate > #{startDate} and s.ishuishou = 1 and t.check3 = 1
        and s.wuliyou = '15天无理由退货'
        and s.id &lt;&gt; #{excludeShouhouId}
    </select>
    <select id="getHuishouUserId" resultType="java.lang.Integer">
        select s.userid from dbo.shouhou s with(nolock) where s.id = #{shouhouId}
              and exists(select 1 from dbo.shouhou_tuihuan t with(nolock) where t.shouhou_id = s.id and isnull(t.isdel,0)=0) and s.ishuishou = 1
    </select>
    <select id="isContainOperatorData" resultType="java.lang.Boolean">
        select 1 from basket b with(nolock) where b.sub_id=#{orderId}
          and exists(select 1 from productinfo p with(nolock) where p.ppriceid = b.ppriceid
                                and p.cid in (select * from f_category_children('3')))
    </select>
</mapper>
