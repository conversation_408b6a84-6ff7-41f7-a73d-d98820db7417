<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.RefundCommonMapper">
    <select id="isLockSub" resultType="java.lang.Integer">
        select islock from dbo.sub with(nolock) where sub_id = #{subId}
    </select>
    <select id="countMkcBeihuo" resultType="java.lang.Integer">
        select top 1 count(1) from dbo.product_mkc with(nolock) where kc_check &lt;&gt; 4
                               and basket_id in (select basket_id from dbo.basket where sub_id=#{subId})
    </select>
    <select id="countBeihuoLock" resultType="java.lang.Integer">
        select top 1 count(1) from dbo.basket_other a with(nolock) where
                     exists(
                         select 1 from dbo.basket b with(nolock) where isnull(b.isdel,0)=0 and a.basket_id=b.basket_id
                              <choose>
                                  <when test="(userName == '陈金龙' || userName == '黄华2') and xtenant &lt; 1000">and b.type &lt;&gt; 22</when>
                                  <otherwise></otherwise>
                              </choose>
                                    and b.sub_id=#{subId}
                     )
    </select>
    <select id="countOutputKc" resultType="java.lang.Integer">
        select top 1 count(1) from dbo.basket b with(nolock) where isnull(b.isdel,0)=0 and b.sub_id=#{subId} and isnull(ischu,0)=1 and ismobile=0
    </select>
    <select id="getShouhouCostpriceAreaId" resultType="com.jiuji.oa.afterservice.refund.bo.CostPriceAndAreaIdBo">
        select costPrice,areaId from shouhou with(nolock) where id = #{subId}
    </select>
    <select id="getRecoverCostpriceAreaId"
            resultType="com.jiuji.oa.afterservice.refund.bo.CostPriceAndAreaIdBo">
        select areaid FROM dbo.recover_marketInfo with(nolock) WHERE sub_check = #{subCheck} AND sub_id= #{subId}
    </select>
    <sql id="selectTuihuanDetail">
        SELECT id, fk_tuihuan_id, pay_kinds, record_id, refund_price, tui_way, tui_group, is_validt, pay_open_id, pay_open_type,
               bank_name, bank_fuming, bank_number, kemu_tui, net_exception_flag, account_exception, create_time, create_user,
               third_refund_type,update_time, is_del,validt_time, refund_business_type
        FROM dbo.shouhou_tuihuan_detail std with(nolock)
    </sql>
    <sql id="stdOtherRefundSql">
        (std.is_del = 1 and std.third_refund_type in(2, 8))
    </sql>
    <select id="listTuihuanDetail" resultType="com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo">
        <include refid="selectTuihuanDetail"></include>
        where (isnull(std.is_del,0)=0
                <if test="isContainOtherRefund">
                    or <include refid="stdOtherRefundSql"></include>
                </if>
              ) and std.fk_tuihuan_id = #{tuihuanId}
        <if test="groupCode != null">
            and tui_group = #{groupCode}
        </if>
    </select>
    <select id="isLuokeSub" resultType="java.lang.Boolean">
        select case when exists(select 1 from dbo.basket b with(nolock) where b.sub_id=#{subId} and b.type=98 and isnull(b.isdel,0)=0)
               then 1 else 0 end
    </select>
    <select id="isLockRecoverSub" resultType="java.lang.Integer">
        select islock from dbo.recover_marketInfo with(nolock) where sub_id = #{subId}
    </select>
</mapper>
