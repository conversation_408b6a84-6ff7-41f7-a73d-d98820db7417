<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.refund.dao.way.ThirdOriginWayMapper">
    <insert id="batchInsert">
        insert into shouhou_tuihuan_detail
        (refund_business_type,fk_tuihuan_id,third_refund_type,refund_price,tui_way,tui_group,pay_kinds,record_id,create_time,create_user,update_time,is_del)
        values
        <foreach collection="myTuiWayDetails" item="tuiWayDetail" separator=",">
            (#{tuiWayDetail.refundBusinessType},#{tuihuanId},#{tuiWayDetail.refundType},#{tuiWayDetail.refundPrice},#{tuiWayDetail.returnWayName},#{tuiWayDetail.groupCode},
            3,#{tuiWayDetail.otherRecordId},getdate(),#{userName},getdate(),isnull(#{tuiWayDetail.isDel},0))
        </foreach>
    </insert>
    <update id="batchUpdateShouyingOther">
        update dbo.shouyin_other
        <set>
            <trim prefix="refund_price=case" suffix=" end,">
                <foreach collection="myTuiWayDetails" item="tuiWayDetail">
                    when  id = #{tuiWayDetail.otherRecordId} then
                            (case when isnull(refund_price,0)+#{tuiWayDetail.refundPrice} &lt; 0 then 0.0 else isnull(refund_price,0)+#{tuiWayDetail.refundPrice} end)
                </foreach>
            </trim>
        </set>
        <where>
            id in
            <foreach collection="myTuiWayDetails" open="(" separator="," close=")" item="tuiWayDetail">
                #{tuiWayDetail.otherRecordId}
            </foreach>
        </where>

    </update>

    <select id="listAllThird" resultType="com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo">
        select o.id otherRecordId,y.sub_id subId,isnull(y.sub_pay04, 0.0)+isnull(y.sub_pay05, 0.0) actualPayPrice,
                isnull(o.refund_price,0.0) refundedPrice, isnull(y.sub_pay04, 0.0)+isnull(y.sub_pay05, 0.0) - isnull(o.refund_price,0.0) refundPrice,
               o.num tradeNo,o.type_ otherType,y.id shouyingId,o.coupon_price couponPrice,y.dtime dtime,y.basket_id basketId,
                <!--默认退款方式名称-->
                y.inuser returnWayName,o.outordernum
        from dbo.shouying y with(nolock)
        inner join dbo.shouyin_other o with(nolock) on o.shouyinid=y.id and y.shouying_type in
        <foreach collection="shouyingTypes" open="(" separator="," close=")" item="shouyingType">
            #{shouyingType}
        </foreach>
        where
        <choose>
            <when test="isType">o.type_ in</when>
            <otherwise>o.id in</otherwise>
        </choose>
        <foreach collection="typeOrOtherIds" open="(" separator="," close=")" item="typeOrOtherId">
            #{typeOrOtherId}
        </foreach>
           and y.sub_id in
           <foreach collection="orderIds" item="orderId" open="(" close=")" separator=",">
               #{orderId}
           </foreach>
    </select>
    <select id="listOtherRefundDetail" resultType="com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo">
        <!--其他退款is_del = 1 -->
        <include refid="com.jiuji.oa.afterservice.refund.dao.RefundCommonMapper.selectTuihuanDetail"></include>
        where <include refid="com.jiuji.oa.afterservice.refund.dao.RefundCommonMapper.stdOtherRefundSql"></include> and std.fk_tuihuan_id = #{tuihuanId}
        <if test="groupCode != null">
            and std.tui_group = #{groupCode}
        </if>
    </select>
    <select id="listNoCheck3RefundedPrice"
            resultType="com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo">
        select std.record_id otherRecordId,sum(std.refund_price) refundedPrice
        from dbo.shouhou_tuihuan_detail std with(nolock)
        where std.record_id in
        <foreach collection="otherIds" open="(" close=")" item="otherId" separator=",">
            #{otherId}
        </foreach>
        and std.pay_kinds = 3 and (isnull(std.is_del,0) = 0 or <include refid="com.jiuji.oa.afterservice.refund.dao.RefundCommonMapper.stdOtherRefundSql"></include>)
        and exists(select 1 from dbo.shouhou_tuihuan st with(nolock) where isnull(st.isdel,0)=0 and st.id = std.fk_tuihuan_id and st.check3 is null)
        group by std.record_id
    </select>
    <select id="listCompleteTdOtherId" resultType="java.lang.Integer">
        select std.record_id
        from dbo.shouhou_tuihuan_detail std with(nolock)
        where std.record_id in
        <foreach collection="otherIds" open="(" close=")" item="otherId" separator=",">
            #{otherId}
        </foreach>
        and std.pay_kinds = 3 and isnull(std.is_del,0) = 0 and std.create_time &gt; #{tradeDate}
        and exists(select 1 from dbo.shouhou_tuihuan st with(nolock) where isnull(st.isdel,0)=0 and st.id = std.fk_tuihuan_id
                                              and (st.sub_id=#{orderId}
        <if test="basketIds != null and basketIds.size() > 0">
            or std.record_id in ( select o.id otherRecordId from dbo.shouying y with(nolock)
            inner join dbo.shouyin_other o with(nolock) on o.shouyinid=y.id
            where y.basket_id in
            <foreach collection="basketIds" open="(" close=")" item="otherId" separator=",">
                #{otherId}
            </foreach>
            )
        </if>
       ) and st.check3=1 and st.tuihuan_kind in(6, 8, 11))
    </select>
    <select id="selectGuoBuOtherType" resultType="java.lang.Integer">
        select s.value from [dbo].[payment_guobu_config] p with(nolock) left join dbo.sysConfig s with(nolock) on p.payment_link_id=s.id
        where isnull(p.is_del,0)=0 and p.payment_type=1
    </select>
    <select id="isGuoBuOrder" resultType="java.lang.Boolean">
        select iif(exists(select * from dbo.subFlagRecord sfr with(nolock ) where flagType = 7 and sub_id = #{orderId}), 1, 0)
    </select>
</mapper>
