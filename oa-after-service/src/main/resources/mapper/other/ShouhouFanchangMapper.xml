<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.ShouhouFanchangMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.ShouhouFanchang">
        <id column="id" property="id" />
        <result column="smallproid" property="smallproid" />
        <result column="ppid" property="ppid" />
        <result column="ppid1" property="ppid1" />
        <result column="qudao" property="qudao" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="rstats" property="rstats" />
        <result column="fcdtime" property="fcdtime" />
        <result column="fcinuser" property="fcinuser" />
        <result column="zxdtime" property="zxdtime" />
        <result column="zxinuser" property="zxinuser" />
        <result column="bftime" property="bftime" />
        <result column="bfinuser" property="bfinuser" />
        <result column="basket_id" property="basketId" />
        <result column="pzid" property="pzid" />
    </resultMap>
    <update id="updateStatusById">
        UPDATE shouhou_fanchang
        SET rstats = 2,
        zxinuser = #{shouhouFanchang.zxinuser},
        zxdtime = #{shouhouFanchang.zxdtime}
        WHERE
            id = #{shouhouFanchang.id}
            AND rstats =0
    </update>

    <select id="getSmallProVirtualGoods" resultType="com.jiuji.oa.afterservice.other.bo.SmallOldProVirtualInfo">
        select f.id as fcId, f.ppid,f.smallproid as smallProId,p.cid from shouhou_fanchang f with(nolock)
        left join smallpro s with(nolock) on s.id = f.smallproid
        left join productinfo p with(nolock) on f.ppid = p.ppriceid
        where isnull(s.Stats,0) = 1 and s.id = #{smallProId}
        and isnull(f.rstats,0) != 3
        <!--虚拟商品判断-->
        and(
             p.cid in (50,295,508)
             <foreach collection="virtualPpids" item="item" open="or f.ppid in(" close=")" separator=",">
                 #{item}
             </foreach>
        )
        <!--xxk增加虚拟商品 不能存在成本-->
        and not exists(select 1 from dbo.product_kc pk with(nolock) where pk.ppriceid = f.ppid and pk.inprice>0)
    </select>
    <select id="getSmallProGoods" resultType="com.jiuji.oa.afterservice.other.bo.SmallOldProVirtualInfo">
        select f.id as fcId, f.ppid,f.smallproid as smallProId,p.cid,p.product_name from shouhou_fanchang f with(nolock)
        left join smallpro s with(nolock) on s.id = f.smallproid
        left join productinfo p with(nolock) on f.ppid = p.ppriceid
        where isnull(s.Stats,0) = 1   and s.id = #{smallProId}
        and isnull(f.rstats,0) = 0

    </select>
</mapper>
