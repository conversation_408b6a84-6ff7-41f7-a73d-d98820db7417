<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.NetPayRefundInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.NetPayRefundInfo">
        <id column="id" property="id" />
        <result column="netRecordId" property="netRecordId" />
        <result column="price" property="price" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="returnid" property="returnid" />
        <result column="out_refund_no" property="outRefundNo" />
        <result column="refund_fee" property="refundFee" />
        <result column="refundtime" property="refundtime" />
    </resultMap>

    <update id="updateRefundPriceByTuihuanId">
        update n set n.refundPrice=n.refundPrice-f.price
        from netPayRefundInfo f with(nolock)
        join dbo.netpay_record n with(nolock) on f.netRecordId = n.id
        where f.returnid =#{tuihuanId}
    </update>
</mapper>
