<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.BasketMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.Basket">
        <id column="basket_id" property="basketId"/>
        <result column="basket_count" property="basketCount"/>
        <result column="basket_date" property="basketDate"/>
        <result column="product_peizhi" property="productPeizhi"/>
        <result column="seller" property="seller"/>
        <result column="ismobile" property="ismobile"/>
        <result column="price" property="price"/>
        <result column="sub_id" property="subId"/>
        <result column="price1" property="price1"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="inprice" property="inprice"/>
        <result column="giftid" property="giftid"/>
        <result column="type" property="type"/>
        <result column="isdel" property="isdel"/>
        <result column="ischu" property="ischu"/>
        <result column="price2" property="price2"/>
        <result column="iskc" property="iskc"/>
    </resultMap>
    <select id="getSubSimpleInfoList" resultType="com.jiuji.oa.afterservice.bigpro.bo.SubSimpleBo">
        SELECT
        s.userid as userId,
        s.areaid as areaId,
        s.sub_check as subCheck,
        s.tradedate1,
        basket_id as basketId,
        p.ppriceid as ppid,
        p.product_name as productName
        FROM basket b with(nolock)
        INNER JOIN sub s with(nolock) ON b.sub_id= s.sub_id
        LEFT JOIN productinfo p with(nolock) ON b.ppriceid= p.ppriceid
        WHERE 1=1
        <if test="subId != null">
            and s.sub_id=#{subId}
        </if>
        AND ismobile = 0
        AND sub_check = 3
        AND b.ischu= 1
        AND isnull( b.isdel, 0 ) =0
    </select>

    <select id="getShouHouBasketInfoById" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouHouBasketBo">
        SELECT
        s.sub_check AS subCheck,
        isnull( kc_check, 0 ) AS kcCheck,
        s.areaid AS areaId
        FROM basket b with(nolock)
        LEFT JOIN product_mkc k with(nolock) ON b.basket_id= k.basket_id
        LEFT JOIN sub s with(nolock) ON s.sub_id= b.sub_id
        WHERE
        isnull( b.isdel, 0 ) = 0
        <if test="basketId != null">
            AND b.basket_id = #{basketId}
        </if>
    </select>

    <select id="getRecoverMarketInfoById" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouHouSubBo">
       SELECT
            isnull( sa.issms_send, 0 ) AS issmsSend1,
            s.zitidianID AS zitidianId,
            sa.issms_send AS issmsSend,
            s.delivery,
            s.areaid AS areaId
        FROM recover_marketInfo s with(nolock)
            LEFT JOIN RecoverSubAddress sa  with(nolock) ON s.sub_id= sa.sub_id
        WHERE
            s.sub_id=#{subId}
    </select>

    <select id="getShouhouSubInfoById" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouHouSubBo">
        SELECT
        isnull( sa.issms_send, 0 ) AS issmsSend1,
        s.zitidianID AS zitidianId,
        sa.issms_send AS issmsSend,
        s.delivery,
        s.areaid AS areaId
        FROM sub s with(nolock)
        LEFT JOIN subaddress sa  with(nolock) ON s.sub_id= sa.sub_id
        WHERE 1=1
        <if test="subId != null ">
            AND s.sub_id = #{subId}
        </if>
    </select>

    <select id="getMobiles" resultType="java.lang.String">
        SELECT
            u.mobile
        FROM BBSXP_Users u with(nolock)
        JOIN zitidian z  with(nolock) ON u.id= z.userid
        WHERE
            z.id> 0
            AND z.id = #{zitidianid}
    </select>

    <update id="updateMsgSendState">
        update subaddress set issms_send=1 where sub_id = #{subId}
    </update>

    <update id="updateRecoverSubAddressMsgState">
        update RecoverSubAddress set issms_send=1 where sub_id= = #{subId}
    </update>

    <select id="huanMKC" resultType="com.jiuji.oa.afterservice.bigpro.bo.HuanMkcBo">

        <choose>
            <when test="ishuishou != null and ishuishou != 1">
                select b.price,k.inprice as inBeihuoPrice,price2 as buyPrice,b.sub_id as subId,b.basket_id as
                basketId,s.userid as userId from basket b with(nolock)
                left join product_mkc k  with(nolock) on b.basket_id=k.basket_id
                left join sub s  with(nolock) on s.sub_id=b.sub_id
                where isnull(b.isdel,0)=0 and k.kc_check=5
                <if test="userId != null">
                    and s.userid=#{userId}
                </if>
                <choose>
                    <when test="mkcId != null and mkcId != 0">
                        and k.id=#{mkcId}
                    </when>
                    <otherwise>
                        and k.imei=#{imei}
                    </otherwise>
                </choose>
                order by k.id desc
            </when>
            <otherwise>
                SELECT b.price,k.inprice as inBeihuoPrice,b.price as buyPrice,b.sub_id as subId,b.basket_id as
                basketId,s.userid as userId FROM recover_marketSubInfo b with(nolock)
                INNER JOIN recover_marketInfo s  with(nolock) ON b.sub_id=s.sub_id
                INNER JOIN recover_mkc k  with(nolock) ON b.basket_id=k.to_basket_id
                WHERE s.sub_check=3 AND isnull(b.isdel,0)=0 AND mkc_check=5
                <choose>
                    <when test="mkcId != null and mkcId != 0">
                        and k.id=#{mkcId}
                    </when>
                    <otherwise>
                        and k.imei=#{imei}
                    </otherwise>
                </choose>
                order by k.id desc
            </otherwise>
        </choose>

    </select>

    <select id="getSubGiftPrice" resultType="java.math.BigDecimal">
        select sum(price2) allPrice from dbo.basket b with(nolock)
        where [type]=1 and isnull(isdel,0)=0
        and giftid=#{basketId}
        and exists(select 1 from sub s with(nolock) where s.sub_id=b.sub_id and s.sub_check=3)
        <if test="isContainsFilm != null and isContainsFilm == true">
            and not exists(select 1 from dbo.productinfo p with(nolock) where p.ppriceid=b.ppriceid and p.cid=297 )
        </if>
    </select>

    <select id="getSubGiftPriceV2" resultType="java.math.BigDecimal">

        <choose>
            <when test="isContainsFilm != null and isContainsFilm == true">
                select sum(isnull(b.price_shouhou, price2)) allPrice
                from dbo.basket b with(nolock)
                where [type] = 1 and isnull(isdel, 0) = 0
                    and giftid in
                    <foreach collection="basketId" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    and exists(select 1 from sub s with(nolock) where s.sub_id = b.sub_id and s.sub_check = 3)
                    and not exists(select 1 from dbo.productinfo p with(nolock) where p.ppriceid = b.ppriceid and p.cid = 297)
            </when>
            <otherwise>
                select sum(b.giftPrice) allPrice from dbo.basket b with(nolock)
                WHERE ISNULL(isdel,0) = 0
                    and b.basket_id in
                    <foreach collection="basketId" item="id" open="(" close=")" separator=",">
                        #{id}
                    </foreach>
                    and exists(select 1 from sub s with(nolock) where s.sub_id = b.sub_id and s.sub_check = 3)
            </otherwise>
        </choose>
    </select>

    <select id="huanMKC2" resultType="com.jiuji.oa.afterservice.bigpro.bo.HuanMkcSecondBo">
        select p.product_name as productName,p.product_color as productColor,imei,p.productid as productId,p.ppriceid from product_mkc k with(nolock)
        left join productinfo p with(nolock)  on k.ppriceid=p.ppriceid
        where k.areaid=#{areaId} and k.kc_check in(6,13) and id=#{baseketId}
    </select>

    <select id="getSubZenpingByGiftId" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouTuiDtInfo">
        select b.basket_id as basketId,b.basket_count as basketCount,b.ppriceid as ppid,p1.product_name as productName,p1.ppriceid1 as ppid1,b.sub_id as subId,s.tradeDate from basket b with(nolock)
        left join sub s  with(nolock) on s.sub_id=b.sub_id
        left join productinfo p1  with(nolock) on b.ppriceid=p1.ppriceid
        where s.sub_check=3 and b.giftid=#{giftId}
        and (b.[type]=1 OR b.ppriceid IN(65485,65486,65487,65488,65489,65490,65491,65491,65493,65494,65495,65496,65497,65498,65499,65500,65501,65502,65503,65504,65505,65492))
        and isnull(b.isdel,0)=0 and b.price=0 and not exists(select 1 from dbo.productinfo p with(nolock) where p.ppriceid=b.ppriceid and p.cid=297 )
    </select>
    <select id="getScarcityByBasketids" resultType="java.lang.Integer">
        select 1 from dbo.basket b with(nolock) left join dbo.productinfo p with(nolock) on p.ppriceid=b.ppriceid where
        isnull(p.Scarcity,0)=1
        <if test="basketIds!=null and basketIds.size>0">
            and b.basket_id in
            <foreach collection="basketIds" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>
    <select id="getBasketInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.BasketInfoBO">
        select b.ppriceid,k.id as mkc_id as mkcId,p.product_name as name,p.product_color as productColor,s.tradedate,k.imei,
        s.userid,p.productid,s.sub_check as subCheck,s.areaid,s.sub_mobile as mobile,s.sub_tel as tel,u.userclass,
        u.username,u.blacklist,p.brandid,b.sub_id as subId,b.basket_id as basketId
        from basket b  with(nolock) left join sub s  with(nolock) on b.sub_id=s.sub_id left join bbsxp_users u  with(nolock) on s.userid=u.id
        left join productinfo p  with(nolock) on p.ppriceid=b.ppriceid left join product_mkc k  with(nolock) on k.basket_id=b.basket_id
        where isnull(b.isdel,0)=0 and b.basket_id=#{id}
    </select>
    <select id="getProductMKC" resultType="com.jiuji.oa.afterservice.bigpro.bo.ProductMkcInfoBO">
        <choose>
            <when test="ishuishou==1 or ishuishou==2">
                select k.id,k.areaid,k.imei,p.productid,k.ppriceid,brandid, p.product_name as name,
                p.product_color as productColor
                from recover_mkc k WITH(NOLOCK) left join productinfo p WITH(NOLOCK) on k.ppriceid=p.ppriceid
                where k.id=#{mkcId}
            </when>
            <otherwise>
                select k.id,k.areaid,k.imei,p.productid,k.ppriceid,brandid, p.product_name as name,
                p.product_color as productColor
                from product_mkc k WITH(NOLOCK) left join productinfo p WITH(NOLOCK) on k.ppriceid=p.ppriceid
                where k.id=#{mkcId}
            </otherwise>
        </choose>

    </select>

    <!-- listGiftBasketByBasketIds -->
    <select id="listGiftBasketByBasketIds" resultType="com.jiuji.oa.afterservice.other.bo.GiftBasketBo">
        select b.basket_id as basketId, b.basket_count as basketCount, b.ppriceid,p1.ismobile1 isMobile,
               p1.product_name as productName, p1.ppriceid1, b.sub_id as subId, s.tradeDate
        from basket b with(nolock)
            left join sub s with(nolock) on s.sub_id = b.sub_id
            left join productinfo p1 with(nolock) on b.ppriceid = p1.ppriceid
        where s.sub_check = 3 and b.[type] = 1 and b.giftid = #{subBasketId}
        <if test="giftBasketIds != null and giftBasketIds.size > 0">
            and b.basket_id in
            <foreach collection="giftBasketIds" item="basketId" open="(" close=")" separator=",">
                #{basketId}
            </foreach>
        </if>
    </select>
    <select id="getMobileListPurchaseBuy1" resultType="java.lang.String">
        SELECT * INTO #cids1 from dbo.f_category_children('43,385')

        SELECT DISTINCT bu.mobile FROM basket b with(nolock)
        inner join sub s with(nolock) on b.sub_id = s.sub_id
        inner join productinfo p with(nolock) on p.ppriceid = b.ppriceid
        inner join BBSXP_Users bu with(nolock) on bu.ID = s.userid
        where isnull(b.isdel,0) = 0
        and s.tradeDate1 <![CDATA[ >= ]]> #{startTime}
        and s.tradeDate1 <![CDATA[ < ]]> #{endTime}
        and p.cid in (SELECT ID FROM #cids1)
        and p.supportService <![CDATA[ & ]]> 16 = 16
        and s.sub_check = 3
        and EXISTS(SELECT 1 FROM areainfo a with(nolock) where a.id = s.areaid and a.xtenant = 0 and a.authorizeid=1)
        and b.giftid is NULL
        and isnull(b.[type],0) <![CDATA[ <> ]]> 86 and b.price <![CDATA[ > ]]> 0
        and NOT EXISTS(
        SELECT 1 FROM tiemoCard tc with(nolock) where tc.isdel = 0 and tc.etime <![CDATA[ > ]]> GETDATE() and tc.useCount <![CDATA[ < ]]> tc.allCount and tc.basket_idBind = b.basket_id
        )
    </select>
    <select id="getMobileListPurchaseBuy2" resultType="java.lang.String">
        SELECT * INTO #cids2 from dbo.f_category_children('43,385')

        SELECT DISTINCT t.mobile
        FROM
        (
        SELECT s.userid,bu.mobile,s.sub_id,DATEDIFF(DAY,s.tradeDate1,GETDATE()) days,ROW_NUMBER() over(PARTITION by s.userid ORDER BY s.tradeDate1 DESC) rn FROM sub s with(nolock)
        inner join BBSXP_Users bu with(nolock) on bu.ID = s.userid
        where EXISTS(
        SELECT 1 FROM basket b with(nolock)
        inner join productinfo p with(nolock) on b.ppriceid = p.ppriceid
        where isnull(b.isdel,0) = 0 and p.cid in (SELECT ID FROM #cids2) and b.sub_id = s.sub_id
        ) and s.sub_check = 3 and EXISTS(SELECT 1 FROM areainfo a with(nolock) where a.id = s.areaid and a.xtenant = 0 and a.authorizeid=1)
        ) t where EXISTS(
        SELECT 1 FROM basket b with(nolock)
        inner join productinfo p with(nolock) on b.ppriceid = p.ppriceid
        where isnull(b.isdel,0) = 0 and p.cid in (SELECT ID FROM #cids2) and p.supportService <![CDATA[ & ]]> 16 = 16
        and b.giftid is NULL
        and b.giftid is NULL
        and isnull(b.[type],0) not in (86,91)
        and b.price <![CDATA[ > ]]> 0
        and b.price <![CDATA[ > ]]> 0
        and NOT EXISTS(
        SELECT 1 FROM tiemoCard tc with(nolock) where tc.isdel = 0 and tc.etime <![CDATA[ > ]]> GETDATE() and tc.useCount <![CDATA[ < ]]> tc.allCount and tc.basket_idBind = b.basket_id
        ) and b.sub_id = t.sub_id
        ) and t.rn = 1 and t.days in (60,120,180)
    </select>

    <insert id="copyBasket" useGeneratedKeys="true" keyProperty="colValMap.id">
        <bind name="isValue" value="false"/>
        INSERT INTO basket (<include refid="com.jiuji.oa.afterservice.sys.dao.SqlServerColumnMapper.columnNameCOMMASql"></include>)
        <bind name="isValue" value="true"/>
            select <include refid="com.jiuji.oa.afterservice.sys.dao.SqlServerColumnMapper.columnNameCOMMASql"></include>
        from basket where basket_id = #{basketId}
    </insert>

    <insert id="copyBasketExtend"  keyProperty="colValMap.id">
        <bind name="isValue" value="false"/>
        INSERT INTO basket_extend (<include refid="com.jiuji.oa.afterservice.sys.dao.SqlServerColumnMapper.columnNameCOMMASql"></include>)
        <bind name="isValue" value="true"/>
        select <include refid="com.jiuji.oa.afterservice.sys.dao.SqlServerColumnMapper.columnNameCOMMASql"></include>
        from basket_extend where basket_id = #{basketId}
    </insert>

    <insert id="copyBasketBindRecord"  keyProperty="colValMap.id">
        <bind name="isValue" value="false"/>
        INSERT INTO basketBindRecord (<include refid="com.jiuji.oa.afterservice.sys.dao.SqlServerColumnMapper.columnNameCOMMASql"></include>)
        <bind name="isValue" value="true"/>
        select <include refid="com.jiuji.oa.afterservice.sys.dao.SqlServerColumnMapper.columnNameCOMMASql"></include>
        from basketBindRecord where id = #{id}
    </insert>

</mapper>
