<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.ShouyinOtherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.ShouyinOther">
        <id column="id" property="id"/>
        <result column="shouyinid" property="shouyinid"/>
        <result column="num" property="num"/>
        <result column="type_" property="type"/>
        <result column="islock" property="islock"/>
        <result column="lockuser" property="lockuser"/>
        <result column="locktime" property="locktime"/>
        <result column="htPrice" property="htPrice"/>
        <result column="comment" property="comment"/>
        <result column="dzprice" property="dzprice"/>
    </resultMap>

    <select id="getOtherShouYinBo" resultType="com.jiuji.oa.afterservice.other.bo.OtherShouYinBo">
        select s.sub_id as subId,s.sub_pay04 price,a.num,s.dtime from dbo.shouyin_other a with(nolock),dbo.shouying s with(nolock) where a.shouyinid=s.id and a.type_=#{type} and s.sub_id=#{subId}
    </select>

    <select id="getInstallmentAmount" resultType="java.math.BigDecimal">
      select sum(y.sub_pay04) as price from dbo.shouying y with(nolock),dbo.shouyin_other o with(nolock) where o.shouyinid=y.id and y.sub_id= #{subId} and o.type_ in (25,28)
    </select>

    <select id="getInstallmentAmountMobileSubsidy" resultType="java.math.BigDecimal">
      select sum(isnull(y.sub_pay05, 0.0) + isnull(y.sub_pay04, 0.0)) as price from dbo.shouying y with(nolock),dbo.shouyin_other o with(nolock) where o.shouyinid=y.id and y.sub_id= #{subId} and o.type_ in (29,32)
    </select>

    <select id="getSumOffSetMoney" resultType="java.math.BigDecimal">
        SELECT sum(sumoffsetMoney) FROM
        (SELECT sum(ob.offsetMoney) sumoffsetMoney FROM OperatorBasket ob with(nolock)
        inner join basket b with(nolock) on b.basket_id = ob.bindBasketId
        where ob.status = 1 and isnull(b.isdel,0) = 0 and ob.isChecked = 1 AND iSNULL(ob.cardId,0) = 0 and b.basket_id in
        <foreach collection="basketIds" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach>
        UNION ALL
        SELECT sum(ob.offsetMoney) sumoffsetMoney FROM OperatorBasket ob with(nolock)
        inner join basket b with(nolock) on b.basket_id = ob.bindBasketId
        inner JOIN sub s with(nolock) on s.sub_id = b.sub_id
        where ob.status = 2 and isnull(b.isdel,0) = 0 and ob.isChecked = 1 AND iSNULL(ob.cardId,0) = 0
          and s.tradeDate1 &lt; ob.returnTime and b.basket_id in
        <foreach collection="basketIds" item="basketId" separator="," open="(" close=")">
            #{basketId}
        </foreach> ) t
    </select>
</mapper>
