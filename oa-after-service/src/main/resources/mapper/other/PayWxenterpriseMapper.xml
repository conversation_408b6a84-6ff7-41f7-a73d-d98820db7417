<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.PayWxenterpriseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.PayWxenterprise">
        <id column="id" property="id"/>
        <result column="orderId" property="orderId"/>
        <result column="checkName" property="checkName"/>
        <result column="re_user_name" property="reUserName"/>
        <result column="req_ip" property="reqIp"/>
        <result column="remark" property="remark"/>
        <result column="openid" property="openid"/>
        <result column="tradeDate" property="tradeDate"/>
        <result column="tradeResult" property="tradeResult"/>
        <result column="amount" property="amount"/>
        <result column="subsystem" property="subsystem"/>
        <result column="payment_no" property="paymentNo"/>
        <result column="mchid" property="mchid"/>
        <result column="payType" property="payType"/>
        <result column="xtenant" property="xtenant"/>
    </resultMap>

    <select id="getDailyPayLimit" resultType="java.math.BigDecimal">
        SELECT SUM
        ( amount )
        FROM Pay_WXEnterprise WITH ( nolock )
        WHERE
        payment_no IS NOT NULL
        AND datediff( DAY, tradeDate, getdate( ) ) = 0
        AND xtenant =#{xtenant}
    </select>

    <select id="listWeixinEnterprise" resultMap="BaseResultMap">
        select id,remark,openid,amount,tradeDate,subsystem
        from dbo.Pay_WXEnterprise with(nolock) where
        tradeResult='1'
        <if test="openId != null ">
            and openid = #{openId}
        </if>
        <if test="openIds!=null ">
            and openid in
            <foreach collection="openIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        and datediff(month,tradeDate,getdate()) &lt;=6
        and (
        <if test="systems!=null and systems.size>0">
            subsystem in
            <foreach collection="systems" item="system" open="(" separator="," close=")">
                #{system}
            </foreach>
            or
        </if>
        remark like '退换机款项，售后单号%')
    </select>

</mapper>
