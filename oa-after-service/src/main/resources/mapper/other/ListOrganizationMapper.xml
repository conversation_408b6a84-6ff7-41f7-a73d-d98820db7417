<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.ListOrganizationMapper">


    <select id="selectSubList" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select s.sub_id as id,p.productid as productId
        from dbo.basket b with (nolock)
        left join dbo.productinfo p with(nolock ) on p.ppriceid=b.ppriceid
        left join dbo.sub s with (nolock) on s.sub_id = b.sub_id
        where isnull(b.isdel, 0) = 0
        and s.sub_check in (0, 1, 2, 5, 6)
        and p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectProcureList" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select cs.id,p.productid as productId
        from dbo.caigou_basket cb with (nolock)
         left join dbo.productinfo p with(nolock ) on p.ppriceid=cb.ppriceid
            left join dbo.caigou_sub cs with (nolock) on cs.id = cb.sub_id
        where cs.stats in (0, 1, 2)
        and p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectTransferList" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select ds.id,p.productid as productId
        from dbo.diaobo_basket db with (nolock)
         left join dbo.productinfo p with(nolock ) on p.ppriceid=db.ppriceid
            left join dbo.diaobo_sub ds with (nolock) on ds.id = db.sub_id
        where ds.stats in (1, 2, 5, 3)
        and p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectInventory" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select p.productid as productId ,sum(lcount) as id
        from dbo.product_kc pk with(nolock )
         left join dbo.productinfo p with(nolock ) on p.ppriceid=pk.ppriceid
        where p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
         group by p.productid having sum(lcount)>0
    </select>
    <select id="selectReportingLossesList" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select rs.id,p.productid as productId
        from dbo.return_basket rb with (nolock)
         left join dbo.productinfo p with (nolock) on p.ppriceid = rb.ppriceid
            left join dbo.return_sub rs with (nolock) on rs.id = rb.sub_id
        where states in (1,2) and p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
         and type_ = 2
    </select>
    <select id="selectReturnOfGoodsList" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select rs.id,p.productid as productId
        from dbo.return_basket rb with (nolock)
         left join dbo.productinfo p with (nolock) on p.ppriceid = rb.ppriceid
            left join dbo.return_sub rs with (nolock) on rs.id = rb.sub_id
        where states in (1,2) and p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
         and type_ = 1
    </select>
    <select id="selectMinorRepairsList" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select s.id, p.productid as productId
        FROM dbo.SmallproBill sb with (nolock)
         left join dbo.productinfo p
        with (nolock)
        on p.ppriceid = sb.ppriceid
            left join dbo.Smallpro s
        with (nolock)
        on sb.smallproID = s.id
        where p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
          and (s.Stats=0 OR (s.Stats=1 and exists (select 1 from dbo.shouhou_fanchang sf with (nolock) where isnull(sf.rstats, 0)=0
          and sf.smallproid=s.id)))
    </select>
    <select id="selectBigTransferList" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select mt.mkc_id as id,p.productid as productId
        from dbo.mkc_toarea mt with (nolock)
        left join dbo.product_mkc pm with (nolock) on mt.mkc_id = pm.id
        left join dbo.productinfo p with (nolock) on p.ppriceid = pm.ppriceid
        where stats in (0, 1, 2) and p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
    </select>
    <select id="selectBigInventory" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select count(pm.id) as id , p.productid as productId
        from dbo.product_mkc pm with (nolock)
         left join dbo.productinfo p with (nolock) on p.ppriceid = pm.ppriceid
        where pm.kc_check in (0,1,2,3,6,7,8,10) and  p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        group by p.productid
    </select>
    <select id="selectBigReturnOfGoodsList" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select rs.id,p.productid as productId
        from dbo.return_basket rb with (nolock)
        left join dbo.product_mkc mkc with(nolock ) on mkc.id=rb.ppriceid
        left join dbo.productinfo p with (nolock) on p.ppriceid = mkc.ppriceid
        left join dbo.return_sub rs with (nolock) on rs.id = rb.sub_id
        where states in (1,2) and p.productid in
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
         and type_ = 3
    </select>
    <select id="selectRepairsList" resultType="com.jiuji.oa.afterservice.other.po.OrganizationInfo">
        select s.id, p.productid as productId
        from dbo.shouhou s with (nolock)
   left join dbo.productinfo p with (nolock) on p.ppriceid = s.ppriceid

        where isnull(s.issoft, 1) = 0
        and isnull(s.xianshi,1) = 1
          and s.stats in (0, 1, 3)
          and isnull(s.isquji, 0) = 0
          and p.productid in  
        <foreach collection="productIdList" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>

    </select>
</mapper>
