<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.ReturnsDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.ReturnsDetail">
        <id column="RdID" property="RdID"/>
        <result column="SHTHID" property="shthid"/>
        <result column="BasketID" property="BasketID"/>
        <result column="BasketCount" property="BasketCount"/>
    </resultMap>

    <select id="getShouhouTuiHuanListBySubId" resultType="com.jiuji.oa.afterservice.other.bo.ShouHouTuiHuanBo">
        SELECT
        id,
        isdel as isDel,
        sub_id,
        tuikuanM returnPrice,
        dtime dTime,
        inuser inUser,
        comment,
        tuihuan_kind refundKind,
        tui_way refundWay,
        bankname bankName,
        bankfuming accountName,
        banknumber bankNo,
        shouhou_id,
        tuikuanM1 refundMoney1,
        sub_idM subIdMoney,
        zhejiaM discountPrice,
        check1,
        check2,
        check3,
        case when isnull(check2,0)= 1 then inuser else check1user end as check1User,
        check2user check2User,
        check3user check3User,
        case when isnull(check2,0)= 1 then dtime else check1dtime end as check1Time,
        check2dtime check2Time,
        check3dtime check3Time,
        areaid areaId,
        pzid voucherId,
        coinM jiuJiCoin,
        baitiaoM baiTiaoPrice,
        kuBaiTiaoM kuBaiTiaoPrice,
        payOpenId
        -- kemuTui,
        -- BankNameBranch bankBranchName
        FROM shouhou_tuihuan with(nolock)
        WHERE
        isnull(isdel,0) = 0 and sub_id = #{subId}
        <if test="refundKind != null and refundKind != 0">
            and tuihuan_kind = #{refundKind}
        </if>
        order by id desc
    </select>

    <select id="getKuBaiTiaoPriceBySubId" resultType="java.math.BigDecimal">
        select sum(money-isnull(refundPrice,0)) leftPrice from dbo.netpay_record with(nolock)
        where sub_number=#{subId}
        <if test="refundKind == 6">
            and type = 1
        </if>
        <if test="refundKind == 8">
            and type = 3
        </if>
        and payWay in
        <foreach collection="payWayList" item="payWay" separator="," open="(" close=")" index="index">
            #{payWay}
        </foreach>
    </select>

    <select id="getSubOnlinePayInfo" resultType="com.jiuji.oa.afterservice.other.bo.SubOnlinePayItemBo">
        <if test="businessType == 1">
            select top 1 s.sub_id,yifuM-isnull(os.offsetMoney,0) yiFuM,yingFuM,areaid areaId from sub s with(nolock)
            left join (select s.sub_id,sum(isnull(ob.offsetMoney,0))  offsetMoney
            from OperatorBasket ob with(nolock)
            left join basket b with(nolock) on b.basket_id=ob.bindBasketId
            left join sub s with(nolock) on s.sub_id=b.sub_id where s.sub_id=#{subId} and ob.status in (1,2) and ob.isChecked=1 group by s.sub_id) os
            on os.sub_id=s.sub_id
            where s.yifuM>0 and s.sub_check=1 and s.sub_id=#{subId}
        </if>
        <if test="businessType == 2">
            select top 1 sub_id,yifuM as yiFuM,yingFuM,areaid areaId from recover_marketInfo with(nolock) where sub_id = #{subId}
        </if>

    </select>

    <select id="getPayInfo" resultType="com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO">
        SELECT
        id,
        money - isnull( refundPrice, 0 ) price,
        trade_no AS tradeNo,
        sub_number AS subNumber,
        payWay
        FROM dbo.netpay_record with(nolock)
        WHERE 1=1
        <if test="businessType == 1">
            and type = 1
        </if>
        <if test="businessType == 2">
            and type = 3
        </if>
        <if test="businessType == 4">
            and type = 2
        </if>
        AND CAST ( dtime AS DATE ) &gt;= '2018-03-20'
        AND sub_number = #{subId}
        <if test="payWayList != null and payWayList.size > 0">
            AND payWay IN
            <foreach collection="payWayList" index="index" item="payWay" separator="," open="(" close=")">
                #{payWay}
            </foreach>
        </if>
        AND money - isnull( refundPrice, 0 ) &gt;0
    </select>

    <select id="getMemberPayPwdInfo" resultType="com.jiuji.oa.afterservice.other.bo.MemberPayPwdInfo">
        select id,saltPay,payPwd from dbo.BBSXP_Users with(nolock) where id = #{userId}
    </select>

    <select id="getBeiHuoMkcCount" resultType="java.lang.Integer">
        select count(1) from dbo.product_mkc with(nolock)
        where kc_check != 4 and basket_id in (select basket_id from dbo.basket with(nolock) where sub_id = #{subId} )
    </select>

    <select id="getBeiHuoBasketId" resultType="java.lang.Integer">
       select top 1 basket_id from dbo.basket_other a with(nolock)
       where exists(select 1 from dbo.basket b with(nolock) where isnull(b.isdel,0)=0 and a.basket_id=b.basket_id and b.sub_id= #{subId} )
    </select>

    <select id="getOutPutKcBasketId" resultType="java.lang.Integer">
       select top  1 basket_id from dbo.basket b with(nolock)
       where isnull(b.isdel,0)=0 and b.sub_id = #{subId} and isnull(ischu,0)=1 and ismobile=0
    </select>

    <select id="checkAlipayCoupon" resultType="java.lang.Boolean">
        select top 1 y.sub_id from dbo.shouying y with(nolock) where sub_id = #{subId} and y.inuser='支付宝(pay1)'
        and exists(select 1 from dbo.basket b with(nolock) where b.sub_id=y.sub_id
        and b.ppriceid in
        <foreach collection="aliPayPpidList" index="index" item="ppid" open="(" close=")" separator=",">
            #{ppid}
        </foreach>
    </select>

    <select id="getSubPay03" resultType="java.math.BigDecimal">
        select isnull(s.sub_pay03,0) from dbo.shouying s with(nolock) where s.sub_id =  #{subId}
    </select>

    <select id="checkZtAccount" resultType="java.lang.Boolean">
        seLect count(1) from BBSXP_Users u with(nolock)
        left join zitidian z with(nolock) on u.id=z.userid
        left join sub s with(nolock) on s.zitidianid = z.id where z.id > 0 and s.delivery = 3  and s.sub_id = #{subId}
    </select>

    <select id="getKuBaiTiaoPriceInfo" resultType="com.jiuji.oa.afterservice.other.bo.KuBaiTiaoPriceInfoBo">
        select id,money-isnull(refundPrice,0) leftPrice from dbo.netpay_record with(nolock)
        where sub_number = #{subId} and type = 1 and money-isnull(refundPrice,0)>0
        <if test="payWayList != null and payWayList.size > 0">
            AND payWay IN
            <foreach collection="payWayList" index="index" item="payWay" separator="," open="(" close=")">
                #{payWay}
            </foreach>
        </if>
    </select>
</mapper>
