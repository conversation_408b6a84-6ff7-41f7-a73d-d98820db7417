<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.TuihuanConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.TuihuanConfig">
        <id column="id" property="id" />
        <result column="sDay" property="sDay" />
        <result column="eDay" property="eDay" />
        <result column="kinds" property="kinds" />
        <result column="faultOption" property="faultOption" />
        <result column="dealOption" property="dealOption" />
        <result column="checkOption" property="checkOption" />
        <result column="prices" property="prices" />
        <result column="isdel" property="isdel" />
        <result column="xtenant" property="xtenant" />
        <result column="min_price" property="minPrice" />
        <result column="sPrice" property="sPrice" />
        <result column="ePrice" property="ePrice" />
        <result column="product_type" property="productType" />
        <result column="product_values" property="productValues" />
        <result column="butie_prices" property="buTiePrices" />
    </resultMap>

    <select  id="listTuihuanConfig" resultMap="BaseResultMap">
        SELECT
        tc.id,
        tc.sDay,
        tc.eDay,
        tc.kinds,
        tc.faultOption,
        tc.dealOption,
        tc.checkOption,
        tc.prices,
        tc.isdel,
        tc.xtenant,
        tc.min_price,
        tc.sPrice,
        tc.ePrice,
        tc.product_type,
        tc.product_values,
        tc.butie_prices
        FROM
        tuihuanConfig tc with(nolock)
        WHERE
        tc.isdel = 0
        AND tc.kinds = #{req.tradeType}
        AND tc.xtenant = #{req.xtenant}
        AND tc.sDay &lt;= #{req.tradeDay}
        AND tc.eDay &gt;= #{req.tradeDay}
        and (tc.product_type is null
        or tc.product_type = 1 and exists(select split_value from dbo.F_SPLIT(isnull(#{req.productCids},''),',')
                                                             where split_value in (select pv.split_value from dbo.F_SPLIT(isnull(tc.product_values ,''),',') pv))
        or tc.product_type = 2 and #{req.productId} in (select pv.split_value from dbo.F_SPLIT(isnull(tc.product_values ,''),',') pv))
    </select>

</mapper>
