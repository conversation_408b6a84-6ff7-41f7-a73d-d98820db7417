<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.WxsmsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.Wxsms">
        <id column="mainKey" property="mainKey" />
        <result column="openid" property="openid" />
        <result column="sendtime" property="sendtime" />
        <result column="msg" property="msg" />
        <result column="delivery" property="delivery" />
        <result column="inuser" property="inuser" />
        <result column="url" property="url" />
        <result column="type_" property="type" />
        <result column="subTitle" property="subTitle" />
        <result column="userid" property="userid" />
        <result column="sub_id" property="subId" />
        <result column="mobile" property="mobile" />
        <result column="smsChannel" property="smsChannel" />
        <result column="wxid" property="wxid" />
    </resultMap>

    <update id="updateSendTimeBySubId">
        update dbo.wxsms set sendtime=dateadd(hour,1,sendtime) where sub_id=#{subId} and type_ in (1,3)
    </update>
</mapper>
