<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.ReplacementMachineLossManageMapper">

	<sql id="statisticsCndSql_sh_a_t_Part">
		<if test="req.repairNumberType != null and req.repairNumberType != '' and req.selected != null and req.selected == 'originalRepairNumber'">
			and sh.id = #{req.repairNumberType}
		</if>
		<if test="req.repairNumberType != null and req.repairNumberType != '' and req.selected != null and req.selected == 'goodsInStockRepairNumber'">
			and sh.id in (select shg.fromshouhouid from shouhou shg with(nolock) where shg.id = #{req.repairNumberType})
		</if>
		<if test="req.repairNumberType != null and req.repairNumberType != '' and req.selected != null and req.selected == 'machineType'">
			and (sh.name like concat('%',#{req.repairNumberType},'%') or sh.product_color like concat('%',#{req.repairNumberType},'%') or #{req.repairNumberType} like concat('%',concat(sh.name,' ',sh.product_color),'%'))
		</if>
		<if test="req.areaIds != null">
			and a.area in
			<foreach collection="req.areaNameList" open="(" separator="," close=")" item="areaName">
				#{areaName}
			</foreach>
		</if>
		<if test="req.cidList != null and req.cidList.size > 0">
			and p.cid in
			<foreach collection="req.cidList" open="(" separator="," close=")" item="areaName">
				#{areaName}
			</foreach>
		</if>
		<if test="req.brandIdList != null and req.brandIdList.size > 0">
			and p.brandID in
			<foreach collection="req.brandIdList" open="(" separator="," close=")" item="areaName">
				#{areaName}
			</foreach>
		</if>

		<if test="req.auditType != null and !req.auditType.isEmpty()">
			and t.checkType in
			<foreach collection="req.auditTypeList" open="(" separator="," close=")" item="auditType">
				#{auditType}
			</foreach>
		</if>
		<if test="req.returnType != null and !req.returnType.isEmpty()">
			and t.tuihuan_kind in
			<foreach collection="req.returnTypeList" open="(" separator="," close=")" item="returnType">
				#{returnType}
			</foreach>
		</if>
		<if test="req.brokenCondition != null and !req.brokenCondition.isEmpty()">
			and t.faultType in
			<foreach collection="req.brokenConditionList" open="(" separator="," close=")" item="brokenCondition">
				#{brokenCondition}
			</foreach>
		</if>

		<if test="req.startTime != null and req.startTime != '' and req.timeType == 'returnExchangeProcessTime'">
			and #{req.startTime}  <![CDATA[<=]]> t.check3dtime
		</if>
		<if test="req.startTime != null and req.startTime != '' and req.timeType == 'originalAfterServiceGetMachineTime'">
			and #{req.startTime}  <![CDATA[<=]]> sh.offtime
		</if>
		<if test="req.startTime != null and req.startTime != '' and req.timeType == 'dealCompleteTime'">
			and #{req.startTime}  <![CDATA[<=]]> sh.tradedate
		</if>
		<if test="req.endTime != null and req.endTime != '' and req.timeType == 'returnExchangeProcessTime'">
			and #{req.endTime}  <![CDATA[>=]]> t.check3dtime
		</if>
		<if test="req.endTime != null and req.endTime != '' and req.timeType == 'originalAfterServiceGetMachineTime'">
			and #{req.endTime}  <![CDATA[>=]]> sh.offtime
		</if>
		<if test="req.endTime != null and req.endTime != '' and req.timeType == 'dealCompleteTime'">
			and #{req.endTime}  <![CDATA[>=]]> sh.tradedate
		</if>
		<if test="req.paramStartTime != null and req.paramStartTime != ''">
			AND sh.offtime BETWEEN #{req.paramStartTime}
			AND getdate()
		</if>
	</sql>
	<sql id="allSearchSqlPart">
		SELECT DISTINCT shat.d_name bigAreaName,shat.a_area areaName,shat.sh_id afterServiceId,shat.sh_offtime getMachineTime,shat.t_check3dtime  returnExchangeCompleteTime
		,shat.modidate,CASE WHEN isnull( shat.sh_isXcMkc, 0 ) = 1 AND isnull( shat.sh_ishuishou, 0 ) = 0 THEN '优品单'
			  WHEN isnull( shat.sh_ishuishou, 0 ) = 1 THEN '良品单'
		WHEN isnull( shat.sh_isXcMkc, 0 ) = 0 AND isnull( shat.sh_ishuishou, 0 ) = 0 AND isnull( shat.sh_sub_id, 0 ) > 0 THEN '销售单'
		WHEN shat.sh_userid= 76783 AND isnull( shat.sh_fromshouhouid, 0 ) = 0 AND isnull( shat.sh_tradedate, 0 ) = 0 THEN '转售后机' ELSE '其他'
		END source
		, iif(shat.sh_product_color is null,shat.sh_name,CONCAT(shat.sh_name,' ',shat.sh_product_color)) machineType
        ,shat.t_comment refundReason, shat.sh_problem brokenDesc,CASE WHEN shat.t_tuihuan_kind= 1 THEN '换机头'
            WHEN shat.t_tuihuan_kind= 3 THEN '退款' WHEN shat.t_tuihuan_kind= 4 THEN '换其他型号' ELSE 'NULL'
		END returnExchangeType, shat.t_checkType auditType , shat.t_faultType brokenCondition, shat.t_zhejiaM discountMoney
		  , shat.sh_tradedate buyTime,shat.sh_id shouHouId,shat.t_basket_id tuiHuanBasketId,shat.sh_mkc_id mkcId,shat.cidStr,shat.bidStr
		FROM (
		    select sh.modidate as modidate,d.name d_name,a.area a_area,t.id t_id,sh.offtime sh_offtime,t.check3dtime t_check3dtime,sh.isXcMkc sh_isXcMkc
		    ,sh.ishuishou sh_ishuishou,sh.sub_id sh_sub_id,sh.userid sh_userid,sh.fromshouhouid sh_fromshouhouid
		    ,sh.tradedate sh_tradedate,sh.product_color sh_product_color,sh.name sh_name,t.comment t_comment,sh.problem sh_problem
		    ,t.tuihuan_kind t_tuihuan_kind,t.checkType t_checkType,t.faultType t_faultType,t.zhejiaM t_zhejiaM,sh.id sh_id
		    ,t.basket_id t_basket_id,sh.mkc_id sh_mkc_id,ca.Name as cidStr,br.name as bidStr
		    from shouhou sh WITH ( nolock )
		left join dbo.productinfo p with(nolock ) on p.ppriceid = sh.ppriceid
		left join dbo.category ca with(nolock ) on p.cid = ca.ID
		left join dbo.brand br with(nolock ) on br.id = p.brandID
				inner join shouhou_tuihuan t WITH ( nolock ) ON t.shouhou_id= sh.id
				left join areainfo a WITH ( nolock ) ON a.id= sh.areaid
				left join departInfo d with(nolock) ON dbo.getDepartTypeId(a.depart_id,3) = d.id
				WHERE exists(select 1 from shouhou_tuihuan t WITH ( nolock ) where t.check3= 1 AND isnull( t.isdel, 0 ) = 0 and t.tuihuan_kind in(1,3,4) and t.shouhou_id= sh.id)
				and t.check3= 1 AND isnull( t.isdel, 0 ) = 0 and t.tuihuan_kind in(1,3,4)
			<include refid="statisticsCndSql_sh_a_t_Part"></include>
		    union all
			select sh.modidate as modidate,d.name d_name,a.area a_area,t.id t_id,sh.offtime sh_offtime,t.check3dtime t_check3dtime,sh.isXcMkc sh_isXcMkc
			,sh.ishuishou sh_ishuishou,sh.sub_id sh_sub_id,sh.userid sh_userid,sh.fromshouhouid sh_fromshouhouid
			,sh.tradedate sh_tradedate,sh.product_color sh_product_color,sh.name sh_name,t.comment t_comment,sh.problem sh_problem
			,t.tuihuan_kind t_tuihuan_kind,t.checkType t_checkType,t.faultType t_faultType,t.zhejiaM t_zhejiaM,sh.id sh_id
			,t.basket_id t_basket_id,sh.mkc_id sh_mkc_id,ca.Name as cidStr,br.name as bidStr
			from shouhou sh WITH ( nolock )
		left join dbo.productinfo p with(nolock ) on p.ppriceid = sh.ppriceid
		left join dbo.category ca with(nolock ) on p.cid = ca.ID
		left join dbo.brand br with(nolock ) on br.id = p.brandID
			left join (select null id,null shouhou_id,null check3dtime,null tuihuan_kind,null checkType,null comment,null faultType,null zhejiaM,null basket_id) t  on t.shouhou_id=sh.id
			left join areainfo a WITH ( nolock ) ON a.id= sh.areaid
			left join departInfo d with(nolock) ON dbo.getDepartTypeId(a.depart_id,3) = d.id
			WHERE sh.userid=76783 and isnull(sh.fromshouhouid,0)=0 and isnull(sh.tradedate,0)=0
			<include refid="statisticsCndSql_sh_a_t_Part"></include>
		    ) shat

	</sql>
    <select id="searchStatistics" resultType="com.jiuji.oa.afterservice.other.vo.res.ReplacementMachineLossRes">
		select * from (<include refid="allSearchSqlPart"></include>) rml order by rml.getMachineTime desc
    </select>
	<select id="totalSearchStatistics"
			resultType="com.jiuji.oa.afterservice.other.vo.res.ReplacementMachineLossRes$Total">
	   SELECT isnull(sum(shat.t_zhejiaM),0.0) discountMoney,isnull(sum(shat.k_inbeihuoprice),0.0) cost,isnull(sum(shat.b_price),0.0) salePrice
		,isnull(sum(shat.b_price-isnull(shat.k_inbeihuoprice,0)),0.0) factLoss,count(1) totalNumber
		FROM (
		select distinct sh.id,t.zhejiaM t_zhejiaM,k.inbeihuoprice k_inbeihuoprice,b.price b_price
		from shouhou sh WITH ( nolock )
		left join dbo.productinfo p with(nolock ) on p.ppriceid = sh.ppriceid
		left join dbo.category ca with(nolock ) on p.cid = ca.ID
		left join dbo.brand br with(nolock ) on br.id = p.brandID
		inner join shouhou_tuihuan t WITH ( nolock ) ON t.shouhou_id= sh.id
		left join shouhou sh1 with(nolock) on sh1.fromshouhouid=sh.id and sh1.userid=76783
		left join product_mkc k with(nolock) on k.id=sh1.mkc_id
		left join basket b with(nolock) on b.basket_id=k.basket_id and isnull(b.isdel,0)=0
		left join areainfo a WITH ( nolock ) ON a.id= sh.areaid
		WHERE exists(select 1 from shouhou_tuihuan t WITH ( nolock ) where t.check3= 1 AND isnull( t.isdel, 0 ) = 0 and t.tuihuan_kind in(1,3,4) and t.shouhou_id= sh.id)
		and t.check3= 1 AND isnull( t.isdel, 0 ) = 0 and t.tuihuan_kind in(1,3,4)
		<include refid="statisticsCndSql_sh_a_t_Part"></include>
		union all
		select sh.id,t.zhejiaM t_zhejiaM,rk.inprice k_inbeihuoprice,rb.price b_price
		from shouhou sh WITH ( nolock )
		left join dbo.productinfo p with(nolock ) on p.ppriceid = sh.ppriceid
		left join dbo.category ca with(nolock ) on p.cid = ca.ID
		left join dbo.brand br with(nolock ) on br.id = p.brandID
		left join (select null id,null shouhou_id,null check3dtime,null tuihuan_kind,null checkType,null comment,null faultType,null zhejiaM,null basket_id) t  on t.shouhou_id=sh.id
		left join shouhou sh1 with(nolock) on sh1.fromshouhouid=sh.id and sh1.userid=76783
		left join recover_mkc rk with(nolock) on rk.id=sh1.mkc_id
		left join recover_marketSubInfo rb with(nolock) on rb.basket_id=rk.to_basket_id and isnull(rb.isdel,0)=0
		left join areainfo a WITH ( nolock ) ON a.id= sh.areaid
		WHERE sh.userid=76783 and isnull(sh.fromshouhouid,0)=0 and isnull(sh.tradedate,0)=0
		<include refid="statisticsCndSql_sh_a_t_Part"></include>
		) shat
	</select>
	<select id="searchPageStatistics"
			resultType="com.jiuji.oa.afterservice.other.vo.res.ReplacementMachineLossRes">
		select * from (<include refid="allSearchSqlPart"></include>) rml
		order by rml.getMachineTime desc offset ${req.page > 0 ? (req.page - 1) * req.pageSize : 0} row fetch next #{req.pageSize} row only
	</select>
	<select id="listShXianHuo" resultType="com.jiuji.oa.afterservice.other.vo.res.ReplacementMachineLossRes$Sh1">
		select sh.id,sh.mkc_id,sh.fromshouhouid from shouhou sh with(nolock) where sh.fromshouhouid in
		  <foreach collection="ids" item="id" open="(" close=")" separator=",">
			  #{id}
		  </foreach>
		  and sh.userid=76783;
	</select>
	<select id="listPmkc" resultType="com.jiuji.oa.afterservice.other.vo.res.ReplacementMachineLossRes$Mkc">
		select pm.id,pm.basket_id,isnull(pm.inbeihuoprice,0.0) inprice from product_mkc pm with (nolock)
		where id in
		<foreach collection="ids" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>
	<select id="listReMkc" resultType="com.jiuji.oa.afterservice.other.vo.res.ReplacementMachineLossRes$Mkc">
		select rm.id,rm.to_basket_id basketId,isnull(rm.inprice,0.0) inprice from recover_mkc rm with (nolock)
		where id in
		<foreach collection="ids" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>
	<select id="listSubAndBasket"
			resultType="com.jiuji.oa.afterservice.other.vo.res.ReplacementMachineLossRes$SubAndBasket">
		select b.sub_id,isnull(b.price,0.0) price,b.basket_id
		from basket b with(nolock)
		left join sub s with(nolock) on s.sub_id= b.sub_id and s.sub_check in(3,9)
		where isnull(b.isdel,0)=0 and b.basket_id in
		<foreach collection="ids" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>
	<select id="listRecoverSubAndBasket"
			resultType="com.jiuji.oa.afterservice.other.vo.res.ReplacementMachineLossRes$SubAndBasket">
		select rb.sub_id,isnull(rb.price,0.0) price,rb.basket_id
		from recover_marketSubInfo rb with(nolock)
		left join recover_marketInfo  rs with(nolock) on rs.sub_id= rb.sub_id and rs.sub_check in(3,9)
		where isnull(rb.isdel,0)=0 and rb.basket_id in
		<foreach collection="ids" item="id" open="(" close=")" separator=",">
			#{id}
		</foreach>
	</select>


</mapper>
