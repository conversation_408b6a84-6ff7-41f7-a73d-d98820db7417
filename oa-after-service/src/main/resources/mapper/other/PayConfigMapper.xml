<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.PayConfigMapper">
    <!--   -->
    <select id="queryAlipayConfig" resultType="com.jiuji.oa.afterservice.other.bo.AlipayConfigBO">
        select app_id as appId,
               merchant_private_key as merchantPrivateKey,
               alipay_public_key as alipayPublicKey,
               payName,
               limitArea,xtenant as xTenant,
               authorizeid as authorizeId,
               pid,nickName,kemu as keMu,
               kinds
        from dbo.alipayConfig with(nolock) where ISNULL(isdel,0) = 0
    </select>
    <select id="queryUnionPayConfig" resultType="com.jiuji.oa.afterservice.other.bo.UPayConfigBO">
        select * from dbo.upayConfig with(nolock);
    </select>

    <select id="getKuBaiTiaoNames" resultType="java.lang.String">
        select payName from kubaitiaoConfig with(nolock) ORDER BY id DESC;
    </select>

    <select id="queryWechatConfig" resultType="com.jiuji.oa.afterservice.other.bo.WeixinConfigBO">
        select appId,
               appsecret as appSecret,
               mch_id as mchId,
               payName,nickName,limitArea,xtenant as xTenant,
               authorizeid as authorizeId,
               kinds,partnerKey,kemu as keMu,
               certPath,hostUrl,wxid as wxId
        from dbo.weixinConfig with(nolock) where isnull(isdel,0) = 0
    </select>

    <select id="queryTongLianConfig" resultType="com.jiuji.oa.afterservice.other.bo.TongLianConfigBO">
        select * from dbo.tonglian_config with(nolock);
    </select>

    <select id="queryPosBankConfig" resultType="com.jiuji.oa.afterservice.other.bo.PosBankConfigBO">
        select id,mch_id as mchId,
               mch_private_key as mchPrivateKey,
               payName,nickName,kemu
        from postBankPayConfig with(nolock);
    </select>

    <select id="queryKuBaiTiaoConfig" resultType="com.jiuji.oa.afterservice.other.bo.KubaitiaoConfigBO">
        select * from dbo.kubaitiaoConfig with(nolock) ORDER BY id DESC;
    </select>

    <select id="queryCcbConfig" resultType="com.jiuji.oa.afterservice.other.bo.CcbConfigBO">
        select id,MERCHANTID as merchantId,
               POSID as postId,BRANCHID as branchId,
               publicKey,payName,nickName,limitArea,xtenant,authorizeid,kemu
               <if test="isJiuji">
                   ,interestPayment,realName,rank
               </if>
        from dbo.ccbPayConfig with(nolock);
    </select>
    <select id="queryAlipayConfigForSaas" resultType="com.jiuji.oa.afterservice.other.bo.AlipayConfigBO">
        select app_id,merchant_private_key,alipay_public_key,payName,limitArea,xtenant,authorizeid,pid,nickName,kemu,kinds,AlipayPublicCertPath,AppCertPath,RootCertPath,host_url
        from dbo.alipayConfig with(nolock) WHERE ISNULL(isdel,0) = 0
    </select>
    <select id="listSwiftpassConfig" resultType="com.jiuji.oa.afterservice.other.bo.SwiftpassConfigBo">
        select id,mch_id,mch_key,platKey,platUrl,payName,nickName,limitArea,xtenant,authorizeid,kemu,RSAFlag,isdel from dbo.swiftpassConfig with(nolock)
    </select>
    <select id="queryUPayConfig" resultType="com.jiuji.oa.afterservice.other.bo.UPayConfigBO">
        SELECT id, merchantId, pfxPassword, pfxpath, crepath, payName, nickName, limitArea, xtenant, authorizeid, kemu, isdel
             <choose>
                 <when test="isJiuji">, limitAreaCode, [rank]</when>
                 <otherwise>,reportSerialNo</otherwise>
             </choose>
        from dbo.upayConfig with(nolock)
    </select>
    <select id="queryIcbcConfig" resultType="com.jiuji.oa.afterservice.other.IcbcPayConfig">
        SELECT * FROM dbo.icbc_psy_config with(nolock)
    </select>
    <select id="queryZeroConfig" resultType="com.jiuji.oa.afterservice.other.IcbcPayConfig">
        SELECT * FROM dbo.zero_pay_config with(nolock)
    </select>
</mapper>
