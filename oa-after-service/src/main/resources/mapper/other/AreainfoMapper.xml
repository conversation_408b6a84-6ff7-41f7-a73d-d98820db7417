<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.AreainfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.Areainfo">
        <id column="id" property="id"/>
        <result column="cityid" property="cityid"/>
        <result column="pid" property="pid"/>
        <result column="zid" property="zid"/>
        <result column="did" property="did"/>
        <result column="city" property="city"/>
        <result column="city_name" property="cityName"/>
        <result column="Province_name" property="provinceName"/>
        <result column="company_tel1" property="companyTel1"/>
        <result column="company_tel2" property="companyTel2"/>
        <result column="company_address" property="companyAddress"/>
        <result column="company_address2" property="companyAddress2"/>
        <result column="company_qq" property="companyQq"/>
        <result column="area" property="area"/>
        <result column="kind1" property="kind1"/>
        <result column="kind2" property="kind2"/>
        <result column="isweb" property="isweb"/>
        <result column="leve" property="leve"/>
        <result column="area_name" property="areaName"/>
        <result column="company_name" property="companyName"/>
        <result column="ispass" property="ispass"/>
        <result column="rank" property="rank"/>
        <result column="hours" property="hours"/>
        <result column="baozhengM" property="baozhengM"/>
        <result column="jiamengM" property="jiamengM"/>
        <result column="startdtime" property="startdtime"/>
        <result column="enddtime" property="enddtime"/>
        <result column="guanliM" property="guanliM"/>
        <result column="contact" property="contact"/>
        <result column="contact_tel" property="contactTel"/>
        <result column="userid" property="userid"/>
        <result column="mkc_maxcount" property="mkcMaxcount"/>
        <result column="userid_sh" property="useridSh"/>
        <result column="url" property="url"/>
        <result column="area_M" property="areaM"/>
        <result column="position" property="position"/>
        <result column="gzpercent" property="gzpercent"/>
        <result column="nianpercent" property="nianpercent"/>
        <result column="butie" property="butie"/>
        <result column="authorizeid" property="authorizeid"/>
        <result column="area_M1" property="areaM1"/>
        <result column="level1" property="level1"/>
        <result column="areaCode" property="areaCode"/>
        <result column="curAdmin" property="curAdmin"/>
        <result column="preAdmin" property="preAdmin"/>
        <result column="preAdminF" property="preAdminF"/>
        <result column="mappoints" property="mappoints"/>
        <result column="mappoints3h" property="mappoints3h"/>
        <result column="mch_id" property="mchId"/>
        <result column="mch_key" property="mchKey"/>
        <result column="allowappcheckin" property="allowappcheckin"/>
        <result column="dcArea" property="dcArea"/>
        <result column="dcAreaID" property="dcAreaID"/>
        <result column="kemu" property="kemu"/>
        <result column="IsSupportYanji" property="IsSupportYanji"/>
        <result column="isSend" property="isSend"/>
        <result column="printName" property="printName"/>
        <result column="cityid0" property="cityid0"/>
        <result column="courier" property="courier"/>
        <result column="cityAreas" property="cityAreas"/>
        <result column="logo" property="logo"/>
        <result column="SupportService" property="SupportService"/>
        <result column="deliveryBeginTime" property="deliveryBeginTime"/>
        <result column="deliveryEndTime" property="deliveryEndTime"/>
        <result column="autoTransfer" property="autoTransfer"/>
        <result column="xtenant" property="xtenant"/>
        <result column="supportDelivery" property="supportDelivery"/>
        <result column="AreaServiceType" property="AreaServiceType"/>
        <result column="payCardNum" property="payCardNum"/>
        <result column="payCardName" property="payCardName"/>
        <result column="DisplayLevel" property="DisplayLevel"/>
    </resultMap>

    <select id="getAreaInfoSimpleByAreaId" resultType="com.jiuji.oa.afterservice.other.bo.AreaInfoSimpleBO">
        SELECT cityid as cityId,pid,zid,did,company_address as companyAddress,area_M as areaM,area_name as areaName,company_tel1 as companyTel, hours FROM areainfo with(nolock) WHERE id = #{areaId}
    </select>

    <select id="getAccountSetIdByAreaId" resultType="java.lang.Integer">
        select a.ztid
        from authorize a with(nolock)
        left join areainfo area with(nolock) on a.id=area.authorizeid
        where area.id=#{areaId}
    </select>

    <select id="getAccountSetIdByAuthorizeId" resultType="java.lang.Integer">
        select a.ztid
        from authorize a with(nolock)
        where a.id = #{id}
    </select>

    <select id="getAreaIdByDepartInfo" resultType="com.jiuji.oa.afterservice.other.po.Areainfo">
        select id, areaCode
        from areainfo with (nolock)
        where areaCode is not null
    </select>

    <select id="getAreaBelongsDcHqD1AreaId" resultType="com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId">
        SELECT top 1 a.id as areaId,au.D1AreaId as d1AreaId,au.dcAreaId,au.hqAreaId,au.h1AreaId
        from areainfo a with(nolock)
        LEFT JOIN authorize au with(nolock) on au.id = a.authorizeid
        where a.id = #{currentAreaId}
    </select>

    <select id="getBigAndSmallAreaInfoByIds"
            resultType="com.jiuji.oa.afterservice.batchreturn.vo.res.BigAndSmallAreaInfo">
        select a.id as areaId,a.area,d.name as bsArea
        FROM areainfo a with(nolock) left join departInfo d with(nolock) on dbo.getDepartTypeId(a.depart_id,#{dataType}) = d.id
        WHERE a.ispass = 1 and isnull(d.isdel,0) = 0
        and a.id in
        <foreach collection="areaIds" item="areaId" separator="," open="(" close=")" index="index">
            #{areaId}
        </foreach>
    </select>

    <select id="getSmsChannelById" resultType="com.jiuji.oa.afterservice.other.bo.SmsChannelBo">
        select id as areaId,vCodeChannel,marketChannel from areainfo with(nolock) where id = #{areaId};
    </select>
    <select id="listAreaIdByDepartId" resultType="com.jiuji.oa.afterservice.other.bo.AreaDepartBo">
        SELECT a.id areaId,di.id departId
             <if test="!mainRoles.isEmpty()">
                 ,cu.ch999_id mainRoleCh999Id
             </if>
        from departInfo di with(nolock)
        inner join (SELECT value FROM (VALUES
        <foreach collection="bigDepartIds" item="bigDepartId" separator=",">
            (#{bigDepartId})
        </foreach>) as big_ids(value) ) bigd on bigd.value = di.id
        or bigd.value in (select cast(f.split_value as int) from dbo.F_SPLIT(isnull(di.departPath,''),',') f)
        inner join areainfo a with(nolock) on a.depart_id = di.id
        <if test="!mainRoles.isEmpty()">
            left  join ch999_user cu with(nolock) on cu.area1id = a.id and cu.iszaizhi = 1 and cu.mainRole in
            <foreach collection="mainRoles" item="mainRole" open="(" separator="," close=")">
                #{mainRole}
            </foreach>
        </if>
    </select>

    <select id="listCh999IdByAreaIdAndMainRole" resultType="com.jiuji.oa.afterservice.other.bo.AreaDepartBo">
        select cu.area1id areaId, cu.ch999_id mainRoleCh999Id from ch999_user cu with(nolock)
        where  cu.iszaizhi = 1
        and cu.area1id in
        <foreach collection="area1Ids" item="area1Id" open="(" separator="," close=")">
            #{area1Id}
        </foreach>
        and cu.mainRole in
        <foreach collection="mainRoles" item="mainRole" open="(" separator="," close=")">
            #{mainRole}
        </foreach>
    </select>

</mapper>
