<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.ReceivePersonConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.ReceivePersonConfig">
        <id column="id" property="id"/>
        <result column="addTime" property="addTime"/>
        <result column="fromArea" property="fromArea"/>
        <result column="toArea" property="toArea"/>
        <result column="wType" property="wType"/>
        <result column="isdel" property="isdel"/>
        <result column="receiveUserid" property="receiveUserid"/>
        <result column="receiveUserName" property="receiveUserName"/>
    </resultMap>

    <select id="getNormalReceiver" resultType="com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO">
        SELECT
        ch999_id AS receiveUserId,
        ch999_name AS receiveUserName
        FROM ch999_user cu with(nolock)
        LEFT JOIN zhiwu z with(nolock) on cu.zhiwuid=z.id
        WHERE
        zhiwu IN ( '门店主管','主管', '店长', '副店长', '职员', '专员' )
        AND iszaizhi = 1
        AND isnull(isshixi,0) &lt;&gt; 4
        AND islogin = 0
        AND area1id =#{areaId}
        ORDER BY
        z.leve,
        ch999_id
    </select>
    <select id="getExecutiveDirectorAndShopowner"
            resultType="com.jiuji.oa.afterservice.other.bo.LogisticsRecipientBO">
        SELECT
            ch999_id AS receiveUserId,
            ch999_name AS receiveUserName
        FROM ch999_user cu with(nolock)
        LEFT JOIN zhiwu z with(nolock) on cu.zhiwuid=z.id
        WHERE
            zhiwu IN ( '门店主管','主管', '店长' )
          AND iszaizhi = 1
          AND isnull(isshixi,0) &lt;&gt; 4
          AND islogin = 0
          AND area1id =#{areaId}
        ORDER BY
            z.leve,
            ch999_id

    </select>
    <select id="getAfterSalesSupervisor" resultType="java.lang.String">
        SELECT cu.ch999_id FROM areainfo a with(nolock)
            inner join ch999_user cu with(nolock) on cu.area1id = a.id
        where cu.iszaizhi = 1 and cu.mainRole in (18,1043) and EXISTS(
            SELECT 1 FROM areainfo a2 with(nolock)
            inner join departInfo di2 with(nolock) on dbo.getDepartTypeId(a2.depart_id,4) = di2.id
            CROSS join departInfo di3 with(nolock)
            where a2.id = #{areaId} and EXISTS(SELECT 1 FROM f_find_departChildNew(di2.id) ff where ff.id = di3.id) and di3.id = a.depart_id
            )
    </select>
    <select id="getAfterSalesManager" resultType="java.lang.String">
        SELECT cu.ch999_id from areainfo a with(nolock)
                             inner join areainfo a2 with(nolock) on a2.depart_id = dbo.getDepartTypeId(a.depart_id,3)
                             inner join ch999_user cu with(nolock) on cu.area1id = a2.id
        where a.id = #{areaId} and cu.iszaizhi = 1 and cu.mainRole = 314

    </select>
    <select id="getCh999IdByName" resultType="java.lang.String">
        select ch999_id from dbo.ch999_user cu with(nolock) where ch999_name=#{name} and iszaizhi = 1
    </select>
    <select id="selectRegionByAreaIdAndMainRole" resultType="java.lang.String">
        SELECT cu.ch999_id FROM areainfo a with(nolock)
        inner join ch999_user cu with(nolock) on cu.area1id = a.id
        where cu.iszaizhi = 1 and cu.mainRole = #{mainRole} and EXISTS(
            SELECT 1 FROM areainfo a2 with(nolock)
            inner join departInfo di2 with(nolock) on dbo.getDepartTypeId(a2.depart_id,3) = di2.id
            CROSS join departInfo di3 with(nolock)
            where a2.id = #{areaId} and EXISTS(SELECT 1 FROM f_find_departChildNew(di2.id) ff where ff.id = di3.id) and di3.id = a.depart_id
            )
    </select>

</mapper>
