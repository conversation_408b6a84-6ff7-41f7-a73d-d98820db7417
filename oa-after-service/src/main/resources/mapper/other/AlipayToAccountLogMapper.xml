<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.other.dao.AlipayToAccountLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.other.po.AlipayToAccountLog">
        <id column="id" property="id"/>
        <result column="sub_id" property="subId"/>
        <result column="payType" property="payType"/>
        <result column="real_id" property="realId"/>
        <result column="payee_type" property="payeeType"/>
        <result column="payee_account" property="payeeAccount"/>
        <result column="amount" property="amount"/>
        <result column="remark" property="remark"/>
        <result column="order_id" property="orderId"/>
        <result column="pay_date" property="payDate"/>
        <result column="inuser" property="inuser"/>
        <result column="intime" property="intime"/>
        <result column="callBackMsg" property="callBackMsg"/>
    </resultMap>

    <select id="getDailyPayLimit" resultType="java.math.BigDecimal">
        SELECT SUM
        ( amount )
        FROM alipayToAccountLog WITH ( nolock )
        WHERE
        order_id IS NOT NULL
        AND datediff( DAY, pay_date, getdate( ) ) = 0
    </select>

    <select id="listLogForException" resultMap="BaseResultMap">
        select id,real_id,payee_account,remark,amount,pay_date,inuser
        FROM alipayToAccountLog WITH ( nolock )
        WHERE payee_type='ALIPAY_USERID' and  payee_account=#{userId}
        and order_id is not null and datediff(month,pay_date,getdate()) &lt;=6
    </select>

</mapper>
