<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.stock.dao.MkcDellogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.stock.po.MkcDellogs">
        <id column="id" property="id" />
        <result column="mkc_id" property="mkcId" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="check1" property="check1" />
        <result column="check1dtime" property="check1dtime" />
        <result column="check1user" property="check1user" />
        <result column="check2" property="check2" />
        <result column="check2dtime" property="check2dtime" />
        <result column="check2user" property="check2user" />
        <result column="comment" property="comment" />
        <result column="area" property="area" />
        <result column="kinds" property="kinds" />
        <result column="price1" property="price1" />
        <result column="price2" property="price2" />
        <result column="ppriceid" property="ppriceid" />
        <result column="areaid" property="areaid" />
        <result column="pzid" property="pzid" />
        <result column="frareaid" property="frareaid" />
        <result column="youhuiPrice" property="youhuiPrice" />
        <result column="lpToAreaId" property="lpToAreaId" />
    </resultMap>

    <!-- insertMkcDellogs -->
    <insert id="insertMkcDellogs" keyColumn="id" useGeneratedKeys="true" keyProperty="mkcLogs.id">
        insert into mkc_dellogs(mkc_id,inuser,dtime,areaid,comment,kinds,price1,price2,ppriceid,frareaid,youhuiPrice,
                                lpToAreaId,check1user,check1dtime,check1,check2user,check2dtime,check2)
        output inserted.id
        values (#{mkcLogs.mkcId}, #{mkcLogs.inuser}, getdate(), #{mkcLogs.areaid}, #{mkcLogs.comment}, #{mkcLogs.kinds}, #{mkcLogs.price1},
            #{mkcLogs.price2}, #{mkcLogs.ppriceid}, #{mkcLogs.frareaid}, #{mkcLogs.youhuiPrice}, #{mkcLogs.lpToAreaId}, #{username},
            getdate(), 1, #{username}, getdate(), 1)
    </insert>

</mapper>
