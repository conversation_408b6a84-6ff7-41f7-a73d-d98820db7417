<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.stock.dao.NaHuoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.stock.po.NaHuoProInfo">
        <id column="id" property="id"/>
        <result column="basket_id" property="basketId"/>
        <result column="area" property="area"/>
        <result column="addtime" property="addTime"/>
        <result column="isna" property="naFlag"/>
        <result column="natime" property="naTime"/>
        <result column="adduser" property="addUser"/>
        <result column="nauser" property="naUser"/>
        <result column="toarea" property="toArea"/>
        <result column="orderidd" property="orderIdd"/>
        <result column="kinds" property="kinds"/>
        <result column="areaid" property="areaId"/>
        <result column="cid" property="cId"/>
        <result column="sub_check" property="subCheck"/>
        <result column="basket_count" property="basketCount"/>
        <result column="sub_id" property="subId"/>
        <result column="price" property="price"/>
        <result column="ppriceid" property="pPriceId"/>
        <result column="ismobile" property="mobileFlag"/>
        <result column="ischu" property="chuFlag"/>
        <result column="area1id" property="area1Id"/>
        <result column="type" property="type"/>
        <result column="userid" property="userId"/>
        <result column="sub_mobile" property="subMobile"/>
        <result column="proinfo" property="proInfo"/>
    </resultMap>
    <resultMap id="ProductKcMap" type="com.jiuji.oa.afterservice.stock.po.ProductKc">
        <id column="id" property="id"/>
        <result column="leftCount" property="leftCount"/>
    </resultMap>

    <resultMap id="ProductGiftKcMap" type="com.jiuji.oa.afterservice.stock.po.ProductKc">
        <id column="id" property="id"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="lcount" property="lcount"/>
        <result column="leftCount" property="leftCount"/>
        <result column="orderCount" property="orderCount"/>
        <result column="areaid" property="areaid"/>
    </resultMap>

    <resultMap id="BasketOtherMap" type="com.jiuji.oa.afterservice.stock.po.BasketOtherStock">
        <result column="lcount" property="lCount"/>
        <result column="isDone" property="doneFlag"/>
        <result column="ppriceid" property="pPriceId"/>
        <result column="areaid" property="areaId"/>
    </resultMap>

    <resultMap id="DuiDiaoProMap" type="com.jiuji.oa.afterservice.stock.po.DuiDiaoProductStock">
        <result column="basket_id" property="basketId"/>
        <result column="mkc_id" property="mkcId"/>
        <result column="areaid" property="areaId"/>
        <result column="ppriceid" property="pPriceId"/>
        <result column="ispay1" property="payFlag1"/>
        <result column="ispay2" property="payFlag2"/>
        <result column="kc_check" property="kcCheck"/>
        <result column="imei" property="imei"/>
        <result column="tradeDate" property="tradeDate"/>
        <result column="tradeDate1" property="tradeDate1"/>
        <result column="sub_check" property="subCheck"/>
        <result column="type" property="type"/>
        <result column="inbeihuodate" property="inBeiHuoDate"/>
        <result column="mouldFlag" property="moduleFlag"/>
        <result column="rank" property="rank"/>
        <result column="insourceid2" property="insourceId2"/>
        <result column="inbeihuoprice" property="inBieHuoPrice"/>
        <result column="kmtype" property="kmType"/>
    </resultMap>

    <resultMap id="ProBasketSubMap" type="com.jiuji.oa.afterservice.stock.po.ProductBasketSub">
        <result column="id" property="id"/>
        <result column="mkc_id" property="mkcId"/>
        <result column="basket_id" property="basketId"/>
        <result column="areaid" property="areaId"/>
        <result column="kc_check" property="kcCheck"/>
        <result column="ispay1" property="payFlag1"/>
        <result column="ispay2" property="payFlag2"/>
        <result column="sub_check" property="subCheck"/>
        <result column="imei" property="imei"/>
        <result column="inbeihuodate" property="inBeiHuoDate"/>
        <result column="mouldFlag" property="moduleFlag"/>
        <result column="rank" property="rank"/>
        <result column="inbeihuoprice" property="inBeiHuoPrice"/>
    </resultMap>

    <resultMap id="MkcToAreaMap" type="com.jiuji.oa.afterservice.stock.po.MkcToArea">
        <result column="id" property="id"/>
        <result column="mkc_id" property="mkcId"/>
        <result column="area" property="area"/>
        <result column="toarea" property="toArea"/>
        <result column="dtime" property="dTime"/>
        <result column="sendtime" property="sendTime"/>
        <result column="senduser" property="sendUser"/>
        <result column="recivedtime" property="reciveTime"/>
        <result column="reciveuser" property="reciveUser"/>
        <result column="shibian" property="shiBian"/>
        <result column="stats" property="stats"/>
        <result column="deliveryTime" property="deliveryTime"/>
        <result column="wuliuid" property="wuLiuId"/>
        <result column="areaid" property="areaId"/>
        <result column="toareaid" property="toAreaId"/>
        <result column="expectTime" property="expectTime"/>
        <result column="needPay" property="needPay"/>
        <result column="isPay" property="payFlag"/>
    </resultMap>

    <resultMap id="MobileProSeller" type="com.jiuji.oa.afterservice.stock.po.MobileProSeller">
        <result column="Mobile" property="mobile"/>
        <result column="pro" property="pro"/>
        <result column="seller" property="seller"/>
    </resultMap>


    <select id="naHuoList" resultMap="BaseResultMap">
        select n.*,p.cid,s.sub_check,b.ppriceid,b.basket_count,b.basket_id,s.sub_id,b.price ,b.ismobile,p.ppriceid1,isnull(b.ischu,0) as ischu,isnull(s.kcAreaid,s.areaid) as area1id,b.type,s.userid,s.sub_mobile,p.product_name+' '+p.product_color as proInfo
        from nahuoduilie n with(nolock)
            left join basket b with(nolock) on n.basket_id=b.basket_id
            left join productinfo p with(nolock) on p.ppriceid=b.ppriceid
            LEFT JOIN sub s with(nolock) ON b.sub_id=s.sub_id
        where isnull(b.isdel,0)=0 and s.sub_check in(1,3,2,6) and id=#{naHuoDuiLieId}
    </select>

    <select id="mkcIdByBasketId" resultType="java.lang.Integer">
        select id from dbo.product_mkc with(nolock) WHERE  kc_check IN (2,3) and basket_id=#{basketId}
    </select>

    <update id="updateBasket">
        UPDATE dbo.basket SET ischu=1
        WHERE basket_id=#{basketId}
          AND ismobile=0
          and isnull(ischu,0)=0
          AND EXISTS(SELECT * FROM sub s with(nolock) WHERE s.sub_id=basket.sub_id AND s.sub_check=1)
    </update>

    <update id="updateNaHuoDuiLieById">
        UPDATE dbo.nahuoduilie SET isna=#{naFlag},nauser=#{userName},natime=GETDATE() WHERE id=#{naHuoDuiLieId}
    </update>

    <update id="updateSubByNHId">
        UPDATE dbo.sub SET trader=#{trader}
        FROM dbo.basket b,dbo.nahuoduilie n WHERE b.sub_id=sub.sub_id AND b.basket_id=n.basket_id AND sub_check IN (1,3,2,6) AND n.id=#{naHuoDuiLieId}
    </update>

    <select id="getProductKc" resultMap="ProductKcMap">
        select id,leftCount from dbo.product_kc with(nolock) where areaid=#{areaid} and ppriceid=#{ppriceid}
    </select>

    <select id="getProductGiftKc" resultMap="ProductGiftKcMap">
        select id,ppriceid,lcount,leftCount,orderCount,areaid from dbo.productGift_kc with(nolock) where ppriceid=#{ppriceid} and areaid=#{areaid}
    </select>

    <update id="updateProductGiftKc">
        update dbo.productGift_kc set lcount = lcount - #{count} where id=#{giftid}
    </update>

    <update id="updateProductKc">
        update dbo.product_kc set orderCount=iif(isnull(orderCount,0)-#{count}>=0,isnull(orderCount,0)-#{count},0) where id=#{kcid}
    </update>

    <insert id="insertProcudtGiftKcLog">
        insert into dbo.productGift_kcLog ( ppriceid , count ,  lastcount , inuser ,   dtime ,  comment ,  logkind , basket_id , areaid )
        values (#{ppriceid},(0 - #{count}),#{lastCount},#{user},getdate(),#{comments},#{kind},#{linkid},#{areaid})
    </insert>

    <select id="basketOtherList" resultMap="BasketOtherMap">
        select lcount,isDone,ppriceid,areaid from basket_other with(nolock) where basket_id=#{basketId} and isDone=#{isdone}
    </select>
    
    <update id="updateProductKcById">
        update dbo.productGift_kc set lcount = lcount - #{count},orderCount=iif(orderCount - #{orderCount}>=0,orderCount - #{orderCount},0)
        where id=#{giftId}
    </update>

    <update id="updateBasketById">
        update dbo.basket set inprice=#{inprice} where  basket_id=#{basketId}
    </update>

    <select id="getXcByMkcId" resultType="java.lang.Integer">
        select mkc_id from dbo.xc_mkc with(nolock) where isnull(isLock,0)=1 and mkc_id=#{mkcId}
    </select>

    <select id="duiDiaoProList" resultMap="DuiDiaoProMap">
        select top 2 k.basket_id,k.id as mkc_id,k.areaid,k.ppriceid,isnull(j.ispay1,0) as ispay1,
               isnull(j.ispay2,0) as ispay2,k.kc_check,k.imei,s.tradeDate,s.tradeDate1,s.sub_check,b.[type],
               k.inbeihuodate,k.mouldFlag,k.rank,k.insourceid2,isnull(k.transferPrice,k.inbeihuoprice) as inbeihuoprice,
               dbo.getMobileType(p.cid) as kmtype
        from product_mkc k with(nolock)
                 left join basket b with(nolock) on k.basket_id=b.basket_id
                 left join sub s with(nolock) on s.sub_id=b.sub_id
                 left join joinMkcFee j with(nolock) on j.mkc_id=k.id
                 left join dbo.productinfo p with(nolock) on k.ppriceid=p.ppriceid
        where (s.sub_check in(0,1,3) or s.sub_check is null )
          and kc_check in(2,3,5,8,10)
          and k.id=#{mkcId}
          and isnull(b.isdel,0)=0
    </select>

    <update id="updateRecordBasket">
        update recordbasket set completed=0,completedtime=GETDATE() where mainid=#{mkcId}
    </update>

    <select id="productBasketSubs" resultMap="ProBasketSubMap">
        select k.id as mkc_id,k.basket_id,k.areaid,k.kc_check,isnull(j.ispay1,0) as ispay1,isnull(j.ispay2,0) as ispay2,
               isnull(s.sub_check,1) sub_check,k.imei,k.inbeihuodate,k.mouldFlag,k.rank,isnull(k.transferPrice,k.inbeihuoprice) as inbeihuoprice
        from  product_mkc k with(nolock)
                  left join basket b with(nolock) on k.basket_id=b.basket_id
                  left join sub s with(nolock) on s.sub_id=b.sub_id
                  left join joinMkcFee j with(nolock) on j.mkc_id=k.id
        where isnull(b.isdel,0)=0 and k.ppriceid=#{ppid} and id=#{mkcId}
    </select>

    <update id="updateServiceRecord">
        update ServiceRecord set imei=#{imei} where isnull(isdel,0)=0 and basket_idBind=#{basketId} and imei=#{oldimei}
    </update>

    <update id="updateBasketISKc">
        update basket set iskc=1 where basket_id=#{basket_id}
    </update>

    <select id="basketSubId" resultType="java.lang.Long">
        select sub_id from basket with(nolock) where basket_id=#{basket_id}
    </select>

    <select id="xcMkcYouHuiPrice" resultType="java.math.BigDecimal">
        select youhuiPrice from dbo.xc_mkc with(nolock) where mkc_id=#{mkcId} and isnull(isLock,0)=0
    </select>

    <update id="updateBasketPrice">
        update dbo.basket set price=price+#{youhuiPrice} where basket_id=#{basketId}
    </update>

    <update id="updateBasketPrice1">
        update dbo.basket set price=price1-#{youhuiPrice} where basket_id=#{basketId}
    </update>

    <select id="orderPriceTotal" resultType="java.math.BigDecimal">
        select isnull(b.price_,0)-s.youhui1M-s.jidianM+shouxuM+feeM-isnull(coinM,0) priceM
        from sub s with(nolock)
        left join
        (select sum(basket_count*price) price_,sub_id from dbo.basket b with(nolock) where isnull(b.isdel,0)=0 and sub_id=#{subId} group by sub_id ) b on b.sub_id = s.sub_id
        where 1=1 and s.sub_id=#{subId}
    </select>

    <update id="updateSubYingFuM">
        update sub set yingfuM=#{price} where sub_id=#{subId} and sub_check in (0,1,5,2,6,4)
    </update>

    <update id="updateProductKCByppidAndMkcid">
        <if test="ppid != null and ppid != 0">
            update product_mkc set ${sqlText} where ppriceid=#{ppid} and id=#{mkcid}
        </if>
        <if test="ppid == null or ppid == 0">
            update product_mkc set ${sqlText} where kc_check in(2,3,5,8,10) and id=#{mkcid}
        </if>
    </update>

    <select id="mkcToAreaList" resultMap="MkcToAreaMap">
        select * from mkc_toarea with(nolock) where recivedtime is null and mkc_id in(#{mkcid},#{oldMkcid})
    </select>

    <update id="updateMkcToArea">
        update mkc_toarea set mkc_id=#{oldMkcid} where recivedtime is null and id=#{id}
    </update>

    <select id="mobileProSeller" resultMap="MobileProSeller">
        select t.Mobile,p.product_name+' '+isnull(p.product_color,'') as pro,b.seller
        from dbo.Taocan  t with(nolock)
                 left join dbo.basket b with(nolock) on b.basket_id = t.basket_id
                 left join dbo.productinfo p with(nolock) on p.ppriceid = b.ppriceid
        where t.PackageType = 5
          and t.isptype = 3 and t.flag in (2,5)
          and t.imei = #{imei}
          and b.ppriceid in (56768,66467,70809,70810,70811,70808,70812,66473,56769)
          and datediff(month,t.AddDate,getdate())=0
    </select>

    <insert id="insertMobileBackRecord">
        insert into dbo.mobileBackRecord(indate , stats_ ,   name ,  mobile  , ch999_id , remark  , imei )
        values( getdate() , 0 ,   #{name} ,  #{mobile} , #{ch999_id} , #{remark} , #{imei} )
    </insert>

</mapper>
