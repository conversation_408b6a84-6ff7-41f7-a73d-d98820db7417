<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.dao.ProductKclogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.stock.po.ProductKclogs">
        <id column="id" property="id" />
        <result column="count" property="count" />
        <result column="lastcount" property="lastcount" />
        <result column="inprice" property="inprice" />
        <result column="area" property="area" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="insource" property="insource" />
        <result column="check1" property="check1" />
        <result column="check1dtime" property="check1dtime" />
        <result column="check2" property="check2" />
        <result column="check2dtime" property="check2dtime" />
        <result column="comment" property="comment" />
        <result column="basket_id" property="basketId" />
        <result column="ppriceid" property="ppriceid" />
        <result column="shouhou_id" property="shouhouId" />
        <result column="areaid" property="areaid" />
    </resultMap>

</mapper>
