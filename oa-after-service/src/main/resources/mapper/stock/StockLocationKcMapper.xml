<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.stock.dao.StockLocationKcMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.stock.po.StockLocationKc">
        <id column="id" property="id"/>
        <result column="areaid" property="areaid"/>
        <result column="locationId" property="locationId"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="lcounts" property="lcounts"/>
        <result column="locationCode" property="locationCode"/>
        <result column="pan_user" property="panUser"/>
        <result column="pan_count" property="panCount"/>
        <result column="pan_dtime" property="panDtime"/>
    </resultMap>

    <resultMap id="StockLocationKcVo" type="com.jiuji.oa.afterservice.stock.vo.StockLocationKcVO">
        <result column="ppriceid" property="ppid"/>
        <result column="locationId" property="locationId"/>
        <result column="locationCode" property="locationCode"/>
        <result column="product_name" property="productName"/>
        <result column="barCode" property="barCode"/>
        <result column="lcounts" property="stockCount"/>
        <result column="product_color" property="productColor"/>
    </resultMap>


    <select id="getPjDiaoboList" resultType="com.jiuji.oa.afterservice.stock.vo.PjDiaoboVO">
        SELECT sum(bsk.lcount) totalCount,sub.toareaid as areaId,sub.id,sub.stats
        FROM diaobo_basket bsk with(nolock) inner join diaobo_sub sub with(nolock) on sub.id=bsk.sub_id
        left join product_kc pk with(nolock) on pk.ppriceid = bsk.ppriceid and pk.areaid = sub.areaid
        where sub.areaid=#{areaId} AND sub.stats in (2,5) AND pk.lcount > 0
        <if test="pjkind != null and pjkind.length() > 0">
            AND sub.kinds = #{pjkind}
        </if>
        <if test="areaIdsNew != null and areaIdsNew.size() > 0">
            AND sub.toareaid in
            <foreach item="areaId" collection="areaIdsNew" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <choose>
            <when test='kind == "0"'>
                AND bsk.basket_id is null
            </when>
            <when test='kind == "1"'>
                AND bsk.basket_id is not null
            </when>
            <when test='kind == "2"'>
                AND sub.jiaji = 1
            </when>
        </choose>
        <if test="jiaji != null and jiaji == 0">
            AND (sub.jiaji = 0 OR sub.jiaji is null)
        </if>
        <if test="jiaji != null and jiaji > 0">
            AND sub.jiaji = #{jiaji}
        </if>
        <if test="checkDtime != null">
            AND datediff(day,sub.check_dtime,#{checkDtime}) <![CDATA[>=]]> 0
        </if>
        GROUP BY sub.toareaid,sub.id,sub.stats,sub.check_dtime
        order by check_dtime ASC
    </select>

    <select id="getStockLocationPjDiaoboList" resultType="com.jiuji.oa.afterservice.stock.vo.PjDiaoboVO">
        SELECT sum(bsk.lcount) totalCount,sub.toareaid as areaId,sub.id,sub.stats
        FROM diaobo_basket bsk with(nolock) inner join diaobo_sub sub with(nolock) on sub.id=bsk.sub_id
        where sub.areaid=#{areaId} AND sub.stats in (2,5)
        <if test="pjkind != null and pjkind.length() > 0">
            AND sub.kinds = #{pjkind}
        </if>
        <if test="areaIdsNew != null and areaIdsNew.size() > 0">
            AND sub.toareaid in
            <foreach item="areaId" collection="areaIdsNew" index="index" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <choose>
            <when test='kind == "0"'>
                AND bsk.basket_id is null
            </when>
            <when test='kind == "1"'>
                AND bsk.basket_id is not null
            </when>
            <when test='kind == "2"'>
                AND sub.jiaji = 1
            </when>
        </choose>
        <if test="jiaji != null and jiaji == 0">
            AND (sub.jiaji = 0 OR sub.jiaji is null)
        </if>
        <if test="jiaji != null and jiaji > 0">
            AND sub.jiaji = #{jiaji}
        </if>
        <if test="checkDtime != null">
            AND datediff(day,sub.check_dtime, #{checkDtime}) <![CDATA[>=]]> 0
        </if>
        AND EXISTS (select 1 from stockLocationKc pk with(nolock) where pk.ppriceid = bsk.ppriceid and pk.areaid = sub.areaid and pk.lcounts >0
        <if test="stockArea != null and stockArea != ''">
            AND left(pk.locationCode,1) = #{stockArea}
        </if>
        )
        GROUP BY sub.toareaid,sub.id,sub.stats,sub.check_dtime
        order by check_dtime ASC
    </select>

    <select id="getProductStockLocationKcByPpid" resultType="com.jiuji.oa.afterservice.stock.bo.ProductStockKcResultBO">
        SELECT k.areaid,k.locationId,k.ppriceid as ppid,k.lcounts as stockCount,k.locationCode,p.product_name as
        productName, p.product_color as productColor,
        p.barCode, s.rank FROM dbo.stockLocationKc k with(nolock)
        left join dbo.productinfo p with(nolock) on k.ppriceid=p.ppriceid left join dbo.stockLocation s with(nolock) on
        k.locationId = s.id
        where k.areaid = #{areaId}
        <if test="ppidList != null and ppidList.size() > 0">
            AND k.ppriceid in
            <foreach item="ppid" collection="ppidList" index="index" open="(" separator="," close=")">
                #{ppid}
            </foreach>
        </if>
        <if test="locationCode != null and locationCode != ''">
            AND k.locationCode = #{locationCode}
        </if>
        <if test="locationId != null">
            AND k.locationId = #{locationId}
        </if>
        ORDER BY s.rank ASC
    </select>

    <select id="getPjProductByDiaoboId" resultType="com.jiuji.oa.afterservice.stock.bo.DiaoboProductToAreaResultBO">
        SELECT s.toareaid, a.area as toarea, b.ppriceid as ppid, b.lcount as pjCount, p.product_name as productName,
        p.product_color as productColor, p.barCode, a.rank, b.sub_id as subId, b.id, b.basket_id as basketId, s.kinds, s.check_dtime as checkDtime, b.inprice FROM dbo.diaobo_basket b with(nolock)
        left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
        left join dbo.diaobo_sub s with(nolock) on b.sub_id = s.id
        left join dbo.areainfo a with(nolock) on a.id = s.toareaid
        where s.areaid = #{areaId}
        <if test="diaoboIdList != null and diaoboIdList.size() > 0">
            AND s.id in
            <foreach item="diaoboId" collection="diaoboIdList" index="index" open="(" separator="," close=")">
                #{diaoboId}
            </foreach>
        </if>
        ORDER BY a.rank ASC
    </select>

    <select id="getProductLocationKC" resultMap="StockLocationKcVo">
        select k.locationId,k.locationCode,k.lcounts,k.ppriceid,p.product_name,p.barCode,p.product_color
        from dbo.stockLocationKc k with(nolock)
        left join dbo.productinfo p with(nolock) on k.ppriceid=p.ppriceid
        where 1=1
        <if test="areaId != null and areaId != 0">
            and k.areaid=#{areaId}
        </if>
        <if test="ppid != null and ppid != 0">
            and k.ppriceid=#{ppid}
        </if>
        <if test="locationCode != null and locationCode != ''">
            and k.locationCode=#{locationCode}
        </if>
    </select>

    <update id="stockLocationCheck">
        update dbo.stockLocationKc
        set pan_user=#{user},
            pan_count=#{count},
            pan_dtime=getdate()
        where locationCode = #{locationCode}
          and locationId = #{locationId}
          and areaid = #{areaId}
    </update>

    <select id="getDiaoboProductCount" resultType="com.jiuji.oa.afterservice.stock.vo.DiaoboSumVO">
        select sum(b.lcount) as totalCount, a.area, s.id
        from diaobo_sub s with(nolock)
        left join diaobo_basket b with(nolock) on s.id = b.sub_id
        left join areainfo a with(nolock) on s.toareaid = a.id
        where s.id in
            <foreach item="diaoboId" collection="diaoboIdList" index="index" open="(" separator="," close=")">
                #{diaoboId}
            </foreach>
        group by s.id, a.area order by s.id;
    </select>

    <select id="getDiaoboBasketBySubId" resultType="com.jiuji.oa.afterservice.stock.bo.DiaoboBasketInfoBO">
        select p.product_name as productName, p.product_color as productColor, b.lcount, b.ppriceid, b.inprice, b.id, isnull(inprice,0) as yaPrice,
        b.basket_id as basketId, p.costprice, p.OEMPrice
        from diaobo_basket b with(nolock)
        left join productinfo p with(nolock) on b.ppriceid=p.ppriceid
        where b.sub_id=#{subId} order by p.cid
    </select>

    <select id="getStockProductKc" resultType="com.jiuji.oa.afterservice.stock.vo.StockLocationKcVO">
        select k.areaid areaId,
        k.locationId locationId,
        k.locationCode locationCode,
        k.ppriceid ppid,
        k.lcounts lCounts,
        k.pan_user panUser,
        k.pan_count panCount,
        k.pan_dtime dTime,
        p.product_name productName,
        p.product_color productColor,
        p.barCode barCode
        from dbo.stockLocationKc k with (nolock)
        left join dbo.productinfo p with (nolock) on k.ppriceid = p.ppriceid
        where k.areaid = #{areaId} and k.lcounts > 0
        <if test="locationCode != null and locationCode != ''">
            and k.locationCode=#{locationCode}
        </if>
        <if test="ppid != null and ppid > 0">
            and k.ppriceid=#{ppid}
        </if>
        order by lcounts desc
    </select>

</mapper>
