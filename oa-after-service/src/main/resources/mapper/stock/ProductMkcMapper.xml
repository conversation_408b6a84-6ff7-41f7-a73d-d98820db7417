<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.stock.dao.ProductMkcMapper">
    <insert id="batchInsert" parameterType="java.util.ArrayList" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO product_mkc
        (ppriceid, inbeihuo, inbeihuoprice, imeidate, imei, inuser, kc_check, inprice, caigoulock, insourceid, areaid,
        origareaid, frareaid, voucherId )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.ppriceid},
            #{item.inbeihuo},
            #{item.inbeihuoprice},
            #{item.imeidate},
            #{item.imei},
            #{item.inuser},
            #{item.kcCheck},
            #{item.inprice},
            #{item.caigoulock},
            #{item.insourceid},
            #{item.areaid},
            #{item.origareaid},
            #{item.frareaid},
            #{item.voucherId}
            )
        </foreach>
    </insert>

    <select id="listByMkcId" resultType="com.jiuji.oa.afterservice.stock.po.ProductMkc">
        select
        id,
        ppriceid ,
        inprice ,
        insourceid2,
        insourceid,
        inbeihuoprice ,
        imei,
        pzid,
        imeidate,
        voucherId,
        protectPrice,
        fanli,
        modifyPrice,
        inner_price
        from product_mkc with(nolock)
        where id in
        <foreach collection="mkcIdList" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <sql id="shouhouMkcQuery">
        from batch_return_mkc mkc with(nolock)
        left join product_mkc k with(nolock) on mkc.to_mkc_id=k.id
        left join productinfo p with(nolock) on k.ppriceid=p.ppriceid
        left join areainfo a with(nolock) on a.id=k.areaid
        left JOIN dbo.CashbackSettlementItem pp with(nolock) ON k.id= pp.MkcId
        where 1=1
        <if test="req.batchZxFlag != null and req.batchZxFlag==1">
            and k.kc_check=6
        </if>
        <if test="req.batchZxFlag != null and req.batchZxFlag==2">
            and k.kc_check in (3,5)
        </if>
        and a.authorizeid = #{req.authorizeId}
        <if test="req.imei != null and req.imei!=''">
            and k.imei=#{req.imei}
        </if>
        <if test="req.productName !=null and req.productName != ''">
            and p.product_name like concat('%',#{req.productName},'%')
        </if>
        <if test="req.pid != null and req.pid !=0">
            and p.productid=#{req.pid}
        </if>
        <if test="req.ppid != null and req.ppid!=0">
            and p.ppriceid=#{req.ppid}
        </if>
        <if test="req.mkcId != null and req.mkcId != 0">
            and k.id= #{req.mkcId}
        </if>
        <if test="req.orderId !=null and req.orderId !=0">
            and k.orderid =#{req.orderId}
        </if>
        <if test="req.imeiUser != null and req.imeiUser!= ''">
            and k.inuser =#{req.imeiUser}
        </if>
        <if test="req.areaIds!=null and req.areaIds.size>0">
            and k.areaid in
            <foreach collection="req.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>
        <if test="req.isXc !=null">
            and
            <if test="!req.isXc">not</if>
            exists( select 1 from xc_mkc x with(nolock) where x.mkc_id=k.id )
        </if>
        <if test="req.isGh !=null">
            and
            <if test="!req.isGh">not</if>
            exists( select 1 from gh_mkc AS gh with(nolock) where gh.mkc_id=k.id )
        </if>
        <if test="req.isZx !=null">
            and
            <if test="!req.isZx">not</if>
            exists( select 1 from zx_mkc x with(nolock) where x.mkc_id=k.id )
        </if>
        <if test="req.isYj !=null and req.isYj">
            and isnull(k.mouldFlag,0)=1
        </if>
        <if test="req.isYj !=null and !req.isYj">
            and isnull(k.mouldFlag,0)=0
        </if>

        <if test="req.cid!=null and req.cid!=0">
            and exists( select 1 from f_category_children(#{req.cid}) f where f.id=p.cid )
        </if>
        <if test="req.brandId !=null and req.brandId !=0">
            and p.brandID = #{req.brandId}
        </if>
        <if test="req.isSale !=null and req.isSale">
            and exists(select 1 from office..mobileMkc m with(nolock) where k.id=m.mkc_id)
        </if>
        <if test="req.inSourceId !=null and req.inSourceId !=0">
            and exists(select 1 from dbo.f_qudao_children(#{req.inSourceId}) f where f.id=k.insourceid2 )
        </if>
        <if test="req.timeType != null and req.timeType==4">
            <if test="req.startTime != null">
                and mkc.to_mkc_time <![CDATA[ >= ]]> #{req.startTime}
            </if>
            <if test="req.endTime != null">
                and mkc.to_mkc_time <![CDATA[ <= ]]> #{req.endTime}
            </if>
        </if>
    </sql>

    <select id="countShouhouMkc" resultType="java.lang.Integer">
        select count(1)
        <include refid="shouhouMkcQuery">
        </include>
    </select>

    <select id="pageShouhouMkc" resultType="com.jiuji.oa.afterservice.stock.vo.ShouhouMkcPageVO">
        select
        mkc.id batchReturnMkcId,
        mkc.to_mkc_time toMkcTime,
        a.area area,
        k.areaid areaId,
        k.orderid orderId,
        p.product_name productName,
        p.product_color productColor,
        k.basket_id basketId,
        k.kc_check kcCheckCode,
        k.imei imei,
        k.inuser inUser,
        k.insourceid2 inSourceId2,
        k.staticPrice statisticalPrice
        <include refid="shouhouMkcQuery">
        </include>
        order by k.id desc
        OFFSET #{current} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <update id="afterBatchZx">
        update product_mkc
        set product_mkc.kc_check=3
        where id in
        <foreach collection="mkcIds" item="mkcId" open="(" separator="," close=")">
            #{mkcId}
        </foreach>
        and kc_check=6
    </update>

    <select id="getMkcIdByImei" resultType="java.lang.Integer">
        select id from product_mkc with(nolock) where imei is not null
        <if test="imei != null and imei != ''">
            and imei = #{imei}
        </if>
        order by id desc
    </select>


    <!--    只查看自己地区的订单-->
    <sql id="ownAreaWhereAnd">
        <if test="req.userOwnAreaIds != null and req.userOwnAreaIds.size > 0">
            and exists(SELECT 1 FROM dbo.F_SPLIT(
            <foreach collection="userOwnAreaIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
            ,',') f WHERE f.split_value =
            CAST(k.areaid AS VARCHAR(20)))
        </if>
    </sql>


    <sql id="areaCodeTop">
        <if test="req.top20 == true">
            TOP 20
        </if>
    </sql>

    <sql id="smallAndRepairSql">
        from kcSnaps kc with (nolock)
        left join productinfo p with (nolock) on kc.ppriceid = p.ppriceid
        <if test="req.cids !=  null and req.cids != ''">
            right join (SELECT id from f_category_children(#{req.cids})) f
            on p.cid= f.id
        </if>
        left join areainfo a with (nolock) on kc.areaid = a.id
        left join brand bd with (nolock) on bd.id = p.brandID
        left join Ok3w_qudao ok with (nolock) on ok.id = kc.insourceid2

        where kc.dtime = #{req.date}
        <if test="req.stockQueryEnum != null">
            <if test="req.stockQueryEnum.code == 0">
                and p.ismobile1 = 1
            </if>
            <if test="req.stockQueryEnum.code == 1">
                and p.ismobile1 = 0
            </if>
            <if test="req.stockQueryEnum.code == 2">
                and exists( select 1 from f_category_children(23) f where f.id=p.cid )
            </if>
        </if>

        <if test="req.areaLevelEnum != null and req.areaLevelEnum.code >= 0">
            AND exists (select 1 from dbo.areainfo ar with(nolock) where kc.areaid = ar.id and ar.level1=
            (#{req.areaLevelEnum.code} + 1))
        </if>
        <if test="req.stockQueryByEnum != null and req.mainkey != null and req.mainkey != ''">
            <if test="req.stockQueryByEnum.code == 0">
                and kc.ppriceid =#{req.mainkey}
            </if>
            <if test="req.stockQueryByEnum.code == 1">
                and p.productid =#{req.mainkey}
            </if>
            <if test="req.stockQueryByEnum.code == 2">
                and p.product_name like CONCAT('%',#{req.mainkey},'%')
            </if>
        </if>
        <if test="req.areaIds != null and req.areaIds.size > 0 ">
            and kc.areaid in
            <foreach collection="req.areaIds" item="areaId" open="(" separator="," close=")">
                #{areaId}
            </foreach>
        </if>

    </sql>
    <sql id="reCidWhere">
        <if test="req.cids !=  null and req.cids != ''">
            and exists( select 1 from f_category_children(#{req.cids}) f where f.id=p.cid )
        </if>
    </sql>

    <sql id="sonGroupBy">
        GROUP BY kc.areaid ,
        a.area,
        kc.ppriceid ,
        p.product_name ,
        p.product_color ,
        p.memberprice,
        ok.company,
        p.viewsWeek ,
        p.viewsweekr ,
        bd.name
    </sql>

    <select id="aggregateCountSmallAndRepair" resultType="java.lang.Integer">
        SELECT COUNT(1) from (
        SELECT kc.areaid
        <include refid="smallAndRepairSql"/>
        <include refid="sonGroupBy"/>
        ) x where 1=1
    </select>

    <select id="getStockHQSmallAndRepair" resultType="com.jiuji.oa.afterservice.stock.vo.AggregatedStockVO">
        select * from (
        SELECT kc.areaid areaId,
        a.area areaName,
        kc.ppriceid ppid,
        p.product_name pName,
        p.product_color specs,
        p.viewsWeek ,
        p.viewsweekr ,
        p.memberprice memberPrice,
        bd.name brand,
        isnull(ok.company, #{req.vendor}) vendor,
        sum(kc.kcCount) stockCount,
        sum(kc.kcPrices) stockAmount,
        sum(kc.wayCount) transportCount,
        sum(kc.wayPrices) transportAmount,
        sum(kc.orderCount) debtCount,
        sum(kc.orderPrices) debtAmount,
        sum(kc.allCount) totalCount,
        sum(kc.allPrices) totalAmount
        <include refid="smallAndRepairSql"/>
        <include refid="sonGroupBy"/>
        ) x
        order by x.viewsWeek desc , x.viewsweekr desc , x.memberPrice asc
        <if test="req.pageQuery == true">
            OFFSET #{offset} ROWS FETCH NEXT #{pSize} ROWS ONLY
        </if>
    </select>

    <select id="sumAll" resultType="com.jiuji.oa.afterservice.stock.vo.AggregatedStockVO">
        SELECT
        sum(kc.kcCount) stockCount,
        sum(kc.kcPrices) stockAmount,
        sum(kc.wayCount) transportCount,
        sum(kc.wayPrices) transportAmount,
        sum(kc.orderCount) debtCount,
        sum(kc.orderPrices) debtAmount,
        sum(kc.allCount) totalCount,
        sum(kc.allPrices) totalAmount
        <include refid="smallAndRepairSql"/>
    </select>

    <select id="company" resultType="java.lang.String">
        select top 1 sysConfig.value from sysConfig with(nolock) where dsc = '公司主体'
    </select>
    <select id="queryInStock" resultType="java.lang.Integer">
        select id from  batch_return_mkc with(nolock)
        where id in
        <foreach collection="batchReturnMkcId" item="mkcId" open="(" separator="," close=")">
            #{mkcId}
        </foreach>
        and voucher_id is not null
    </select>

    <select id="getMkcKindsValue" resultType="java.lang.String">
        select kinds from mkc_dellogs with(nolock) where id = #{id}
    </select>

    <select id="getMkcDealLogsInfo" resultType="com.jiuji.oa.afterservice.stock.vo.res.MkcDelQueryRes">
        select l.id,l.mkc_id,l.inuser as inUser,l.dtime as dTime,isnull(l.check1,0),l.check1dtime as check1Time,l.check1user as check1User,isnull(l.check2,0),
        l.check2dtime as check2Time,l.check2user as check2User,l.comment,l.kinds,isnull(l.price1,0),isnull(l.price2,0),l.ppriceid as newPpid,l.areaid as areaId,a.area,
        l.frareaid as frAreaId,p.product_name,k.imei,k.kc_check,k.inbeihuoprice inBeiHuoPrice,k.inbeihuodate inBeiHuoDate,k.ppriceid as ppid,isnull(k.caigoulock,0) as caiGouLock,
        l.youhuiPrice as youHuiPrice,l.check1,l.check2
        from mkc_dellogs l with(nolock)
        left join product_mkc k with(nolock) on l.mkc_id=k.id
        left join productinfo p with(nolock) on p.ppriceid=k.ppriceid
        left join areainfo a with(nolock) on l.areaid = a.id
        where l.id=#{id}
    </select>

    <select id="getRecoverMkcDealLogsInfo" resultType="com.jiuji.oa.afterservice.stock.vo.res.MkcDelQueryRes">
        select l.id,l.mkc_id,l.inuser as inUser,l.dtime as dTime,isnull(l.check1,0),l.check1dtime as check1Time,l.check1user as check1User,isnull(l.check2,0),
        l.check2dtime as check2Time,l.check2user as check2User,l.comment,l.kinds,isnull(l.price1,0),isnull(l.price2,0),l.ppriceid as newPpid,l.areaid as areaId,a.area,
        l.frareaid as frAreaId,p.product_name,k.imei,k.mkc_check as kcCheck,k.inprice+isnull(k.addprice,0) inBeiHuoPrice,k.intime inBeiHuoDate,k.ppriceid as ppid,1 as caiGouLock,
        l.youhuiPrice as youHuiPrice,l.check1,l.check2
        from mkc_dellogs l with(nolock)
        left join recover_mkc k with(nolock) on l.mkc_id=k.id
        left join productinfo p with(nolock) on p.ppriceid=k.ppriceid
        left join areainfo a with(nolock) on l.areaid = a.id
        where l.id=#{id}
    </select>

    <sql id="kindsCondition">
        <if test="kinds == 'zf'">
            and kc_check in(2,3,6,10,13) and basket_id is null
        </if>
        <if test="kinds == 'h1'">
            and kc_check in(2,3)
        </if>
        <if test="kinds == 'xc'">
            and kc_check in(2,3,6)
        </if>
        <if test="kinds == 'h2'">
            and kc_check in(6,13)
        </if>
        <if test="kinds == 'h3'">
            and mkc_check in(6)
        </if>
        <if test="kinds == 'h4'">
            and mkc_check in(3)
        </if>
        <if test="kinds == 'h5'">
            and mkc_check in(3)
        </if>
        <if test="kinds == 'h6'">
            and mkc_check in(3)
        </if>
        <if test="kinds == 'zx'">
            and kc_check in(3)
        </if>
        <if test="kinds == 'yj'">
            and kc_check in(3)
        </if>
        <if test="kinds == 'xcz'">
            and kc_check in(3)
        </if>
    </sql>

    <select id="getRecoverMkcDealLogCheck" resultType="com.jiuji.oa.afterservice.stock.vo.res.MkcDelQueryRes">
        select  p.product_name+' '+isnull(product_color,'') product_name, k.imei ,k.mkc_check kc_check ,k.inprice+isnull(k.addprice,0) inBeiHuoPrice,k.intime inBeiHuoDate ,
        k.ppriceid ppid ,d.kinds ,d.id as mkcDealId ,1 as caiGouLock,k.checkCommentUser
        from dbo.recover_mkc k with(nolock)
        left join productinfo p with(nolock) on p.ppriceid = k.ppriceid
        left join ( select  kinds ,areaid ,mkc_id ,id from mkc_dellogs with(nolock) where kinds in ('h3','h4','h5','h6') and check2 is null) d on k.id = d.mkc_id
        where k.to_basket_id is null and k.areaid= #{curAreaId}
        and k.id=#{mkcId}
       <include refid="kindsCondition"></include>
    </select>

    <select id="getRecoverMkcDealLogXcjCheck" resultType="com.jiuji.oa.afterservice.stock.vo.res.MkcDelQueryRes">
        select p.product_name,k.imei,k.kc_check,k.inbeihuoprice inBeiHuoPrice,k.inbeihuodate inBeiHuoDate,k.ppriceid ppid,d.kinds,d.id as mkcDealId,
        isnull(k.caigoulock,0) as caiGouLock,k.frareaid frAreaId
        from product_mkc k with(nolock)
        left join productinfo p with(nolock) on p.ppriceid=k.ppriceid
        left join (seLect kinds,area,mkc_id,id from mkc_dellogs with(nolock) where kinds in('zf','h1','h2','xc','gh','zx','yj') and check2 is null) d on k.id=d.mkc_id
        where k.kc_check in(2,3,6,10,13)
        <if test="kinds == 'xc' or kinds == 'zx'">
            and k.basket_id is null
        </if>
        and k.areaid= #{curAreaId} and k.id=#{mkcId}
        <include refid="kindsCondition"></include>
    </select>

    <select id="getInPriceByPpid" resultType="com.jiuji.oa.afterservice.stock.vo.res.MkcInpriceInfoRes">
        select top 1 inbeihuoprice as inBeiHuoPrice,isnull(inbeihuodate,'') as inBeiHuoDate,ppriceid as ppid from product_mkc with(nolock)
        where insourceid != 5 and inbeihuoprice is not null and inbeihuodate is not null and inbeihuoprice > 0 and ppriceid = #{ppid}
    </select>

    <update id="updateShouhouReturnCbShzcPrice">
        update shouhou_returnCb set shzcDate=GETDATE(),
        shzcPrice=(SELECT TOP 1 memberprice FROM productinfo p with(nolock) where p.ppriceid = shouhou_returnCb.ppid)
        where mkc_id = #{mkcId}
    </update>

    <select id="getProductKcCheckStatus" resultType="com.jiuji.oa.afterservice.stock.bo.ProductKcCheckStatus">
        SELECT k.kc_check,dbo.getMobileType(p.cid) mobileType FROM product_mkc k with(nolock) left join dbo.productinfo p with(nolock) on p.ppriceid = k.ppriceid WHERE id=#{mkcId}
    </select>

    <select id="getSubIdByMkcId" resultType="java.lang.Integer">
        SELECT top 1 b.sub_id FROM dbo.shouhou a with(nolock) LEFT JOIN dbo.shouhou b with(nolock) ON a.fromshouhouid=b.id WHERE a.mkc_id=#{mkcId}
    </select>

    <select id="getShouHouBasicInfoByMkcId" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        select  * from shouhou with(nolock) where isnull(isquji,0)=0 and dyjid is null and mkc_id=#{mkcId} and isnull(ishuishou,0)=0
    </select>

    <select id="checkTuiHuanByShouHouId" resultType="java.lang.Integer">
        select count(1) from shouhou_tuihuan with(nolock) where tuihuan_kind in(1,2,3,4) and isdel=0 and check2 is null and shouhou_id = #{shouhouId}
    </select>


    <select id="checkToareaByShouHouId" resultType="java.lang.Integer">
        seLect count(1) from shouhou_toarea with(nolock) where [check]=0 and shouhou_id = #{shouhouId}
    </select>

    <select id="LoadImeiThreeMonthCache" resultType="java.lang.Integer">
        select id from dbo.product_mkc with(nolock)  where imei = #{imei} and dtime between #{startTime} and #{endTime}
    </select>

    <update id="updateShouhouMkc">
        update product_mkc set kc_check=6 where kc_check=3 and areaid=#{areaId} and basket_id is null and id=#{mkcId}
    </update>
</mapper>
