<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.stock.dao.StockLocationMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.stock.po.StockLocation">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="areaid" property="areaid"/>
        <result column="isdel" property="isdel"/>
        <result column="inuser" property="inuser"/>
        <result column="dtime" property="dtime"/>
        <result column="rank" property="rank"/>
        <result column="comment" property="comment"/>
        <result column="stockArea" property="stockArea"/>
        <result column="tunnel" property="tunnel"/>
        <result column="shelfGroup" property="shelfGroup"/>
        <result column="shelfLayers" property="shelfLayers"/>
        <result column="locations" property="locations"/>
    </resultMap>

    <select id="getAllLocations" resultMap="BaseResultMap">
        select *
        from stockLocation with (nolock)
    </select>

    <select id="chkAreaLocationCount" resultType="java.lang.Integer">
        select count(1)
        from stockLocation with (nolock)
        where areaid = #{areaId}
    </select>

    <update id="stockLocationCheck">
        update dbo.stockLocationKc
        set pan_user=null,
            pan_count=null,
            pan_dtime=null
        where locationCode = #{locationCode}
          and locationId = #{locationId}
          and areaid = #{areaId}
    </update>

    <select id="getKCPanSubId" resultType="java.lang.Integer">
        select id
        from kc_pansub with(nolock)
        where DATEDIFF(D, dtime, GETDATE()) = 0
          and areaid = #{query.areaId}
          and kinds = #{query.kinds}
          and type = #{query.type}
          and isnull(pankind, 0) = 0
          and locationCode = #{query.locationCode}
          and locationId = #{query.locationId}
    </select>

    <insert id="insertKCPanSub">
        insert into kc_pansub (dtime, areaid, kinds, inuser, comment, pankind, type, locationCode, locationId)
        output inserted.id
        values ( getdate()
               , #{query.areaId}
               , #{query.kinds}
               , #{query.inUser}
               , #{query.comment}
               , #{query.panKind}
               , #{query.type}
               , #{query.locationCode}
               , #{query.locationId})
    </insert>

    <update id="updateKCPanSub">
        update kc_pansub
        set dtime=getdate(),
            inuser=#{query.inUser},
            comment=#{query.comment}
        where id = #{id}
    </update>

    <sql id="sonSelectKC">
        select k.ppriceid, k.lcounts lcount, isnull(k.pan_count, 0) pan_count
        from stockLocationKc k with(nolock)
        where locationCode = #{query.locationCode}
          and locationId = #{query.locationId}
          and (k.pan_count is not null or k.lcounts > 0)
    </sql>

    <update id="zhuiJiaUpdate">
        update k
        set k.pan_count=(k.pan_count + t.pan_count),
        k.lcount=t.lcount
        from kc_panbasket k,
        (<include refid="sonSelectKC"/>) t
        where k.ppriceid = t.ppriceid
        and k.pan_id = #{subId}
    </update>

    <insert id="zhuiJiaInsert">
        insert into kc_panbasket(pan_id, ppriceid, pan_count, lcount)
        select #{subId}, t.ppriceid, t.pan_count, t.lcount
        from (<include refid="sonSelectKC"/>) t
        left join (select * from kc_panbasket with(nolock) where pan_id = #{subId}) k on t.ppriceid = k.ppriceid
        where k.ppriceid is null
    </insert>

    <update id="fuGaiUpdate">
        update k
        set k.pan_count=t.pan_count,
        k.lcount=t.lcount
        from kc_panbasket k with(nolock),
        (<include refid="sonSelectKC"/>) t
        where k.ppriceid = t.ppriceid
        and k.pan_id = #{subId}
    </update>

    <insert id="fuGaiInsert">
        insert into kc_panbasket(pan_id, ppriceid, pan_count, lcount)
        select #{subId}, t.ppriceid, t.pan_count, t.lcount
        from (<include refid="sonSelectKC"/>) t
        left join (select * from kc_panbasket with(nolock) where pan_id = #{subId}) k on t.ppriceid = k.ppriceid
        where k.ppriceid is null
    </insert>

    <delete id="dayDelete">
        delete
        from kc_panbasket
        where pan_id = #{subId}
    </delete>

    <insert id="dayInsert">
        insert into kc_panbasket(pan_id, ppriceid, pan_count, lcount)
        select #{subId}, ppriceid, pan_count, lcount
        from (<include refid="sonSelectKC"/>) t
    </insert>

    <insert id="allInsert">
        insert into kc_panbasket(pan_id, ppriceid, pan_count, lcount)
        select #{subId}, ppriceid, pan_count, lcount
        from (<include refid="sonSelectKC"/>) t
    </insert>

    <sql id="keyKinds">
        <if test="req.kinds != null and req.kinds == 0">
            and p.product_name like CONCAT('%',#{req.key},'%')
        </if>
        <if test="req.kinds != null and req.kinds == 1">
            and p.productid=#{req.key}
        </if>
        <if test="req.kinds != null and req.kinds == 2">
            and p.ppriceid=#{req.key}
        </if>
        <if test="req.kinds != null and req.kinds == 3">
            and k.locationCode like CONCAT('%',#{req.key},'%')
        </if>
        <if test="req.kinds != null and req.kinds == 4">
            and p.barCode like CONCAT('%',#{req.key},'%')
        </if>
    </sql>

    <sql id="stockCheckState">
        <if test="req.stockCheckState != null and req.stockCheckState == 0">
            and pan_count is null
        </if>
        <if test="req.stockCheckState != null and req.stockCheckState == 1">
            and pan_count is not null
        </if>
        <if test="req.stockCheckState != null and req.stockCheckState == 2">
            and pan_count &gt; lcounts
        </if>
        <if test="req.stockCheckState != null and req.stockCheckState == 3">
            and pan_count &lt; lcounts
        </if>
    </sql>

    <select id="listStockLocation" resultType="com.jiuji.oa.afterservice.stock.vo.StockLocationKcVO">
        select k.areaid areaId,
        k.locationId locationId,
        k.locationCode locationCode,
        k.ppriceid ppid,
        k.lcounts lCounts,
        k.pan_user panUser,
        k.pan_count panCount,
        k.pan_dtime dTime,
        p.product_name productName,
        p.product_color productColor,
        p.barCode barCode
        from dbo.stockLocationKc k with (nolock)
        left join dbo.productinfo p with (nolock) on k.ppriceid = p.ppriceid
        where k.areaid = #{req.areaId}
        <if test="req.key != null and req.key != ''">
            <include refid="keyKinds"/>
        </if>
        <if test="req.stockCheckState != null">
            <include refid="stockCheckState"/>
        </if>
        order by lcounts desc
        OFFSET #{req.current} ROWS FETCH NEXT #{req.size} ROWS ONLY
    </select>

    <select id="countStockLocation" resultType="java.lang.Integer">
        select count(1)
        from dbo.stockLocationKc k with (nolock)
        left join dbo.productinfo p with (nolock) on k.ppriceid = p.ppriceid
        where k.areaid = #{req.areaId}
        <if test="req.key != null and req.key != ''">
            <include refid="keyKinds"/>
        </if>
        <if test="req.stockCheckState != null">
            <include refid="stockCheckState"/>
        </if>
    </select>

    <select id="listCurLocation" resultType="com.jiuji.oa.afterservice.stock.vo.CurrentLocationVO">
        select id, lcounts lCount
        from dbo.stockLocationKc with(nolock)
        where areaid = #{req.areaId}
          and locationId = #{req.locationId}
          and ppriceid = #{req.ppid}
          and locationCode = #{req.locationCode}
    </select>


    <update id="updateStockLCounts">
        update dbo.stockLocationKc
        set lcounts=lcounts + #{lCount}
        output Inserted.lcounts
        where id = #{stockLocationKcId}
    </update>

    <insert id="insertStockLCounts">
        insert into dbo.stockLocationKc(areaid, locationId, ppriceid, lcounts, locationCode)
        output Inserted.lcounts
        values (#{req.areaId}, #{req.locationId}, #{req.ppid}, #{req.counts}, #{req.locationCode})
    </insert>

    <insert id="insertLogs">
        insert into dbo.stockLocationKcLogs (areaid, ppriceid, locationId, counts, lastCounts, comment, dtime, inuser,
                                             basket_id, shouhou_id, locationCode)
        values (#{req.areaId},
                #{req.ppid},
                #{req.locationId},
                #{req.counts},
                #{req.lastCount},
                #{req.comment},
                getdate(),
                #{req.inUser},
                #{req.basketId},
                #{req.shouHouId},
                #{req.locationCode})
    </insert>

    <select id="naHuoList" resultType="com.jiuji.oa.afterservice.stock.vo.PickUpProductVO">
        select b.sub_id,
        b.basket_id basketId,
        b.ismobile isMobile,
        b.basket_count count,
        n.orderidd orderId,
        n.adduser,
        n.addtime,
        n.id naHuoId,
        p.product_name productName,
        p.product_color productColor,
        p.ppriceid ppid,
        p.productid,
        k.orderid orderId,
        k.imei,
        s.sub_check,
        s.trader,
        k.id as mkcId,
        pk.number number,
        case when isnull(x.mkc_id, 0) = 0 then 0 else 1 end isxc,
        p.barCode barCode,
        b.type,
        s.subtype,
        d.id as displayId,
        n.toarea as toArea
        from nahuoduilie n with (nolock)
        INNER join basket b with (nolock)
        on n.basket_id = b.basket_id
        LEFT JOIN dbo.sub s with (nolock)
        ON s.sub_id = b.sub_id
        LEFT join productinfo p with (nolock)
        on b.ppriceid = p.ppriceid
        left join product_kc pk with (nolock) on pk.areaid = #{area} and pk.ppriceid = b.ppriceid
        LEFT join
        product_mkc k with (nolock) on k.basket_id = b.basket_id
        left join xc_mkc x with (nolock) on x.mkc_id = k.id
        left join dbo.displayProductInfo d with (nolock) on b.basket_id = d.basket_id
        WHERE ISNULL(b.isdel, 0) = 0
        and s.sub_check = 1
        and n.areaid = #{area}
        <if test="isMobile != null and isMobile == 1">
            and n.isna=0
        </if>
        <if test="isMobile != null and isMobile == 0">
            and n.isna=5 and p.cid != 297
        </if>
        <if test="isMobile != null and isMobile == -1">
            and n.isna=9 and s.sub_check=1 and p.cid ! =297
        </if>
        <if test="isMobile != null and isMobile == -2">
            and n.isna=15 and s.sub_check=1 and k.kc_check = 3
        </if>
        order by n.addtime asc
    </select>
</mapper>
