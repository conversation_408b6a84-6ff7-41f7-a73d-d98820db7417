<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.stock.dao.RecoverMkcMapper">

    <!-- 根据mkcId查询库存信息 -->
    <select id="getRecoverMkcVOByMkcId" resultType="com.jiuji.oa.afterservice.stock.vo.RecoverMkcVO">
        SELECT 
            id,
            ppriceid,
            imei,
            areaid, 
            k.inprice + isnull(k.addprice, 0) as inbeihuoprice 
        FROM 
            recover_mkc k 
        WHERE 
            k.id = #{mkcId} 
            AND k.mkc_check = 3 
            AND ISNULL(k.issalf, 0) = 0 
            AND ISNULL(k.isauction, 0) = 0
            AND NOT EXISTS (
                SELECT 1 
                FROM dbo.recover_helpSell_product hp WITH(nolock) 
                WHERE hp.mkc_id = k.id
            )
    </select>

</mapper>
