<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.stock.dao.ProductKcMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.stock.po.ProductKc">
        <id column="id" property="id" />
        <result column="lcount" property="lcount" />
        <result column="pan_user" property="panUser" />
        <result column="pan_dtime" property="panDtime" />
        <result column="pan_count" property="panCount" />
        <result column="kinds" property="kinds" />
        <result column="area" property="area" />
        <result column="alarm_count" property="alarmCount" />
        <result column="inprice" property="inprice" />
        <result column="ppriceid" property="ppriceid" />
        <result column="alarm_count1" property="alarmCount1" />
        <result column="number" property="number" />
        <result column="orderCount" property="orderCount" />
        <result column="leftCount" property="leftCount" />
        <result column="areaid" property="areaid" />
        <result column="inprice1" property="inprice1" />
    </resultMap>
    <select id="getKcCount" resultType="java.lang.Integer">
         select leftcount from product_kc with(nolock) where ppriceid =#{ppid} and areaid=#{areaId}
    </select>

    <update id="lockKc">
        update product_kc set ordercount=isnull(ordercount,0)+#{orderCount} where  ppriceid =#{ppid} and areaid=#{areaId}
    </update>

    <update id="unlockKc">
        update product_kc set ordercount=iif(isnull(ordercount,0)-1>=0,isnull(ordercount,0)-1,0) where ppriceid =#{ppid} and areaid=#{areaId}
    </update>

    <select id="lockKcNew" resultType="com.jiuji.oa.afterservice.stock.bo.LockKcInfoBO">
        update product_kc set ordercount=isnull(ordercount,0)+#{orderCount} output inserted.orderCount new_value,DELETED.orderCount old_value,inserted.id where  ppriceid =#{ppid} and areaid=#{areaId}
        and lcount >= (case when isnull(orderCount,0)+ #{orderCount} &lt; #{orderCount} then #{orderCount} else isnull(orderCount,0)+#{orderCount} end)
    </select>

    <select id="updateProductKcNew"  resultType="com.jiuji.oa.afterservice.stock.bo.LockKcInfoBO">
        update dbo.product_kc set orderCount=iif(isnull(orderCount,0)-#{count}>=0,isnull(orderCount,0)-#{count},0) output inserted.orderCount new_value,DELETED.orderCount old_value,inserted.id where id=#{kcid}
    </select>

    <select id="unlockKcNew" resultType="com.jiuji.oa.afterservice.stock.bo.LockKcInfoBO">
        update product_kc set ordercount=iif(isnull(ordercount,0)-1>=0,isnull(ordercount,0)-1,0) output inserted.orderCount new_value,DELETED.orderCount old_value,inserted.id where ppriceid =#{ppid} and areaid=#{areaId}
    </select>
    <sql id="leftKcAuthQuery">
        and exists(
                    select 1 from areainfo a with(nolock) where a.id = ${areaIdKey} and a.kind1=1 and a.ispass=1
                    <!-- 配置启用授权隔离或者是九机 -->
                    <if test="isAuthPart || xtenant &lt; 1000">
                        and a.authorizeid = #{authorizeId}
                    </if>
                    <!--智乐方-->
                    <if test="shortXtenant == 1">
                        <choose>
                            <when test="departPathList.contains(1135)">and </when>
                            <otherwise>and not </otherwise>
                        </choose>
                        exists(select 1 from dbo.f_find_departChildNew(1135) f where f.id=a.depart_id  )
                    </if>
                )
    </sql>

    <select id="getProductLeftKcInfoByPpid" resultType="com.jiuji.oa.afterservice.bigpro.bo.productkc.ProductLeftKcInfoBo">
        select sum(k.lcount) lcounts,sum(isnull(k.inprice,0)*k.lcount) inprices from dbo.product_kc k with(nolock)
        where ppriceid= #{ppid} and lcount>0
        <include refid="leftKcAuthQuery">
            <property name="areaIdKey" value="k.areaid"/>
        </include>
    </select>

    <select id="getDiaoBoBasketLeftKcInfoByPpid" resultType="com.jiuji.oa.afterservice.bigpro.bo.productkc.ProductLeftKcInfoBo">
        select sum(b.lcount) lcounts,sum(isnull(b.lcount*b.inprice,0)) inprices
        from dbo.diaobo_basket b with(nolock) left join dbo.diaobo_sub s with(nolock)  on b.sub_id=s.id
        where ppriceid=#{ppid} and b.lcount>0 and b.inprice>0 and s.stats in (2,6,5,3)
        <include refid="leftKcAuthQuery">
            <property name="areaIdKey" value="s.toareaid"/>
        </include>
    </select>

    <select id="hasProductMKC" resultType="java.lang.Integer">
        select 1 from product_mkc with(nolock) where kc_check in(6,13) and basket_id is null and (imei=#{imei}
        <if test='mkcId!=null and mkcId>0'>
            or mkc_id= #{mkcId}
        </if>)
    </select>
    <select id="listKcCount" resultType="com.jiuji.oa.afterservice.stock.po.ProductKc">
        select ppriceid,leftcount from product_kc with(nolock)
        <where>
            ppriceid in
            <foreach collection="ppidList" item="ppid" open="(" close=")" separator="," >
                #{ppid}
            </foreach>
            and areaid=#{areaId}
        </where>
    </select>

    <update id="updateDcCostPrice">
        update k set k.inprice=#{inprice} from product_kc k where ppriceid=#{ppid}
        <include refid="leftKcAuthQuery">
            <property name="areaIdKey" value="k.areaid"/>
        </include>
    </update>

    <sql id="basketIdsForEach">
        <foreach collection="basketIds" item="basketId" open="(" close=")" separator=",">
            #{basketId}
        </foreach>
    </sql>
    <select id="listStockStatusCount" resultType="com.jiuji.oa.afterservice.stock.po.StockStatusCountBo">
        select distinct coalesce(c.basket_id, t.basket_id, cc.basket_id, dd.basket_id) as basketId
        ,isnull(c.lcount,0) beihuoCount,case when t.basket_id is not null and isnull(t.isDone,0)=0 then t.lcount else 0 end beiCount
        ,case when t.basket_id is not null and isnull(t.isDone,0)=1 then t.lcount else 0 end takeCount
        ,isnull(cc.count_,0) as cgCount,isnull(dd.count_,0) as ztCount,isnull(dd.count1,0) as dbCount
        from (select sum(lcount) lcount,basket_id from caigouinfo with(nolock)
                      where isnull([type],0)=1 and basket_type= #{basketType} and basket_id in <include refid="basketIdsForEach"/>
                        group by basket_id
             ) c
        <choose>
            <when test="basketType == 0">
                full outer join (select lcount,basket_id,o.isDone from dbo.basket_other o with(nolock)
                                              where o.basket_id in <include refid="basketIdsForEach"/>
                                ) t on 1=1
            </when>
            <otherwise>
                full outer join (select o.lock_count lcount,o.basket_id,o.isDone from dbo.product_kc_lock_info o with(nolock)
                                         where o.is_del =0 and o.basket_type= #{basketType} and o.basket_id in <include refid="basketIdsForEach"/>
                                 ) t on 1=1
            </otherwise>
        </choose>
        full outer join (select cb.basket_id,sum(cb.lcount) as count_ from dbo.caigou_basket cb with(nolock) left join dbo.caigou_sub cs with(nolock) on cb.sub_id=cs.id
                                where cb.basket_type= #{basketType} and cb.basket_id in <include refid="basketIdsForEach"/> and cs.stats in (0,1,2)
                                group by cb.basket_id
                        ) cc on 1=1
        full outer join (select db.basket_id,sum(case when ds.stats=3 then db.lcount else 0 end) as count_,sum(db.lcount) as count1
                                from dbo.diaobo_basket db with(nolock) left join dbo.diaobo_sub ds with(nolock) on db.sub_id=ds.id
                                where db.basket_type= #{basketType} and db.basket_id in <include refid="basketIdsForEach"/>
                                  and ds.stats in (1,2,3,6,5)
                                group by db.basket_id
                        ) dd on 1=1
    </select>
</mapper>
