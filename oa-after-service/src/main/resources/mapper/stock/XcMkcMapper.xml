<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.stock.dao.XcMkcMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.stock.po.XcMkc">
        <id column="mkc_id" property="mkcId" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="area" property="area" />
        <result column="isLock" property="isLock" />
        <result column="areaid" property="areaid" />
        <result column="youhuiPrice" property="youhuiPrice" />
    </resultMap>
    <select id="getLockXcMkcByMkcId" resultMap="BaseResultMap">
        select mkc_id from dbo.xc_mkc with(nolock) where mkc_id=#{mkcId} and isnull(isLock,0)=1
    </select>
    <select id="getXcDescription" resultType="java.lang.String">
        select top 1 des from  paimai with(nolock) where mkc_id=#{mkcId} and pmtype=1 and isdel=0 order by id desc
    </select>

</mapper>
