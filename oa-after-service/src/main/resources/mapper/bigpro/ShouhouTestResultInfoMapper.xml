<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouTestResultInfoMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouTestResultInfo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="shouhouId" column="shouhou_id" jdbcType="INTEGER"/>
            <result property="testResult" column="test_result" jdbcType="BIT"/>
            <result property="testType" column="test_type" jdbcType="TINYINT"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,shouhou_id,test_result,
        test_type,remark,inuser,
        create_time,update_time,is_delete,
        shouhou_test_info_rv
    </sql>

    <resultMap id="shouhouTestResultMap" type="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestResultRes">
        <result property="id" column="id"/>
        <result property="shouhouId" column="shouhouId"/>
        <result property="testResult" column="testResult"/>
        <result property="testType" column="testType"/>
        <result property="remark" column="remark"/>
        <collection property="testResultList" ofType="com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouTestAttrResultInfo">
            <result property="testAttrId" column="testAttrId" />
            <result property="testAttrName" column="testAttrName" />
            <result property="testAttrType" column="testAttrType" />
            <result property="isRequired" column="isRequired" />
            <result property="attrItemLable" column="attrItemLable" />
            <result property="testValue" column="testValue" />
        </collection>
    </resultMap>

    <select id="getTestInfoPageByShouhouId"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestResultInfoRes">
        select stri.id,
               stri.shouhou_id shouhouId,
               stri.test_result testResult,
               case when stri.test_result = 1 then '已测试' else '测试不通过' end testResultDes,
               stri.test_type testType,
               case when stri.test_type = 1 then '修前测试' else '修后测试' end testTypeName,
               stri.remark,
               stri.inuser,
               stri.create_time createTime
        from shouhou_test_result_info stri with(nolock)
        where stri.is_delete = 0
        and stri.shouhou_id = #{req.shouhouId}
    </select>
    <select id="getTestInfoListByShouhouId"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestResultInfoRes">
        select stri.id,
               stri.shouhou_id shouhouId,
               stri.test_result testResult,
               case when stri.test_result = 1 then '已测试' else '测试不通过' end testResultDes,
               stri.test_type testType,
               case when stri.test_type = 1 then '修前测试' else '修后测试' end testTypeName,
               stri.remark,
               stri.inuser,
               stri.handle_type,
               stri.create_time createTime
        from shouhou_test_result_info stri with(nolock)
        where stri.is_delete = 0
        and stri.shouhou_id = #{shouhouId}
    </select>
    <select id="getLastResultByTypeAndShouhouId"
            resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouTestResultInfo">
        select top 1 * from shouhou_test_result_info stri with(nolock)
        where stri.is_delete = 0
        and stri.test_type = #{type}
        and stri.shouhou_id = #{shouhouId}
        order by stri.id desc
    </select>
    <select id="getLastResultByShouhouId"
            resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouTestResultInfo">
        select top 1 * from shouhou_test_result_info stri with(nolock)
        where stri.is_delete = 0
        and stri.shouhou_id = #{shouhouId}
        order by stri.id desc
    </select>
    <select id="getTestResultInfoResById"
            resultMap="shouhouTestResultMap">
        select stri.id,
               stri.shouhou_id shouhouId,
               stri.test_result testResult,
               case when stri.test_result = 1 then '已测试' else '测试不通过' end testResultDes,
               stri.test_type testType,
               case when stri.test_type = 1 then '修前测试' else '修后测试' end testTypeName,
               stri.remark,
               str.test_attr_id testAttrId,
               str.test_attr_name testAttrName,
               str.test_attr_type testAttrType,
               str.test_value testValue,
               str.attr_item_lable attrItemLable,
               str.is_required isRequired
        from shouhou_test_result_info stri with(nolock)
        left join shouhou_test_result str with(nolock) on stri.id = str.shouhou_test_info_id and str.is_delete = 0
        where stri.is_delete = 0
          and stri.id = #{shouhouTestInfoId}
    </select>
</mapper>
