<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.SmsContentMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.SmsContent" id="smsContentMap">
        <result property="id" column="id"/>
        <result property="areaid" column="areaid"/>
        <result property="sms" column="sms"/>
        <result property="sms2" column="sms2"/>
        <result property="sms3" column="sms3"/>
        <result property="sms4" column="sms4"/>
        <result property="sms5" column="sms5"/>
        <result property="sms6" column="sms6"/>
        <result property="sms7" column="sms7"/>
        <result property="sms8" column="sms8"/>
        <result property="sms9" column="sms9"/>
    </resultMap>
    <insert id="insertAppInfo" useGeneratedKeys="true" keyProperty="appInfoVo.id" keyColumn="id">
        INSERT INTO dbo.sms_app_config
        (app_id, secret_key, xtenant, create_time, create_user, update_time, is_del)
        select #{appInfoVo.appId}, #{appInfoVo.secretKey},#{xtenant},getdate(),'系统',getdate(),0
        where not exists(select 1 from dbo.sms_app_config sac with(nolock) where sac.xtenant=#{xtenant} and isnull(sac.is_del,0) = 0)

    </insert>
    <update id="updateAppInfo">
        UPDATE dbo.sms_app_config
        SET app_id=#{appInfoVo.appId}, secret_key=#{appInfoVo.secretKey}
        WHERE xtenant = #{xtenant}
    </update>
    <select id="getAppInfo" resultType="com.jiuji.oa.afterservice.shouhou.vo.sms.AppInfoVo">
        SELECT top 1 app.id,app.value appId,secret.value secretKey,iif(app.xtenant = #{xtenant},-100,app.xtenant) xRank
        FROM sysConfig app with(nolock)
        left join sysConfig secret with(nolock) on secret.code = 241 and secret.xtenant = app.xtenant
        where app.code = 240 AND isnull(app.isdel,0)=0 and isnull(secret.isdel,0)=0
        order by xRank ASC
    </select>


</mapper>