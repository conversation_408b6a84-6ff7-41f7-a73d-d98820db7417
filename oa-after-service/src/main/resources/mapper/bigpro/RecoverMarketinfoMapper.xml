<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.RecoverMarketinfoMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.RecoverMarketinfo" id="recoverMarketinfoMap">
        <result property="subId" column="sub_id"/>
        <result property="subDate" column="sub_date"/>
        <result property="subCheck" column="sub_check"/>
        <result property="subTo" column="sub_to"/>
        <result property="subTel" column="sub_tel"/>
        <result property="subPay" column="sub_pay"/>
        <result property="comment" column="comment"/>
        <result property="inuser" column="Inuser"/>
        <result property="subMobile" column="sub_mobile"/>
        <result property="printxcount" column="printxcount"/>
        <result property="area" column="area"/>
        <result property="zitidianid" column="zitidianID"/>
        <result property="userid" column="userid"/>
        <result property="onlinePay" column="online_pay"/>
        <result property="marketingid" column="Marketingid"/>
        <result property="ispiao" column="ispiao"/>
        <result property="subtype" column="subtype"/>
        <result property="delivery" column="delivery"/>
        <result property="yingfum" column="yingfuM"/>
        <result property="yifum" column="yifuM"/>
        <result property="feem" column="feeM"/>
        <result property="youhui1m" column="youhui1M"/>
        <result property="shouxum" column="shouxuM"/>
        <result property="jidianm" column="jidianM"/>
        <result property="tradedate" column="tradeDate"/>
        <result property="tradedate1" column="tradeDate1"/>
        <result property="dingjing" column="dingjing"/>
        <result property="subpid" column="subPID"/>
        <result property="trader" column="trader"/>
        <result property="fankuan" column="fankuan"/>
        <result property="saletype" column="saleType"/>
        <result property="areaid" column="areaid"/>
        <result property="kuaididan" column="kuaididan"/>
        <result property="coinm" column="coinM"/>
        <result property="islock" column="islock"/>
        <result property="qudaoname" column="qudaoname"/>
        <result property="isfahuo" column="isfahuo"/>
        <result property="expecttime" column="expectTime"/>
        <result property="newsubid" column="newSubId"/>
    </resultMap>

    <select id="checkBankTransfer" resultType="java.lang.Integer">
        SELECT 1 FROM dbo.recover_marketInfo s WITH(NOLOCK)
        INNER JOIN dbo.shouying y with(nolock) ON s.sub_id = y.sub_id
        WHERE ISNULL(s.saleType,0) = 1 AND s.sub_id = #{subId} AND y.shouying_type in ('订金2','交易2')
        AND s.sub_to != '回收机退回渠道'
        AND NOT EXISTS(SELECT 1 FROM dbo.RecoverAuction r with(nolock) WHERE r.SubId = s.sub_id)
        AND y.sub_pay01 > 0
    </select>
</mapper>