<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouServiceReportMapper">

  <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouServiceReport">
    <!--@mbg.generated-->
    <!--@Table shouhou_service_report-->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="acceptance_time" jdbcType="TIMESTAMP" property="acceptanceTime" />
    <result column="shouhou_id" jdbcType="INTEGER" property="shouhouId" />
    <result column="after_user_name" jdbcType="VARCHAR" property="afterUserName" />
    <result column="after_user_mobile" jdbcType="VARCHAR" property="afterUserMobile" />
    <result column="user_class" jdbcType="TINYINT" property="userClass" />
    <result column="user_class_name" jdbcType="VARCHAR" property="userClassName" />
    <result column="imei" jdbcType="VARCHAR" property="imei" />
    <result column="device_name" jdbcType="VARCHAR" property="deviceName" />
    <result column="appearance_description" jdbcType="VARCHAR" property="appearanceDescription" />
    <result column="to_configure" jdbcType="VARCHAR" property="toConfigure" />
    <result column="sub_id" jdbcType="INTEGER" property="subId" />
    <result column="purchase_type" jdbcType="TINYINT" property="purchaseType" />
    <result column="purchasing_date" jdbcType="TIMESTAMP" property="purchasingDate" />
    <result column="purchasing_user_name" jdbcType="VARCHAR" property="purchasingUserName" />
    <result column="purchasing_user_mobile" jdbcType="VARCHAR" property="purchasingUserMobile" />
    <result column="fault_description" jdbcType="VARCHAR" property="faultDescription" />
    <result column="fault_analysis" jdbcType="VARCHAR" property="faultAnalysis" />
    <result column="fault_result" jdbcType="VARCHAR" property="faultResult" />
    <result column="area_id" jdbcType="INTEGER" property="areaId" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="area_mobile" jdbcType="VARCHAR" property="areaMobile" />
    <result column="check_user" jdbcType="VARCHAR" property="checkUser" />
    <result column="check_time" jdbcType="TIMESTAMP" property="checkTime" />
    <result column="reminder" jdbcType="VARCHAR" property="reminder" />
    <result column="fid" jdbcType="VARCHAR" property="fid" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="is_del" jdbcType="BIT" property="isDel" />
    <result column="xtenant" jdbcType="TINYINT" property="xtenant" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id, acceptance_time, shouhou_id, after_user_name, after_user_mobile,
    user_class,user_class_name, imei, device_name, appearance_description, to_configure, sub_id, purchase_type,
    purchasing_date, purchasing_user_name, purchasing_user_mobile, fault_description, 
    fault_analysis, fault_result, area_id,area_name, area_mobile, check_user, check_time,
    reminder,fid, create_time, create_user, update_time, is_del, xtenant
  </sql>
  <insert id="add" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    <!--@mbg.generated-->
    insert into shouhou_service_report
    (acceptance_time, shouhou_id, after_user_name, after_user_mobile,
    user_class,user_class_name, imei, device_name, appearance_description, to_configure, sub_id, purchase_type,
    purchasing_date, purchasing_user_name, purchasing_user_mobile, fault_description,
    fault_analysis, fault_result, area_id,area_name, area_mobile, check_user,
    check_time, reminder, create_time, create_user, update_time, is_del, xtenant)
    values
    (#{item.acceptanceTime}, #{item.shouhouId},
    #{item.afterUserName}, #{item.afterUserMobile},
    #{item.userClass},#{item.userClassName}, #{item.imei}, #{item.deviceName},
    #{item.appearanceDescription}, #{item.toConfigure},
    #{item.subId}, #{item.purchaseType}, #{item.purchasingDate},
    #{item.purchasingUserName}, #{item.purchasingUserMobile},
    #{item.faultDescription}, #{item.faultAnalysis},
    #{item.faultResult}, #{item.areaId}, #{item.areaName},#{item.areaMobile},
    #{item.checkUser}, #{item.checkTime},
    #{item.reminder}, #{item.createTime}, #{item.createUser},
    #{item.updateTime}, #{item.isDel}, #{item.xtenant})
  </insert>

  <select id="getShouhouServiceReportList" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouServiceReportRes$ServiceReportData">
    select <include refid="Base_Column_List"/> from shouhou_service_report with(nolock) where is_del = 0
    <if test="imei != null and imei != ''">
        and imei = #{imei}
    </if>
    <if test="subId != null and subId != 0">
      and sub_id = #{subId}
    </if>
  </select>

  <select id="getShouhouServiceReportById"
          resultType="com.jiuji.oa.afterservice.bigpro.vo.res.PrintShouhouServiceReportRes">
    select
    <include refid="Base_Column_List"/>
    from shouhou_service_report with(nolock) where is_del = 0 and id = #{id}
  </select>
</mapper>