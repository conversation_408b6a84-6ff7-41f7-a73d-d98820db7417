<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.CaigouBasketRefShouhouMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.CaigouBasketRefShouhou">
        <id column="Id" property="Id" />
        <result column="ShouhouId" property="ShouhouId" />
        <result column="CaigouBasketId" property="CaigouBasketId" />
        <result column="IsFahuo" property="IsFahuo" />
    </resultMap>

    <select id="getPeijianfahuo" resultType="java.lang.Integer">
        SELECT DISTINCT ShouhouId
        FROM CaigouBasketRefShouhou  with(nolock)
        JOIN caigou_basket  with(nolock)  ON caigou_basket.id=CaigouBasketRefShouhou.CaigouBasketId
        JOIN caigou_sub  with(nolock)  ON caigou_basket.sub_id=caigou_sub.id
        WHERE IsFahuo=0 and caigou_sub.stats=3
        <if test="shouhouIds != null and shouhouIds.size>0">
            AND ShouhouId  IN
            <foreach collection="shouhouIds" index="index" item="shouhouId" open="(" close=")" separator=",">
                #{shouhouId}
            </foreach>
        </if>
    </select>

</mapper>
