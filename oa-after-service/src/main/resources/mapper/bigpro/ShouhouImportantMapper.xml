<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouImportantMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.ShouhouImportant" id="shouhouImportantMap">
        <result property="id" column="id"/>
        <result property="shouhouid" column="shouhouid"/>
        <result property="stats" column="stats"/>
        <result property="commnet" column="commnet"/>
        <result property="inuser" column="inuser"/>
        <result property="dtime" column="dtime"/>
        <result property="confirmuser" column="confirmUser"/>
        <result property="confirmtime" column="confirmTime"/>
        <result property="finishcomment" column="finishComment"/>
        <result property="finishuser" column="finishUser"/>
        <result property="finishtime" column="finishTime"/>
    </resultMap>

    <select id="getMsgPushUserId" resultType="Integer">
        select ch999_id from dbo.ch999_user with(nolock) where iszaizhi=1 and Roles like '%570%'
                union
                select d.curAdmin from dbo.shouhou s with(nolock)
                left join dbo.areainfo are with(nolock) on are.id=isnull(s.toareaid,s.areaid)
                left join (select a.parentCode,u.ch999_id curAdmin from dbo.departInfo a with(nolock) left join dbo.ch999_user u with(nolock) on u.ch999_name=a.curAdmin where a.name='售后部' and u.iszaizhi=1 and isnull(a.isdel,0)=0) d on d.parentCode=left(are.areaCode,6)
                where s.id=#{shouhouId}
    </select>
</mapper>