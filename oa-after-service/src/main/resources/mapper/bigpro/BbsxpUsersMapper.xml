<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.BbsxpUsersMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.BbsxpUsers" id="bbsxpUsersMap">
        <result property="id" column="ID"/>
        <result property="username" column="UserName"/>
        <result property="userpass" column="UserPass"/>
        <result property="mobile" column="mobile"/>
        <result property="tel" column="tel"/>
        <result property="usermail" column="UserMail"/>
        <result property="usersex" column="UserSex"/>
        <result property="userlastip" column="UserLastIP"/>
        <result property="userregtime" column="UserRegTime"/>
        <result property="userlandtime" column="UserLandTime"/>
        <result property="userbuytime" column="UserBuyTime"/>
        <result property="realname" column="realname"/>
        <result property="userclass" column="userclass"/>
        <result property="totalpoint" column="totalpoint"/>
        <result property="points" column="points"/>
        <result property="chengzhangzhi" column="chengzhangzhi"/>
        <result property="blacklist" column="blacklist"/>
        <result property="comment" column="comment"/>
        <result property="inuser" column="inuser"/>
        <result property="istemp" column="istemp"/>
        <result property="area" column="area"/>
        <result property="kinds" column="kinds"/>
        <result property="erdu" column="erdu"/>
        <result property="saveMoney" column="save_money"/>
        <result property="hezuo" column="hezuo"/>
        <result property="hezuoName" column="hezuo_name"/>
        <result property="lastarea" column="lastarea"/>
        <result property="birthday" column="birthday"/>
        <result property="areaid" column="areaid"/>
        <result property="lastareaid" column="LastAreaId"/>
        <result property="salt" column="salt"/>
        <result property="agegroup" column="AgeGroup"/>
        <result property="haschild" column="HasChild"/>
        <result property="childsex" column="ChildSex"/>
        <result property="childagegroup" column="ChildAgeGroup"/>
        <result property="ucoin" column="uCoin"/>
        <result property="lastloginclient" column="LastLoginClient"/>
        <result property="regclient" column="regClient"/>
        <result property="cityid" column="cityid"/>
        <result property="isvalidate" column="isValidate"/>
        <result property="effectm" column="EffectM"/>
        <result property="paypwd" column="payPwd"/>
        <result property="saltpay" column="saltPay"/>
        <result property="userbuytime1" column="UserBuyTime1"/>
        <result property="lastareaid1" column="LastAreaId1"/>
        <result property="frozenmoney" column="frozenMoney"/>
        <result property="xtenant" column="xtenant"/>
        <result property="specialtype" column="specialType"/>
        <result property="iamarry" column="iaMarry"/>
        <result property="occupation" column="occupation"/>
        <result property="hascar" column="hasCar"/>
        <result property="isbigcustomer" column="isBigCustomer"/>
        <result property="usernature" column="userNature"/>
        <result property="visittime" column="visitTime"/>
        <result property="hasnoticedway" column="hasNoticedWay"/>
        <result property="blacklisttype" column="blackListType"/>
        <result property="headimg" column="headImg"/>
        <result property="wxheadimg" column="wxHeadImg"/>
        <result property="firstapplogintime" column="firstAppLoginTime"/>
        <result property="bindgroupid" column="bindGroupId"/>
        <result property="userlastlandtime" column="UserLastLandTime"/>
        <result property="userapplandtime" column="UserAppLandtime"/>
    </resultMap>


    <select id="getSimpleUser" resultType="com.jiuji.oa.afterservice.sys.vo.res.BbsxpUsersVO">
        SELECT * from (
            SELECT mobile, userName ,ch999_name, ROW_NUMBER ( ) OVER (PARTITION BY mobile ORDER BY ranking asc ) rn from (
                SELECT bu.mobile ,min(bu.realname) as userName,cu.ch999_id ,cu.ch999_name, 1 as ranking
                        from BBSXP_Users bu with(nolock)
                        left join ch999_user cu with(nolock) on cu.mobile  = bu.mobile and isnull(cu.iszaizhi,0) = 1
                        where 1 = 1 and bu.mobile in
                        <foreach collection="mobileList" item="mobile" open="(" close=")" separator="," >
                            #{mobile}
                        </foreach>
                        <if test="xtenant != null">
                            and bu.xtenant = #{xtenant}
                        </if>
                        GROUP by bu.mobile ,cu.ch999_id ,cu.ch999_name

                        union ALL

                SELECT bu.mobile ,min(bu.realname ) as userName,cu.ch999_id ,cu.ch999_name, 2 as ranking
                        from BBSXP_Users bu with(nolock)
                        left join ch999_user cu with(nolock) on cu.mobile  = bu.mobile and isnull(cu.iszaizhi,0) = 1
                        where 1 = 1 and bu.mobile in
                        <foreach collection="mobileList" item="mobile" open="(" close=")" separator="," >
                            #{mobile}
                        </foreach>
                        GROUP by bu.mobile ,cu.ch999_id ,cu.ch999_name
            ) t
        )tt where tt.rn = 1
    </select>
</mapper>