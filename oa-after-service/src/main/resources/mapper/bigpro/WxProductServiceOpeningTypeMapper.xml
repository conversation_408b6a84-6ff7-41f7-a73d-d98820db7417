<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.WxProductServiceOpeningTypeMapper">

    <select id="getDianChiPeiJianAnXinBaoLiang"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.DianChiPeiJianAnXinBaoLiangVO">
        SELECT s.areaid, count(DISTINCT s.id) as dianChiPeiJianAnXinBaoLiang
        from shouhou s with(nolock)
        left JOIN dbo.wxkcoutput wx WITH(NOLOCK,index=index_wxid) ON  wx.wxid = s.id
        left join wx_product_service_opening_type wpsot WITH(nolock) on wx.id=wpsot.wxkcId
        <where>
            wpsot.product_service_opening_type = 118 and s.stats=1
            and s.isquji = 1
            AND ISNULL(wx.stats,0)!=3
            AND ISNULL(s.xianshi, 0) = 1
            AND ISNULL(s.issoft, 0) = 0
            <if test="areaIdList != null and areaIdList.size>0">
                and s.areaid in
                <foreach collection="areaIdList" open="(" separator="," close=")" item="areaId">
                    #{areaId}
                </foreach>
            </if>
            <if test="req != null and req.start != null and req.end != null">
                AND s.offtime BETWEEN #{req.start} AND #{req.end}
            </if>
        </where>
        group by s.areaid
    </select>
</mapper>
