<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ServiceFeeConfigMapper">

    <select id="selectServiceFeeByPpidList" resultType="com.jiuji.oa.afterservice.bigpro.bo.ServiceFeeBO">
        select p.ppriceid,fee.cid,fee.service_fee as serviceFee
        from dbo.service_fee_config fee with (nolock)
        left join dbo.productinfo p with(nolock ) on  p.cid in(select id from dbo.f_category_children(fee.cid))
        where  fee.is_del=0 and fee.maintenance_level in(1,2,3)  and p.ppriceid in
        <foreach collection="ppidList" item="ppid" open="(" close=")" separator=",">
            #{ppid}
        </foreach>
    </select>
    <select id="selectChildrenCid" resultType="java.lang.Integer">
        select cid from dbo.f_category_children(#{cid})
    </select>
</mapper>
