<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.AttachmentsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Attachments">
        <id column="id" property="id" />
        <result column="linkedID" property="linkedID" />
        <result column="type" property="type" />
        <result column="filename" property="filename" />
        <result column="filepath" property="filepath" />
        <result column="kind" property="kind" />
        <result column="dtime" property="dtime" />
        <result column="userid" property="userid" />
        <result column="fid" property="fid" />
        <result column="Extension" property="Extension" />
        <result column="receiveUser" property="receiveUser" />
        <result column="receiveDate" property="receiveDate" />
        <result column="fileSize" property="fileSize" />
    </resultMap>

    <select id="getAttachmentList" resultType="com.jiuji.oa.afterservice.bigpro.po.Attachments">
        SELECT top 20 id,linkedID,type,filename,filepath,kind,kind1,dtime,userid,fid,Extension,receiveUser,receiveDate,fileSize
         FROM attachments with(nolock)
         WHERE isnull(kind,0) = #{kind} AND linkedID = #{linkId} AND type = #{type}
    </select>

    <select id="getAttachmentsSeparateConfig"
            resultType="com.jiuji.oa.afterservice.bigpro.service.newAttachments.po.AttachmentsSeparateConfig">
        SELECT
            id,
            attachments_type,
            separate_time,
            separate_time_name,
            create_time,
            is_del
        FROM
            dbo.attachments_separate_config with(nolock)
        WHERE
            isnull(is_del,0) = 0 AND attachments_type = #{attachmentsType}
    </select>

    <select id="getSendTimeByShouhouId"
            resultType="com.jiuji.oa.afterservice.bigpro.service.newAttachments.res.BusinessTimeRes">
        select s.id AS businessId,s.modidate AS businessCreationTime from shouhou s with(nolock) where s.id in
        <foreach collection="shouhouIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>
