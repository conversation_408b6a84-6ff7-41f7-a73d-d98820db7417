<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.WeixinUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.WeixinUser">
        <id column="userid" property="userid" />
        <result column="openid" property="openid" />
        <result column="dtime" property="dtime" />
        <result column="type" property="type" />
        <result column="kinds" property="kinds" />
        <result column="unionid" property="unionid" />
        <result column="wxid" property="wxid" />
        <result column="nickname" property="nickname" />
        <result column="xtenant" property="xtenant" />
        <result column="follow" property="follow" />
        <result column="followCheckTime" property="followCheckTime" />
    </resultMap>

    <select id="getWeiXinBindInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.WeiXinBindInfoBo">
        SELECT w.openId,w.follow,w.followCheckTime,w.wxid FROM dbo.WeixinUser w with(nolock)
		INNER JOIN dbo.BBSXP_Users u with(nolock) ON w.userid = u.ID
		INNER JOIN dbo.WeiXinxTenant t WITH(NOLOCK) ON u.xtenant = t.xtenant
		WHERE w.userid = #{userId} AND t.wxid = w.wxid AND w.kinds = 1 AND w.type = 1 AND w.follow =1
    </select>

    <select id="getOpenIdByCh999IdList" resultType="java.lang.String">
        SELECT w.openid from WeixinUser w WITH(nolock)
        left join BBSXP_Users b WITH(nolock) on w.userid=b.id
        left join ch999_user u WITH(nolock) on u.mobile = b.mobile
        where w.openid is not null
        and u.ch999_id in
        <foreach collection="ch999IdList" index="index" item="ch999Id" separator="," open="(" close=")">
            #{ch999Id}
        </foreach>
        and w.kinds = 1
    </select>
</mapper>
