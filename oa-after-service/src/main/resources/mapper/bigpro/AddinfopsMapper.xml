<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.AddinfopsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Addinfops">
        <id column="id" property="id" />
        <result column="Reciver" property="Reciver" />
        <result column="Address" property="Address" />
        <result column="mobile" property="mobile" />
        <result column="Tel" property="Tel" />
        <result column="Email" property="Email" />
        <result column="cityid" property="cityid" />
        <result column="type" property="type" />
        <result column="BindId" property="BindId" />
        <result column="psuser" property="psuser" />
        <result column="Consignee" property="Consignee" />
    </resultMap>

    <update id="updateConsignee">
            UPDATE Addinfops SET consignee = #{consignee} WHERE BindId =#{bindId}
    </update>
    <select id="getAddinfopsByBindIdAndTypes" resultType="com.jiuji.oa.afterservice.bigpro.po.Addinfops">
            select REPLACE(dbo.getCityName(a.cityid),' ','') as cityName,a.* from Addinfops a with(nolock)
            where a.BindId = #{bindId}
            and a.type in
            <foreach collection="typeList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
    </select>

</mapper>
