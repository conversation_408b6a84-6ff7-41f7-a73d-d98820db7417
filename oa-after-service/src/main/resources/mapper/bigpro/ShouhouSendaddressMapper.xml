<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouSendaddressMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouSendaddress">
        <id column="id" property="id" />
        <result column="linkid" property="linkid" />
        <result column="kind" property="kind" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="address" property="address" />
        <result column="phone" property="phone" />
        <result column="cityid" property="cityid" />
        <result column="recover" property="recover" />
    </resultMap>

    <select id="checkHasShouhouSendaddress" resultType="java.lang.Integer">
        SELECT count(1) FROM shouhou_sendaddress WITH(NOLOCK) WHERE kind=#{kind} AND linkid=#{linkid}
    </select>

</mapper>
