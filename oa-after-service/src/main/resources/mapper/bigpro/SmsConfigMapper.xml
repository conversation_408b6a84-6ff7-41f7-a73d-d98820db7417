<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.SmsConfigMapper">
    <select id="getSmsContentByCode" resultType="java.lang.String">
        select top 1 sc.value from dbo.smsconfig sc with(nolock)
            where isnull(sc.isdel,0)=0 and isnull(type,0)=0 and sc.xtenant = #{xtenant} and sc.code = #{code}
            order by sc.id desc
    </select>
</mapper>