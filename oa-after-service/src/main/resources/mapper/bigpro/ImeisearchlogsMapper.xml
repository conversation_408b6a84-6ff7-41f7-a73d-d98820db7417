<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ImeisearchlogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Imeisearchlogs">
        <id column="id" property="id" />
        <result column="ch999_user" property="ch999User" />
        <result column="key" property="key" />
        <result column="dtime" property="dtime" />
        <result column="isclear" property="isclear" />
        <result column="area" property="area" />
        <result column="areaid" property="areaid" />
    </resultMap>

    <select id="getImeiSearchLogsByImei" resultType="com.jiuji.oa.afterservice.bigpro.bo.ImeiSearchLogBo">
        select top 50 i.ch999_user userName,i.dtime dTime,i.areaid areaId,a.area,u.ch999_id as ch999Id,i.isclear clear
        from imeisearchlogs i WITH(NOLOCK)
        left join areainfo a WITH(NOLOCK) on i.areaid = a.id
        left join ch999_user u  WITH(NOLOCK) on i.ch999_user = u.ch999_name
        where i.dtime&lt;dateadd(SS,-300,GETDATE()) and i.[key]=#{imei} order by i.id desc
    </select>

</mapper>
