<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouTestResultMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouTestResult">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="shouhouTestInfoId" column="shouhou_test_info_id" jdbcType="BIGINT"/>
            <result property="testAttrId" column="test_attr_id" jdbcType="BIGINT"/>
            <result property="testAttrName" column="test_attr_name" jdbcType="VARCHAR"/>
            <result property="testAttrType" column="test_attr_type" jdbcType="TINYINT"/>
            <result property="testValue" column="test_value" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,shouhou_test_info_id,test_attr_id,
        test_attr_name,test_attr_type,test_value,
        create_time,update_time,is_delete,
        shouhou_test_result_rv
    </sql>
    <insert id="saveShouhouTestResultBatch">
        insert into shouhou_test_result (
                 shouhou_test_info_id,
                 test_attr_id,
                 test_attr_name,
                 test_attr_type,
                 is_required,
                 attr_item_lable,
                 test_value)
        values
        <foreach collection="shouhouTestResultList" item="item" separator=",">
            (
                #{item.shouhouTestInfoId},
                #{item.testAttrId},
                #{item.testAttrName},
                #{item.testAttrType},
                #{item.isRequired},
                #{item.attrItemLable},
                #{item.testValue}
            )
        </foreach>
    </insert>
    <select id="getTestResultByTestInfoId" resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouTestResult">
        select * from shouhou_test_result str with(nolock)
        where str.is_delete = 0
        and str.shouhou_test_info_id = #{testInfoId}
    </select>
</mapper>
