<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ProductMapper">
    <select id="getBrandCategoryByProductIds" resultType="com.jiuji.oa.afterservice.bigpro.bo.BrandCategoryNameBo">
        SELECT p.id AS productId,b.id brandId, b.name AS brandName,c.ID categoryId, c.Name AS categoryName
        FROM product p
        left JOIN brand b ON p.brandID = b.id
        left JOIN category c ON p.cid = c.id
        WHERE p.id IN
        <foreach item="productId" collection="productIds" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </select>
</mapper>
