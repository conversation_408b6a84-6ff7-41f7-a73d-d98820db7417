<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhoutestInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhoutestInfo">
        <id column="id" property="id" />
        <result column="shouhou_id" property="shouhouId" />
        <result column="dtime" property="dtime" />
        <result column="testResult" property="testResult" />
        <result column="inuser" property="inuser" />
        <result column="testResultInfo" property="testResultInfo" />
        <result column="testParms" property="testParms" />
        <result column="waiguan" property="waiguan" />
        <result column="testtype" property="testtype" />
        <result column="comment" property="comment" />
    </resultMap>

    <select id="checkShouhouTestResult" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouTestResultBo">
        SELECT shouhou_id,dtime,testResultInfo,inuser
        FROM ( SELECT shouhou_id, dtime, testResultInfo, inuser, ROW_NUMBER() OVER (PARTITION BY shouhou_id ORDER BY
        dtime desc) AS rn
        FROM shouhoutestInfo with(nolock) where 1=1
        <if test="shouhouIds != null and shouhouIds.size>0">
            and shouhou_id IN
            <foreach collection="shouhouIds" index="index" item="shouhouId" open="(" separator="," close=")">
                #{shouhouId}
            </foreach>
        </if>
        )t WHERE rn=1
    </select>

    <select id="checkShouhouTestResultV2" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouTestResultBo">
        SELECT shouhou_id,dtime,testResultInfo,inuser
        FROM ( SELECT shouhou_id, dtime, testResultInfo, inuser, ROW_NUMBER() OVER (PARTITION BY shouhou_id ORDER BY dtime desc) AS rn
               FROM shouhoutestInfo sti with(nolock) where NOT EXISTS (SELECT 1 FROM shouhou_test_result_info stri with(nolock) WHERE stri.id = sti.shouhou_id)
                and shouhou_id IN
                <foreach collection="shouhouIds" index="index" item="shouhouId" open="(" separator="," close=")">
                    #{shouhouId}
                </foreach>
             )t WHERE rn=1
        UNION
        SELECT shouhou_id,dtime,testResultInfo,inuser
        FROM ( SELECT stri.shouhou_id, stri.create_time dtime,CAST(stri.remark as varchar) testResultInfo,stri.inuser inuser, ROW_NUMBER() OVER (PARTITION BY shouhou_id ORDER BY stri.id desc) AS rn
               FROM shouhou_test_result_info stri with(nolock) where stri.is_delete=1
                and shouhou_id IN
                <foreach collection="shouhouIds" index="index" item="shouhouId" open="(" separator="," close=")">
                    #{shouhouId}
                </foreach>
             )t WHERE rn=1
    </select>

</mapper>
