<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouYuyuelockppidsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyuelockppids">
        <id column="id" property="id" />
        <result column="yyid" property="yyid" />
        <result column="ppid" property="ppid" />
        <result column="areaid" property="areaid" />
    </resultMap>

    <select id="getYuyueLockProductList" resultType="com.jiuji.oa.afterservice.bigpro.bo.YuyueLockProductInfoBo">
        SELECT p.product_name as productName,y.ppid,p.memberprice as memberPrice,k.inprice, k.lcount
        from shouhou_yuyuelockppids y WITH(NOLOCK)
        INNER JOIN dbo.product_kc k WITH(NOLOCK) ON k.ppriceid=y.ppid
        <if test="areaId != null and areaId != 0">
            AND k.areaid=#{areaId}
        </if>
        INNER JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=k.ppriceid
        WHERE yyid=#{yyId}
    </select>

</mapper>
