<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.WeixiuzuKindMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.WeixiuzuKind" id="weixiuzuKindMap">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="rank" column="rank"/>
        <result property="departCode" column="DepartCode"/>
    </resultMap>

    <sql id="shouhouMaintainLogPageQuery">
        from weixiuzulogs w with(nolock) left join
        shouhou h with(nolock) on w.shouhou_id=h.id where 1=1
        <if test="req.searchKey!=null and req.searchKey!=''">
            <choose>
                <when test="req.searchType == 'inuser'">
                    AND w.inuser like concat('%',#{req.searchKey},'%')
                </when>
                <when test="req.searchType == 'imei'">
                    AND h.imei like concat('%',#{req.searchKey},'%')
                </when>
                <when test="req.searchType == 'shouhou_id' ">
                    AND h.id = #{req.searchKey}
                </when>
                <when test=" req.searchType == 'product_name' ">
                    AND h.name like concat('%',#{req.searchKey},'%')
                </when>
            </choose>
        </if>

        <choose>
            <when test=" req.weixiuzuid1 == null">
                AND w.weixiuzuid1 is null
            </when>
            <otherwise>
                AND w.weixiuzuid1 =#{req.weixiuzuid1}
            </otherwise>
        </choose>
        <if test=" req.weixiuzuid2 != null and req.weixiuzuid2!='' ">
            AND w.weixiuzuid2 =#{req.weixiuzuid2}
        </if>
        <if test=" req.areaid !=null and req.areaid !=0">
            AND areaid=#{req.areaid}
        </if>
        <if test=" req.dateType !=null and req.dateType !='' and req.dtimeS!=null and req.dtimeS!='' and req.dtimeE!=null and req.dtimeE!='' and req.dateType =='dtime'">
            AND dtime between #{req.dtimeS} and #{req.dtimeE}
        </if>
    </sql>

    <select id="queryMaintainLogPage" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.WeixiuzuLogListRes">
        select w.*,h.name,h.areaid as areaId,isnull(h.toareaid,h.areaid)
        as nowArea,h.orderid as orderId,h.problem
        <include refid="shouhouMaintainLogPageQuery">
        </include>

        order by id desc
        OFFSET #{req.current} ROWS FETCH NEXT #{req.size} ROWS ONLY
    </select>

    <select id="getWeiXiuGroupList" resultType="com.jiuji.oa.afterservice.bigpro.vo.WeixiuzuKindVo">
        SELECT w.id,w.name,ISNULL(w.rank,0) rank, ISNULL(d.name,'') departName
        FROM dbo.weixiuzu_kind w WITH(NOLOCK) LEFT JOIN dbo.departInfo d WITH(NOLOCK) ON w.depart_id=d.id
        where isnull(w.is_del,0) = 0
        order by w.rank
    </select>

    <select id="getQueryCount" resultType="Integer">
        select count(*)
        <include refid="shouhouMaintainLogPageQuery"/>
    </select>

    <select id="getWeiXiuRenByWxzId" resultType="java.lang.String">
        select ch999_name from ch999_user WITH(NOLOCK) where work='售后' and bumeng2='维修'
        and iszaizhi=1
        <if test="weixiuzuId != null and weixiuzuId > 0">
            and bumeng3id=#{weixiuzuId}
        </if>
        order by ch999_id asc
    </select>

</mapper>