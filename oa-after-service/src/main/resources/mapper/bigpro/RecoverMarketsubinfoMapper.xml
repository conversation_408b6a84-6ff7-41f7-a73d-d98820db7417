<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.RecoverMarketsubinfoMapper">

    <select id="selectResale" resultType="com.jiuji.oa.afterservice.bigpro.po.RecoverMarketsubinfo">
        SELECT b.*
        FROM dbo.recover_marketSubInfo b with (nolock)
         LEFT JOIN dbo.recover_marketInfo s with (nolock) ON s.sub_id = b.sub_id
        WHERE b.imei2 = #{imei}
          and sub_to = '回收机退回渠道'
    </select>
    <select id="selectLastReSale" resultType="java.time.LocalDateTime">
        SELECT top 1 b.basket_date
        FROM dbo.recover_marketSubInfo b with (nolock)
        WHERE b.imei2 = #{imei}
          and exists(select 1 from dbo.recover_marketInfo s with (nolock) where s.sub_id = b.sub_id and s.sub_to = '回收机退回渠道' and s.sub_check  &lt;&gt; 4)
          and isnull(b.isdel,0) = 0
        order by b.basket_date desc
    </select>
</mapper>
