<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouToareaMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.ShouhouToarea" id="shouhouToareaMap">
        <result property="id" column="id"/>
        <result property="shouhouId" column="shouhou_id"/>
        <result property="area" column="area"/>
        <result property="toarea" column="toarea"/>
        <result property="dtime" column="dtime"/>
        <result property="inuser" column="inuser"/>
        <result property="checkinuser" column="checkinuser"/>
        <result property="checkdtime" column="checkdtime"/>
        <result property="check" column="check"/>
        <result property="shibian" column="shibian"/>
        <result property="areaid" column="areaid"/>
        <result property="toareaid" column="toareaid"/>
        <result property="wuliuid" column="wuliuid"/>
    </resultMap>

    <select id="getShiBieMa" resultType="java.lang.String">
        select top 1 shibian from shouhou_toarea WITH(NOLOCK)
        where [check]=0 and datediff(d,dtime,getdate())=0
        and areaid=#{toAreaId} order by id desc
    </select>
    <select id="getCount" resultType="java.lang.Integer">
        select top 1 1 from shouhou_toarea WITH(NOLOCK) where ISNULL([check],0)=0 and shouhou_id=#{id}
    </select>
    <select id="getShouhouToarea" resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouToarea">
        SELECT top 20 t.id,t.shouhou_id,t.inuser,t.toareaid from shouhou_toarea t with(nolock)
        LEFT JOIN shouhou s with(nolock) on t.shouhou_id = s.id
        WHERE t.dtime > '2020-11-06' and datediff( day, t.dtime, getdate() ) = 3 and
        s.xianshi = 1 and isnull(s.isquji,0) = 0 and t.checkdtime is null
    </select>
    <select id="getShouhouToareaByshouHouId" resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouToarea">
        SELECT top 1 * from shouhou_toarea st where st.shouhou_id = #{shouhouId} and isnull(st.[check],0)=0 order by st.id desc;
    </select>

</mapper>