<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.statistics.dao.ShouhouInventoryMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.ShouhouInventoryRes">
        <id column="id" property="id"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="imei" property="imei"/>
        <result column="orderid" property="orderid"/>
        <result column="kc_check" property="kcCheck"/>
        <result column="mkc_id" property="mkcId"/>
        <result column="inuser" property="inuser"/>
        <result column="basket_id" property="basketId"/>
        <result column="check1" property="check1"/>
        <result column="dtime" property="dtime"/>
        <result column="check1dtime" property="check1dtime"/>
        <result column="check2" property="check2"/>
        <result column="check2dtime" property="check2dtime"/>
        <result column="check1user" property="check1user"/>
        <result column="check2user" property="check2user"/>
        <result column="comment" property="comment"/>
        <result column="areaid" property="areaid"/>
        <result column="kinds" property="kinds"/>
    </resultMap>


    <select id="getShouhouInventory"
            parameterType="com.jiuji.oa.afterservice.bigpro.statistics.vo.req.ShouhouInventoryQueryReq"
            resultType="com.jiuji.oa.afterservice.bigpro.statistics.vo.res.ShouhouInventoryRes">
        <!--当状态值不为空的时候-->
        <choose>
            <when test="req.kinds != 'h3' and req.kinds != 'h4' and req.kinds != 'h5'">
                <include refid="not_h3_h4_h5"/>
                and d.kinds=#{req.kinds}
                <include refid="shouhouInventoryQuery"/>
            </when>
            <otherwise>
                <include refid="Remaining"/>
                <include refid="shouhouInventoryQuery"/>
                <if test="req.kinds == 'xc'">
                    and k.kc_check != 5
                </if>
            </otherwise>
        </choose>
    </select>

    <sql id="shouhouInventoryQuery">
        <!--  查询参数构建  1：imei  2:提交人     -->
        <if test="req.inquireType != null and req.inquireType != ''">
            <choose>
                <when test="req.mkcIdList != null and req.mkcIdList.size > 0">
                    and k.id in
                    <foreach item="ids" collection="req.mkcIdList" index="index" open="(" separator="," close=")">
                        #{ids}
                    </foreach>
                </when>
                <when test="req.inquireType == 1 and req.inquire!=null and req.inquire!=''">
                    and k.imei like concat('%',#{req.inquire},'%')
                </when>
                <when test="req.inquireType == 2 and req.inquire!=null and req.inquire!=''">
                    and d.inuser like concat('%',#{req.inquire},'%')
                </when>
            </choose>
        </if>
        <!-- 提交时间状态 0:提交时间  1:审核一时间  2:审核二时间       -->
        <if test="req.startTime != null and req.endTime != null">
            <choose>
                <when test="req.timeType == 1">
                    and d.check1dtime BETWEEN #{req.startTime} and #{req.endTime}
                </when>
                <when test="req.timeType == 2">
                    and d.check2dtime BETWEEN #{req.startTime} and #{req.endTime}
                </when>
                <!--                提交时间-->
                <otherwise>
                    and d.dtime BETWEEN #{req.startTime} and #{req.endTime}
                </otherwise>
            </choose>
        </if>
        <!--状态 0:全部  1:未审核  2:审核1  3:审核2 -->
        <if test="req.auditType != null and req.auditType != ''">
            <choose>
                <when test="req.auditType == 1">
                    and (check1 is null)
                </when>
                <when test="req.auditType == 2">
                    and check1=1 and check2 is null
                </when>
                <when test="req.auditType == 3">
                    and check2=1
                </when>
            </choose>
        </if>
        <!--库存状态-->
        <if test="req.stockType != null and req.stockType != ''">
            <choose>
                <when test="req.stockType == 15">
                    and k.kc_check is null
                </when>
                <otherwise>
                    and k.kc_check = #{req.stockType}
                </otherwise>
            </choose>
        </if>
    </sql>

    <sql id="not_h3_h4_h5">
        SELECT p.product_name,
               p.product_color,
               k.imei,
               k.orderid,
               k.kc_check,
               k.basket_id,
               d.id,
               d.mkc_id,
               d.inuser,
               d.dtime,
               d.check1,
               d.check1dtime,
               d.check2,
               d.check2dtime,
               d.check1user,
               d.check2user,
               d.comment,
               d.areaid,
               d.kinds
        FROM mkc_dellogs d with(nolock)
                LEFT JOIN product_mkc k with (nolock) ON d.mkc_id = k.id
                LEFT JOIN productinfo p with (nolock) ON p.ppriceid = k.ppriceid
        WHERE 1 = 1
    </sql>

    <sql id="Remaining">
        SELECT p.product_name,
               p.product_color,
               k.imei,
               orderid,
               k.mkc_check            kc_check,
               k.to_basket_id         basket_id,
               d.id,
               d.mkc_id,
               d.inuser,
               d.dtime,
               d.check1,
               d.check1dtime,
               d.check2,
               d.check2dtime,
               d.check1user,
               d.check2user,
               d.comment,
               d.areaid,
               d.kinds
        FROM mkc_dellogs d with(nolock)
                                LEFT JOIN recover_mkc k with (nolock) ON d.mkc_id = k.id
                LEFT JOIN productinfo p with (nolock)
        ON p.ppriceid = k.ppriceid
        WHERE 1 = 1
    </sql>
</mapper>
