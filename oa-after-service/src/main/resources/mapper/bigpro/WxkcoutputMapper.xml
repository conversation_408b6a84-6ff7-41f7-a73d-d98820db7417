<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.WxkcoutputMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput">
        <id column="id" property="id" />
        <result column="wxid" property="wxid" />
        <result column="name" property="name" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="area" property="area" />
        <result column="price" property="price" />
        <result column="price1" property="price1" />
        <result column="tui" property="tui" />
        <result column="lock1" property="lock1" />
        <result column="lock1dtime" property="lock1dtime" />
        <result column="tuidtime" property="tuidtime" />
        <result column="inprice" property="inprice" />
        <result column="ppriceid" property="ppriceid" />
        <result column="price_gs" property="priceGs" />
        <result column="stats" property="stats" />
        <result column="dianping" property="dianping" />
        <result column="areaid" property="areaid" />
        <result column="isna" property="isna" />
        <result column="natime" property="natime" />
        <result column="islockkc" property="islockkc" />
        <result column="wxtongjitotal" property="wxtongjitotal" />
        <result column="isyouhuima" property="isyouhuima" />
        <result column="output_dtime" property="outputDtime" />
    </resultMap>
    <update id="updateYouHuiFeiYongBatch">
        UPDATE wxkcoutput
        <set>
            <trim prefix="youhuifeiyong =isnull(youhuifeiyong,0)+case" suffix="else 0 end,">
                <foreach collection="wxkcoutputs" item="wxkc">
                    <if test="wxkc.youhuifeiyong != null">
                        when id=#{wxkc.id} then #{wxkc.youhuifeiyong}
                    </if>
                </foreach>
            </trim>
            <trim prefix="discount_code =isnull(discount_code,'') + case" suffix="else null end,">
                <foreach collection="wxkcoutputs" item="wxkc">
                    <if test="wxkc.youhuifeiyong != null and youHuiMa != null and youHuiMa != ''">
                        when id=#{wxkc.id} then '|' + #{youHuiMa}
                    </if>
                </foreach>
            </trim>
            <trim prefix="refund_price =case" suffix="else isnull(price,0)+isnull(price_gs,0)-isnull(youhuifeiyong,0) end,">
                <foreach collection="wxkcoutputs" item="wxkc">
                    <if test="wxkc.youhuifeiyong != null">
                        when id=#{wxkc.id} then
                        iif(isnull(price,0)+isnull(price_gs,0)-isnull(youhuifeiyong,0)-#{wxkc.youhuifeiyong}>0,isnull(price,0)+isnull(price_gs,0)-isnull(youhuifeiyong,0)-#{wxkc.youhuifeiyong},0)
                    </if>
                </foreach>
            </trim>
        </set>
        WHERE wxid = #{shouhouId} and stats &lt;&gt;3
        <if test="youHuiMa != null and youHuiMa != ''">
            and concat('|',discount_code,'|') not like concat('%|',#{youHuiMa},'|%')
        </if>
    </update>
    <update id="updateServiceType">
        update wxkcoutput
        set service_type = #{serviceType}
        where id in
        <foreach collection="servicePjIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <delete id="cancelServiceType">
        update wxkcoutput
        set service_type = null
        where wxid = #{wxId} and service_type = #{serviceType}
    </delete>

    <select id="getHexiao" resultType="com.jiuji.oa.afterservice.bigpro.bo.HexiaoBo">
        SELECT
        x.name AS wxpjName,
        x.islockkc as islockc,
        x.id,
        (
        isnull( p.product_name, x.name ) + isnull( p.product_color, '' )) AS productName,
        isnull( p.product_name, x.name ) AS originProductName,
        isnull( p.product_color, '' ) originProductColor,
        isnull(pxi.product_label,0) as pLabel,
        p.cid,
        p.isSn,
        x.price,
        isnull(refund_price,isnull(x.price,0)+isnull(x.price_gs,0)) refundPrice,
        x.tui_status,
        isnull(x.refunded_price,0) refundedPrice,
        x.price1,
        ISNULL( x.inprice, 0 ) inprice,
        ISNULL( x.price_gs, 0 ) priceGs,
        x.dtime,
        x.inuser,
        x.stats,
        h.hxprice,
        h.ishexiao,
        h.hexiaodtime,
        x.ppriceid as ppid,
        x.lock1dtime,
        ISNULL( hs.id, 0 ) AS hsid,
        ISNULL( hs.issale, 0 ) AS issale,
        ISNULL( hs.price, 0 ) AS hsprice,
        ISNULL( hs.isfan, 0 ) AS isfan,
        ISNULL( x.dianping, 0 ) AS dianping,
        hs.dktype,
        hs.ishuanhuo,
        hs.hsjj_saletype,
        hs.areaid hsAreaId,
        hs.toareaid hsToAreaId,
        hs.isfan hsIsFan,
        hs.confirm hsConfirm,
        hs.confirm hsConfirm,
        hs.issalecheck hsIsSaleCheck,
        hs.complete hsComplete,
        hs.from_source hsFromSource,
        hs.fancheck hsFancheck,
        x.punish_sub,
        x.special_quality_assurance,
        x.part_type
    FROM
        wxkcoutput x with(nolock)
        LEFT JOIN productinfo p with(nolock) ON x.ppriceid= p.ppriceid
        LEFT JOIN product_xtenant_info pxi with(nolock) on pxi.ppriceid = p.ppriceid and xTenant = #{xTenant}
        LEFT JOIN shouhou_hexiao h with(nolock) ON h.Kc_logsId= x.id
        LEFT JOIN shouhou_huishou hs with(nolock) ON hs.wxkcid= x.id and isnull(hs.from_source,0)=0
        AND hs.isdel= 0
    WHERE 1=1
        <if test="wxId != null and wxId != 0">
            and wxid = #{wxId}
        </if>
    ORDER BY
        id ASC
    </select>

    <select id="getWeixiuPJ" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.WeiXiuPjRes">
        SELECT * FROM shouhou_hexiao h WITH(NOLOCK) LEFT JOIN product_kclogs l WITH(NOLOCK) ON h.Kc_logsId=l.id
        LEFT JOIN productinfo p WITH(NOLOCK) on l.ppriceid=p.ppriceid
        WHERE 1=1
        AND exists( select 1 from f_category_children(23)  f where f.id=p.cid )
        <if test="shouhouId != null">
            and  l.shouhou_id=#{shouhouId}
        </if>
        ORDER BY ID
    </select>

    <select id="getKcIdByWxId" resultType="java.lang.Integer">
        SELECT top 1 wx.id FROM dbo.wxkcoutput wx WITH(NOLOCK)  INNER JOIN productinfo p WITH(NOLOCK) ON wx.ppriceid=p.ppriceid
        WHERE wx.wxid=#{wxId} and (p.cid=68 or p.cid = 481) AND wx.stats!=3
    </select>

    <select id="getWxKcCount" resultType="java.lang.Integer">
        select count(1) from dbo.wxkcoutput WITH(NOLOCK)  where isnull(member_discount_amount,0)=0 and wxid=#{shouhouId} and price != price1
    </select>

    <select  id="cidCheck" resultType="java.lang.Integer">
        select w.id from wxkcoutput w WITH(NOLOCK) left join productinfo p WITH(NOLOCK) on p.ppriceid = w.ppriceid where cid in
        <foreach collection="limitIdList" index="index" item="limitId" separator="," open="(" close=")">
            #{limitId}
        </foreach>
        and isnull(w.stats,0) != 3
        and w.wxid =#{shouhouId}
    </select>

    <select id="cidCheck1" resultType="java.lang.Integer">
        select w.id from wxkcoutput w WITH(NOLOCK) left join productinfo p WITH(NOLOCK)  on p.ppriceid = w.ppriceid where p.productid in
        <foreach collection="limitIdList" index="index" item="limitId" separator="," open="(" close=")">
            #{limitId}
        </foreach>
        and isnull(w.stats,0) != 3
        and w.wxid =#{shouhouId}
    </select>

    <!--<select id="getWxPeijianByShouhouId" resultType=""-->

    <select id="getTransferSubInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.wxpj.PjTransferBo">
        <choose>
            <when test="orderType !=null and orderType == 1">
                select sh.id as wxId,b.ppriceid as ppid,b.sub_id,cs.areaid toAreaId,cs.areaid areaId,sa.id applyId
                FROM shouhou sh  with(nolock)
                inner join dbo.shouhou_apply sa with(nolock) on isnull(sa.endtype,0)=0 and sa.wxid = sh.id AND sa.ppid != 15790
                                                                and isnull(sa.kindstats, 0) not in(6,7)
                inner join dbo.caigou_basket b with(nolock) on sa.caigouid = b.sub_id and sa.ppid = b.ppriceid
                inner JOIN dbo.caigou_sub cs with(nolock) ON cs.id = b.sub_id
                inner JOIN caigouInputBasket cib with(nolock) ON cib.cgBasketId = b.id
                WHERE sa.caigouid = #{transferId} and sh.xianshi = 1 and isnull(sh.isquji,0) != 1
                    and (case when sh.toareaid >0 then sh.toareaid else sh.areaid end) = cs.areaid
            </when>
            <otherwise>
                select b.basket_id as wxId,b.ppriceid as ppid,s.id sub_id,s.toAreaId toAreaId,s.areaId areaId
                FROM dbo.diaobo_basket b with(nolock)
                LEFT JOIN diaobo_sub s with(nolock) ON b.sub_id = s.id
                left join shouhou sh  with(nolock) ON b.basket_id = sh.id
                WHERE s.kinds = 'wx' AND b.ppriceid != 15790 AND s.id = #{transferId} and s.stats != 3 and isnull(isquji,0) != 1
                    and sh.xianshi = 1 and (case when sh.toareaid >0 then sh.toareaid else sh.areaid end) = s.toAreaId
            </otherwise>
        </choose>

    </select>
    <select id="getByIdAndXtenantAndAuthPart" resultMap="BaseResultMap">
        SELECT id, wxid, name, inuser, dtime, area, price, price1, tui, lock1, lock1dtime, tuidtime, inprice, ppriceid, price_gs, stats, dianping, areaid, isna, natime, islockkc, wxtongjitotal, isyouhuima, output_dtime
        FROM dbo.wxkcoutput wxk with(nolock)
        where wxk.id = #{id}
        <!--授权隔离-->
        <if test="hasAuthPart">
            <!--租户隔离-->
            and exists (SELECT id from areainfo a with(nolock) where a.id = wxk.areaid and a.xtenant = #{xtenant})
            and (wxk.areaid = #{areaId} or exists (select id from areainfo a2 with(nolock) where a2.id = wxk.areaid and a2.authorizeid = #{authorizeId}))
        </if>
    </select>
    <select id="listYouhuiMaAllocation" resultType="com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput">
        SELECT op.id, op.wxid, op.name, op.inuser, op.dtime, op.area, isnull(op.price,0) price, op.price1, op.tui, op.lock1, op.lock1dtime, op.tuidtime, op.inprice, op.ppriceid, op.price_gs
             , op.stats, op.dianping, op.areaid, op.isna, op.natime, op.islockkc, op.wxtongjitotal, op.isyouhuima, op.output_dtime
        FROM dbo.wxkcoutput op with(nolock) where op.wxid = #{shouhouId} and op.stats in(0,1,2)
        <if test="wxkcIds != null">
            <foreach collection="wxkcIds" item="wxkcId" open="and op.id in (" separator="," close=")">
                #{wxkcId}
            </foreach>
        </if>
        <if test="shouhouServicesPpriceids != null">
             <foreach collection="shouhouServicesPpriceids" open="and op.ppriceid not in(" close=")" separator="," item="ppriceid">
                 #{ppriceid}
             </foreach>
        </if>

    </select>
    <select id="sumCancelPjYouHuiFeiyong" resultType="java.math.BigDecimal">
        select isnull(sum(youhuifeiyong),0) from wxkcoutput with(nolock) where wxid = #{wxId} and stats = 3 and youhuifeiyong is not null
    </select>
</mapper>
