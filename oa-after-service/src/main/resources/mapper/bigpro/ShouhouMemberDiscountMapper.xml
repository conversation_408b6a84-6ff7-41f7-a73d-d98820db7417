<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouMemberDiscountMapper">
    <select id="getUserIdByShouhouId" resultType="java.lang.Integer">
        select s.userid from shouhou s with(nolock) where s.id = #{shouhouId} and s.xianshi = 1
    </select>
    <select id="existDiscount" resultType="java.lang.Boolean">
        select iif(exists(select 1 from wxkcoutput w with(nolock) where isnull(w.member_discount_amount,0)>0 and w.wxid = #{shouhouId}),1,0)
    </select>
    <update id="cancelDiscount">
        update wxkcoutput
        set price = price1,member_discount_amount = 0
        where isnull(member_discount_amount,0)>0 and wxid = #{shouhouId}
        <if test="wxkcId != null and wxkcId>0">
            and id = #{wxkcId}
        </if>
    </update>
</mapper>