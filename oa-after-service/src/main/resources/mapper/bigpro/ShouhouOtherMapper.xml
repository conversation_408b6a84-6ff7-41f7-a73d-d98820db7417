<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouOtherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouOther">
        <id column="id" property="id" />
        <result column="kind" property="kind" />
        <result column="shouhouid" property="shouhouid" />
        <result column="isend" property="isend" />
        <result column="timeoutdate" property="timeoutdate" />
        <result column="mark" property="mark" />
        <result column="inuser" property="inuser" />
    </resultMap>

    <select id="getWxUserId" resultType="Integer">
        SELECT userid FROM dbo.shouhou with(nolock) WHERE 1=1
        <if test="wxid!=null">
         AND id=#{ wxid}
        </if>
    </select>

    <select id="getNowAreaId" resultType="Integer">
        SELECT ISNULL(toareaid,areaid) FROM dbo.shouhou with(nolock) WHERE 1=1
        <if test="wxid!=null">
            AND id=#{ wxid}
        </if>
    </select>

    <select id="getNowAreaInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouHouNowAreaBo">
        SELECT userid as userId,name,areaid as areaId,ISNULL(toareaid,areaid) AS nowArea FROM dbo.shouhou with(nolock) WHERE 1=1
        <if test="wxid!=null">
            AND id=#{ wxid}
        </if>
    </select>

    <select id="getZongHeZuDirector" resultType="Integer">
       SELECT ch999_id  FROM dbo.ch999_user with(nolock) WHERE area1id=13 AND iszaizhi=1
       AND (mainRole IN(439,440) OR CHARINDEX(',439,',','+Roles+',')>0 OR CHARINDEX(',440,',','+Roles+',')>0)
    </select>

    <select id="getShouHouEngineer" resultType="Integer">
      SELECT ch999_id FROM dbo.ch999_user WITH(NOLOCK) WHERE
        iszaizhi=1
        <if test="areaId!=null">
            AND area1id=#{ areaId }
        </if>
      AND (mainRole IN(18,180) OR CHARINDEX(',18,',','+Roles+',')>0 OR CHARINDEX(',180,',','+Roles+',')>0)
    </select>

    <select id="getSubCompanyLeader" resultType="Integer">
       SELECT ch999_id  FROM dbo.ch999_user with(nolock)
       WHERE iszaizhi=1
        <if test="parentAreaId!=null">
            AND area1id=#{ parentAreaId }
        </if>
       AND (mainRole IN(18,180,314) OR CHARINDEX(',18,',','+Roles+',')>0 OR CHARINDEX(',180,',','+Roles+',')>0 OR CHARINDEX(',314,',','+Roles+',')>0)
    </select>

    <select id="getParentAreaId" resultType="Integer">
       SELECT aa.id FROM dbo.areainfo a WITH(NOLOCK)
       INNER JOIN dbo.areainfo aa WITH(NOLOCK) ON aa.areaCode=LEFT(a.areaCode,6) AND aa.kind2=3
       WHERE 1=1
        <if test="areaId!=null">
            AND a.id=#{ areaId }
        </if>
    </select>

</mapper>
