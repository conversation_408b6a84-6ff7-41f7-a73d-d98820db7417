<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.WXSmsReceiverMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.WXSmsReceiver">
        <id column="id" property="id" />
        <result column="Classify" property="Classify" />
        <result column="Ch999ids" property="Ch999ids" />
        <result column="DepartCodes" property="DepartCodes" />
        <result column="DepartNames" property="DepartNames" />
        <result column="ZhiWus" property="ZhiWus" />
        <result column="ZhiWuNames" property="ZhiWuNames" />
        <result column="IsDel" property="IsDel" />
        <result column="ToTopOne" property="ToTopOne" />
        <result column="Areaids" property="Areaids" />
        <result column="RoleIds" property="RoleIds" />
    </resultMap>

    <select id="getWxSmsReceivers" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.SmsReceiverRes">
        SELECT
        *
        FROM
        (
        SELECT
        name,
        type_,
        ch999_id
        FROM
        (
        SELECT
        CASE

        WHEN
        u.area1id IN (
        SELECT distinct(dcAreaId) from authorize with(nolock) where dcAreaId is not null union
        SELECT distinct(H1AreaId) from authorize with(nolock) where H1AreaId is not null union
        SELECT DISTINCT (a.HQAreaId) FROM(
        select cast(f.split_value as int) AS HQAreaId  from dbo.authorize a with(nolock) cross apply dbo.F_SPLIT(a.HQAreaId,',') f where isnumeric(f.split_value)=1
        ) a where a.HQAreaId is not null
        ) THEN
        CAST (
        dbo.getBuMenCode ( u.departCode ) AS nvarchar ( 20 )) ELSE CAST (
        u.area1id AS nvarchar ( 10 ))
        END AS name,
        CASE
        WHEN u.area1id IN (
        SELECT distinct(dcAreaId) from authorize with(nolock) where dcAreaId is not null union
        SELECT distinct(H1AreaId) from authorize with(nolock) where H1AreaId is not null union
        SELECT DISTINCT (a.HQAreaId) FROM(
        select cast(f.split_value as int) AS HQAreaId  from dbo.authorize a with(nolock) cross apply dbo.F_SPLIT(a.HQAreaId,',') f where isnumeric(f.split_value)=1
        ) a where a.HQAreaId is not null
        ) THEN
        2 ELSE 1
        END AS type_,
        ch999_id
        FROM
        dbo.ch999_user u with(nolock)
        INNER JOIN zhiwu z with(nolock) ON u.zhiwuid = z.id
        AND u.iszaizhi = 1
        AND isnull( u.isshixi, 0 ) &lt;> 4
        AND u.islogin=0
        <choose>
            <when test="ch999Ids != null and ch999Ids.size > 0">
                and (u.ch999_id in
                <foreach collection="ch999Ids" index="index" item="ch99Id" open="(" close=")" separator=",">
                    #{ch99Id}
                </foreach>
                <choose>
                    <when test="deptCodes != null and deptCodes.size>0 and (zhiwuIds == null or zhiwuIds.size == 0)">
                        or u.departCode in
                        <foreach collection="deptCodes" index="index" item="deptCode" open="(" close="))" separator=",">
                            #{deptCode}
                        </foreach>
                        ) aa ) aa
                    </when>
                    <when test="zhiwuIds != null and zhiwuIds.size>0">
                        or u.zhiwuid in
                        <foreach collection="zhiwuIds" index="index" item="zhiwuId" open="(" close="))" separator=",">
                            #{zhiwuId}
                        </foreach>
                        ) aa ) aa
                    </when>
                    <when test="deptCodes != null and deptCodes.size>0 and zhiwuIds != null and zhiwuIds.size>0">
                        or (u.departCode in
                        <foreach collection="deptCodes" index="index" item="deptCode" open="(" close="))" separator=",">
                            #{deptCode}
                        </foreach>
                        and  u.zhiwuid in
                        <foreach collection="zhiwuIds" index="index" item="zhiwuId" open="(" close=")))" separator=",">
                            #{zhiwuId}
                        </foreach>
                        ) aa ) aa
                    </when>
                    <otherwise>
                        )) aa ) aa
                    </otherwise>
                </choose>
            </when>
            <otherwise>
                <choose>
                    <when test="deptCodes != null and deptCodes.size>0 and (zhiwuIds == null or zhiwuIds.size == 0)">
                        and u.departCode in
                        <foreach collection="deptCodes" index="index" item="deptCode" open="(" close="))" separator=",">
                            #{deptCode}
                        </foreach>
                        ) aa ) aa
                    </when>
                    <when test="zhiwuIds != null and zhiwuIds.size>0">
                        and u.zhiwuid in
                        <foreach collection="zhiwuIds" index="index" item="zhiwuId" open="(" close="))" separator=",">
                            #{zhiwuId}
                        </foreach>
                        ) aa ) aa
                    </when>
                    <when test="deptCodes != null and deptCodes.size>0 and zhiwuIds != null and zhiwuIds.size>0">
                        and (u.departCode in
                        <foreach collection="deptCodes" index="index" item="deptCode" open="(" close="))" separator=",">
                            #{deptCode}
                        </foreach>
                        and  u.zhiwuid in
                        <foreach collection="zhiwuIds" index="index" item="zhiwuId" open="(" close=")))" separator=",">
                            #{zhiwuId}
                        </foreach>
                        ) aa ) aa
                    </when>
                    <otherwise>
                        )) aa ) aa
                    </otherwise>
                </choose>
            </otherwise>
        </choose>
    </select>

</mapper>
