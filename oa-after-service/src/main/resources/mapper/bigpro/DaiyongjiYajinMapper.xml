<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.DaiyongjiYajinMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.DaiyongjiYajin" id="daiyongjiYajinMap">
        <result property="id" column="id"/>
        <result property="wxid" column="wxid"/>
        <result property="dyjid" column="dyjid"/>
        <result property="yajin" column="yajin"/>
        <result property="applydate" column="applydate"/>
        <result property="paystate" column="paystate"/>
        <result property="payorderid" column="payorderid"/>
        <result property="cancel" column="cancel"/>
        <result property="inuser" column="inuser"/>
        <result property="canceluser" column="cancelUser"/>
        <result property="canceldate" column="cancelDate"/>
        <result property="total" column="total"/>
        <result property="paytype" column="paytype"/>
        <result property="pzid" column="pzid"/>
        <result property="pzid2" column="pzid2"/>
        <result property="signimg" column="signimg"/>
        <result property="signtime" column="signtime"/>
    </resultMap>

    <select id="getDyjYajinById" resultType="com.jiuji.oa.afterservice.bigpro.po.DaiyongjiYajin">
        SELECT top 1 * FROM daiyongji_yajin WITH(NOLOCK) WHERE wxid=#{wxId} AND dyjid=#{dyjid} ORDER BY ID DESC
    </select>
</mapper>