<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouApplyMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouApply">
        <id column="ID" property="id" />
        <result column="wxid" property="wxid" />
        <result column="Title" property="Title" />
        <result column="Content" property="Content" />
        <result column="Inuser" property="Inuser" />
        <result column="addTime" property="addTime" />
        <result column="checkUser" property="checkUser" />
        <result column="checkTime" property="checkTime" />
        <result column="checkUser2" property="checkUser2" />
        <result column="checkTime2" property="checkTime2" />
        <result column="caigouid" property="caigouid" />
        <result column="caigoutime" property="caigoutime" />
        <result column="caigouuser" property="caigouuser" />
        <result column="link" property="link" />
        <result column="caigouend" property="caigouend" />
        <result column="caigouendtime" property="caigouendtime" />
        <result column="area" property="area" />
        <result column="ppid" property="ppid" />
        <result column="endtype" property="endtype" />
        <result column="remindtime" property="remindtime" />
        <result column="comment" property="comment" />
        <result column="qudao" property="qudao" />
        <result column="areaid" property="areaid" />
        <result column="kindstats" property="kindstats" />
        <result column="logs" property="logs" />
    </resultMap>
    <select id="getShouhouApplyHexiaoBo" resultType="com.jiuji.oa.afterservice.bigpro.bo.HexiaoBo">
        select
            sa.id,
            isnull(p.product_name,sa.Title) + isnull(p.product_color,'') AS productName,
            isnull(p.product_name,sa.Title) AS originProductName,
            isnull(p.product_color,'') originProductColor,
            isnull(pxi.product_label,0) as pLabel,
            p.cid,
            isnull(p.memberprice,0) price,
            isnull(p.memberprice,0) price1,
            0 priceGs,
            1 isShouhouApply,
            sa.ppid,
            sa.addTime dtime,
            sa.Inuser inuser,
            sa.kindstats beihuoStats,
            isnull(sa.buy_type, 0) as buyType,
            CASE
            WHEN ISNULL(sa.buy_type, 0) = 0 THEN 1
            WHEN ISNULL(sa.buy_type, 0) = 1 THEN 2
            ELSE NULL
            END AS orderType
        from
            shouhou_apply sa with(nolock)
        LEFT JOIN productinfo p with(nolock) ON sa.ppid = p.ppriceid
        LEFT JOIN product_xtenant_info pxi with(nolock) on pxi.ppriceid = p.ppriceid and xTenant = #{xtenant}
        where kindstats not in (5,6,7)
        <if test="wxId != null and wxId != 0">
            and sa.wxid = #{wxId}
        </if>
        and not (sa.endtype = 1 and exists(select 1 from diaobo_sub ds with(nolock) where ds.id = sa.caigouid and ds.stats in(4, 0))
        or sa.endtype = 0 and exists(select 1 from caigou_sub cs with(nolock) where cs.id = sa.caigouid and cs.stats in(-1, 3)))
    </select>

</mapper>
