<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouHouPjMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Wxkcoutput">
        <id column="id" property="id"/>
        <result column="wxid" property="wxid"/>
        <result column="name" property="name"/>
        <result column="inuser" property="inuser"/>
        <result column="dtime" property="dtime"/>
        <result column="area" property="area"/>
        <result column="price" property="price"/>
        <result column="price1" property="price1"/>
        <result column="tui" property="tui"/>
        <result column="lock1" property="lock1"/>
        <result column="lock1dtime" property="lock1dtime"/>
        <result column="tuidtime" property="tuidtime"/>
        <result column="inprice" property="inprice"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="price_gs" property="priceGs"/>
        <result column="stats" property="stats"/>
        <result column="dianping" property="dianping"/>
        <result column="areaid" property="areaid"/>
        <result column="isna" property="isna"/>
        <result column="natime" property="natime"/>
        <result column="islockkc" property="islockkc"/>
        <result column="wxtongjitotal" property="wxtongjitotal"/>
        <result column="isyouhuima" property="isyouhuima"/>
        <result column="output_dtime" property="outputDtime"/>
    </resultMap>


    <select id="listWxkcOutput" resultType="com.jiuji.oa.afterservice.bigpro.bo.wxpj.WxKcBo">
        select isnull(s.toareaid,s.areaid) as nowAreaId,wx.*,s.baoxiu,ISNULL(isnull(wx.refunded_price,wx.wxtongjitotal),wx.price) tuiPrice,
                     ISNULL(hs.id,0) as hsId,ISNULL(hs.issale,0) AS issale, ISNULL(hs.price,0) as hsprice,ISNULL(hs.isfan,0),
                     hs.isfan,hs.hsjj_pzid as hsjjPzid,isnull(hs.dktype,0) as dktype,p.cid
        from wxkcoutput wx with(nolock)
        LEFT JOIN shouhou_huishou hs with(nolock) on (hs.wxkcid=wx.id AND hs.isdel=0 and isnull(hs.from_source,0)=0)
        left join shouhou s with(nolock) on wx.wxid = s.id
        LEFT JOIN dbo.productinfo p with(nolock) ON wx.ppriceid &gt; 0 and p.ppriceid = wx.ppriceid
        <where>
            <if test="ids != null and !ids.isEmpty()">
                and wx.id in
                <foreach collection="ids" open="(" close=")" separator="," item="id">
                    #{id}
                </foreach>
            </if>
            <if test="wxId != null and wxId>0">
                and wx.wxid = #{wxId}
            </if>
        </where>


    </select>

    <select id="listServiceRecordInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.wxpj.ServiceRecordBo">
        select id, ServiceType as serviceType,tradedate,price,feiyong,startTime,endTime,servicesTypeBindId,basket_id basketId, imei
        from dbo.ServiceRecord with(nolock)
        where sub_id = #{wxId}
          <if test="wxKcId != null and wxKcId>0">
              and basket_id = #{wxKcId}
          </if>
          and ServiceType in
         <foreach collection="serviceTypes" separator="," open="(" close=")" item="serviceType">
             #{serviceType}
         </foreach>
         and isnull(isdel,0)=0
    </select>

    <select id="getServiceDataList" resultType="com.jiuji.oa.afterservice.bigpro.bo.wxpj.ShServiceDataInfoBo">
        select r.imei,r.ServiceType as serviceType,r.tradedate,p.product_name as productName,p.product_color productColor,s.userid,isnull(s.toareaid,s.areaid) areaid,s.name as pro,isnull(u.realname,u.UserName) as userName,
        s.mobile  from dbo.ServiceRecord r with(nolock)
        join dbo.wxkcoutput w with(nolock) on w.id=r.basket_id
        join dbo.shouhou s with(nolock) on w.wxid = s.id
        left join dbo.productinfo p with(nolock) on w.ppriceid=p.ppriceid
        left join dbo.BBSXP_Users u with(nolock) on s.userid=u.ID
        where isnull(r.isdel,0)=0 and r.sub_id=#{wxId} and r.ServiceType in
        <foreach collection="serviceTypes" open="(" close=")" separator="," item="serviceType">
            #{serviceType}
        </foreach>
    </select>

    <select id="getWxkcOutputByIds" resultType="com.jiuji.oa.afterservice.bigpro.bo.wxpj.WxKcBo">
        select isnull(s.toareaid,s.areaid) as nowAreaId,wx.id, wx.wxid, wx.name, wx.inuser, wx.dtime, wx.area, wx.price, wx.price1, wx.tui, wx.lock1, wx.lock1dtime
               , wx.tuidtime, wx.inprice, wx.ppriceid, wx.price_gs, wx.stats, wx.dianping, wx.areaid, wx.isna, wx.natime, wx.islockkc, wx.wxtongjitotal, wx.isyouhuima
             , wx.output_dtime, wx.youhuifeiyong, wx.tui_status, wx.refund_price, wx.discount_code,ISNULL(hs.id,0) as hsId,ISNULL(hs.issale,0) AS isSale,
               ISNULL(hs.price,0) as hsPrice,ISNULL(hs.isfan,0), hs.isFan,isnull(hs.dktype,0) as dkType ,hs.hsjj_pzid as pzId,wx.service_type serviceType
        from wxkcoutput wx with(nolock)
        LEFT JOIN shouhou_huishou hs with(nolock) on (hs.wxkcid=wx.id AND hs.isdel=0)
        left join shouhou s with(nolock) on wx.wxid = s.id
        where wx.id in
        <foreach collection="ids" index="index" item="wxKcId" open="(" close=")" separator=",">
            #{wxKcId}
        </foreach>

    </select>

    <select id="getCidByIds" resultType="java.lang.Integer">
        SELECT DISTINCT ISNULL(cid,0) AS cid
        FROM wxkcoutput wk with(nolock)
        LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = wk.ppriceid
        WHERE wxid=#{wxId}
        AND wk.id not in
        <foreach collection="ids" index="index" item="wxId" open="(" close=")" separator=",">
            #{wxId}
        </foreach>
    </select>

    <update id="updateServiceRecordDelFlagById">
        update ServiceRecord set isdel=1 where (id in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
                    or servicesTypeBindId in
                <foreach collection="ids" item="id" separator="," open="(" close=")">
                    #{id}
                </foreach>
            )
             and isnull(isdel,0)=0
    </update>

    <update id="updateCostPriceById">
        update shouhou set costprice=isnull(costprice,0)-#{inprice},feiyong=
        CASE WHEN (ISNULL(feiyong,0)-#{feiyong}) &lt; 0 THEN 0 ELSE ISNULL(feiyong,0)-#{feiyong} END
        <!--where isnull(shouyinglock,0)=0 and isnull(isquji,0)=0 and id=#{wxId} -->
        where isnull(isquji,0)=1 and id=#{wxId}
    </update>

    <update id="updateCostPriceByWxId">
        update shouhou set costprice=isnull(costprice,0)-#{inprice},feiyong= CASE WHEN
        (ISNULL(feiyong,0)-#{feiyong}) &lt; 0 THEN 0 ELSE ISNULL(feiyong,0)-#{feiyong} END WHERE ID=#{wxId}
    </update>

    <update id="updateWxkcoutputStats">
        UPDATE wxkcoutput SET stats=3,tuidtime=GETDATE()
        WHERE id in
        <foreach collection="ids" index="index" item="wxkcId" open="(" close=")" separator=",">
            #{wxkcId}
        </foreach>
    </update>

    <delete id="deleteWxkcoutputByIds">
        DELETE wxkcoutput WHERE id in
        <foreach collection="ids" index="index" item="wxId" open="(" close=")" separator=",">
            #{wxId}
        </foreach>
    </delete>

    <select id="getCardIdfromCardLogsByWxId" resultType="java.lang.Integer">
        SELECT TOP 2 ISNULL(cardid,0) FROM cardlogs with(nolock) WHERE sub_id=#{wxId} AND useType =1
        <if test="liangpin != null">
            AND ISNULL(liangpin,0)=#{liangpin}
        </if>
        <if test="cardId != null and cardId >0">
            AND cardid=#{cardId}
        </if>
    </select>

    <select id="getShouYinLockById" resultType="java.lang.Integer">
        SELECT shouyinglock FROM dbo.shouhou with(nolock) WHERE id=#{wxId}
    </select>

    <update id="updateWxkcoutputPriceByWxId">
        update wxkcoutput set price=price1 where ppriceid>0 and isnull(member_discount_amount,0)=0 and wxid = #{wxId}
    </update>

    <select id="getWxPjCountByWxId" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM dbo.wxkcoutput with(nolock) WHERE wxid=#{wxId}
    </select>

    <update id="updateNumberCardInfoByWxId">
        UPDATE nc SET nc.State=0,nc.use_count=0  FROM dbo.cardLogs cl INNER JOIN dbo.NumberCard nc ON cl.cardid=nc.ID
        WHERE cl.sub_id=#{wxId} AND ((limitType=1 AND limitids='23') OR (ch999_id=-25 OR ch999_id=-23) OR (limitType=3 AND limitids='30108,39308'))
		AND nc.State=1
    </update>

    <select id="getUpdatedNumberCardIdByWxId" resultType="cn.hutool.core.lang.Dict">
        select nc.id,nc.Total total
        FROM dbo.cardLogs cl with(nolock) INNER JOIN dbo.NumberCard nc with(nolock) ON cl.cardid=nc.ID
        WHERE cl.sub_id=#{wxId} AND ((limitType=1 AND limitids='23') OR (ch999_id=-25 OR ch999_id=-23) OR (limitType=3 AND limitids='30108,39308'))
		AND nc.State=1
    </select>

    <select id="getWxFeiYongSum" resultType="com.jiuji.oa.afterservice.bigpro.bo.wxpj.WxFeiYongBo">
        SELECT top 1 SUM(wxk.price+wxk.price_gs-ISNULL(hs.price,0.00)) feiyong,SUM(wxk.inprice) costPrice
        FROM dbo.wxkcoutput wxk with(nolock)
        LEFT JOIN dbo.shouhou_huishou hs with(nolock) ON wxk.id=hs.wxkcid WHERE wxk.wxid=#{wxId} and wxk.stats != 3 and isnull(wxk.tui_status,0)=0
    </select>

    <select id="getWxFeiYongSumZlf" resultType="com.jiuji.oa.afterservice.bigpro.bo.wxpj.WxFeiYongBo">
        SELECT top 1 SUM(wxk.price+wxk.price_gs) feiyong,SUM(wxk.inprice) costPrice FROM dbo.wxkcoutput wxk with(nolock)
        WHERE wxk.wxid=#{wxId} and wxk.stats != 3
    </select>

    <select id="checkSubCategory" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM dbo.category with(nolock) WHERE ID IN
        <foreach collection="limitIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getQuji" resultType="com.jiuji.oa.afterservice.bigpro.bo.wxpj.QujiBo">
        select  mobile,yuyueid,isBakData,id,isnull(ServiceType,0) ServiceType,name,offtime,modidate,modidtime,dyjid,costprice,userid,sxuserid,isquji,feiyong,shouyinglock,stats,isweixiu,weixiudtime,isnull(pandianinuser,'') as pandianinuser,isnull(issoft,0) as issoft,baoxiu,ISNULL(mkc_id,0) as mkc_id,ISNULL(ishuishou,0) as ishuishou,ISNULL(huiprint,0) as huiprint,wxkind,codemsg,isnull(toareaid,areaid) nowareaId,youhuima,product_id,yifum
        from shouhou with(nolock) where isnull(isquji,0)=0
        and id = #{id}
    </select>

    <select id="checkQuji" resultType="java.lang.Integer">
        select count(id) from shouhou_tuihuan with(nolock)
        where tuihuan_kind in(1,2,3,4) and isdel=0 and check2 is null and shouhou_id=#{id}
    </select>

    <select id="checkQujiToArea" resultType="java.lang.Integer">
        seLect count(id) from shouhou_toarea with(nolock) where [check]=0 and shouhou_id=#{id}
    </select>

    <select id="queryDaiyongjiUseInfo" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        SELECT dyjid,ISNULL(yifum,0) yifum
        FROM shouhou WITH(NOLOCK) WHERE id = #{shouhouId} AND (ISNULL(yifum,0)>0 OR ISNULL(dyjid,0)>0)
    </select>

    <update id="updateShouhouQujiStats">
        update shouhou set xianshi=0,isquji=1,stats=3
        OUTPUT INSERTED.yuyueid
        where id= #{shouhouId} and ((ISNULL(stats,0)=0 AND webstats=1) or (ISNULL(stats,0)=0 and webstats != 3 and (yuyueid=-1 or yuyueid>0)))
    </update>

    <select id="getShouhouYuyueId" resultType="java.lang.Integer">
        select yuyueid from  shouhou with(nolock) where id= #{shouhouId} and ((ISNULL(stats,0)=0 AND webstats=1) or (ISNULL(stats,0)=0 and webstats != 3 and (yuyueid=-1 or yuyueid>0)))
    </select>

    <update id="updateNumberCardInfo">
      UPDATE dbo.NumberCard SET use_count=0,State=0
      WHERE id in (SELECT TOP 1  ISNULL(cardid,0)
      FROM cardlogs WHERE sub_id=#{shouhouId} AND useType =1)
    </update>

    <select id="getCancelYouhuimaIdInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.wxpj.YouHuiMaBo">
        SELECT TOP 1 cl.id cid, n.ID nid FROM dbo.cardLogs cl WITH(nolock) LEFT JOIN dbo.NumberCard n WITH(nolock) ON n.id = cl.cardid
        WHERE n.State=1 AND n.ch999_id IN(-23,-25) AND sub_id=#{yuyueId}
    </select>

    <select id="queryShouhouShoujianInfo" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        select problem,yuyueid,baoxiu,webstats,stats,modidate,inuser,isnull(areaid,'') as areaid,imei,wcount,isticheng,
               isnull(sxuserid,userid) as userid, waiguan_status, waiguan
        from shouhou with(nolock) where webstats=2 and id= #{shouhouId}
    </select>

    <select id="getShouhouIsticheng" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        select TOP 1 id,offtime  from shouhou with(nolock) where xianshi=1 and isnull(isquji,0)=1 and inuser != '系统' and imei=#{imei} and imei is not null and imei !='' and isnull(iszp,0) = 0  order by offtime desc
    </select>

    <update id="acept">
        update wxkcoutput set stats=(case when ppriceid>0 then 2 else 1 end),lock1dtime=(case when ppriceid>0 then getdate() else null end) where isnull(stats,0)=0 and id= #{id}
    </update>
    <update id="updateToDelShouhouApply">
        <!--待审核 = 1, 等待采购 = 2, 调拨中 = 3, 采购中 = 4, 已完成 = 5,已删除 = 6,无法采购 = 7 -->
        UPDATE dbo.shouhou_apply SET  kindstats = 6,logs=ISNULL(logs,'')+#{sapLogMsg}
        where wxid = #{shouhouId} AND kindstats IN (1,2,3,4,7) and exists(select 1 from shouhou where id = #{shouhouId} and xianshi = 0)
    </update>

    <select id="getYouhuiTotalByWxId" resultType="java.math.BigDecimal">
        SELECT SUM(nc.Total) total FROM dbo.cardLogs cl with(nolock)
        INNER JOIN dbo.NumberCard nc with(nolock)  ON cl.cardid=nc.ID
        WHERE cl.sub_id=#{wxId} AND ((limitType=1 AND CHARINDEX(','+nc.limitids+',',',23,')>0) OR ch999_id in(-25,-23)
                                         OR (limitType=3 AND (CHARINDEX(','+nc.limitids+',',',30108,')>0 or CHARINDEX(','+nc.limitids+',',',39308,')>0)))
    </select>
    <select id="existKcOut" resultType="java.lang.Boolean">
        select iif(exists(select 1 from product_kclogs pkcl with(nolock) where shouhou_id in (#{shouhouId},#{wxkcId}) and pkcl.count &lt; 0
                                                              and pkcl.ppriceid = #{ppid} and exists(select 1 from wxkcoutput w with(nolock) where w.wxid = #{shouhouId} and pkcl.dtime >= w.dtime)),1,0)
    </select>
    <select id="getCidByWxIds" resultType="java.lang.Integer">
        SELECT DISTINCT ISNULL(cid,0) AS cid
        FROM wxkcoutput wk with(nolock)
                 LEFT JOIN dbo.productinfo p with(nolock) ON p.ppriceid = wk.ppriceid
        WHERE wxid=#{wxId}
    </select>

    <select id="listPjRproduct" resultType="com.jiuji.oa.afterservice.bigpro.bo.shouhou.RProductResultBo">
        select *
        from(
                SELECT       r.ppid, <!-- ppid 主商品商品ID-->
                             r.pid,
                             row_number() over (partition by r.pid order by case r.ppid when #{productId} then -1 else r.ppid end asc) sort_rank
                from rproduct r
                <where>
                    <!-- pid 配件商品ID -->
                    and r.pid in
                    <foreach collection="pjProductIds" item="pjProductId" separator="," open="(" close=")">
                        #{pjProductId}
                    </foreach>
                    <!-- 维修单 -->
                    and _type=1
                    and isdel=0
                </where>
        ) r where r.sort_rank = 1
    </select>
</mapper>
