<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouServiceConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouServiceConfig">
        <id column="id" property="id" />
        <result column="ppid" property="ppid" />
        <result column="price" property="price" />
        <result column="service_ppid" property="servicePpid" />
        <result column="service_type" property="serviceType" />
        <result column="xtenant" property="xtenant" />
        <result column="is_del" property="isDel" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>
    <select id="getProductName" resultType="com.jiuji.oa.afterservice.bigpro.po.Productinfo">
        select * from productinfo WITH(NOLOCK) where ppriceid in
        <foreach collection="ppids" item="id" close=")" open="(" separator="," index="index">
            #{id}
        </foreach>
    </select>
    <select id="getShouhouServiceConfigs"
            resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouServiceConfig">
        select * from shouhou_service_config WITH(NOLOCK) WHERE 1=1
        <if test="req.productName != null and req.productName != ''">
            AND product_name like concat('%',#{req.productName},'%')
        </if>
        <if test="req.createTime_1 != null and req.createTime_2 != null">
            AND create_Time between #{req.createTime_1}  and #{req.createTime_2}
        </if>
        <if test="req.ppid != null and req.ppid != 0">
            or ppid = #{req.ppid}
        </if>
        <if test="req.serviceType != null and req.serviceType != 0">
            AND service_type = #{req.serviceType}
        </if>
         <if test="req.xtenant != null and req.xtenant != 0">
            AND xtenant = #{req.xtenant}
        </if>
        <if test="req.price_1 != null and req.price_2 != null">
            AND price  between #{req.price_1}  and #{req.price_2}
        </if>

        <choose>
            <when test="req.isDel != null">
                AND is_del =#{req.isDel}
            </when>
            <!--<otherwise>
                and isnull(is_del,0) = 0
            </otherwise>-->
        </choose>
    </select>

</mapper>
