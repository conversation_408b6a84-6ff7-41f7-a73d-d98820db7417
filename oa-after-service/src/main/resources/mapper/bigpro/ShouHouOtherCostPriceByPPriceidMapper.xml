<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouHouOtherCostPriceByPPriceidMapper">


    <select id="isOutSameFitting" resultType="Integer">

        select top 1 id from wxkcoutput w with(nolock)
        where 1=1
        <if test="wxid !=null ">
            AND wxid=#{wxid}
        </if>
        <if test="ppriceid !=null ">
            AND ppriceid=#{ppriceid}
        </if>
    </select>

    <select id="getCostPriceKcCount" resultType="com.jiuji.oa.afterservice.bigpro.bo.WxCostAndKcCount">

        select k.leftcount as leftCount,k.inprice as inPrice,p.cid,p.product_color as productColor,p.product_name as productName
             ,p.memberprice as memberPrice
        from product_kc k with(nolock)
        left join productinfo p with(nolock) on p.ppriceid = k.ppriceid
        where 1=1
        AND k.ppriceid in
        <foreach collection="ppriceids" separator="," open="(" close=")" item="ppid">
            #{ppid}
        </foreach>
        AND k.areaid=#{areaid}
    </select>

    <update id="updateShouHouFeiYong" >
        UPDATE shouhou set costprice=isnull(costprice,0.0)+ #{inprice},feiyong=isnull(feiyong,0.0)+ #{price} + #{priceGs}
        where ((isnull(shouyinglock,0)=0 and isnull(isquji,0)=0) or yuyueid = -1)
        <if test="wxid !=null ">
            AND id= #{wxid}
        </if>

    </update>
</mapper>
