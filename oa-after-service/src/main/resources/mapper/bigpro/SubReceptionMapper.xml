<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.SubReceptionMapper">
    <select id="isSubReception" resultType="java.lang.Integer">
        select count(1)
        from dbo.sub_reception with (nolock)
        where type_ = #{type}
          and sub_id = #{subId}
          and isnull(ch999_id, 0) > 0
    </select>

    <select id="isSubNeedReception" resultType="java.lang.Integer">
        select count(1)
        from dbo.sub_reception with (nolock)
        where type_ = #{type}
          and userid = #{userId}
          and areaid = #{areaId}
          and datediff(hour, pushdate, getdate()) &lt;= 3
          and isnull(ch999_id, 0) > 0
    </select>
</mapper>
