<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.CardLogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.CardLogs">
        <id column="id" property="id" />
        <result column="cardid" property="cardid" />
        <result column="sub_id" property="subId" />
        <result column="PushTime" property="PushTime" />
        <result column="area" property="area" />
        <result column="useren" property="useren" />
        <result column="userid" property="userid" />
        <result column="useType" property="useType" />
        <result column="areaid" property="areaid" />
        <result column="liangpin" property="liangpin" />
        <result column="prices" property="prices" />
    </resultMap>
    <select id="getNumberCardInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.NumberCardSimpleBo">
        SELECT TOP 1 cl.id cid, n.ID nid FROM dbo.cardLogs cl WITH(nolock) LEFT JOIN dbo.NumberCard n WITH(nolock) ON n.id = cl.cardid
        WHERE n.State=1 AND n.ch999_id IN(-23,-25) AND sub_id=#{subId}
    </select>

    <select id="getYuyueUsedCoupon" resultType="java.lang.String">
            SELECT TOP 1 n.CardID FROM dbo.cardLogs cl WITH(nolock) LEFT JOIN dbo.NumberCard n WITH(nolock) ON n.id = cl.cardid  WHERE ((n.limitids='23' AND n.State=1 AND n.limitType=1 AND n.ch999_id=-25) OR ( n.ch999_id=-23 ))
            <if test="yyId">
                AND sub_id = #{yyId}
            </if>
    </select>

</mapper>
