<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouQudaoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouQudao">
        <id column="id" property="id"/>
        <result column="shqdid" property="shqdid"/>
        <result column="shqd2id" property="shqd2id"/>
        <result column="shqd2name" property="shqd2name"/>
        <result column="shouhouid" property="shouhouid"/>
        <result column="inuser" property="inuser"/>
        <result column="dtime" property="dtime"/>
        <result column="starttime" property="starttime"/>
        <result column="endtime" property="endtime"/>
        <result column="type" property="type"/>
    </resultMap>

    <select id="getShouhouQudaoTel" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouQudaoTelBo">
        SELECT top 1 qd.tel,qd.company FROM dbo.shouhou_qudao a WITH(NOLOCK) INNER JOIN dbo.Ok3w_qudao qd WITH(NOLOCK)
        ON qd.id=a.shqd2id WHERE 1=1
        <if test="shouhouId != null and shouhouId != 0">
            and a.shouhouid=#{shouhouId}
        </if>
    </select>

    <select id="getshouhouQudaoByImeiAndCurrentQudaoId" resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouQudao">
        select top 1 * from shouhou_qudao q with(NOLOCK) inner join shouhou s with(NOLOCK) on q.shouhouid = s.id
        where s.imei = #{imei} and q.id != #{currentQudaoId}
        order by s.modidate desc
    </select>

    <select id="getJieJianInfoListByIds" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouJieJianInfo">
        select sh.id as shouhouId,sh.areaid as areaId, a.area,sh.inuser as inUser, sh.name+isnull(sh.product_color,'') as productColor
        from shouhou sh with(nolock)
        left join shouhou_qudao q with(nolock) on sh.id = q.shouhouid
        left join areainfo a with(nolock) on sh.areaid = a.id
        where sh.id in
        <foreach collection="shouhouIds" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and q.id is null
    </select>

    <select id="getShouHouQuDaoList" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouQuDaoList">
        select distinct sh.id ,isnull( sh.truename,u.UserName) memberName,sh.mobile,
        sh.name+isnull(sh.product_color,'') productColor,sh.imei,
        case when tp.id is not null then '已开' else '未开' end piaoStatus,sh.problem,sh.peizhi,
        case when sh.ServiceType=1 then '意外保'
        when sh.ServiceType=2 then '延保'
        when sh.ServiceType=4 then '电池保'
        when sh.ServiceType=5 then '碎屏保'
        when sh.ServiceType=7 then '屏背保'
        when sh.ServiceType=8 then '以换待修'
        when sh.ServiceType=9 then '售后屏幕保'
        when sh.ServiceType=10 then '售后电池保'
        when sh.ServiceType=11 then '售后电后盖保'
        else '无' end serviceType,case when sh.baoxiu=0 then '不在'
        when sh.baoxiu=1 then '在'
        when sh.baoxiu=2 then '外修'
        when sh.baoxiu=3 then '待检测'
        else '' end baoXiuStatus ,sh.modidate receiveDate,sq.shqd2name qdName,sq.dtime qdDate,
        sh.inuser inUser
        from shouhou sh with(nolock)
        left join shouhou_qudao sq with(nolock)on sq.shouhouid = sh.id
        left join tax_piao tp with(nolock) on tp.sub_id=sh.id and isweixiu=1 and tp.flag in (3,4)
        left join BBSXP_Users u with(nolock) on u.ID=sh.userid
        where sh.xianshi=1
              <choose>
                  <when test="req.shouhouIdList == null || req.shouhouIdList.isEmpty()">and sq.inuser=#{req.userName} and DATEDIFF(DAY,sq.dtime ,GETDATE()) = 0</when>
                  <otherwise>
                    and sh.id in
                    <foreach collection="req.shouhouIdList" open="(" close=")" separator="," item="shouhouId">
                        #{shouhouId}
                    </foreach>
                  </otherwise>
              </choose>
        order by sq.dtime desc
    </select>
    <select id="existsQuDao" resultType="java.lang.Boolean">
        select iif(exists(select 1 from shouhou_qudao sq with(nolock) where sq.shouhouid= #{shouhouId}),1,0)
    </select>
</mapper>
