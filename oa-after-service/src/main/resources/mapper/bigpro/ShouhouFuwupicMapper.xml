<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouFuwupicMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.ShouhouFuwupic" id="shouhouFuwupicMap">
        <result property="id" column="id"/>
        <result property="shouhouid" column="shouhouid"/>
        <result property="picid" column="picid"/>
        <result property="fid" column="fid"/>
        <result property="fkind" column="fkind"/>
        <result property="inuser" column="inuser"/>
        <result property="indate" column="indate"/>
        <result property="qujiuser" column="qujiuser"/>
        <result property="qujidate" column="qujidate"/>
    </resultMap>

    <select id="getFuwuPicInfo" resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouFuwupic">
        SELECT f.* FROM dbo.shouhou_fuwupic f WITH(NOLOCK)
        INNER JOIN dbo.shouhou s WITH(NOLOCK) ON f.shouhouid=s.id
        WHERE s.id=#{shouhouId}
        AND ISNULL(s.ServiceType,0)!=0
    </select>

</mapper>