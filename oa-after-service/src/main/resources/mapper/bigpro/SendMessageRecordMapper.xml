<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.SendMessageRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.SendMessageRecord">
        <id column="Id" property="Id" />
        <result column="dtime" property="dtime" />
        <result column="Type" property="Type" />
        <result column="ReciverIds" property="ReciverIds" />
        <result column="Message" property="Message" />
        <result column="RecordIP" property="RecordIP" />
    </resultMap>

    <insert id="saveSendMesaageRecord">
        insert into ${officeName}.dbo.SendMessageRecord(Type,ReciverIds,Message,RecordIP,dtime) VALUES(#{type},#{reciverIds},#{message},#{ip},#{dTime})
    </insert>

</mapper>
