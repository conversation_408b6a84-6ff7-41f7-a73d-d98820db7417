<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.WaisongHexiaoMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.WaisongHexiaoPo">
        <id column="is_del" property="del" />
    </resultMap>
    <sql id="listWhere">
        <!--授权隔离-->
        <if test="query.authPart">
            <!--租户隔离-->
            and exists (SELECT id from areainfo a with(nolock) where a.id = swr.area_id and a.xtenant = #{query.xtenant})
            and (swr.area_id = #{query.currAreaId} or exists (select id from areainfo a2 with(nolock) where a2.id = swr.area_id and a2.authorizeid = #{query.authorizeId}))
        </if>
        <if test="query.searchType != null and query.searchText != null and query.searchText != ''">
            <choose>
                <when test="query.searchType == 0">
                    AND swr.wx_id = #{query.searchText}
                </when>
                <when test="query.searchType == 1">
                    AND p.product_id = #{query.searchText}
                </when>
                <when test="query.searchType == 2">
                    AND p.product_name like CONCAT('%',#{query.searchText},'%')
                </when>
                <when test="query.searchType == 3">
                    AND  swr.account_id = #{query.searchText}
                </when>
                <when test="query.searchType == 4">
                    AND  swr.logistics_id = #{query.searchText}
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
        <if test="query.areaIds != null and !query.areaIds.isEmpty()">
            AND swr.area_id in
            <foreach collection="query.areaIds" separator="," open="(" close=")" item="areaId">
                #{areaId}
            </foreach>
        </if>
        <if test="query.status != null">
            AND swr.status = #{query.status}
        </if>
        <if test="query.recoverBillStatus != null">
            AND swr.recover_bill_status = #{query.recoverBillStatus}
        </if>
        <if test="query.timeType != null and query.timeRange != null and query.timeRange.length >0">
            <choose>
                <when test="query.timeType == 1">
                    AND swr.create_time between #{query.timeRange[0]} and #{query.timeRange[1]}
                </when>
                <when test="query.timeType == 2">
                    AND swr.pay_time between #{query.timeRange[0]} and #{query.timeRange[1]}
                </when>
                <when test="query.timeType == 3">
                    AND swr.account_time between #{query.timeRange[0]} and #{query.timeRange[1]}
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
    </sql>
    <select id="selectLists" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.WaisongHexiaoVO">
        SELECT distinct swr.id,swr.ppriceid,swr.applicant, swr.price, swr.status, swr.recover_bill_status, swr.account_id, swr.logistics_id, swr.wxk_id
        ,swr.area,swr.area_id,swr.wx_id, swr.service_type, swr.bills as billsInt, swr.create_time, pay_time, account_time,p.product_color,p.product_name
        FROM dbo.[shouhou_waisong_reimbursement ] swr with(nolock)
        left join dbo.productinfo p with(nolock) on p.ppriceid = swr.ppriceid
        where 1=1 <include refid="listWhere"></include>
    </select>
    <select id="getHexiaoNotDelAndWxNotDelAndWxkNotDel" resultMap="BaseResultMap">
        SELECT id, applicant, price, remarks, status, recover_bill_status, account_id, logistics_id, wxk_id, wx_id, service_type
        , bills, alipay_bind_account, create_time, update_time, is_del, applicant_id, wx_create_time, area_id, area, pay_time, account_time
        FROM dbo.[shouhou_waisong_reimbursement ] swr with(nolock)
        <where>
            <!--授权隔离-->
            <if test="isAuthPart">
                <!--租户隔离-->
                exists (SELECT id from areainfo a with(nolock) where a.id = swr.area_id and a.xtenant = #{xtenant})
                and (swr.area_id = #{areaId} or exists (select id from areainfo a2 with(nolock) where a2.id = swr.area_id and a2.authorizeid = #{authorizeId}))
            </if>
            <!--维修单-->
            and EXISTS (SELECT id from shouhou s with(nolock) where s.id= swr.wx_id and isnull(s.xianshi,1) = 1)
            <!--配件单-->
            and exists (SELECT id from wxkcoutput wc with(nolock) where wc.id= swr.wxk_id and wc.stats &lt;&gt;3)
            <choose>
                <when test="id == null">
                    AND swr.wx_id = #{wxId} and swr.wxk_id = #{wxkId}
                </when>
                <otherwise>
                    and swr.id = #{id}
                </otherwise>
            </choose>
             and swr.is_del = 0 and swr.status &lt;&gt;6
        </where>
    </select>
    <select id="sumPrice" resultType="java.math.BigDecimal">
        select sum(swr.price) FROM dbo.[shouhou_waisong_reimbursement ] swr with(nolock)
        left join dbo.productinfo p with(nolock) on p.ppriceid = swr.ppriceid
        where 1=1 <include refid="listWhere"></include>
    </select>
    <select id="statisticsByApplicantId"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.WaisongHexiaoApplyVO$StatisticsApplicantMonth">
        select sum(swr.price) as applicantMonthTotal,count(swr.id) as applicantMonthCount FROM dbo.[shouhou_waisong_reimbursement ] swr with(nolock)
        where swr.applicant_id = #{applicantId} and swr.create_time between #{startTime} and #{endTime} and swr.is_del = 0
          and swr.status &lt;&gt;6
    </select>
    <select id="getHexiao" resultType="com.jiuji.oa.afterservice.bigpro.po.WaisongHexiaoPo">
        SELECT id, applicant, price, remarks, status, recover_bill_status, account_id, logistics_id, wxk_id, wx_id, service_type
        , bills, alipay_bind_account, create_time, update_time, is_del, applicant_id, wx_create_time, area_id, area, pay_time, account_time
        FROM dbo.[shouhou_waisong_reimbursement ] swr with(nolock)
        <where>
            <!--授权隔离-->
            <if test="isAuthPart">
                <!--租户隔离-->
                exists (SELECT id from areainfo a with(nolock) where a.id = swr.area_id and a.xtenant = #{xtenant})
                and (swr.area_id = #{areaId} or exists (select id from areainfo a2 with(nolock) where a2.id = swr.area_id and a2.authorizeid = #{authorizeId}))
            </if>
            <choose>
                <when test="id == null">
                    AND swr.wx_id = #{wxId} and swr.wxk_id = #{wxkId}
                </when>
                <otherwise>
                    and swr.id = #{id}
                </otherwise>
            </choose>
            and swr.is_del = 0
        </where>
    </select>
</mapper>