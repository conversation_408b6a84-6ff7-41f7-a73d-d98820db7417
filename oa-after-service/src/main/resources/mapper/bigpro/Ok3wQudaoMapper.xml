<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.Ok3wQudaoMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.Ok3wQudao" id="ok3wQudaoMap">
        <result property="id" column="id"/>
        <result property="kinds" column="kinds"/>
        <result property="company" column="company"/>
        <result property="username" column="username"/>
        <result property="tel" column="tel"/>
        <result property="fax" column="fax"/>
        <result property="mobile" column="mobile"/>
        <result property="qq" column="QQ"/>
        <result property="wangwang" column="wangwang"/>
        <result property="address" column="address"/>
        <result property="area" column="Area"/>
        <result property="comment" column="comment"/>
        <result property="adddate" column="adddate"/>
        <result property="inuser" column="inuser"/>
        <result property="ispass" column="ispass"/>
        <result property="email" column="email"/>
        <result property="insourceid" column="insourceid"/>
        <result property="companyJc" column="company_jc"/>
        <result property="gZh1" column="g_zh1"/>
        <result property="gHm1" column="g_hm1"/>
        <result property="gKhh1" column="g_khh1"/>
        <result property="gZh2" column="g_zh2"/>
        <result property="gHm2" column="g_hm2"/>
        <result property="gKhh2" column="g_khh2"/>
        <result property="sZh1" column="s_zh1"/>
        <result property="sHm1" column="s_hm1"/>
        <result property="sKhh1" column="s_khh1"/>
        <result property="sZh2" column="s_zh2"/>
        <result property="sHm2" column="s_hm2"/>
        <result property="sKhh2" column="s_khh2"/>
        <result property="cwFzr" column="cw_fzr"/>
        <result property="cwLxfs" column="cw_lxfs"/>
        <result property="sZh3" column="s_zh3"/>
        <result property="sHm3" column="s_hm3"/>
        <result property="sKhh3" column="s_khh3"/>
        <result property="sZh4" column="s_zh4"/>
        <result property="sHm4" column="s_hm4"/>
        <result property="sKhh4" column="s_khh4"/>
        <result property="sZh5" column="s_zh5"/>
        <result property="sHm5" column="s_hm5"/>
        <result property="sKhh5" column="s_khh5"/>
        <result property="sZh6" column="s_zh6"/>
        <result property="sHm6" column="s_hm6"/>
        <result property="sKhh6" column="s_khh6"/>
        <result property="sZh7" column="s_zh7"/>
        <result property="sHm7" column="s_hm7"/>
        <result property="sKhh7" column="s_khh7"/>
        <result property="sZh8" column="s_zh8"/>
        <result property="sHm8" column="s_hm8"/>
        <result property="sKhh8" column="s_khh8"/>
        <result property="kfp" column="kfp"/>
        <result property="authorizeid" column="authorizeid"/>
        <result property="signstarttime" column="signStartTime"/>
        <result property="signendtime" column="signEndTime"/>
        <result property="cityid" column="cityid"/>
        <result property="pid" column="pid"/>
        <result property="zid" column="zid"/>
        <result property="did" column="did"/>
        <result property="companynature" column="CompanyNature"/>
        <result property="qudaonature" column="QuDaoNature"/>
        <result property="qudaolevel" column="QuDaoLevel"/>
        <result property="paytype" column="PayType"/>
        <result property="registeredcapital" column="RegisteredCapital"/>
        <result property="legalrepresent" column="LegalRepresent"/>
        <result property="legalmobile" column="LegalMobile"/>
        <result property="weixin" column="WeiXin"/>
        <result property="shouhoucontacts" column="shouhouContacts"/>
        <result property="shouhoumobile" column="shouhouMobile"/>
        <result property="yajin" column="yajin"/>
        <result property="samekindqudao" column="SameKindQuDao"/>
        <result property="loanamountM" column="LoanAmount_M"/>
        <result property="loanamount" column="LoanAmount"/>
        <result property="caiwucheckuser" column="CaiWuCheckUser"/>
        <result property="caiwuchecktime" column="CaiWuCheckTime"/>
        <result property="shenjicheckuser" column="ShenJiCheckUser"/>
        <result property="shenjichecktime" column="ShenJiCheckTime"/>
        <result property="pandianuser" column="PanDianUser"/>
        <result property="pandiantime" column="PanDianTime"/>
        <result property="kemu" column="kemu"/>
        <result property="charger" column="charger"/>
        <result property="username1" column="username_1"/>
        <result property="tel1" column="tel_1"/>
        <result property="username2" column="username_2"/>
        <result property="tel2" column="tel_2"/>
        <result property="comment1" column="comment1"/>
        <result property="seltpurchase" column="seltPurchase"/>
        <result property="cooperationid" column="CooperationId"/>
        <result property="customcode" column="CustomCode"/>
        <result property="userid" column="userid"/>
        <result property="channeltype" column="ChannelType"/>
        <result property="channelscale" column="ChannelScale"/>
        <result property="deposithasreceipt" column="DepositHasReceipt"/>
        <result property="cids" column="cids"/>
        <result property="brandid" column="brandid"/>
        <result property="receiver" column="Receiver"/>
        <result property="shippingaddress" column="ShippingAddress"/>
        <result property="bindareaid" column="bindAreaId"/>
    </resultMap>

    
    <select id="getChannelList" resultType="com.jiuji.oa.afterservice.bigpro.po.Ok3wQudao">
        SELECT q.*,l.kind FROM dbo.Ok3w_qudao q WITH(NOLOCK)
                INNER JOIN dbo.channel_kind_link l WITH(NOLOCK) ON q.id = l.channel_id
                where l.channel_state = 1 or isnull(q.ispass,0) = 1
    </select>

</mapper>