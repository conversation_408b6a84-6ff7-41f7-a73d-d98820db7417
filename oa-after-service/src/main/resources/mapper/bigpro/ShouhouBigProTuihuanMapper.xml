<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouBigProTuihuanMapper">
    <!--    获取用户电话-->
    <select id="getSubMobileFromSub" resultType="java.lang.String">
        select sub_mobile from sub  WITH(NOLOCK)  where sub_id=(select sub_id from shouhou WITH(NOLOCK) where id = #{shouhouid})
    </select>
    <!--    获取用户电话 （良品）-->
    <select id="getSubMobileFromRecover" resultType="java.lang.String">
        select sub_mobile from recover_marketinfo   WITH(NOLOCK) where sub_id=(select sub_id from shouhou  WITH(NOLOCK) where id = #{shouhouid})
    </select>
    <!--    退换需要的查询条件1-->
    <select id="getHuanInfo1" resultType="java.lang.String">
     	 SELECT
-- 	h.name,
-- 	h.product_color,
-- 	imei,
-- 	stats,
-- 	h.product_id AS productid,
-- 	baoxiu,
		p.ppriceid1 AS ppriceid
	FROM
		shouhou h  WITH(NOLOCK)
		LEFT JOIN productinfo p  WITH(NOLOCK) ON h.ppriceid= p.ppriceid
	WHERE
		(
			( areaid =#{ areaId } AND toareaid IS NULL )
				OR ( toareaid =#{ areaId } AND toareaid IS NOT NULL )
			)
		AND id =#{ id }
    </select>
    <!--    退换需要的查询条件1-->
    <select id="getHuanInfo2" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.HuanInfo">
      SELECT
		id,
		product_name,
		product_color,
		imei,
		k.ppriceid
	  FROM
		product_mkc k  WITH(NOLOCK)
		LEFT JOIN productinfo p  WITH(NOLOCK) ON k.ppriceid= p.ppriceid
	  WHERE
		k.areaid=#{ aredId }
		AND k.kc_check IN ( 6, 13 )
		AND k.id=#{ id }
    </select>
    <!--    退换需要的查询条件1-->
    <select id="GetHuanBySub1" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
      SELECT
		name,
		product_color,
		imei,
		stats,
		product_id,
		baoxiu,
		userid,
		sub_id
	  FROM
		shouhou  WITH(NOLOCK)
	  WHERE
		(
			( areaid =#{ areaId } AND toareaid IS NULL )
			OR ( toareaid =#{ areaId } AND toareaid IS NOT NULL )
		)
		AND id =#{ id}
    </select>
	<!--    退换需要的查询条件1-->
	<select id="GetHuanBySub2" resultType="com.jiuji.oa.afterservice.sub.po.Sub">
      SELECT
		sub_id,
		sub_pay,
		yingfuM,
		yifuM
	  FROM
		sub  WITH(NOLOCK)
	  WHERE
		sub_id =#{ subId }
		AND userid =#{ userId }
		AND areaid =#{ areaId }
		AND sub_check IN ( 1, 6 )
    </select>

	<select id="checkUserIsBindWeixin" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
		select s.sub_id as subId,s.userid,s.areaid from shouhou_tuihuan t  WITH(NOLOCK) inner join shouhou  WITH(NOLOCK) on t.shouhou_id = s.id where t.id = #{tuihuanId}
	</select>
</mapper>
