<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouHuishouMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouHuishou">
        <id column="id" property="id"/>
        <result column="shouhou_id" property="shouhouId"/>
        <result column="ppid" property="ppid"/>
        <result column="name" property="name"/>
        <result column="price" property="price"/>
        <result column="kcount" property="kcount"/>
        <result column="inuser" property="inuser"/>
        <result column="indate" property="indate"/>
        <result column="isdel" property="isdel"/>
        <result column="issale" property="issale"/>
        <result column="saleprice" property="saleprice"/>
        <result column="company_id" property="companyId"/>
        <result column="saleuser" property="saleuser"/>
        <result column="saledate" property="saledate"/>
        <result column="area" property="area"/>
        <result column="isfan" property="isfan"/>
        <result column="fancheck" property="fancheck"/>
        <result column="checkUser" property="checkUser"/>
        <result column="checkDate" property="checkDate"/>
        <result column="istoarea" property="istoarea"/>
        <result column="toarea" property="toarea"/>
        <result column="suser" property="suser"/>
        <result column="sdtime" property="sdtime"/>
        <result column="ruser" property="ruser"/>
        <result column="rdtime" property="rdtime"/>
        <result column="confirm" property="confirm"/>
        <result column="complete" property="complete"/>
        <result column="completedate" property="completedate"/>
        <result column="completeuser" property="completeuser"/>
        <result column="ishuan" property="ishuan"/>
        <result column="dktype" property="dktype"/>
        <result column="ishuanhuo" property="ishuanhuo"/>
        <result column="qudao" property="qudao"/>
        <result column="huanhuoUser" property="huanhuoUser"/>
        <result column="huanhuoTime" property="huanhuoTime"/>
        <result column="returnBasketid" property="returnBasketid"/>
        <result column="areaid" property="areaid"/>
        <result column="toareaid" property="toareaid"/>
        <result column="issalecheck" property="issalecheck"/>
        <result column="wuliuid" property="wuliuid"/>
        <result column="wxkcid" property="wxkcid"/>
        <result column="kind" property="kind"/>
        <result column="pzid" property="pzid"/>
        <result column="yaping" property="yaping"/>
        <result column="inprice" property="inprice"/>
        <result column="bindtoolcount" property="bindtoolcount"/>
        <result column="bindtoolprice" property="bindtoolprice"/>
        <result column="baofeitime" property="baofeitime"/>
        <result column="srktime" property="srktime"/>
        <result column="PreSaleChannel" property="PreSaleChannel"/>
        <result column="baofeipz" property="baofeipz"/>
        <result column="baofeipq" property="baofeipq"/>
        <result column="sale_piqianid" property="salePiqianid"/>
        <result column="sale_pzid" property="salePzid"/>
        <result column="ch999_name" property="ch999Name"/>
        <result column="ch999_id" property="ch999Id"/>
    </resultMap>

    <sql id="query">
        FROM dbo.shouhou_huishou hs WITH ( NOLOCK )
        left join dbo.shouhou s with (nolock) on s.id = hs.shouhou_id
        left join dbo.ch999_user u with(nolock ) on u.ch999_name=s.weixiuren
        LEFT JOIN dbo.shouhou_returnbasket r WITH ( NOLOCK ) ON r.comment = cast(hs.shouhou_id as char(20))
        LEFT JOIN wxkcoutput w with(nolock) on w.wxid=hs.shouhou_id and w.ppriceid = hs.ppid
        where isnull(hs.ishuanhuo,0) = 1
        and hs.toareaid IS NOT NULL and hs.istoarea=0
        and not exists (select 1 from dbo.smallpro_wx ws with (nolock) where hs.shouhou_id=ws.sub_id and ws.type=1)
        and hs.indate &gt;= '2020-04-01 00:00:00'
    </sql>

    <select id="countHuishouToWx" resultType="java.lang.Integer">
        select count(1)
        <include refid="query"></include>
    </select>

    <select id="listHuishouToWx" resultMap="BaseResultMap">
        SELECT
        hs.shouhou_id AS id,
        hs.ppid,
        hs.name,
        w.inprice AS price,
        hs.areaid,
        u.ch999_name,
        u.ch999_id,
        hs.indate
        <include refid="query"></include>
        order by id desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <sql id="diaoboQuery">
        from diaobo_basket b with(nolock)
        left join diaobo_sub s with(nolock) on s.id=b.sub_id
        left join dbo.ch999_user u with(nolock ) on u.ch999_name=s.inuser
        where 1=1
        and isnull(s.toareaid,0) = 13
        and s.stats = 4
        and s.kinds='wx'
        and not exists (select 1 from dbo.smallpro_wx ws with (nolock) where b.id=ws.basket_id and ws.type=2)
        and s.ruku_dtime &gt;= '2020-04-01 00:00:00'
    </sql>

    <select id="countDiaoboToWx" resultType="java.lang.Integer">
        select count(1)
        <include refid="diaoboQuery"></include>
    </select>

    <select id="listDiaoboToWx" resultType="com.jiuji.oa.afterservice.other.bo.DiaoboToWxBO">
        select
        b.id as id,
        s.id as subId,
        b.ppriceid as ppid,
        isnull(b.inprice,0) as price,
        b.lcount as count,
        s.areaid as areaId,
        u.ch999_id as ch999Id,
        s.dtime as dTime
        <include refid="diaoboQuery"></include>
        order by dtime desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <select id="getgetHuishouListCount" parameterType="com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouHuishouListReq"
            resultType="java.lang.Integer">
        select count(1)
        <include refid="huishouQuery"></include>
    </select>

    <select id="getHuishouList" parameterType="com.jiuji.oa.afterservice.bigpro.vo.req.ShouhouHuishouListReq"
            resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouHuishouListBo">
        SELECT (select top 1 fid from attachments with(nolock) where type = 6 and linkedID=hs.shouhou_id) as topfid,
        p.product_name as productName,p.product_color AS productColor,p.pLabel,
        <!--之前sql   CASE WHEN CHARINDEX( '', 23, '', p.cidFamily ) > 0 THEN '' wx '' ELSE '' pj ''  -->
        CASE WHEN PATINDEX(23, p.cidFamily)> 0 THEN 'wx' ELSE 'pj' END AS pjkinds,
        hs.*, c.company_jc AS companyJc ,hp.huanhuiprice,sh.problem,isnull(sh.weixiuren,'''') weixiuren
        <include refid="huishouQuery">
        </include>
        order by id desc
        OFFSET #{startRows} ROWS FETCH NEXT #{size} ROWS ONLY
    </select>

    <sql id="huishouQuery">
        FROM shouhou_huishou hs WITH(NOLOCK)
        LEFT JOIN shouhou sh WITH(NOLOCK) on hs.shouhou_id = sh.id
        LEFT JOIN productinfo p WITH(NOLOCK) on hs.ppid=p.ppriceid
        LEFT JOIN Ok3w_qudao c WITH(NOLOCK) on hs.company_id = c.id and c.IsPass=1 and c.company_jc is not null
        LEFT JOIN shouhou_huishou_huanhuiprice hp WITH(NOLOCK) on hs.ppid=hp.ppriceid
        WHERE 1=1
        <if test="param.hanghuo != null and param.hanghuo == 1">
            and hs.ppid=0
        </if>
        <if test="param.ishuan !=null and param.ishuan ==1 or param.stats ==5 or param.stats ==6 or param.stats ==7 or param.stats ==8">
            and hs.ishuan=1
        </if>
        <if test="param.stats != null">
            <choose>
                <when test="param.stats == 9">
                    and isnull(hs.issale,0)=0 and hs.isfan=0 and isnull(hs.issalecheck,0) = 1
                </when>
                <when test="param.stats == 2">
                    and hs.issale=1 and hs.isfan=0
                </when>
                <when test="param.stats == 3">
                    and hs.isdel=1
                </when>
                <when test="param.stats == 4">
                    and hs.isfan=1
                </when>
                <otherwise>
                    <choose>
                        <when test="param.stats == 5">
                            and [confirm] is null
                        </when>
                        <when test="param.stats == 6">
                            and [confirm]=1
                        </when>
                        <when test="param.stats == 7">
                            and [confirm]=1 and [complete] is null
                        </when>
                        <when test="param.stats == 8">
                            and [complete]=1
                        </when>
                    </choose>
                </otherwise>
            </choose>
            <if test="param.stats != 4">
                and ISNULL(hs.isfan,0)=0
            </if>
        </if>
        <if test="param.key != null and param.key !='' ">
            <choose>
                <when test="param.keyKind=='wxid' ">
                    and hs.shouhou_id = #{param.key}
                </when>
                <when test="param.keyKind=='ppid' ">
                    and hs.ppid= = #{param.key}
                </when>
                <when test="param.keyKind=='inuser' ">
                    and hs.inuser = #{param.key}
                </when>
                <when test="param.keyKind=='saleuser' ">
                    and hs.saleuser = #{param.key}
                </when>
                <when test="param.keyKind=='weixiuren' ">
                    and sh.weixiuren = #{param.key}
                </when>
                <when test="param.keyKind=='wuliuid' ">
                    and hs.wuliuid = #{param.key}
                </when>
            </choose>
        </if>
        <if test="param.kind != null ">
            AND ISNULL(hs.kind,0)=#{param.kind}
        </if>
        <if test="param.preSaleChannel !=null ">
            AND ISNULL(hs.PreSaleChannel,0)=#{param.preSaleChannel}
        </if>
        <if test="param.areaid != null and param.areaid != 0 ">
            and ISNULL(hs.toareaid,hs.areaid)=#{param.areaid}
        </if>
        <if test="param.isgaoji != null and param.isgaoji == 1 and param.startTime !=null and  param.startTime !='' and param.endTime !=null and param.endTime !='' ">
            <choose>
                <when test="loginArea !=null and loginArea==360">
                    and hs.#{param.timeType} between #{param.startTime} and #{param.endTime}
                </when>
                <otherwise>
                    <choose>
                        <when test="param.stats != null and param.stats==2">
                            and hs.saledate between #{param.startTime} and #{param.endTime}
                        </when>
                        <otherwise>
                            and hs.indate between #{param.startTime} and #{param.endTime}
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </if>
        <if test="param.yaping != null and param.yaping != 0">
            and isnull(hs.yaping,0)=#{param.yaping}
        </if>
        <if test="param.ishuanhuo != null">
            <choose>
                <when test="param.ishuanhuo == 1">
                    and isnull(hs.ishuanhuo,0) = 1
                    <if test="param.huanhuokind != null and param.huanhuokind ==1">
                        and hs.huanhuoUser is not null
                    </if>
                    <if test="param.huanhuokind != null and param.huanhuokind ==2">
                        andand huanhuoUser is null
                    </if>
                </when>
                <otherwise>
                    and isnull(hs.ishuanhuo,0) = 0
                </otherwise>
            </choose>
        </if>
        <if test="param.istoarea != null">
            <choose>
                <!--转地区已接收-->
                <when test="param.istoarea == 2">
                    and hs.toareaid IS NOT NULL and hs.istoarea=0
                </when>
                <!--转地区提交-->
                <when test="param.istoarea == 1">
                    and hs.istoarea=1
                </when>
                <!--未提交，没有转地区记录-->
                <when test="param.istoarea == 3">
                    and hs.toareaid IS NULL
                </when>
            </choose>
        </if>
        <if test="param.areaCode != null and param.areaCode.size()>0 ">
            <choose>
                <when test="param.toareakind != null param.toareakind == 1">
                    and hs.toareaid IS NOT NULL and hs.toareaid IN(select id from AreaInfo with(nolock) where areaCode in
                    <foreach collection="param.areaCode" item="areaCode" open="AND (" close=")" separator="OR">
                        #{areaCode}
                    </foreach>
                    )
                </when>
                <when test="param.toareakind != null param.toareakind == 2">
                    and hs.areaid IN
                    (select id from AreaInfo with(nolock) where areaCode in
                    <foreach collection="param.areaCode" item="areaCode" open="AND (" close=")" separator="OR">
                        #{areaCode}
                    </foreach>
                    )
                </when>
                <otherwise>
                    and ISNULL(hs.toareaid,hs.areaid)
                    IN (select id from AreaInfo with(nolock) where areaCode in
                    <foreach collection="param.areaCode" item="areaCode" open="AND (" close=")" separator="OR">
                        #{areaCode}
                    </foreach>
                    )
                </otherwise>
            </choose>
        </if>

        <if test="param.cids != null and param.cids.size()>0 ">
            and p.cid in
            <foreach collection="param.cids" item="cid" open="AND (" close=")" separator="OR">
                #{cid}
            </foreach>
        </if>
        <if test="param.brandIds != null and param.cids.brandIds()>0 ">
            and p.brandid in
            <foreach collection="param.brandIds" item="brandId" open="AND (" close=")" separator="OR">
                #{brandId}
            </foreach>
        </if>
    </sql>
    <select id="getHuiSouSumData" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouHuishouSumDataBo">
        SELECT COUNT(1) AS jiujianCount,
        SUM(ISNULL(price, 0)) AS jiujianTotal,
        SUM(ISNULL(saleprice, 0)) AS saleTotal,
        SUM(ISNULL(huanhuiprice, 0)) AS huanhuiTotal
        FROM(
        SELECT (select top 1 fid from attachments with(nolock) where type = 6 and linkedID=hs.shouhou_id) as topfid,
        p.product_name as productName,p.product_color AS productColor,p.pLabel,
        CASE WHEN PATINDEX(23, p.cidFamily)> 0 THEN 'wx' ELSE 'pj' END AS pjkinds,
        hs.*, c.company_jc AS companyJc ,hp.huanhuiprice,sh.problem,isnull(sh.weixiuren,'''') weixiuren
        <include refid="huishouQuery"></include>
        ) temp

    </select>

    <select id="getHuiSouSumDataV2"
            resultType="com.jiuji.oa.afterservice.smallpro.recover.vo.res.SmallProRecoverSumData">
        SELECT COUNT(1) AS recoverCount,
        SUM(ISNULL(price, 0)) AS recoverTotal,
        SUM(ISNULL(saleprice, 0)) AS saleTotal,
        SUM(ISNULL(huanhuiprice, 0)) AS exchangeTotal
        FROM(
        SELECT (select top 1 fid from attachments with(nolock) where type = 6 and linkedID=hs.shouhou_id) as topfid,
        p.product_name as productName,p.product_color AS productColor,p.pLabel,
        CASE WHEN PATINDEX(23, p.cidFamily)> 0 THEN 'wx' ELSE 'pj' END AS pjkinds,
        hs.*, c.company_jc AS companyJc ,hp.huanhuiprice,sh.problem,isnull(sh.weixiuren,'''') weixiuren
        <include refid="huishouQueryv2"></include>
        ) temp
    </select>

    <select id="getHuiShouListPage"
            resultType="com.jiuji.oa.afterservice.smallpro.recover.vo.res.SmallProRecoverListQueryPageItem">
        SELECT (select top 1 fid from attachments with(nolock) where type = 6 and linkedID=hs.shouhou_id) as topFid,
        p.product_name as productName,p.product_color AS productColor,p.pLabel,p.cidFamily,
        c.company_jc AS companyJc ,hp.huanHuiPrice,sh.problem,isnull(sh.weixiuren,'''') weixiuren,
        hs.indate inDate,hs.areaid areaId,hs.toareaid toAreaId,hs.shouhou_id,hs.ppid,hs.name,hs.kind,
        hs.PreSaleChannel channelCode,
        hs.isFan,hs.isHuan,sh.baoXiu,hs.price,hs.salePrice,hs.inUser,hs.saleDate,hs.sDTime,hs.saleUser,hs.isSale,hs.huanHuoUser,
        hs.huanHuoTime,hs.yaPing,hs.inPrice,hs.fanCheck,hs.checkUser,hs.checkDate,hs.returnBasketId,hs.isSaleCheck,hs.bindToolCount,
        hs.bindToolPrice,hs.baoFeiPq,hs.baoFeiPz,hs.sale_piqianid salePiQianId,hs.sale_pzid
        salePzId,hs.isToArea
        <include refid="huishouQueryv2">
        </include>
    </select>

    <sql id="huishouQueryv2">
        FROM shouhou_huishou hs WITH(NOLOCK)
        LEFT JOIN shouhou sh WITH(NOLOCK) on hs.shouhou_id = sh.id
        LEFT JOIN productinfo p WITH(NOLOCK) on hs.ppid=p.ppriceid
        LEFT JOIN Ok3w_qudao c WITH(NOLOCK) on hs.company_id = c.id and c.IsPass=1 and c.company_jc is not null
        LEFT JOIN shouhou_huishou_huanhuiprice hp WITH(NOLOCK) on hs.ppid=hp.ppriceid
        WHERE 1=1
        <if test="req.hangHuo != null and req.hangHuo == true">
            and hs.ppid=0
        </if>
        <if test="req.isHuanHuo !=null and req.isHuanHuo ==true or req.saleStatus ==5 or req.saleStatus ==6 or req.saleStatus ==7 or req.saleStatus ==8">
            and hs.ishuan=1
        </if>
        <if test="req.saleStatus != null">
            <choose>
                <when test="req.saleStatus == 1">
                    and isnull(hs.issale,0)=0 and isnull(hs.issalecheck,0) = 0
                    <if test="loginArea != null and loginArea != 360">
                        and ISNULL(hs.toareaid,hs.areaid) != 360
                    </if>
                </when>
                <when test="req.saleStatus == 9">
                    and isnull(hs.issale,0)=0 and hs.isfan=0 and isnull(hs.issalecheck,0) = 1
                </when>
                <when test="req.saleStatus == 2">
                    <choose>
                        <when test="loginArea != null and loginArea != 360">
                            and (hs.issale=1 or ISNULL(hs.toareaid,hs.areaid)=360) and hs.isfan=0
                        </when>
                        <otherwise>
                            and hs.issale=1 and hs.isfan=0
                        </otherwise>
                    </choose>
                </when>
                <when test="req.saleStatus == 3">
                    and hs.isdel=1
                </when>
                <when test="req.saleStatus == 4">
                    and hs.isfan=1
                </when>
                <otherwise>
                    <choose>
                        <when test="req.saleStatus == 5">
                            and [confirm] is null
                        </when>
                        <when test="req.saleStatus == 6">
                            and [confirm]=1
                        </when>
                        <when test="req.saleStatus == 7">
                            and [confirm]=1 and [complete] is null
                        </when>
                        <when test="req.saleStatus == 8">
                            and [complete]=1
                        </when>
                    </choose>
                </otherwise>
            </choose>
        </if>
        <if test="req.saleStatus != 4">
            and ISNULL(hs.isfan,0)=0
        </if>
        <if test="req.key != null and req.key !='' ">
            <choose>
                <when test="req.keyKind==1 ">
                    and hs.name like CONCAT('%',#{req.key},'%')
                </when>
                <when test="req.keyKind==2 ">
                    and hs.shouhou_id = #{req.key}
                </when>
                <when test="req.keyKind==4 ">
                    and hs.ppid = #{req.key}
                </when>
                <when test="req.keyKind==3 ">
                    and hs.inuser = #{req.key}
                </when>
                <when test="req.keyKind==5 ">
                    and hs.saleuser = #{req.key}
                </when>
                <when test="req.keyKind==6 ">
                    and sh.weixiuren = #{req.key}
                </when>
                <when test="req.keyKind==7 ">
                    and hs.wuliuid = #{req.key}
                </when>
            </choose>
        </if>
        <if test="req.kind != null ">
            AND ISNULL(hs.kind,0)=#{req.kind}
        </if>
        <if test="req.saleChannel != null and req.saleChannel !=0">
            AND ISNULL(hs.PreSaleChannel,0)=#{req.saleChannel}
        </if>
        <if test="req.baoXiu != null and req.baoXiu != 0">
            and isnull(sh.baoxiu,0)= #{req.baoXiu}
        </if>
        <if test="req.areaId != null and req.areaId != 0 ">
            and ISNULL(hs.toareaid,hs.areaid)=#{req.areaId}
        </if>
        <if test="req.advancedQuery != null and req.advancedQuery == true and req.startTime !=null and  req.startTime !='' and req.endTime !=null and req.endTime !='' ">
            <choose>
                <when test="loginArea !=null and loginArea ==360">
                    and hs.#{req.timeType} between #{req.startTime} and #{req.endTime}
                </when>
                <otherwise>
                    <choose>
                        <when test="req.saleStatus != null and req.saleStatus==2">
                            and hs.saledate between #{req.startTime} and #{req.endTime}
                        </when>
                        <otherwise>
                            and hs.indate between #{req.startTime} and #{req.endTime}
                        </otherwise>
                    </choose>
                </otherwise>
            </choose>
        </if>
        <if test="req.yaPing != null and req.yaPing != 0">
            and isnull(hs.yaping,0)=#{req.yaPing}
        </if>
        <choose>
            <when test="req.isHuanHuo != null and req.isHuanHuo == true">
                and isnull(hs.ishuanhuo,0) = 1
                <if test="req.exchangeKind != null and req.exchangeKind ==1">
                    and hs.huanhuoUser is not null
                </if>
                <if test="req.exchangeKind != null and req.exchangeKind ==2">
                    andand huanhuoUser is null
                </if>
            </when>
            <otherwise>
                and isnull(hs.ishuanhuo,0) = 0
            </otherwise>
        </choose>
        <if test="req.isToArea != null and req.isToArea != 0">
            <choose>
                <!--转地区已接收-->
                <when test="req.isToArea == 2">
                    and hs.toareaid IS NOT NULL and hs.istoarea=0
                </when>
                <!--转地区提交-->
                <when test="req.isToArea == 1">
                    and hs.istoarea=1
                </when>
                <!--未提交，没有转地区记录-->
                <when test="req.isToArea == 3">
                    and hs.toareaid IS NULL
                </when>
            </choose>
        </if>
        <if test="req.areaIds != null and req.areaIds.size()>0 ">
            <choose>
                <when test="req.toAreaKind != null req.toAreaKind == 1">
                    and hs.toareaid IS NOT NULL and hs.toareaid IN
                    <foreach collection="req.areaIds" item="areaId" open="(" close=")" separator=",">
                        #{areaId}
                    </foreach>
                </when>
                <when test="req.toareakind != null req.toareakind == 2">
                    and hs.areaid IN
                    <foreach collection="req.areaIds" item="areaId" open="(" close=")" separator=",">
                        #{areaId}
                    </foreach>
                </when>
                <otherwise>
                    and ISNULL(hs.toareaid,hs.areaid) IN
                    <foreach collection="req.areaIds" item="areaId" open="(" close=")" separator=",">
                        #{areaId}
                    </foreach>
                </otherwise>
            </choose>
        </if>

        <if test="req.cids != null and req.cids.size()>0 ">
            and p.cid in
            <foreach collection="req.cids" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
        </if>
        <if test="req.brandIds != null and req.brandIds().size()>0 ">
            and p.brandid in
            <foreach collection="req.brandIds" item="brandId" open="AND (" close=")" separator="OR">
                #{brandId}
            </foreach>
        </if>
    </sql>

    <select id="getRecoverInfoList" resultType="com.jiuji.oa.afterservice.smallpro.recover.bo.SmallProRecoverInfo">
        SELECT hs.id,shouhou_id,hs.price,hs.salePrice,ISNULL(s.toareaid,s.areaid) nowAreaId,hs.company_id
        FROM dbo.shouhou_huishou hs WITH(NOLOCK)
        INNER JOIN dbo.wxkcoutput wx WITH(NOLOCK) ON wx.id=hs.wxkcid
        INNER JOIN dbo.shouhou s WITH(NOLOCK) ON s.id=wx.wxid
        WHERE ISNULL(hs.hsjj_saletype,0)=1 and ISNULL(hs.isSale,0)=1 and isnull(hs.isdel,0)=0
        and hs.id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </select>

    <select id="getRecoverSaleInfoList" resultType="com.jiuji.oa.afterservice.smallpro.recover.bo.SmallProRecoverInfo">
        SELECT h.id, hp.huanHuiPrice ,h.hsjj_saletype saleType
        FROM shouhou_huishou as h WITH(NOLOCK)
        LEFT JOIN shouhou_huishou_huanhuiprice hp WITH(NOLOCK) on h.ppid = hp.ppriceid
        WHERE ISNULL(h.company_id,0)>0 AND ID IN
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and isnull(issalecheck,0)=1 and isnull(issale,0) = 0 and isnull(h.isdel,0)=0
    </select>

    <select id="getWaitRecoverSaleInfoList" resultType="com.jiuji.oa.afterservice.smallpro.recover.bo.SmallProRecoverInfo">
        SELECT h.id,hp.huanHuiPrice,h.ppid
        FROM shouhou_huishou as h WITH(NOLOCK)
        LEFT JOIN shouhou_huishou_huanhuiprice hp WITH(NOLOCK) on h.ppid = hp.ppriceid
        WHERE ISNULL(h.company_id,0)=0 AND h.ID INSmallProRecoverInfo
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        and isnull(h.issalecheck,0)=0 and  isnull(h.issale,0) = 0
    </select>


    <select id="getYaPingToolList" resultType="com.jiuji.oa.afterservice.smallpro.recover.vo.res.RecoverProBindTool">
        SELECT b.id,b.toolId,b.hsId,b.count_ bindCount,b.price,b.inUser,b.inDate,t.name toolName FROM shouhou_huishou_bindtool b WITH(NOLOCK)
        INNER JOIN shouhou_tool t WITH(NOLOCK) ON b.toolid=t.id
        WHERE hsid = #{hsId}
    </select>

    <insert id="insertShouHouZhiHuanShenHe">
        INSERT INTO shouhou_zhihuanshenhe(ShouhouHuishouId, dtime, status) values (#{data.hsId},#{data.dTime},#{data.status})
    </insert>


    <select id="listReceiveSend" resultType="com.jiuji.oa.afterservice.smallpro.recover.vo.res.ReceiveSendRes$DataInfo">
        SELECT
            sh.id,
            sh.shouhou_id shouhouId,
            sh.ppid,
            sh.name,
            isnull(sh.toarea_state,0) toAreaState,
            sh.areaid fromAreaId,
            sh.wuliuid wuliuId,
            isnull(sh.ishuanhuo,0) isHuanHuo,
            sh.toareaid toAreaId,
            a.area fromArea,
            a1.area toArea
        FROM shouhou_huishou sh WITH(NOLOCK)
        left join areainfo a WITH(NOLOCK) on sh.areaid = a.id
        left join areainfo a1 WITH(NOLOCK) on sh.toareaid = a1.id
        where isnull(sh.isdel,0) = 0 and isnull(sh.isfan,0) != 1 AND issale = 0
        <if test="wType == 4 and receiveType == 1">
            and isnull(sh.toarea_state,0) = 1 and sh.areaid = #{areaId} and isnull(sh.ishuanhuo,0) != 1
        </if>
        <if test="wType == 15 and receiveType == 1">
            and isnull(sh.toarea_state,0) = 1 and sh.areaid = #{areaId} and isnull(sh.ishuanhuo,0) = 1
        </if>
        <if test="wType == 4 and receiveType == 2">
            and isnull(sh.toarea_state,0) = 2 and sh.toareaid = #{areaId} and isnull(sh.ishuanhuo,0) != 1
        </if>
        <if test="wType == 15 and receiveType == 2">
            and isnull(sh.toarea_state,0) = 2 and sh.toareaid = #{areaId} and isnull(sh.ishuanhuo,0) = 1
        </if>
        <if test="(wType == null or wType == 0) and receiveType == 2">
            and isnull(sh.toarea_state,0) = 2 and sh.toareaid = #{areaId}
        </if>
        <if test="ids != null and ids.size() != 0">
            and sh.id in
            <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
        <if test="wuliuId != null and wuliuId != 0">
            and sh.wuliuid = #{wuliuId}
        </if>
    </select>

    <update id="saveReceive">
        update shouhou_huishou
        set
            ruser = #{ruser},
            rdtime = GETDATE(),
            toarea_state = #{toAreaState},
            istoarea = #{isToArea}
        WHERE
            isnull(toarea_state,0) = #{receiveType}
            and issale = 0
            and toareaid = #{toAreaId}
            and id IN
            <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
    </update>

    <update id="saveSend">
        update shouhou_huishou
        set
            suser = #{suser},
            sdtime = GETDATE(),
            wuliuid = #{wuliuId},
            toarea_state = #{toAreaState},
            istoarea = #{isToArea}
        WHERE
            isnull(toarea_state,0) = #{receiveType}
            and issale = 0
            and areaid = #{areaId}
            and id IN
            <foreach collection="ids" index="index" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
    </update>

    <insert id="addRemarkLogs">
        insert into shouhou_proRemarkLogs
        (huishouId,content,dtime,inuser)
        values
        <foreach collection="list" item="data" separator=",">
            (#{data.huishouId},#{data.content},GETDATE(),#{data.inuser})
        </foreach>
    </insert>

    <select id="getHuishouByWuliuIds" resultType="java.util.Map">
        WITH WuliuList AS (
            SELECT
                wuliuid
            FROM
            (VALUES
            <foreach collection="wuliuIds" index="index" item="id" separator=",">
                (#{id})
            </foreach>
            ) AS Temp(wuliuid)
        )

        SELECT
            wl.wuliuid,
            COALESCE(COUNT(hs.wuliuid), 0) AS quantity,
            STRING_AGG(hs.id, ',') AS ids
        FROM
            WuliuList wl
        LEFT JOIN
            dbo.shouhou_huishou hs WITH(NOLOCK) ON wl.wuliuid = hs.wuliuid
            AND isnull(hs.toarea_state,0) = 3
            AND ISNULL(hs.isdel, 0) = 0
            AND hs.istoarea = 0
        GROUP BY
            wl.wuliuid
    </select>
</mapper>
