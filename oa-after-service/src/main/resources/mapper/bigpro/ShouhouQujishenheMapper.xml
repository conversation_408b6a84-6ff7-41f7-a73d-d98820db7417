<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouQujishenheMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.ShouhouQujishenhe" id="shouhouQujishenheMap">
        <result property="id" column="id"/>
        <result property="shouhouid" column="shouhouid"/>
        <result property="checkuser1" column="checkUser1"/>
        <result property="dtime1" column="dtime1"/>
        <result property="checkuser2" column="checkUser2"/>
        <result property="dtime2" column="dtime2"/>
        <result property="checkuser3" column="checkUser3"/>
        <result property="dtime3" column="dtime3"/>
        <result property="checkuser4" column="checkUser4"/>
        <result property="dtime4" column="dtime4"/>
        <result property="inuser" column="inuser"/>
        <result property="dtime" column="dtime"/>
    </resultMap>

    <select id="qujiWxpjCheck" resultType="java.lang.Integer">
        EXEC proc_shouhouquji_wxpj_check #{shouhouId}
    </select>

    <select id="isCheckMkcQuji" resultType="java.lang.Integer">
        <choose>
            <when test="ishuishou == 1 || ishuishou == 2">
                SELECT mkc_check FROM dbo.recover_mkc WITH(NOLOCK) WHERE id=#{mkcId}
            </when>
            <otherwise>
                SELECT kc_check FROM dbo.product_mkc WITH(NOLOCK) WHERE id=#{mkcId}
            </otherwise>
        </choose>
    </select>
    <select id="chkCount" resultType="java.lang.Integer">
        select count(1) from shouhou_qujishenhe WITH(NOLOCK) where shouhouid = #{shouhouId}  and (checkUser1 = '未审核' or checkUser2 = '未审核' or checkUser3 = '未审核' or checkUser4 = '未审核')
    </select>
</mapper>