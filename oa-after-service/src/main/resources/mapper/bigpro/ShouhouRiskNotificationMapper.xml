<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouRiskNotificationMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouRiskNotification">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="shouhouId" column="shouhou_id" jdbcType="INTEGER"/>
            <result property="documentId" column="document_id" jdbcType="INTEGER"/>
            <result property="content" column="content" jdbcType="VARCHAR"/>
            <result property="notificationType" column="notification_type" jdbcType="INTEGER"/>
            <result property="status" column="status" jdbcType="TINYINT"/>
            <result property="signature" column="signature" jdbcType="VARCHAR"/>
            <result property="signatureImage" column="signature_image" jdbcType="VARCHAR"/>
            <result property="signatureTime" column="signature_time" jdbcType="TIMESTAMP"/>
            <result property="inuser" column="inuser" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="delFlag" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,shouhou_id,document_id,
        content,notification_type,status,
        signature,signature_image,signature_time,
        inuser,create_time,update_time,
        is_delete,shouhou_risk_notification_rv
    </sql>

    <select id="getShouhouRiskNotificationInfo"
            resultType="com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouRiskNotificationInfoRes">
        select top 1 s.id as shouhouId,
               s.name as productName,
               s.product_color as productColor,
               s.imei,
               p.bpic as imagePath,
               srn.id as shouhouRiskNotificationId,
               srn.document_id as documentId,
               srn.content as notificationContent,
               srn.status as status,
               srn.is_delete,
               srn.signature_image as signatureImage,
               srn.source_type as sourceType
        from shouhou s with(nolock)
        LEFT JOIN shouhou_risk_notification srn with(nolock) on srn.shouhou_id = s.id
        LEFT JOIN productinfo p WITH (nolock) ON p.ppriceid = s.ppriceid
        <where>
            <if test="req.shouhouId != null">
                and s.id = #{req.shouhouId}
            </if>
            <if test="req.shouhouRiskNotificationId != null">
                and srn.id = #{req.shouhouRiskNotificationId}
            </if>
            <if test="req.riskNotificationType != null">
                and srn.notification_type = #{req.riskNotificationType}
            </if>
        </where>
        order by srn.id desc
    </select>
    <select id="getShouhouRiskNotificationByShouhouIdAndType"
            resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouRiskNotification">
        select * from shouhou_risk_notification srn with(nolock)
        where srn.shouhou_id = #{shouhouId}
        and srn.notification_type = #{notificationType}
        and srn.is_delete = 0
    </select>
</mapper>
