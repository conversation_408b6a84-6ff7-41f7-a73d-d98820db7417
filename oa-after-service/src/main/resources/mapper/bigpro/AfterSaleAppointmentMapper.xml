<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.AfterSaleAppointmentMapper">

    <select id="getWorkingCh999IdByAreaIdsAndRoleIds"
            resultType="com.jiuji.oa.afterservice.bigpro.bo.yuyue.NoticePushBeforeCustomerArriveStoreBo">
        select ch999_id as ch999Id,area1Id as areaId from ch999_user u with(nolock)
        where 1=1
        and u.area1id in
        <foreach collection="areaIds" item="areaId" index="index" separator="," open="(" close=")">
            #{areaId}
        </foreach>
        <if test="roleIds != null and roleIds.size > 0">
            and u.mainRole in
            <foreach collection="roleIds" item="roleId" index="index" separator="," open="(" close=")">
                #{roleId}
            </foreach>
        </if>
        and EXISTS(SELECT 1 from kaoqin with(nolock) WHERE DATEDIFF(day,dtime,GETDATE())=0 and ch999_id = u.ch999_id and
        kind1 = 1)
        and not EXISTS(SELECT 1 from kaoqin with(nolock) WHERE DATEDIFF(day,dtime,GETDATE())=0 and ch999_id = u.ch999_id
        and kind1 = 2)
    </select>

    <select id="getAppointmentInfoBeforeCustomerArriveStore"
            resultType="com.jiuji.oa.afterservice.bigpro.bo.yuyue.NoticePushBeforeCustomerArriveStoreBo">
        select id,areaId,stime as arriveTime from shouhou_yuyue with(nolock)
        where stype = 1  and DATEDIFF(minute,GETDATE(),stime) &lt;= 30 and DATEDIFF(minute,GETDATE(),stime) &gt; 0
        and isnull(is_push,0) = 0 and isnull(isdel,0)= 0
    </select>
</mapper>
