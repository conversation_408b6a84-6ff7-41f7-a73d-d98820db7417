<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouJinshuibaoMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.ShouhouJinshuibao" id="shouhouJinshuibaoMap">
        <result property="id" column="id"/>
        <result property="shouhouid" column="shouhouid"/>
        <result property="orderid" column="orderid"/>
        <result property="reorderid" column="reorderid"/>
        <result property="comment" column="comment"/>
        <result property="inuser" column="inuser"/>
        <result property="dtime" column="dtime"/>
        <result property="checkuser" column="checkUser"/>
        <result property="checktime" column="checkTime"/>
        <result property="checkuser2" column="checkUser2"/>
        <result property="checktime2" column="checkTime2"/>
        <result property="checkuser3" column="checkUser3"/>
        <result property="checktime3" column="checkTime3"/>
        <result property="feiyong" column="feiyong"/>
        <result property="isdel" column="IsDel"/>
        <result property="pzid" column="pzid"/>
        <result property="serviceType" column="service_type"/>
    </resultMap>

    <select id="getBasketInfoByShouhouId" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouHouBasketBo">
        select top 1 ISNULL( b.price, 0 ) price,s.tradeDate,ISNULL( s.userid, 0 )  userId,ISNULL( s.areaid, 0 )  areaId from basket b with(nolock)
        left join sub s with(nolock) on b.sub_id = s.sub_id
        where b.basket_id = #{shouhouId} and s.sub_check = 3
    </select>

</mapper>