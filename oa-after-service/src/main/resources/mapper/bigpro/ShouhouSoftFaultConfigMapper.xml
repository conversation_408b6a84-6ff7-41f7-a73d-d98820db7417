<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouSoftFaultConfigMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouSoftFaultConfig">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="categoryId" column="category_id" jdbcType="INTEGER"/>
            <result property="categoryName" column="category_name" jdbcType="VARCHAR"/>
            <result property="fault" column="fault" jdbcType="VARCHAR"/>
            <result property="skuid" column="skuid" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,category_id,category_name,
        fault,skuid,remark,
        create_time,update_time,is_delete
    </sql>
    <select id="getFaultConfigList" resultType="com.jiuji.oa.afterservice.bigpro.po.ShouhouSoftFaultConfig">
        select * from shouhou_soft_fault_config
        where is_delete = 0
    </select>
    <select id="faultConfigCountBySkuId" resultType="java.lang.Integer">
        select count(*) from shouhou_soft_fault_config
        where is_delete = 0 and skuid = #{skuId}
    </select>
</mapper>
