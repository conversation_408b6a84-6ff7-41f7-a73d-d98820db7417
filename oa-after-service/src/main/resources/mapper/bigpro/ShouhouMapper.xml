<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        <id column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="peizhi" property="peizhi"/>
        <result column="problem" property="problem"/>
        <result column="comment" property="comment"/>
        <result column="username" property="username"/>
        <result column="mobile" property="mobile"/>
        <result column="tel" property="tel"/>
        <result column="stats" property="stats"/>
        <result column="baoxiu" property="baoxiu"/>
        <result column="inuser" property="inuser"/>
        <result column="imei" property="imei"/>
        <result column="xianshi" property="xianshi"/>
        <result column="contentcsdate" property="contentcsdate"/>
        <result column="tradedate" property="tradedate"/>
        <result column="modidate" property="modidate"/>
        <result column="feiyong" property="feiyong"/>
        <result column="costprice" property="costprice"/>
        <result column="weixiuren" property="weixiuren"/>
        <result column="dyjid" property="dyjid"/>
        <result column="offtime" property="offtime"/>
        <result column="area" property="area"/>
        <result column="shouyinglock" property="shouyinglock"/>
        <result column="shouyingdate" property="shouyingdate"/>
        <result column="shouyinguser" property="shouyinguser"/>
        <result column="userid" property="userid"/>
        <result column="kinds" property="kinds"/>
        <result column="isticheng" property="isticheng"/>
        <result column="waiguan" property="waiguan"/>
        <result column="result_dtime" property="resultDtime"/>
        <result column="issoft" property="issoft"/>
        <result column="modidtime" property="modidtime"/>
        <result column="product_id" property="productId"/>
        <result column="product_color" property="productColor"/>
        <result column="buyarea" property="buyarea"/>
        <result column="pandian" property="pandian"/>
        <result column="pandiandate" property="pandiandate"/>
        <result column="toarea" property="toarea"/>
        <result column="istui" property="istui"/>
        <result column="pandianinuser" property="pandianinuser"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="mkc_id" property="mkcId"/>
        <result column="isquick" property="isquick"/>
        <result column="wcount" property="wcount"/>
        <result column="weixiuzuid" property="weixiuzuid"/>
        <result column="weixiuzuid_jl" property="weixiuzuidJl"/>
        <result column="isweixiu" property="isweixiu"/>
        <result column="weixiudtime" property="weixiudtime"/>
        <result column="weixiu_startdtime" property="weixiuStartdtime"/>
        <result column="orderid" property="orderid"/>
        <result column="isquji" property="isquji"/>
        <result column="isfan" property="isfan"/>
        <result column="pingjia" property="pingjia"/>
        <result column="pingjia1" property="pingjia1"/>
        <result column="sub_id" property="subId"/>
        <result column="webtype1" property="webtype1"/>
        <result column="webtype2" property="webtype2"/>
        <result column="webstats" property="webstats"/>
        <result column="ServiceType" property="serviceType"/>
        <result column="basket_id" property="basketId"/>
        <result column="ishuishou" property="ishuishou"/>
        <result column="yuyueid" property="yuyueid"/>
        <result column="huiprint" property="huiprint"/>
        <result column="weixiurentime" property="weixiurentime"/>
        <result column="reweixiuren" property="reweixiuren"/>
        <result column="sxname" property="sxname"/>
        <result column="sxmobile" property="sxmobile"/>
        <result column="sxsex" property="sxsex"/>
        <result column="sxuserid" property="sxuserid"/>
        <result column="lockpwd" property="lockpwd"/>
        <result column="testuser" property="testuser"/>
        <result column="wxkind" property="wxkind"/>
        <result column="wxConfig" property="wxConfig"/>
        <result column="noticetime" property="noticetime"/>
        <result column="testtime" property="testtime"/>
        <result column="deviceid" property="deviceid"/>
        <result column="devicepwd" property="devicepwd"/>
        <result column="youhuima" property="youhuima"/>
        <result column="yuyueCheck" property="yuyueCheck"/>
        <result column="isXcMkc" property="isXcMkc"/>
        <result column="isXcMkcInfo" property="isXcMkcInfo"/>
        <result column="wxTestTime" property="wxTestTime"/>
        <result column="wxTestInfo" property="wxTestInfo"/>
        <result column="RepairLevel" property="RepairLevel"/>
        <result column="areaid" property="areaid"/>
        <result column="toareaid" property="toareaid"/>
        <result column="buyareaid" property="buyareaid"/>
        <result column="wxTestStats" property="wxTestStats"/>
        <result column="gjUser" property="gjUser"/>
        <result column="ProcessConfirmStats" property="ProcessConfirmStats"/>
        <result column="oldshouhouid" property="oldshouhouid"/>
        <result column="isBakData" property="isBakData"/>
        <result column="isjbanwxqq" property="isjbanwxqq"/>
        <result column="yuyueCheckuser" property="yuyueCheckuser"/>
        <result column="qujitongzhitime" property="qujitongzhitime"/>
        <result column="daojishi" property="daojishi"/>
        <result column="codeMsg" property="codeMsg"/>
        <result column="result_user" property="resultUser"/>
        <result column="smstime" property="smstime"/>
        <result column="teltime" property="teltime"/>
        <result column="EarnestMoneySubid" property="EarnestMoneySubid"/>
        <result column="serversOutUser" property="serversOutUser"/>
        <result column="serversOutDtime" property="serversOutDtime"/>
        <result column="youhuifeiyong" property="youhuifeiyong"/>
        <result column="truename" property="truename"/>
        <result column="iszy" property="iszy"/>
        <result column="wxAreaid" property="wxAreaid"/>
        <result column="imeifid" property="imeifid"/>
        <result column="yifum" property="yifum"/>
        <result column="kuaixiuFlag" property="kuaixiuFlag"/>
        <result column="kuaixiuSendTime" property="kuaixiuSendTime"/>
        <result column="iszp" property="iszp"/>
        <result column="wuliyou" property="wuliyou"/>
        <result column="mobileServeiceType" property="mobileServeiceType"/>
        <result column="lppeizhi" property="lppeizhi"/>
        <result column="fromshouhouid" property="fromshouhouid"/>
        <result column="ServiceCostprice" property="ServiceCostprice"/>
        <result column="question_type" property="questionType"/>
        <result column="pzid" property="pzid"/>
    </resultMap>
    <select id="getWxRecord" resultType="com.jiuji.oa.afterservice.bigpro.bo.WxRecordBo">
        select name productName,
        product_color productColor,
        h.areaid areaId,
        modidate dTime,
        t.tuihuan_kind tuihuanKind,
        h.issoft issoft
        from shouhou h with(nolock)
        left join shouhou_tuihuan t with(nolock) on isnull(t.isdel,0) = 0 and t.shouhou_id=h.id and t.tuihuan_kind in(1,2,3,4)
        where xianshi=1 and userid=#{userId}
    </select>

    <select id="getHardwareHistoryRecords" resultType="com.jiuji.oa.afterservice.bigpro.bo.HardwareHistoryRecordBo">
        select offtime offTime,inuser inUser,areaid areaId from shouhou with(nolock) where xianshi=1 and isnull(isquji,0)=1 and offtime is not null  and imei=#{imei} order by offtime desc
    </select>

    <select id="getImeiQueryByMobile" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ImeiQueryRes">
        SELECT
        u.ID userId,
        u.mobile,
        u.realname as realName,
        k.imei,
        p.product_id,
        p.product_name as productName,
        p.product_color as productColor,
        p.ppriceid as ppriceid,
        u.userclass as userClass
        FROM dbo.BBSXP_Users u with(nolock)
        INNER JOIN dbo.sub s with(nolock) ON s.userid=u.ID
        INNER JOIN dbo.basket b with(nolock) ON b.sub_id=s.sub_id
        INNER JOIN dbo.product_mkc k with(nolock) ON k.basket_id=b.basket_id
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid=k.ppriceid
        WHERE p.ismobile1=1
        AND s.sub_check NOT IN(4,8,9)
        AND ISNULL(b.isdel,0)=0
        <if test="param != null and param.xtenant != null">
            AND u.xtenant = #{param.xtenant}
        </if>
        <if test="param != null and param.world != null">
            AND u.mobile = #{param.world}
        </if>
        ORDER BY b.basket_id DESC
        OFFSET 0 ROWS FETCH NEXT
        <choose>
            <when test="param != null and param.top != null">
                #{param.top}
            </when>
            <otherwise>10</otherwise>
        </choose>
        ROWS ONLY
    </select>

    <select id="getProductInfoByImei" resultType="com.jiuji.oa.afterservice.bigpro.bo.ProductImeiBo">
        SELECT top 1
        mkc.ppriceid ppid,
        mkc.imei,
        p.productid pid,
        p.product_name productName,
        p.product_color productColor
        FROM
        product_mkc mkc with(nolock)
        LEFT JOIN productinfo p with(nolock) ON mkc.ppriceid = p.ppriceid
        WHERE 1=1
        <if test="imei != null">
            and imei = #{imei}
        </if>

    </select>

    <select id="getWxkcNumber" resultType="java.lang.Integer">
        select isnull(leftCount,0) from product_kc k with(nolock) where 1=1
        <if test="ppid != null and ppid != 0">
            and ppriceid = #{ppid}
        </if>
        <if test="areaId != null and areaId != 0">
            and areaid = #{areaId}
        </if>
    </select>

    <update id="delYuyueUpdateShouhou">
        update shouhou set xianshi=0 where ISNULL(stats,0)=0 and webstats!=3 and yuyueid=#{yyid}
    </update>

    <select id="getShouhouOrderIds" resultType="java.lang.String">
        select orderid from shouhou with(nolock) where xianshi=1 and isnull(isquji,0)=0 and orderid like CONCAT(#{weekN},'%')
    </select>

    <select id="getShouhouInfoByImei" resultMap="BaseResultMap">
        select top 1 id
        from shouhou with(nolock) where xianshi=1
        and isnull(isquji,0)=1 and inuser&lt;>'系统'
        and imei=#{imei}
        and imei is not null and imei &lt;>'' and isnull(iszp,0) = 0  order by offtime desc
    </select>

    <select id="getShouhouList" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouListBo">
        SELECT ISNULL(u.userclass,0) AS userClass,
        u.username as userName,
        u.realname as realName,
        h.id as shouhouId,
        h.username as shouhouUserName,
        h.areaid as areaId,
        h.toareaid as toAreaId,
        h.tradedate as tradeTime,
        h.modidate as modiTime,
        h.name as productName,
        h.product_color as productColor,
        h.orderid as orderId,
        h.ishuishou isHuishou,
        h.userid huishouUserId,
        h.imei,
        h.offtime as offTime,
        h.result_dtime as resultTime,
        h.daojishi,
        h.ppriceid as ppid,
        h.qujitongzhitime qujiantongzhiTime,
        h.product_id as pid,
        h.wxkind as wxKind,
        h.stats as status,
        h.baoxiu baoxiu,
        h.gjUser as gjUser,
        h.weixiuren as weixiuUser,
        h.inuser,
        h.iszy,
        h.problem,
        h.isquji as isQuji,
        h.peizhi,isnull(h.isquji,0) as isQjji_ ,
        sth.tuihuan_kind as tuihuanKind,
        CASE WHEN hs.shouhou_id is null THEN 0 ELSE 1 END as isJiujian
        <choose>
            <when test="param.tuihuanType != null">
                FROM shouhou h with(nolock)
                LEFT JOIN (SELECT tuihuan_kind,shouhou_id FROM shouhou_tuihuan with(nolock) WHERE ISNULL(isdel,0)=0 and
                tuihuan_kind in (1,2,3,4,5)) t ON t.shouhou_id=h.id
                LEFT JOIN (SELECT distinct shouhou_id from shouhou_huishou with(nolock) ) as hs ON hs.shouhou_id=h.id
                LEFT JOIN BBSXP_Users u with(nolock) ON u.id=h.userid
                WHERE 1=1
            </when>
            <otherwise>
                FROM shouhou h with(nolock)
                LEFT JOIN( SELECT shouhou_id, tuihuan_kind from shouhou_tuihuan with(nolock) WHERE ISNULL(isdel,0)=0 and
                tuihuan_kind in (1,2,3,4,5) ) as sth on h.id = sth.shouhou_id
                LEFT JOIN (SELECT distinct shouhou_id from shouhou_huishou with(nolock)) as hs ON hs.shouhou_id=h.id
                LEFT JOIN BBSXP_Users u with(nolock) ON u.id=h.userid
                WHERE 1=1
            </otherwise>
        </choose>
        <include refid="shouhouListWhere"></include>
        <!--排序-->
        <choose>
            <when test="param.orderByFid != null and param.orderByFid !='' and param.orderByType != null">
                order by h.#{param.orderByFid }
                <if test="param.orderByType == 1">
                    asc
                </if>
                <if test="param.orderByType == 2">
                    desc
                </if>
            </when>
            <otherwise>
                order by h.id desc
            </otherwise>
        </choose>
        OFFSET #{param.startRow} ROWS FETCH NEXT #{param.size} ROWS ONLY
    </select>

    <select id="getShouhouTotal" resultType="java.lang.Integer">
        select count(*)
        <choose>
            <when test="param.tuihuanType != null">
                FROM shouhou h with(nolock)
                LEFT JOIN (SELECT tuihuan_kind,shouhou_id FROM shouhou_tuihuan with(nolock) WHERE ISNULL(isdel,0)=0 and
                tuihuan_kind in (1,2,3,4,5)) t ON t.shouhou_id=h.id
                LEFT JOIN (SELECT distinct shouhou_id from shouhou_huishou with(nolock) ) as hs ON hs.shouhou_id=h.id
                LEFT JOIN BBSXP_Users u with(nolock) ON u.id=h.userid
                WHERE 1=1
            </when>
            <otherwise>
                FROM shouhou h with(nolock)
                LEFT JOIN( SELECT shouhou_id, tuihuan_kind from shouhou_tuihuan with(nolock) WHERE ISNULL(isdel,0)=0 and
                tuihuan_kind in (1,2,3,4,5) ) as sth on h.id = sth.shouhou_id
                LEFT JOIN (SELECT distinct shouhou_id from shouhou_huishou with(nolock)) as hs ON hs.shouhou_id=h.id
                LEFT JOIN BBSXP_Users u with(nolock) ON u.id=h.userid
                WHERE 1=1
            </otherwise>
        </choose>
        <include refid="shouhouListWhere"></include>
    </select>

    <select id="getImeiQueryByMobileOrImei" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ImeiQueryRes">
        <choose>
            <!--如果是手机号码，通过手机号码查询-->
            <when test="isMobile != null and isMobile == true">
                SELECT u.ID userId,
                u.mobile,
                u.realname as realName,
                k.imei,
                p.product_name as productName,
                p.product_color as productColor,
                u.userclass as userClass
                FROM
                dbo.BBSXP_Users u with(nolock)
                INNER JOIN dbo.sub s with(nolock) ON s.userid= u.ID
                INNER JOIN dbo.basket b with(nolock) ON b.sub_id= s.sub_id
                INNER JOIN dbo.product_mkc k with(nolock) ON k.basket_id= b.basket_id
                INNER JOIN dbo.productinfo p ON p.ppriceid= k.ppriceid
                WHERE 1=1
                <if test="imei != null and imei != ''">
                    and u.mobile= #{imei}
                </if>
                <if test="xtenant != null">
                    AND u.xtenant=#{xtenant}
                </if>
                AND p.ismobile1= 1
                AND s.sub_check NOT IN ( 4, 8, 9 )
                AND ISNULL( b.isdel, 0 ) = 0
                ORDER BY
                b.basket_id DESC
                OFFSET 0 ROWS FETCH NEXT #{top} ROWS ONLY
            </when>
            <otherwise>
                SELECT t.*, u.ID userId, u.mobile, u.realname as realName, u.userclass as userClass FROM (
                SELECT  k.imei,
                k.basket_id as basketId,
                p.product_name as productName,
                p.product_color as productColor
                FROM
                dbo.product_mkc k with(nolock)
                INNER JOIN dbo.productinfo p with(nolock) ON k.ppriceid= p.ppriceid
                WHERE 1=1
                <if test="imei != null and imei != ''">
                    and k.IMEI= #{imei}
                </if>
                AND p.ismobile1= 1
                ORDER BY
                k.basket_id DESC
                offset 0 ROWS FETCH NEXT #{top} ROWS ONLY
                ) t
                INNER JOIN dbo.basket b with(nolock) ON b.basket_id= t.basketId
                INNER JOIN dbo.sub s with(nolock) ON s.sub_id= b.sub_id
                INNER JOIN dbo.BBSXP_Users u with(nolock) ON u.ID= s.userid
                WHERE 1=1
                <if test="xtenant != null">
                    and u.xtenant=#{xtenant}
                </if>
            </otherwise>
        </choose>
    </select>

    <select id="getImeiQueryByMkcIds" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ImeiQueryRes">
        SELECT t.*, u.ID userId, u.mobile, u.realname as realName, u.userclass as userClass FROM (
        SELECT
        k.imei,
        k.basket_id as basketId,
        p.product_name as productName,
        p.ppriceid as ppriceid,
        p.product_color as productColor
        FROM
        dbo.product_mkc k with(nolock)
        INNER JOIN dbo.productinfo p with(nolock) ON k.ppriceid= p.ppriceid
        WHERE 1=1
        <if test="mkcIds != null and mkcIds.size > 0">
            and k.id IN
            <foreach collection="mkcIds" index="index" item="mkcId" open="(" close=")" separator=",">
                #{mkcId}
            </foreach>
        </if>
        AND p.ismobile1= 1
        ORDER BY
        k.basket_id DESC
        offset 0 ROWS FETCH NEXT #{top} ROWS ONLY
        ) t
        INNER JOIN dbo.basket b with(nolock) ON b.basket_id= t.basketId
        INNER JOIN dbo.sub s with(nolock) ON s.sub_id= b.sub_id
        INNER JOIN dbo.BBSXP_Users u with(nolock) ON u.ID= s.userid
        WHERE 1=1
        <if test="xtenant != null">
            and u.xtenant=#{xtenant}
        </if>
    </select>

    <select id="getRecoverImeiQueryByMobile" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ImeiQueryRes">
        SELECT u.ID userId,
        u.mobile,
        u.realname as realName,
        k.imei,
        '【良品】' + p.product_name AS productName,
        p.product_color as productColor,
        p.ppriceid as ppriceid,
        u.userclass as userClass
        FROM
        dbo.BBSXP_Users u with(nolock)
        INNER JOIN dbo.recover_marketInfo s with(nolock) ON s.userid= u.ID
        INNER JOIN dbo.recover_marketSubInfo b with(nolock) ON b.sub_id= s.sub_id
        INNER JOIN dbo.recover_mkc k with(nolock) ON k.to_basket_id= b.basket_id
        INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid= k.ppriceid
        WHERE 1=1
        <if test="mobile != null and mobile != ''">
            and u.mobile= #{mobile}
        </if>
        AND p.ismobile1= 1
        AND k.mkc_check= 5
        <if test="xtenant != null">
            and u.xtenant=#{xtenant}
        </if>
        ORDER BY
        b.basket_id DESC
        offset 0 ROWS FETCH NEXT #{top} ROWS ONLY
    </select>

    <select id="getJiejianTime" resultType="java.time.LocalDateTime">
         SELECT TOP 1 ISNULL(t.checkdtime,s.modidate) FROM dbo.shouhou s WITH(NOLOCK)
                    LEFT JOIN dbo.shouhou_toarea t WITH(NOLOCK) ON s.id=t.shouhou_id AND t.toareaid=ISNULL(s.toareaid,s.areaid)
                    WHERE s.id=#{shouhouId} ORDER BY t.id DESC
    </select>

    <select id="getShouhouFeiyong" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouFeiyongBo">
        SELECT costprice,feiyong FROM dbo.shouhou with(nolock) where 1=1
        <if test="wxId != null and wxId != 0">
            and id=#{wxId}
        </if>
        <if test="shouhouServicesFlag != null and shouhouServicesFlag == 'true'">
            and ((isnull(shouyinglock,0)=0 and isnull(isquji,0)=0) or yuyueid = -1)
        </if>
    </select>

    <sql id="shouhouListWhere">
        <if test="param != null">
            <choose>
                <!--按维修ID查询，只保留一个条件-->
                <when test="param.key != null and param.key == 'id'">
                    and h.id=#{param.keyVal}
                </when>
                <otherwise>
                    <if test="param.key != null and param.key != '' ">
                        <choose>
                            <when test="param.key == 'mobile'">
                                and (h.sxmobile like CONCAT('%',#{param.keyVal},'%') or h.mobile like
                                CONCAT('%',#{param.keyVal},'%') or h.tel like CONCAT('%',#{param.keyVal},'%'))
                            </when>
                            <when test="param.key == 'imei'">
                                and h.imei like CONCAT('%',#{param.keyVal},'%')
                            </when>
                            <when test="param.key == 'orderid'">
                                and h.orderid = #{param.keyVal}
                            </when>
                            <when test="param.key == 'id'">
                                and h.id=#{param.keyVal}
                            </when>
                            <when test="param.key == 'name'">
                                and h.name like CONCAT('%',#{param.keyVal},'%')
                            </when>
                            <when test="param.key == 'inuser'">
                                and h.inuser=#{param.keyVal}
                            </when>
                            <when test="param.key == 'weixiuren'">
                                and h.weixiuren=#{param.keyVal}
                            </when>
                            <when test="param.key == 'pandianinuser'">
                                and h.pandianinuser=#{param.keyVal}
                            </when>
                            <when test="param.key == 'userid'">
                                and h.userid=#{param.keyVal}
                            </when>
                            <when test="param.key == 'mkc_id'">
                                and h.mkc_id=#{param.keyVal}
                            </when>
                            <!--机器金额大于N元-->
                            <when test="param.key == 'minprice'">
                                and exists(select basket_id from basket with(nolock) where price>=#{param.keyVal} and
                                basket_id=h.basket_id)
                            </when>
                            <when test="param.key == 'day'">
                                and
                                DateDiff(d,h.modidate,isnull(h.qujitongzhitime,isnull(h.offtime,getdate())))>=#{param.keyVal}
                            </when>
                            <when test="param.key == 'minday'">
                                and DateDiff(d,h.modidate,isnull(h.qujitongzhitime,isnull(h.offtime,getdate())))&lt;=#{param.keyVal}
                            </when>
                            <when test="param.key == 'minhour'">
                                and DateDiff(hour,h.modidate,isnull(h.qujitongzhitime,isnull(h.offtime,getdate())))&lt;=#{param.keyVal}
                            </when>
                            <when test="param.key == 'minminute'">
                                and DateDiff(minute,h.modidate,isnull(h.qujitongzhitime,isnull(h.offtime,getdate())))&lt;=#{param.keyVal}
                            </when>
                            <when test="param.key == 'syday'">
                                and DateDiff(d,h.tradedate,modidate)&lt;=#{param.keyVal}
                            </when>
                            <when test="param.key == 'feiyong'">
                                and h.feiyong>=#{param.keyVal}
                            </when>
                            <when test="param.key == 'costprice'">
                                and h.costprice>=#{param.keyVal}
                            </when>
                            <when test="param.key == 'wxlirun'">
                                and (h.feiyong-h.costprice)>=#{param.keyVal}
                            </when>
                            <when test="param.key == 'wxlirunf'">
                                and (h.feiyong-h.costprice)&lt;=#{param.keyVal}
                            </when>
                            <when test="param.key == 'wxcount'">
                                and wcount>=#{param.keyVal}
                            </when>
                            <when test="param.key == 'gjuser'">
                                and h.gjUser like CONCAT('%',#{param.keyVal},'%')
                            </when>
                            <when test="param.key == 'productid'">
                                and h.product_id = #{param.keyVal}
                            </when>
                            <when test="param.key == 'qujizhouqi'">
                                AND ISNULL(isquji,0)=0 AND qujitongzhitime IS NOT NULL AND DATEDIFF(HOUR,
                                qujitongzhitime,GETDATE())>#{param.keyVal}* 24
                            </when>
                            <when test="param.key == 'testuser'">
                                and h.testuser = #{param.keyVal}
                            </when>
                            <when test="param.key == 'wxkindShouyin'">
                                and wxkind = 6 AND ISNULL(shouyinglock,0)=0
                            </when>
                        </choose>
                    </if>
                    <if test="param.isYouhui != null and param.isYouhui == 1">
                        <!--排除“配件换货”、“九机服务”、“在保”、“显示总成置换-->
                        and ISNULL(h.ServiceType,0)=0 AND ISNULL(h.baoxiu,0)&lt;>1 AND ISNULL(h.wxkind,0)&lt;>5
                        AND NOT EXISTS(select id from shouhou_huishou sh WITH(NOLOCK) where ISNULL(ishuanhuo,0)=1 and
                        sh.shouhou_id = h.id)
                    </if>
                    <if test="param.isShouyin != null and param.isShouyin == 1">
                        and isnull(h.feiyong,0) > 0 and isnull(shouyinglock,0) = 1
                    </if>
                    <if test="param.isShouyin != null and param.isShouyin == 0">
                        and isnull(h.feiyong,0) > 0 and isnull(shouyinglock,0) = 0
                    </if>
                    <if test="param.toareaType != null">
                        <choose>
                            <when test="param.toareaType == 0">
                                and (
                                <if test="param.areaIds != null and param.areaIds.size >0">
                                    h.areaid in
                                    <foreach collection="param.areaIds" item="areaId" index="index" open="("
                                             separator="," close=")">
                                        #{areaId}
                                    </foreach>
                                    and
                                </if>
                                h.toareaid is null)
                            </when>
                            <when test="param.toareaType == 1">
                                and h.toareaid in
                                <foreach collection="param.areaIds" item="areaId" index="index" open="(" separator=","
                                         close=")">
                                    #{areaId}
                                </foreach>
                            </when>
                            <when test="param.toareaType == 2">
                                and (h.areaid in
                                <foreach collection="areaIds" item="areaId" index="index" open="(" separator=","
                                         close=")">
                                    #{areaId}
                                </foreach>
                                and h.toareaid is not null)
                            </when>
                            <when test="param.toareaType == 3">
                                and (isnull(h.toareaid,h.areaid) in
                                <foreach collection="areaIds" item="areaId" index="index" open="(" separator=","
                                         close=")">
                                    #{areaId}
                                </foreach>
                                )
                            </when>
                            <otherwise>
                                and (h.areaid in
                                <foreach collection="areaIds" item="areaId" index="index" open="(" separator=","
                                         close=")">
                                    #{areaId}
                                </foreach>
                                or h.toareaid in
                                <foreach collection="areaIds" item="areaId" index="index" open="(" separator=","
                                         close=")">
                                    #{areaId}
                                </foreach>
                                )
                            </otherwise>
                        </choose>
                    </if>
                    <if test="param.weixiuzuType != null and param.weixiuzuType!=''">
                        <choose>
                            <when test="param.weixiuzuType == 'null'">
                                and h.weixiuzuid is null
                            </when>
                            <otherwise>
                                <choose>
                                    <when test="param.isWeixiu != null and (param.isWeixiu == 1 or param.isWeixiu == 0)">
                                        and h.weixiuzuid_jl=#{param.weixiuzuType}
                                    </when>
                                    <otherwise>
                                        and h.weixiuzuid=#{param.weixiuzuType}
                                    </otherwise>
                                </choose>
                            </otherwise>
                        </choose>
                    </if>
                    <if test="param.weixiuren != null and param.weixiuren !=''">
                        <choose>
                            <when test="param.weixiuren =='1'">
                                and h.isweixiu=1
                            </when>
                            <when test="param.weixiuren =='0'">
                                and h.isweixiu=0
                            </when>
                            <when test="param.weixiuren =='null'">
                                and h.isweixiu is null
                            </when>
                        </choose>
                    </if>
                    <if test="param.isZhiHuandan != null and param.isZhiHuandan == 1">
                        and h.wxkind=5
                    </if>
                    <if test="param.testResultType != null and param.testResultType == 1">
                        and exists(select id from shouhoutestinfo with(nolock) where isnull(testResult,0) = 0 and
                        h.id=shouhoutestinfo.shouhou_id)
                    </if>
                    <if test="param.isSoft != null">
                        <choose>
                            <when test="param.isSoft == 1">
                                and h.issoft=1
                            </when>
                            <when test="param.isSoft == 0">
                                and h.issoft=0
                            </when>
                        </choose>
                    </if>
                    <if test="param.isPandian != null">
                        <choose>
                            <when test="param.isPandian == 1">
                                and h.pandian=1
                            </when>
                            <when test="param.isPandian == 0">
                                and isnull(h.pandian,0)=0
                            </when>
                        </choose>
                    </if>
                    <if test="param.isQuji != null">
                        <choose>
                            <when test="param.isQuji == 1">
                                and h.isquji=1
                            </when>
                            <when test="param.isQuji == 0">
                                and isnull(h.isquji,0)=0
                            </when>
                        </choose>
                    </if>
                    <if test="param.isTicheng != null">
                        and isticheng=#{param.isTicheng}
                    </if>
                    <if test="param.noCheck != null and param.noCheck  == 1">
                        and h.baoxiu&lt;>4
                    </if>
                    <!--注意:非保自修和非保交易查询时不能已保修自动同时查询-->
                    <if test="param.feibaoKind != null">
                        <choose>
                            <when test="param.feibaoKind == 'feibaozixiu'">
                                AND isnull(h.baoxiu,0)=0 AND NOT EXISTS(SELECT 1 FROM shouhou_qudao with(nolock) WHERE
                                shouhouid=h.id ) AND h.feiyong>0 AND h.shouyinglock IN(1,2) AND
                                ISNULL(h.weixiuzuid,0)!=0
                            </when>
                            <when test="param.feibaoKind == 'feibaojiaoyi'">
                                AND isnull(h.baoxiu,0)=0 AND h.feiyong>0 AND h.shouyinglock IN(1,2)
                            </when>
                        </choose>
                    </if>
                    <if test="param.baoxiuStatus != null">
                        and isnull(h.baoxiu,0)=#{param.baoxiuStatus}
                    </if>
                    <if test="param.isWaisong != null">
                        <choose>
                            <when test="param.isWaisong == 1">
                                <choose>
                                    <when test="param.waisongName == null || param.waisongName == '' and param.waisongId == null">
                                        and exists(select id from shouhou_qudao sq with(nolock) where sq.shouhouid=h.id)
                                    </when>
                                    <otherwise>
                                        <if test="param.waisongId != null">
                                            and exists(select id from shouhou_qudao sq with(nolock) where
                                            sq.shouhouid=h.id and shqd2id=#{param.waisongId}
                                        </if>
                                        <if test="param.waisongName != null and param.waisongName != ''">
                                            and exists(select id from shouhou_qudao sq with(nolock) where
                                            sq.shouhouid=h.id and shqd2name LIKE CONCAT('%',#{param.waisongName},'%')
                                        </if>
                                    </otherwise>
                                </choose>
                            </when>
                            <when test="param.isWaisong == 0">
                                and not exists(select id from shouhou_qudao sq with(nolock) where sq.shouhouid=h.id)
                            </when>
                        </choose>
                    </if>
                    <if test="param.key != 'inuser'">
                        <if test="param.isXinji != null">
                            <choose>
                                <when test="param.isXinji == 1">
                                    and h.userid=76783
                                </when>
                                <when test="param.isXinji == 0">
                                    and h.userid&lt;>76783
                                </when>
                            </choose>
                        </if>
                    </if>
                    <if test="param.weixiuren != null and param.weixiuren != ''">
                        and h.weixiuren= #{param.weixiuren}
                    </if>
                    <if test="param.isFanxiu != null and param.isFanxiu == 1">
                        and isnull(h.isfan,0)=#{param.isFanxiu}
                    </if>
                    <if test="param.status != null">
                        <choose>
                            <when test="param.status == 0">
                                and h.xianshi=1 and h.stats=0
                            </when>
                            <when test="param.status == 1">
                                and h.xianshi=1 and h.stats=1
                            </when>
                            <when test="param.status == 2">
                                and h.xianshi=1 and h.stats=3
                            </when>
                            <when test="param.status == 3">
                                and h.xianshi=0
                            </when>
                            <otherwise>
                                and h.xianshi=1
                            </otherwise>
                        </choose>
                    </if>
                    <if test="param.areaType != null and param.areaType != ''">
                        <choose>
                            <when test="param.areaType == 'bd'">
                                and h.kinds='bd'
                            </when>
                            <when test="param.areaType == 'dz'">
                                and h.kinds='dz'
                            </when>
                        </choose>
                    </if>
                    <if test="param.ishanghuo != null">
                        <choose>
                            <when test="param.ishanghuo == 0">
                                and h.product_color not like '%行货%'
                            </when>
                            <when test="param.ishanghuo == 1">
                                and h.product_color like '%行货%'
                            </when>
                        </choose>
                    </if>
                    <if test="param.webstats != null">
                        and h.webstats=#{param.webstats}
                    </if>
                    <if test="param.serviceType != null">
                        <choose>
                            <when test="param.serviceType == 3">
                                <!--非三九服务-->
                                and h.ServiceType Is null
                            </when>
                            <otherwise>
                                <!--意外保或延保-->
                                and h.ServiceType=#{param.serviceType}
                            </otherwise>
                        </choose>
                    </if>
                    <if test="param.shouhouServicesSal != null">
                        <choose>
                            <!--售后电池险-->
                            <when test="param.shouhouServicesSal == 1">
                                AND exists(SELECT 1 FROM dbo.wxkcoutput wxk WITH(NOLOCK) WHERE wxk.wxid=h.id AND
                                wxk.ppriceid = 81683)
                            </when>
                            <!--售后碎屏险-->
                            <when test="param.shouhouServicesSal == 2">
                                AND exists(SELECT 1 FROM dbo.wxkcoutput wxk WITH(NOLOCK) WHERE wxk.wxid=h.id AND
                                wxk.ppriceid = 81682)
                            </when>
                        </choose>
                    </if>
                    <if test="param.isHuigouji != null">
                        and isnull(h.ishuishou,0)=#{param.isHuigouji}
                    </if>
                    <if test="param.jiujianType != null">
                        <choose>
                            <when test="param.jiujianType == 1">
                                and exists(select id from shouhou_huishou with(nolock) where shouhou_id=h.id)
                            </when>
                            <when test="param.jiujianType == 2">
                                and not exists(select id from shouhou_huishou with(nolock) where shouhou_id=h.id)
                                and exists(select id from wxkcoutput with(nolock) where wxid=h.id and ppriceid>0)
                            </when>
                        </choose>
                    </if>
                    <!--配件发货-->
                    <if test="param.isPeijianfahuo != null and param.isPeijianfahuo == 1">
                        AND EXISTS(SELECT TOP 1 ShouhouId FROM CaigouBasketRefShouhou with(nolock)
                        JOIN caigou_basket with(nolock) ON caigou_basket.id=CaigouBasketRefShouhou.CaigouBasketId
                        JOIN caigou_sub with(nolock) ON caigou_basket.sub_id=caigou_sub.id
                        WHERE IsFahuo=0 and caigou_sub.stats=3 AND ShouhouId=h.id)
                    </if>
                    <if test="param.repairLevel != null">
                        <choose>
                            <when test="param.repairLevel == 10">
                                AND (SELECT count(id) FROM dbo.wxkcoutput wxk WITH(NOLOCK) INNER JOIN dbo.productinfo
                                wxp WITH(NOLOCK) ON wxk.ppriceid=wxp.ppriceid WHERE wxk.wxid=h.id AND wxk.stats!=3) = 1
                            </when>
                            <when test="param.repairLevel == 11">
                                AND EXISTS(SELECT 1 FROM dbo.wxkcoutput wxk WITH(NOLOCK) INNER JOIN dbo.productinfo wxp
                                WITH(NOLOCK) ON wxk.ppriceid=wxp.ppriceid WHERE wxk.wxid=h.id AND wxp.cidFamily LIKE
                                '%,393,%' AND wxk.stats!=3)
                                and (SELECT count(id) FROM dbo.wxkcoutput wxk WITH(NOLOCK)
                                INNER JOIN dbo.productinfo wxp WITH(NOLOCK) ON wxk.ppriceid=wxp.ppriceid
                                WHERE wxk.wxid=h.id AND wxk.stats!=3 and wxp.ppriceid&lt;>71287
                                and ((isnull(wxk.price,0) > 0 and isnull(wxk.inprice,0) > 0) or (isnull(wxk.price,0) >
                                30 and isnull(wxk.inprice,0)=0)) ) > 1
                            </when>
                            <otherwise>
                                and h.RepairLevel=#{param.repairLevel}
                            </otherwise>
                        </choose>
                    </if>
                    <if test="param.serviceWay != null and param.serviceWay != 0">
                        and webstats IS NOT NULL and ISNULL(h.webtype2,0)=#{param.serviceWay}
                    </if>
                    <if test="param.isGaoji != null and param.isGaoji == 1">
                        <if test="param.startTime != null and param.startTime != '' and param.endTime != null and param.endTime != '' and param.timeType != 'testtime'">
                            and h.#{param.timeType} between #{param.startTime} and #{param.endTime}
                        </if>
                    </if>
                    <if test="param.tuihuanType != null">
                        and tuihuan_kind=#{param.tuihuanType}
                    </if>
                    <if test="param.wxcltype != null and param.wxcltype !=''">
                        <choose>
                            <when test="param.wxcltype =='waisong'">
                                AND EXISTS( SELECT 1 FROM dbo.shouhou_qudao with(nolock) WHERE shouhouid=h.id
                                <if test="param.waisongqidao != null and param.waisongqidao>0">
                                    AND shqd2id=#{param.waisongqidao}
                                </if>
                                )
                            </when>
                            <when test="param.wxcltype =='zixiu'">
                                AND NOT EXISTS( SELECT 1 FROM dbo.shouhou_qudao with(nolock) WHERE shouhouid=h.id)
                            </when>
                        </choose>
                    </if>
                    <if test="param.wxpjKind != null and param.key != null and param.key !='' and param.keyVal != null and param.keyVal != '' and param.key =='userid'">
                        <choose>
                            <when test="param.wxpjKind == 'ping'">
                                and EXISTS(SELECT 1 FROM dbo.wxkcoutput WITH(NOLOCK) INNER JOIN dbo.productinfo p
                                with(nolock) ON p.ppriceid=wxkcoutput.ppriceid WHERE p.cid IN(31,24,25) AND
                                wxkcoutput.wxid=h.id)
                            </when>
                            <when test="param.wxpjKind == 'dianchi'">
                                and EXISTS(SELECT 1 FROM dbo.wxkcoutput WITH(NOLOCK) INNER JOIN dbo.productinfo p
                                with(nolock) ON p.ppriceid=wxkcoutput.ppriceid WHERE p.cid=393 AND wxkcoutput.wxid=h.id)
                            </when>
                        </choose>
                    </if>

                </otherwise>
            </choose>
        </if>
    </sql>

    <update id="updateFeiYongByPriceAndId">
        update shouhou set feiyong= CASE WHEN (ISNULL(feiyong,0.0)-(#{price}))&lt;0
        THEN 0 ELSE ISNULL(feiyong,0.0)-(#{price})
        END where 1=1
        <if test="shouhouId != null ">
            AND id=#{shouhouId}
        </if>
    </update>

    <select id="getBasketIdByImei" resultType="java.lang.Integer">
        select top 1 basket_id from product_mkc with(nolock) where 1=1
        <if test="imei != null and imei != ''">
            and imei = #{imei}
        </if>
    </select>

    <select id="getGrandAmount" resultType="java.math.BigDecimal">
        SELECT top 1 GrandAmount FROM RedPacketsRecord with(nolock) WHERE MobilebasketID = #{basketId} AND ISNULL(isGrant,0) = 1
    </select>

    <select id="getCh999Nmae" resultType="java.lang.String">
        select top 1 UserName from BBSXP_Users with(nolock) where mobile=#{mobile} and xtenant=#{xtenant}
    </select>
    <select id="getCh999User" resultType="java.lang.String">
        select top 1 ch999_name FROM
        dbo.ch999_user a WITH ( nolock )
        LEFT JOIN ${officeName}.dbo.appHeadimg b WITH ( nolock ) ON a.ch999_id= b.ch999_id
        LEFT JOIN zhiwu z with(nolock) ON a.zhiwuid= z.id
        WHERE
        a.iszaizhi= 1
        AND a.ch999_id> 1
        and mobile = #{mobile}
    </select>
    <select id="getShouhouTimeByPid" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouDaoJiShiBo">
        select top 1 * from shouhou_daojishi with(nolock) where
        product_id = (SELECT TOP 1 product_id FROM productinfo with(nolock)
        where 1=1
        <if test="ppriceid != null ">
            and ppriceid = #{ppriceid}
        </if>
        )
    </select>

    <select id="getOne" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouBo">
        SELECT
        h.*,
        isnull( h.isfan, 0 ) AS isfan,
        isnull( h.issoft, 0 ) AS issoft,
        isnull( h.wcount, 0 ) AS wcount,
        isnull( h.isquji, 0 ) AS isqujiE,
        t.tuihuan_kind AS tuihuanKind,
        t.check3 as check3,
        isnull( pandian, 0 ) AS pandian,
        isnull( h.toareaid, h.areaid ) AS nowarea,
        u.username AS username2,
        u.userclass AS userClass,
        zh.areaid AS areaZh,
        zh.toareaid AS toareaZh,
        q.shqd2id AS shqd2Id,
        q.shqd2name AS shqd2Name,
        q.dtime AS qudaoAddTime,
        q.inuser as quDaoInUser,
        q.shqdid AS shqdId,
        sb.username AS zysxUserName,
        sb.mobile AS zysxMobile,
        u.realname,
        u.mobile userMobile,
        p.bpic,
        h.modidate
        FROM
        shouhou h WITH ( nolock )
        LEFT JOIN ( SELECT tuihuan_kind, shouhou_id, check3 FROM shouhou_tuihuan WITH ( nolock )
        WHERE isnull( isdel, 0 ) = 0
        <if test="shouhouId != null ">
            AND shouhou_id = #{shouhouId}
        </if>
        ) t ON h.id= t.shouhou_id
        LEFT JOIN bbsxp_users u WITH ( nolock ) ON h.userid= u.id
        LEFT JOIN ( SELECT areaid, toareaid, shouhou_id FROM shouhou_toarea with(nolock)
        WHERE isnull( [check], 0 ) = 0
        <if test="shouhouId != null ">
            AND shouhou_id = #{shouhouId}
        </if>
        ) zh ON zh.shouhou_id= h.id
        LEFT JOIN shouhou_qudao q WITH ( nolock ) ON h.id = q.shouhouid
        LEFT JOIN shouhou_businessinfo sb WITH ( nolock ) ON sb.shouhouid = h.id
        LEFT JOIN productinfo p WITH ( nolock ) ON p.ppriceid = h.ppriceid
        WHERE 1=1
        <if test="shouhouId != null ">
            AND h.id = #{shouhouId}
        </if>
    </select>

    <select id="getAreaIdByShouhouId" resultType="Integer">
        select isnull(toareaid,areaid) from shouhou with(nolock) where id= #{shouhouId}
    </select>

    <select id="isSubCollectZdb" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouHouImportantBo">
        select s.id,s.shouhouid as shouhouId,s.stats,s.commnet as comment,s.dtime from shouhou_important s with(nolock)
        where 1=1 and isnull(stats,0) != 2
        <if test="shouhouId != null">
            and shouhouid = #{shouhouId}
        </if>
    </select>

    <select id="isSubCollect" resultType="Integer">
        select top 1 id from dbo.subCollection with(nolock)
        where sub_id= #{subId} and kind= #{collectType} and ch999_id= #{ch999Id}
    </select>

    <select id="getSubPidBySubId" resultType="java.lang.Integer">
        select subPID from dbo.sub with(nolock) where sub_id=#{subId}
    </select>

    <select id="getSubInuser" resultType="java.lang.String">
        select top 1 distinct inuser from dbo.shouying with(nolock) where sub_pay03>0
        <choose>
            <when test="type == 6 or type == 7">
                and sub_id in(#{subIds})
                and shouying_type in ('订金','交易')
            </when>
            <when test="type == 8">
                and sub_id = #{subId}
                and shouying_type in ('订金2','交易2')
            </when>
            <when test="type == 5">
                and sub_id = #{subId}
                and shouying_type in ('售后','售后小件','订金3')
            </when>
            <when test="type == 11">
                and sub_id = #{subId}
                and sub_pay03>0 and shouying_type in ('订金3')
            </when>
        </choose>
    </select>
    <select id="getShouhouUserInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouUserInfoBo">
        select CASE WHEN ISNULL(sxuserid,0)>0 THEN sxuserid ELSE userid END userId,
        CASE WHEN ISNULL(sxmobile,'')!='' THEN sxmobile ELSE mobile END mobile,
        stats,isnull(toareaid,areaid) areaId from shouhou with(nolock) where id=#{ShouhouId}
    </select>

    <select id="hasJinshuibao" resultType="java.lang.Integer">
        select count(1) FROM shouhou_jinshuibao j with(nolock) INNER JOIN dbo.shouhou s with(nolock) ON j.shouhouid=s.id
        WHERE s.id=#{shouhouId} and j.isdel=0 AND s.wxkind=2 AND s.ServiceType in(6,8) AND ISNULL(j.service_type,6)=#{serviceType}
    </select>

    <select id="getShouhouToAreaTime" resultType="java.time.LocalDateTime">
        SELECT TOP 1 dtime FROM dbo.shouhou_toarea with(nolock) WHERE shouhou_id=#{shouhouId} ORDER BY id ASC
    </select>

    <update id="updateYouhuiMaUseInfo">
        update shouhou set youhuima = isnull(youhuima,'') + '|' + #{youhuiMa},feiyong=(feiyong-#{youhuimaTotal}),youhuifeiyong=ISNULL(youhuifeiyong,0)+#{youhuimaTotal} where id =#{shouhouId}
    </update>

    <select id="getShouhouHuan" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouHuanBo">
        select name,product_color as productColor,imei,stats,product_id as productId,baoxiu,areaid as areaId,toareaid as toAreaId,sub_id as subId,tradedate as tradeDate,isnull(toareaid,areaid) as ckArea,buyareaid AS buyAreaId,
        userid as userId,isnull(isquji,0) as isQuji, mkc_id as mkcId, DATEDIFF(d,tradedate,modidate) as d1time,ishuishou,modidate,basket_id as basketId from shouhou with(nolock) where id=#{shouhouId} and userid != 76783;

    </select>

    <select id="getMobileType" resultType="java.lang.String">
        select dbo.getMobileType(p.cid) mobileType from shouhou h with(nolock)
        left join productinfo p with(nolock) on h.ppriceid = p.ppriceid
        where h.id = #{shouhouId}
    </select>

    <insert id="insertTest">
        INSERT INTO shouhou ( name, problem, username, mobile, stats, baoxiu, inuser,
         imei, xianshi, modidate, feiyong, costprice, userid, isticheng, issoft, product_id,
         ppriceid, isquick, wcount, weixiuzuid, orderid, isquji, isfan, webtype1, webtype2,
         webstats, yuyueid, sxuserid, areaid, isBakData, truename )
         VALUES ( #{sh.name}, #{sh.problem}, #{sh.username}, #{sh.mobile}, #{sh.stats}, #{sh.baoxiu}, #{sh.inuser},
          #{sh.imei}, #{sh.xianshi}, getdate(), #{sh.feiyong}, #{sh.costprice}, #{sh.userid}, #{sh.isticheng}, #{sh.issoft}, #{sh.productId},
          #{sh.ppriceid}, #{sh.isquick}, #{sh.wcount}, #{sh.weixiuzuid}, #{sh.orderid}, #{sh.isquji}, #{sh.isfan}, #{sh.webtype1}, #{sh.webtype2},
          #{sh.webstats}, #{sh.yuyueid}, #{sh.sxuserid}, #{sh.areaid}, #{sh.isBakData}, #{sh.truename} )
    </insert>

    <select id="getShouhouRepaireUserIds" resultType="java.lang.Long">
        select userid from shouhou with(nolock) where stats = 1
        and userid IN
        <if test="userIds != null">
            <foreach collection="userIds" index="index" item="userId" open="(" close=")" separator=",">
                #{userId}
            </foreach>
        </if>
    </select>

    <select id="getRepairHistoryByImei" resultType="com.jiuji.oa.afterservice.shouhou.vo.res.ShouhouBasicInfo">
        select id as shouhouId,product_id as productId,ppriceid as ppid,areaid as areaId,buyareaid as buyAreaId,name as productName,
               problem,stats as status,baoxiu as baoXiu,inuser,imei,modidate as modiDate,feiyong as feiYong,costprice as costPrice,
               userid as userId from shouhou with(nolock) where imei = #{imei}
    </select>

    <select id="getRepairPjListByImei" resultType="com.jiuji.oa.afterservice.shouhou.vo.res.WxPjBasicInfo">
        SELECT wxkc.wxid as shouhouId,wxkc.name,wxkc.ppriceid as ppid,wxkc.inuser,wxkc.dtime,wxkc.stats as status,wxkc.dtime,
               wxkc.price,wxkc.inprice as inPrice,wxkc.output_dtime as outputTime,isnull(p.cid,0) as cid from wxkcoutput wxkc with(nolock)
               left join productinfo p with(nolock) on wxkc.ppriceid = p.ppriceid
               where wxkc.wxid in (select id from shouhou with(nolock) where imei = #{imei} )
    </select>

    <select id="getBindPpidKcInfo" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.BindPpidKcInfo">
        SELECT spb.id,spb.ppid,spb.name,spb.out_put_limit,spb.negative,pk.leftCount,0 as outPutNumber
        FROM dbo.shouhou_ppid_bind spb with(nolock)
        left join product_kc pk with(nolock) on pk.ppriceid = spb.ppid and pk.areaid = #{areaId}
        where
            exists(select 1 from shouhou_ppid_config spc with(nolock) where spc.ppid = #{ppid} AND spc.id = spb.config_id and spc.xtenant = #{xtenant})
          and isnull(spb.is_del,0) = 0 and isnull(spb.auto_out_put,0) =0
    </select>

    <select id="getModifyPriceByShouHouId" resultType="java.math.BigDecimal">
        SELECT sum(wxk.price1) - sum(wxk.price) from wxkcoutput wxk with(nolock) where wxid = #{shouhouId}
    </select>
    <select id="getNotBaoxiu" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        SELECT top 1 sh.id,sh.offtime FROM dbo.shouhou sh with(nolock)
        <where>
            imei=#{imei} AND ISNULL(xianshi,0)=1 AND issoft=0 AND stats=1 AND ISNULL(baoxiu,0)=0 AND isquji=1 AND userid &lt;&gt; 76783
            AND ISNULL(ishuishou,0)=${islp ? 1 : 0}
            <if test="startTime != null and endTime != null">
                and sh.modidate between #{startTime} and #{endTime}
            </if>
        </where>
         ORDER BY id DESC
    </select>
    <select id="getShouhouNotDel" resultMap="BaseResultMap">
        SELECT id, name, peizhi, problem, comment, username, mobile, tel, stats, baoxiu, inuser, imei, xianshi, contentcsdate
             , tradedate, modidate, feiyong, costprice, weixiuren, dyjid, offtime, area, shouyinglock, shouyingdate, shouyinguser
             , userid, kinds, isticheng, waiguan, result_dtime, issoft, modidtime, product_id, product_color, buyarea, pandian
             , pandiandate, toarea, istui, pandianinuser, ppriceid, mkc_id, isquick, wcount, weixiuzuid, weixiuzuid_jl
             , isweixiu, weixiudtime, weixiu_startdtime, orderid, isquji, isfan, pingjia, pingjia1, sub_id, webtype1, webtype2
             , webstats, ServiceType, basket_id, ishuishou, yuyueid, huiprint, weixiurentime, reweixiuren, sxname, sxmobile
             , sxsex, sxuserid, lockpwd, testuser, wxkind, wxConfig, noticetime, testtime, deviceid, devicepwd, youhuima
             , yuyueCheck, isXcMkc, isXcMkcInfo, wxTestTime, wxTestInfo, RepairLevel, areaid, toareaid, buyareaid, wxTestStats
             , gjUser, ProcessConfirmStats, oldshouhouid, isBakData, isjbanwxqq, yuyueCheckuser, qujitongzhitime, daojishi
             , codeMsg, result_user, smstime, teltime, EarnestMoneySubid, serversOutUser, serversOutDtime, youhuifeiyong
             , truename, iszy, wxAreaid, imeifid, yifum, kuaixiuFlag, kuaixiuSendTime, iszp, wuliyou, mobileServeiceType
             , lppeizhi, fromshouhouid, serviceCostprice, question_type, pzid, voucherId, brand_id, refund_remark
        FROM dbo.shouhou sh with(nolock)
        where sh.id= #{id} and isnull(sh.xianshi,1) = 1

        <!--授权隔离-->
        <if test="hasAuthPart">
            <!--租户隔离-->
            and exists (SELECT id from areainfo a with(nolock) where a.id = sh.areaid and a.xtenant = #{xtenant})
            and (sh.areaid = #{areaId} or exists (select id from areainfo a2 with(nolock) where a2.id = sh.areaid and a2.authorizeid = #{authorizeId}))
        </if>
    </select>
    <select id="qujiTotalDifferentPrice" resultType="java.math.BigDecimal">
        SELECT isnull(cast(s.feiyong as numeric(33,4)),0.000)-isnull(cast(s.costprice as numeric(33,4)),0.000)
                   -sum(isnull(cast(sh.price as numeric(33,4)),0.000))-sum(isnull(cast(she.hxprice as numeric(33,4)),0.000)) FROM
        shouhou s with(nolock)
        left join shouhou_huishou sh with(nolock) on sh.shouhou_id = s.id and isnull(sh.isdel,0) = 0
        left join shouhou_hexiao she with(nolock) on exists(SELECT 1 FROM wxkcoutput w2 with(nolock) where w2.wxid = s.id and w2.id = she.Kc_logsId) and she.ishexiao = 1
        where s.id = #{shouhouId} and s.baoxiu = 1 and (sh.isfan = 1 or (sh.id is not null and isnull(sh.isfan,0)=0 and isnull(sh.ishuan,0)=0))
        GROUP by s.id,s.feiyong,s.costprice
    </select>
    <select id="isCodeAddSub" resultType="java.lang.Boolean">
        SELECT iif(exists(select 1 FROM dbo.userCodeSubRecord ucsr with(nolock) where ucsr.subType=4 and isnull(check_type,0)=0 and ucsr.sub_id = #{shouhouId}),1,0)
    </select>
    <select id="countShouhouTimes" resultType="java.lang.Integer">
        select count(1) from shouhou s with(nolock)
            where s.userid = #{userId} and s.imei = #{imei} and s.xianshi = 1
            and not exists(select 1 from sub_delCollect sd with(nolock) where sd.sub_id = s.id and sd.subType = 4)
        <if test="tradeDate != null">
            and s.modidate >= #{tradeDate}
        </if>
    </select>
    <select id="getYouHuiMaCode" resultType="java.lang.String">
        select s.youhuima from shouhou s with(nolock) where s.id = #{shouhouId}
    </select>
    <select id="getMemberDiscoutAmount" resultType="java.math.BigDecimal">
        select isnull(sum(w.member_discount_amount),0) from wxkcoutput w with(nolock) where w.stats &lt;&gt;3 and w.wxid = #{shouhouId}
    </select>

    <select id="getCorporateFans" resultType="java.lang.String">
        select TOP 1 external_userid
        from WeixinUser with(nolock)
        where type = 1 and kinds = 1 and userid = #{userId}
    </select>
    <update id="unShouYinLock">
        update shouhou
        set shouyinglock = 0
        where isnull(isquji,0) = 0 and shouyinglock = 1 and id = #{shouhouId}
    </update>

    <select id="getShouhouTuiKuan" resultType="java.lang.Integer">
        SELECT count(1) FROM
            shouhou_tuihuan WITH ( nolock )
        WHERE
            shouhou_id = #{shouhouId}
          AND tuihuan_kind IN (1,2,3,4,5,11)
          AND ISNULL( isdel, 0 ) = 0
          AND ISNULL( check3, 0 ) = 0
    </select>

    <select id="canChangeImei" resultType="java.lang.Integer">
        SELECT count(1) FROM
            shouhou_tuihuan WITH ( nolock )
        WHERE
            shouhou_id = #{shouhouId}
          AND tuihuan_kind IN (1,2,3,4,5,11)
          AND ISNULL( isdel, 0 ) = 0
          AND (ISNULL( check3, 0 ) = 0 or ISNULL(check3, 0)=1 and tuihuan_kind IN (1,2,3,4))
    </select>

    <select id="getPhoneAddressByPhoneNumber" resultType="java.lang.String">
        select top 1 concat(m.City,' ' ,m.Type) as phoneAddress
        from Mobile m with(nolock)
        WHERE m.Num = #{phoneNumber}
    </select>

    <select id="getFromsourceById" resultType="java.lang.Integer">
        select sy.from_source from shouhou s with(nolock) LEFT JOIN shouhou_yuyue sy with(nolock) on s.yuyueid = sy.id where s.id = #{id}
    </select>
    <sql id="douYinCouponLogResSql">
        dcl.id,dcl.areaid,dcl.youhui_price youhuiPrice,dcl.pay_price payPrice,dcl.card_id cardId,pay_id payId
                   ,status,dcl.origin_code originCode
                   ,dcl.coupon_kinds couponKinds,dcl.order_id tuangouOrderId
    </sql>
    <select id="getLastDouYinCouponLog"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.DouYinCouponLogRes">
        SELECT top 1 <include refid="douYinCouponLogResSql"></include>
        from douyin_coupon_log dcl with(nolock)
        where dcl.sub_kinds = #{subKinds} and is_del = 0 and sub_id = #{shouhouId}
        order by dcl.id desc
    </select>
    <select id="selectMaintenanceOrder" resultType="com.jiuji.oa.afterservice.bigpro.po.Shouhou">
        select top 10 * from dbo.shouhou s with (nolock )
        where isnull(s.xianshi,0)=1
        and isnull(s.issoft,0) = 0
        and s.userid= #{req.userId}
        <if test="req.isquji !=null and req.isquji">
          and isnull(s.isquji,0)=1
        </if>
        order by s.id desc
    </select>


    <select id="selectRepairOrderType" resultType="java.lang.Integer">
        SELECT case
            when s.userid = 76783 then 1
            when st.tuihuan_kind =3 then 2
            when st.id is not null then 3
           else 4  end as repairOrderType
        from dbo.shouhou s with(nolock)
        left join dbo.shouhou_tuihuan st with(nolock) on st.shouhou_id = s.id and isnull(st.isdel,0)=0 and st.tuihuan_kind in(1,2,3,4)
        where s.id=#{shouHouId}
    </select>


    <update id="delWuLiuById" >
        update dbo.wuliu  set stats=5  where stats=1 and wutype=1 and  isnull(nu,'')='' and wCateId in (4,5) and id= #{id}
    </update>

    <select id="selectCoin9jiByUserId" resultType="java.lang.Integer">
        select id from coin9ji c with(nolock) where type=4 and stats=1  and c.eTime>=getdate() and userid=#{userId}
    </select>
    <select id="getLastDouYinCouponLogByShouYingId"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.DouYinCouponLogRes">
        SELECT top 1 <include refid="douYinCouponLogResSql"></include>
        from douyin_coupon_log dcl with(nolock)
        where is_del = 0 and pay_id = #{shouyingId}
        order by dcl.id desc
    </select>
    <select id="getShouhouIdByRepairOrderId" resultType="java.lang.Integer">
        select s.id from shouhou s with(nolock) where s.repair_order_id = #{shouhouId}
    </select>
    <select id="getPrintFlag" resultType="java.lang.Boolean">
        select 1 from dbo.order_record_flag f where f.flag_type=1 and f.del_flag=0 and f.sub_type=4 and f.sub_id = #{shouhouId}
    </select>
    <select id="searchSubCollect" resultType="java.lang.String">
        select distinct ch999_id from dbo.subCollection with(nolock)
        where sub_id= #{subId} and kind= #{collectType}
    </select>
</mapper>
