<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.tuihuan.ShouhouTuiHuanMapper">
    <update id="updateToDel">
        update shouhou_tuihuan set isdel=1,delUser=#{userName},delTime=getdate()
        where isnull(isdel,0)=0 and check3 is null and tuihuan_kind = #{tuihuanKind} and shouhou_id = #{shouhouId}
    </update>
    <select id="getLastNotCompleteBySubId"
            resultType="com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo">
        SELECT top 1 id,shouhou_id,tuihuan_kind,inuser,areaid,comment,tui_way,bankname,bankfuming,banknumber,tuikuanM,tuikuanM1
                   ,zhejiaM,sub_id,basket_ids,inprice,coinM,smallproid,baitiaoM,kuBaiTiaoM,isValidt,check1,check1dtime,check1user
                   ,check2,check2dtime,check2user,check3,check3dtime,check3user,isdel,dtime,payOpenId
        FROM  shouhou_tuihuan st with(nolock) where isnull(st.isdel,0) = 0 and st.sub_id = #{subId} and tuihuan_kind not in(9)
            and st.check3 is null order by st.id DESC
    </select>
    <select id="listReturnWay" resultType="java.lang.String">
        SELECT DISTINCT tui_way,len(st.tui_way) tuiLen from shouhou_tuihuan st with(nolock)  where st.tui_way is not null and len(st.tui_way)>0 order by tuiLen,st.tui_way
    </select>
    <select id="sumTuiHuanByRefundWay" resultType="java.math.BigDecimal">
        SELECT sum(case when st.tui_kinds = 1 then std.refund_price else isnull(st.tuikuanM,0.0)-isnull(st.peizhiPrice,0.0)-isnull(st.piaoPrice,0.0) end) FROM shouhou_tuihuan st with(nolock)
        left join shouhou_tuihuan_detail std with(nolock) on st.tui_kinds = 1 and (isnull(std.is_del,0) = 0 or
                <include refid="com.jiuji.oa.afterservice.refund.dao.RefundCommonMapper.stdOtherRefundSql"></include>) and std.fk_tuihuan_id = st.id
        where isnull(st.isdel,0) = 0 and
        (
            st.tui_kinds = 0 and st.tui_way in
            <foreach collection="refundWayNames" open="(" close=")" item="refundWayName" separator=",">
                #{refundWayName}
            </foreach>
                or st.tui_kinds = 1 and std.tui_way in
            <foreach collection="refundWayNames" open="(" close=")" item="refundWayName" separator=",">
                #{refundWayName}
            </foreach>
        )
        <include refid="com.jiuji.oa.afterservice.refund.dao.way.CardPayOriginWayMapper.andTuihuanMatchTradeSubIdSql"></include>
    </select>
</mapper>
