<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouImeichangeMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.ShouhouImeichange" id="shouhouImeichangeMap">
        <result property="imei1" column="imei1"/>
        <result property="imei2" column="imei2"/>
        <result property="dtime" column="dtime"/>
        <result property="inuser" column="inuser"/>
        <result property="checkuser" column="checkuser"/>
        <result property="checkdtime" column="checkdtime"/>
        <result property="shouhouId" column="shouhou_id"/>
        <result property="msg" column="msg"/>
    </resultMap>

    <select id="getTransferOutImeiChange" resultMap="shouhouImeichangeMap">
        SELECT top 1 s.imei1, s.imei2, s.dtime, s.inuser, s.checkuser, s.checkdtime, s.shouhou_id, s.msg FROM dbo.shouhou_imeichange s with(nolock)
        where s.imei2=#{imei}
          and checkdtime is not null
          and not exists(select 1 from product_mkc k with(nolock),mkc_dellogs d with(nolock) where d.mkc_id=k.id and d.kinds='h2' and k.imei=s.imei2 )
        order by checkdtime desc
    </select>


</mapper>