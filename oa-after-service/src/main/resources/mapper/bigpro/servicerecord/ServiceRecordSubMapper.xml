<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.servicerecord.ServiceRecordSubMapper">
    <select id="getSimpleSub" resultType="com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo">
        select top 1 s.sub_id subId,s.sub_check,k.imei,isnull(s.tradeDate1,s.sub_date) orderDate
        from product_mkc k with(nolock)
        left join basket b with(nolock) on k.basket_id=b.basket_id
        left join sub s with(nolock) on b.sub_id=s.sub_id
        where  k.imei in
            <foreach collection="imeis" open="(" close=")" item="imei" separator=",">
                #{imei}
            </foreach>
            <if test="userId != null and userId != 0">
                and s.userid = #{userId}
            </if>
            <if test="subId != null and subId != 0">
                and s.sub_id = #{subId}
            </if>
          <choose>
              <when test="isEffect">
                  and s.sub_check not in(4,8,9)
              </when>
              <otherwise>
                  and s.sub_check = 3
              </otherwise>
          </choose>
        order by orderDate desc
    </select>
    <select id="getSubById" resultType="com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO">
        select s.sub_to as subTo,b.ppriceid as ppriceId,k.id as mkcId,s.tradedate as tradeDate,k.imei as imei
        ,s.userid as userId,s.areaid as mkcAreaId,s.sub_mobile subMobile,b.sub_id subId,b.basket_id as basketId
        ,isnull(b.type,0) as basketType,s.subtype subType,b.price as price,s.tradeDate1 as transactionDate,s.sub_date subDate
        ,s.sub_check, b.ismobile isMobile
        from product_mkc k with(nolock)
        left join basket b with(nolock) on k.basket_id=b.basket_id
        left join sub s with(nolock) on b.sub_id=s.sub_id
        where s.sub_id = #{subId} and k.imei = #{imei}
    </select>
    <select id="list9jiServiceRecord" resultType="com.jiuji.oa.afterservice.bigpro.po.ServiceRecord">
        select  sr.id,sr.areaid,sr.tradedate,sr.imei,sr.basket_idBind,sr.ServiceType,isnull(sr.price,0) as price
             ,isnull(sr.feiyong,0) as feiyong,sr.startTime,sr.endTime,sr.ppriceid,sr.servicesTypeBindId
             ,isnull(sr.classification,1) as classification,sr.discountBasketId,sr.server_shouhou_id serverShouhouId
        from ServiceRecord sr with(nolock)
        where isnull(sr.isdel,0)=0 and sr.basket_idBind=#{basketId} and sr.imei=#{imei}  ORDER BY sr.tradedate asc
    </select>
    <select id="getHistoryRecord" resultType="com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO">
        seLect top 1 r.userid as userId,p.product_name as productName,u.mobile as subMobile,r.tradedate as tradeDate
        ,r.areaid as mkcAreaId,u.userclass as userClass,u.UserName as username,u.blacklist as blacklist,r.product_color as productColor
        ,r.product_id as productId,r.ppriceid as ppriceId,r.sub_id as subId,r.basket_id as basketId,r.imei as imei
        ,p.brandid as brandId,p.cid as cid,3 subCheck
        from record1 r with(nolock)
        left join BBSXP_Users u with(nolock) on r.userid=u.ID
        left join productinfo p with(nolock) on p.ppriceid=r.ppriceid
        <where>
            and r.sub_id = #{subId} and r.imei = #{imei}
        </where>
        order by r.id desc
    </select>
    <select id="getSimpleHistoryRecord"
            resultType="com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo">
        seLect top 1 r.sub_id subId,3 subCheck,r.imei,r.tradeDate orderDate
        from record1 r with(nolock)
        <where>
            and r.imei=#{imei}
            <if test="userId != null and userId != 0">
                and r.userid=#{userId}
            </if>
        </where>
        order by r.id desc
    </select>
</mapper>
