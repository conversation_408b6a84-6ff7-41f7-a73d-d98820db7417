<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.servicerecord.ServiceRecordRecoverSubMapper">
    <select id="getSimpleSub"
            resultType="com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo">
        select top 1 s.sub_id as subId,s.sub_check,k.imei,isnull(s.tradeDate1,isnull(s.tradeDate,s.sub_date)) as orderDate
        from   dbo.recover_mkc k with(nolock)
                   left join dbo.recover_marketSubInfo b with(nolock) on k.to_basket_id = b.basket_id
                   left join dbo.recover_marketInfo s with(nolock) on b.sub_id = s.sub_id
        where k.imei = #{imei}
        <choose>
            <when test="isEffect">
                and s.sub_check in ( 1,2,6,3)
            </when>
            <otherwise>
                and s.sub_check = 3
            </otherwise>
        </choose>
          AND ISNULL(s.saleType,0)=0
        <if test="userId != null and userId != 0">
            and s.userid = #{userId}
        </if>
        <if test="subId != null and subId != 0">
            and s.sub_id = #{subId}
        </if>
        order by orderDate desc
    </select>
    <select id="getSubById" resultType="com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO">
        select top 1 s.sub_to subTo,b.ppriceid as ppriceId, k.id as mkcId,isnull(s.tradedate,GETDATE()) as tradeDate,k.imei as imei,s.userid as userId
        ,s.areaid as mkcAreaId,s.sub_mobile as subMobile,b.sub_id as subId,b.basket_id as basketId
        ,b.type as basketType,s.subtype as subType,s.saleType saleType,b.price as price,s.tradeDate1 as transactionDate
        ,s.sub_date subDate,s.sub_check, b.ismobile isMobile
        from   dbo.recover_mkc k with(nolock)
        left join dbo.recover_marketSubInfo b with(nolock) on k.to_basket_id = b.basket_id
        left join dbo.recover_marketInfo s with(nolock) on b.sub_id = s.sub_id
        <where>
            s.sub_id = #{subId} and k.imei = #{imei} and s.sub_check in ( 1,2,6,3) AND ISNULL(s.saleType,0)=0
        </where>
        order by isnull(s.tradeDate1,s.tradeDate) desc
    </select>
    <select id="list9jiServiceRecord" resultType="com.jiuji.oa.afterservice.bigpro.po.ServiceRecord">
        select  sr.id,sr.areaid,sr.tradedate,sr.imei,sr.basket_idBind,sr.ServiceType,isnull(sr.price,0) as price
             ,isnull(sr.feiyong,0) as feiyong,sr.startTime,sr.endTime,sr.ppriceid,sr.servicesTypeBindId
             ,isnull(sr.classification,1) as classification,sr.discountBasketId,sr.server_shouhou_id serverShouhouId
        from ServiceRecord sr with(nolock)
        where isnull(sr.isdel,0)=0 and sr.bargain_basket_id_bind=#{basketId} and sr.imei=#{imei}  ORDER BY sr.tradedate asc
    </select>
</mapper>
