<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.servicerecord.ServiceRecordExternalRepairMapper">
    <select id="getSimpleSub" resultType="com.jiuji.oa.afterservice.bigpro.bo.externalrepair.SimpleServiceSubInfoBo">

        <choose>
            <when test="isEffect">
                SELECT top 1 s.sub_id subId,s.sub_check,be.union_pro_imei imei,isnull(s.tradeDate1,s.sub_date) orderDate from sub s with(nolock)
                inner join basket b with(nolock) on b.sub_id = s.sub_id and b.[type] = 107
                inner join dbo.basket_extend be with(nolock) on be.basket_id = b.basket_id
                where s.sub_check not in(4,8,9) and be.union_pro_imei = #{imei}
            </when>
            <otherwise>
                SELECT top 1 s.sub_id subId,s.sub_check,sr.imei imei,isnull(s.tradeDate1,s.sub_date) orderDate from sub s with(nolock)
                inner join ServiceRecord sr with(nolock) on isnull(sr.isdel,0) = 0 and sr.sub_id = s.sub_id and isnull(sr.basket_idBind,0)=0 and isnull(sr.bargain_basket_id_bind,0)=0
                where s.sub_check = 3 and sr.imei = #{imei}
            </otherwise>
        </choose>

        <if test="userId != null and userId != 0">
            and s.userid = #{userId}
        </if>
        <if test="subId != null and subId != 0">
            and s.sub_id = #{subId}
        </if>
        order by orderDate desc
    </select>
    <select id="getSubById" resultType="com.jiuji.oa.afterservice.shouhou.vo.ServiceInfoVO">
        select top 1 s.sub_to as subTo,be.union_pro_ppid as ppriceId,null mkcId,null as tradeDate,be.union_pro_imei as imei
                   ,s.userid as userId,null as mkcAreaId,s.sub_mobile subMobile,s.sub_id subId,null basketId
                   ,null basketType,null subType,0 as price,null as transactionDate,s.sub_date subDate,s.sub_check, null isMobile
        from sub s with(nolock)
             left join dbo.basket_extend be with(nolock) on exists(select 1 from basket b with(nolock) where b.basket_id = be.basket_id and b.sub_id = s.sub_id)
        where s.sub_id = #{subId} and be.union_pro_imei = #{imei}
        order by s.tradeDate1 desc
    </select>
    <select id="list9jiServiceRecord" resultType="com.jiuji.oa.afterservice.bigpro.po.ServiceRecord">
        select  sr.id,sr.areaid,sr.tradedate,sr.imei,sr.basket_idBind,sr.ServiceType,isnull(sr.price,0) as price
             ,isnull(sr.feiyong,0) as feiyong,sr.startTime,sr.endTime,sr.ppriceid,sr.servicesTypeBindId,sr.server_shouhou_id serverShouhouId
             ,isnull(sr.classification,1) as classification,sr.discountBasketId
        from ServiceRecord sr with(nolock)
        where isnull(sr.isdel,0)=0 and sr.sub_id=#{subId} and sr.imei=#{imei}  ORDER BY sr.tradedate asc
    </select>
</mapper>
