<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.TApplyinfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.TApplyinfo">
        <id column="ApplyId" property="ApplyId" />
        <result column="CategoryId" property="CategoryId" />
        <result column="sCategoryId" property="sCategoryId" />
        <result column="InUser" property="InUser" />
        <result column="InUserName" property="InUserName" />
        <result column="Checker" property="Checker" />
        <result column="CheckerName" property="CheckerName" />
        <result column="Area" property="Area" />
        <result column="ApplyTime" property="ApplyTime" />
        <result column="Remark" property="Remark" />
        <result column="CurrentStatus" property="CurrentStatus" />
        <result column="InUserDepartment" property="InUserDepartment" />
        <result column="AttachFiles" property="AttachFiles" />
        <result column="ApplyTitle" property="ApplyTitle" />
        <result column="IsDel" property="IsDel" />
        <result column="Id" property="Id" />
        <result column="amount" property="amount" />
        <result column="RelatedApply" property="RelatedApply" />
        <result column="HasPreLoad" property="HasPreLoad" />
        <result column="sTime" property="sTime" />
        <result column="eTime" property="eTime" />
        <result column="AttchFiles1" property="AttchFiles1" />
        <result column="oldInUserDepartment" property="oldInUserDepartment" />
        <result column="zhichuid" property="zhichuid" />
        <result column="acceptuser" property="acceptuser" />
        <result column="acceptuserid" property="acceptuserid" />
        <result column="AreaId" property="AreaId" />
        <result column="AttchFiles1tmp" property="AttchFiles1tmp" />
        <result column="AttachFilestmp" property="AttachFilestmp" />
        <result column="isup" property="isup" />
        <result column="kfly" property="kfly" />
        <result column="kfyjxg" property="kfyjxg" />
        <result column="kfmd" property="kfmd" />
        <result column="WishTime" property="WishTime" />
        <result column="PlanFinishTime" property="PlanFinishTime" />
        <result column="UseHours" property="UseHours" />
        <result column="Priority" property="Priority" />
        <result column="NotifyCEO" property="NotifyCEO" />
        <result column="daipeiUser" property="daipeiUser" />
        <result column="zhiji" property="zhiji" />
        <result column="kaoqinType" property="kaoqinType" />
        <result column="sTime0" property="sTime0" />
        <result column="eTime0" property="eTime0" />
        <result column="lastApplyTime" property="lastApplyTime" />
        <result column="lastCount" property="lastCount" />
        <result column="curCount" property="curCount" />
        <result column="mobile" property="mobile" />
        <result column="userType" property="userType" />
        <result column="processStats" property="processStats" />
        <result column="gyType" property="gyType" />
        <result column="businessDestination" property="businessDestination" />
        <result column="buyTicket" property="buyTicket" />
        <result column="ticketType" property="ticketType" />
        <result column="relatedids" property="relatedids" />
        <result column="dailizhiwu" property="dailizhiwu" />
        <result column="isNewFlag" property="isNewFlag" />
        <result column="zhichuids" property="zhichuids" />
        <result column="IsApplyForSomeone" property="IsApplyForSomeone" />
        <result column="RealApplicant" property="RealApplicant" />
        <result column="RealApplicantId" property="RealApplicantId" />
        <result column="QudaoChannelId" property="QudaoChannelId" />
        <result column="inuserDepartCode" property="inuserDepartCode" />
        <result column="departBianZhi" property="departBianZhi" />
        <result column="giftCash" property="giftCash" />
        <result column="flightNumber" property="flightNumber" />
        <result column="takeoffTime" property="takeoffTime" />
        <result column="flightNumber2" property="flightNumber2" />
        <result column="takeoffTime2" property="takeoffTime2" />
        <result column="ArrivalTime" property="ArrivalTime" />
    </resultMap>

</mapper>
