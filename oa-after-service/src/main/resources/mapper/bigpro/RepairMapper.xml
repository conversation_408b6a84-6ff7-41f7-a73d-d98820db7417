<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.RepairMapper">

     <!--通用查询映射结果-->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Imeisearchlogs">
        <id column="id" property="id" />
        <result column="ch999_user" property="ch999User" />
        <result column="key" property="key" />
        <result column="dtime" property="dtime" />
        <result column="isclear" property="isclear" />
        <result column="area" property="area" />
        <result column="areaid" property="areaid" />
    </resultMap>

    <select id="queryRepaireFee" resultType="com.jiuji.oa.afterservice.bigpro.bo.RepairFeeBo">
        SELECT SUM(wxk.price+ISNULL(wxk.price_gs,0)-ISNULL(hs.price,0)) as feiYong,
        SUM(wxk.inprice) as costPrice,SUM(case when wxk.service_type>0 then wxk.inprice else 0 end) serviceCostPrice
        FROM dbo.wxkcoutput wxk with(nolock)
        LEFT JOIN dbo.shouhou_huishou hs with(nolock) ON wxk.id=hs.wxkcid WHERE 1=1
        <if test="wxid != null">
            and wxid = #{wxid}
        </if>
        AND ISNULL(wxk.stats,0) != 3
    </select>

    <select id="queryYouHuiMaInfo" resultType="com.jiuji.oa.afterservice.bigpro.po.NumberCard">
        SELECT distinct nc.ID, nc.GName, nc.Total, nc.CardID, nc.State, nc.Isdel, nc.StartTime, nc.EndTime, nc.AddTime, nc.[Input]
                      , nc.[limit], nc.userid, nc.use_count, nc.limit1, nc.limit2, nc.limitprice, nc.limitType, nc.limitids, nc.areas
                      , nc.takeMethod, nc.limitWay, nc.limintClint, nc.ch999_id, nc.binduid, nc.noticetime, nc.isdjq, nc.authorizeid
                      , nc.other_id, nc.areaids, nc.seller, nc.isNoProfit, nc.intime, nc.lastUpdateTime, nc.lastUpdateUser, nc.excludePpIds
             , nc.rule_code, nc.rule_id
        FROM NumberCard nc with(nolock)
        INNER JOIN dbo.cardLogs cl with(nolock) ON cl.cardid=nc.ID
        WHERE ISNULL(cl.useType,0)=1
        <if test="wxid != null">
            and cl.sub_id= #{wxid}
        </if>
    </select>

    <select id="querySubCategory" resultType="java.lang.Integer">
        SELECT COUNT(1) FROM dbo.category with(nolock) WHERE 1=1 AND Path LIKE '%,23,%'
        and ID IN
        <if test="limitids != null">
            <foreach collection="limitids" index="index" item="id" open="(" close=")" separator=",">
                #{id}
            </foreach>
        </if>
    </select>

</mapper>
