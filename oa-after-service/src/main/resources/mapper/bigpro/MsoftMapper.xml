<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.MsoftMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Msoft">
        <id column="id" property="id"/>
        <result column="problem" property="problem"/>
        <result column="inuser" property="inuser"/>
        <result column="imei" property="imei"/>
        <result column="tradedate" property="tradedate"/>
        <result column="modidate" property="modidate"/>
        <result column="area" property="area"/>
        <result column="userid" property="userid"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="buyarea" property="buyarea"/>
        <result column="isticheng" property="isticheng"/>
        <result column="pingjia" property="pingjia"/>
        <result column="isfan" property="isfan"/>
        <result column="areaid" property="areaid"/>
        <result column="buyareaid" property="buyareaid"/>
        <result column="imeifid" property="imeifid"/>
        <result column="sub_id" property="subId"/>
        <result column="evaluateid" property="evaluateid"/>
        <result column="ishuishou" property="ishuishou"/>
        <result column="mobileServeiceType" property="mobileServeiceType"/>
        <result column="question_type" property="questionType"/>
        <result column="fidlist" property="fidlist"/>
        <result column="isxianchang" property="isxianchang"/>
        <result column="username" property="username"/>
        <result column="usermobile" property="usermobile"/>
        <result column="expectdate" property="expectdate"/>
    </resultMap>

    <select id="isTicheng" resultType="java.lang.Boolean">
        SELECT count(1) FROM dbo.msoft WITH(NOLOCK) WHERE imei=#{imei} AND DATEDIFF(HOUR,modidate,GETDATE()) &lt;= 24
    </select>

    <select id="getSubIdByImei" resultType="java.lang.Long">
        select top 1 b.sub_id from dbo.basket b WITH(NOLOCK) left join dbo.product_mkc k WITH(NOLOCK) on k.basket_id=b.basket_id
        where isnull(b.isdel,0)=0 and b.ismobile=1
        and exists(select 1 from dbo.msoft m WITH(NOLOCK) where m.imei=k.imei and m.imei=#{imei} )
    </select>

    <select id="getJiFenDtByMsoftId" resultType="com.jiuji.oa.afterservice.bigpro.bo.MsoftBo">
        select m.areaid as areaId,b.mobile,m.inuser,m.userid as userId,m.id num,dbo.[getUserNickName](m.userid) as nickName, b.userclass userClass
        from dbo.msoft m WITH(nolock)
        LEFT JOIN BBSXP_Users b WITH(nolock) ON b.id = m.userid
        where DATEDIFF(DAY,tradedate,modidate) != 0 and m.id = #{msoftId}
    </select>

    <select id="getXianchang" resultType="com.jiuji.oa.afterservice.bigpro.bo.MsoftXianChangRecord">
        select top 10 id,p.product_name,imei,modidate as modiData,username as userName,usermobile as userMobile,expectdate as expectDate FROM dbo.msoft m WITH(NOLOCK)
        LEFT JOIN dbo.productinfo p WITH(NOLOCK) ON p.ppriceid=m.ppriceid
        WHERE ISNULL(isxianchang,0)=1 AND inuser=#{inUser} AND DATEDIFF(DAY,modidate,GETDATE())&lt;= 3 ORDER BY id DESC
    </select>

    <select id="getMySoftCount" resultType="java.lang.Integer">
        select count(id) from msoft WITH(NOLOCK) where isticheng=1 and inuser=#{inUser}
        <if test="dataType != null and dataType == 1">
            and DateDiff(day,modidate,GETDATE())=0
        </if>
        <if test="dataType != null and dataType == 2">
            and DateDiff(month,modidate,GETDATE())=0
        </if>
    </select>

    <select id="calcIsTiCheng" resultType="java.lang.Integer">
        SELECT sum(receiveCount) from (
        SELECT count(1) as receiveCount from shouhou with(nolock)
        where imei = #{imei} and xianshi = 1 and DateDiff(day,modidate,GETDATE()) &lt;= 7 and isticheng = 1
        and inuser = #{inUser}
        union all
        SELECT count(1) as receiveCount from msoft with(nolock)
        where imei = #{imei} and DateDiff(day,modidate,GETDATE()) &lt;= 7 and isticheng = 1 and inuser = #{inUser}
        ) t
    </select>

    <select id="calcReceiveCountWithinTwoMinutes" resultType="java.lang.Integer">
        SELECT sum(receiveCount) from (
        SELECT count(1) as receiveCount from shouhou with(nolock)
        where  xianshi = 1 and DateDiff(minute,modidate,GETDATE()) &lt;= 2
        and inuser = #{inUser}
        union all
        SELECT count(1) as receiveCount from msoft with(nolock)
        where  DateDiff(minute,modidate,GETDATE()) &lt;= 2
        and inuser = #{inUser}
        ) t
    </select>

    <select id="getIsAssemblyMachine" resultType="java.lang.Boolean">
        if exists (SELECT TOP 1 id FROM dbo.msoft WITH(NOLOCK) WHERE imei=#{imei}) select '1' else select '0'
    </select>
    <select id="getSubAddSoftSubInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.soft.SubAddSoftBo">
        <choose>
            <when test="subType == 1">
                SELECT b.ppriceid,b.sub_id subId,s.sub_date tradedate,s.userid,s.sub_mobile mobile,s.sub_to truename
                FROM basket b with(nolock)
                inner join sub s with(nolock) on s.sub_id = b.sub_id
                where b.ismobile = 1 and b.basket_id = #{basketId}
            </when>
            <when test="subType == 2">
                SELECT b.ppriceid,b.sub_id subId,s.sub_date tradedate,s.userid,s.sub_mobile mobile,s.sub_to truename
                FROM recover_marketSubInfo b with(nolock)
                inner join recover_marketInfo s with(nolock) on s.sub_id = b.sub_id and s.saleType = 0
                where b.ismobile = 1 and b.basket_id = #{basketId}
            </when>
        </choose>

    </select>

</mapper>
