<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.BasketOtherMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.BasketOther">
        <id column="basket_id" property="basketId" />
        <result column="lcount" property="lcount" />
        <result column="area" property="area" />
        <result column="ppriceid" property="ppriceid" />
        <result column="isDone" property="isDone" />
        <result column="dtime" property="dtime" />
        <result column="inuser" property="inuser" />
        <result column="areaid" property="areaid" />
    </resultMap>


    <select id="getBasketOtherInfoByIdAndIsDone" resultType="com.jiuji.oa.afterservice.bigpro.po.BasketOther">
        select lcount,isDone,ppriceid,areaid from basket_other with(nolock)
        where basket_id=#{basketId} and isnull(isDone,0)=#{isDone}
    </select>

</mapper>
