<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouLogsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouLogs">
        <id column="id" property="id" />
        <result column="shouhou_id" property="shouhouId" />
        <result column="comment" property="comment" />
        <result column="inuser" property="inuser" />
        <result column="dtime" property="dtime" />
        <result column="isweb" property="isweb" />
    </resultMap>

    <select id="getJiXing" resultType="com.jiuji.oa.afterservice.bigpro.bo.JiXingBo">
        select name,imei from shouhou with(nolock) where 1=1
        <if test="wxId != null">
            and id = #{wxId}
        </if>

    </select>
</mapper>
