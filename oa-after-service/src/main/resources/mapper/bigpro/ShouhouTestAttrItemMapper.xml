<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouTestAttrItemMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouTestAttrItem">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="attrId" column="attr_id" jdbcType="BIGINT"/>
            <result property="itemLable" column="item_lable" jdbcType="VARCHAR"/>
            <result property="itemValue" column="item_value" jdbcType="VARCHAR"/>
            <result property="attrGroupId" column="attr_group_id" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,attr_id,item_lable,
        item_value,attr_group_id,sort,
        remark,create_time,update_time,
        is_delete,shouhou_test_attr_item_rv
    </sql>
</mapper>
