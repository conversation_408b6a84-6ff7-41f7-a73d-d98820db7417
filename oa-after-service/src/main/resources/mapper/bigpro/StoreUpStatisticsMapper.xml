<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.statistics.StoreUpStatisticsMapper">

    <sql id="statisticsWhere">
        <!--授权隔离-->
        <if test="query.authPart">
            <!--租户隔离-->
            and exists (SELECT id from areainfo a with(nolock) where a.id = s.areaid and a.xtenant = #{query.xtenant})
            and (s.areaid = #{query.currAreaId} or exists (select id from areainfo a2 with(nolock) where a2.id = s.areaid and a2.authorizeid = #{query.authorizeId}))
        </if>
        and s.sub_check not in(3,4,8,9) and isnull(b.ismobile,0)=1 and isnull(b.isdel,0)=0
        <if test="query.areaIds != null">
            <foreach collection="query.areaIds" separator="," open="AND s.areaid in (" close=")" item="areaId">
                #{areaId}
            </foreach>
        </if>
        <if test="query.areaLevel != null and query.areaLevel != 0">
            and isnull(a.level1,8) = #{query.areaLevel}
        </if>
        <if test="query.salesman != null and query.salesman != ''">
            and b.seller = #{query.salesman}
        </if>
        <if test="query.startTime != null">
            and s.sub_date &gt;= #{query.startTime}
        </if>
        <if test="query.endTime != null">
            and s.sub_date &lt;= #{query.endTime}
        </if>
    </sql>
    <select id="statisticsList"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.statistics.StoreUpStatisticsVO">
        SELECT d.name bigArea,d1.name smallArea ,a.area area,a.id areaId
             <if test="query.countType == 2">
                 , b.seller salesman
             </if>
             ,a.level1 areaLevel
             ,count (k.id) storeUpCount
             ,sum (case when k.kc_check=3 then 1 else 0 end ) inventoryCount
             ,sum (case when k.kc_check=10 then 1 else 0 end ) onPassageCount
        from sub s WITH(NOLOCK)
            inner join basket b WITH(NOLOCK)  on b.sub_id=s.sub_id
            left join  product_mkc k WITH(NOLOCK)  on k.basket_id=b.basket_id
            left join  areainfo a WITH(NOLOCK)  on a.id=s.areaid
            left join departInfo d WITH(NOLOCK)  on d.id=dbo.getDepartTypeId(a.depart_id,3)
            left join departInfo d1 WITH(NOLOCK)  on d1.id=dbo.getDepartTypeId(a.depart_id,4)
        where 1=1 <include refid="statisticsWhere"></include> and k.kc_check in (3,10)

        group by d.name ,d1.name ,a.id,a.area,a.level1
        <if test="query.countType == 2">
            , b.seller
        </if>
        <if test="query.numberType != null and query.numberType != 0">
            <choose>
                <when test="query.numberType == 1">
                    having count (k.id) &gt;#{query.numberValue}
                </when>
                <when test="query.numberType == 2">
                    having sum (case when k.kc_check=3 then 1 else 0 end ) &gt;#{query.numberValue}
                </when>
                <when test="query.numberType == 3">
                    having sum (case when k.kc_check=10 then 1 else 0 end ) &gt;#{query.numberValue}
                </when>
                <otherwise></otherwise>
            </choose>
        </if>
        order by storeUpCount desc
    </select>
    <select id="listOrderId" resultType="java.lang.Integer">
        SELECT s.sub_id from sub s WITH(NOLOCK)
        inner join basket b WITH(NOLOCK)  on b.sub_id=s.sub_id
        left join  product_mkc k WITH(NOLOCK)  on k.basket_id=b.basket_id
        left join  areainfo a WITH(NOLOCK)  on a.id=s.areaid
        where 1=1 <include refid="statisticsWhere"></include>
        and s.areaid = #{query.areaId}
        and
        <choose>
            <when test="query.orderType == 1">
                k.kc_check in (3,10)
            </when>
            <when test="query.orderType == 2">
                k.kc_check = 3
            </when>
            <when test="query.orderType == 3">
                k.kc_check = 10
            </when>
            <otherwise></otherwise>
        </choose>

    </select>
</mapper>