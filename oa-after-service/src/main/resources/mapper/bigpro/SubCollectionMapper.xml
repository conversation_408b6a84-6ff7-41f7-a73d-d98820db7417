<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.SubCollectionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.SubCollection">
        <id column="id" property="id" />
        <result column="sub_id" property="subId" />
        <result column="ch999_id" property="ch999Id" />
        <result column="dtime" property="dtime" />
        <result column="kind" property="kind" />
        <result column="ch999_name" property="ch999Name" />
        <result column="hasNoticedWay" property="hasNoticedWay" />
    </resultMap>

    <select id="getSubCollectionUserIds" resultType="java.lang.Integer">
        SELECT ch999_id FROM subCollection WITH(NOLOCK) WHERE kind=2  AND sub_id=#{subId}
    </select>

</mapper>
