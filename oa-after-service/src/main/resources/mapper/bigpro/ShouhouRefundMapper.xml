<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouRefundMapper">
    <insert id="insertTuihuan" keyColumn="id" useGeneratedKeys="true" keyProperty="tuihuanForm.tuihuanId">
        insert into shouhou_tuihuan(shouhou_id,tuihuan_kind,inuser,areaid,comment,tui_way,bankname,bankfuming,banknumber,tuikuanM,tuikuanM1
        ,zhejiaM,sub_id,basket_ids,inprice,coinM,smallproid,baitiaoM,kuBaiTiaoM,isValidt,dtime)
        select #{tuihuanForm.shouhouId},#{tuihuanForm.tuihuanKind},#{oaUser.userName},#{oaUser.areaId},#{tuihuanForm.comment},#{tuihuanForm.refundWay}
             ,#{tuihuanForm.bankName},#{tuihuanForm.bankFuming},#{tuihuanForm.bankNumber},#{tuihuanForm.refundPrice},#{tuihuanForm.refundPrice},
            0.00,null,null,#{tuihuanForm.refundPrice},0.00,#{tuihuanForm.shouhouId},null,0,0,getdate()
        where not exists(SELECT 1 FROM shouhou_tuihuan st where isnull(st.isdel,0) = 0 and st.tuihuan_kind = #{tuihuanForm.tuihuanKind}
                                                            and st.shouhou_id = #{tuihuanForm.shouhouId} and st.check3 is null);
    </insert>
    <insert id="insertRefundUserInfo" useGeneratedKeys="true" keyColumn="id" keyProperty="userInfo.id">
        INSERT INTO dbo.shouhou_tuihuan_user_info
        (open_type, tuihuan_id, tuihuan_id_type,user_id, user_avatar, nick_name, create_time, create_user, update_time, is_del)
        select #{userInfo.type}, #{tuihuanId}, #{idType},#{userInfo.userId}, #{userInfo.userAvatar}, #{userInfo.nickName},getdate()
        <choose>
            <when test="oaUser == null">,'系统'</when>
            <otherwise>,#{oaUser.userName}</otherwise>
        </choose>
        , getdate(), 0
        where not exists(select 1 from dbo.shouhou_tuihuan_user_info where isnull(is_del,0) = 0 and  tuihuan_id = #{tuihuanId} and tuihuan_id_type = #{idType})
    </insert>
    <insert id="insertNetPayRefundInfo">
        insert into dbo.netPayRefundInfo(netRecordId ,price ,inuser ,dtime ,returnid )
        select #{payRecord.id},#{payRecord.money},#{oaUser.userName},getdate(),#{tuihuanId}
        where exists(select 1 from dbo.netpay_record nr where nr.type=#{payType} and nr.id=#{payRecord.id} and nr.money-isnull(nr.refundPrice,0.00)>=#{payRecord.money})
    </insert>
    <update id="updateCheck">
        update shouhou_tuihuan
        <set>
            <choose>
                <!--审核1-->
                <when test="processStatus == 2">check1 = 1,check1dtime = getdate(),check1user = #{oaUser.userName},</when>
                <!--审核2-->
                <when test="processStatus == 3">check2 = 1,check2dtime = getdate(),check2user = #{oaUser.userName},</when>
            </choose>
        </set>
        <where>
           and isnull(isdel,0)=0 and id = #{id}
            <choose>
                <!--审核1-->
                <when test="processStatus == 2">and check1 is null</when>
                <!--审核2-->
                <when test="processStatus == 3">and check2 is null</when>
            </choose>
        </where>
    </update>
    <update id="cancelNetpayRecord">
        update netpay_record set batch_no=null where batch_no is not null and len(batch_no)>14 and right(batch_no,len(batch_no)-14)=#{tuihuanId,jdbcType=VARCHAR}
    </update>
    <update id="cancelRefund">
        update shouhou_tuihuan set isdel=1,delUser=#{oaUser.userName},delTime=getdate()
        where isnull(isdel,0)=0 and check3 is null and id = #{id}
    </update>
    <delete id="deleteReturnDetails">
        delete from ReturnsDetail  where SHTHID= #{tuihuanId}
    </delete>
    <delete id="deleteNetPayRefundById">
        delete from dbo.netPayRefundInfo where id = #{refundId} and netRecordId = #{netRecordId}
    </delete>
    <update id="cancelNetPayRefundInfo">
        update n set n.refundPrice= case when n.refundPrice-f.price>0 then n.refundPrice-f.price else 0 end
            from dbo.netpay_record n
                join (select f.netRecordId,f.returnid,sum(f.price) price from dbo.netPayRefundInfo f
                        where f.returnid = #{tuihuanId} group by f.netRecordId,f.returnid) f on f.netRecordId = n.id
            where f.returnid = #{tuihuanId}
    </update>
    <update id="updateNetPayRefundPrice">
        update dbo.netpay_record set refundPrice=isnull(refundPrice,0)+#{money} where id=#{id} and trade_no=#{tradeNo} and money>=isnull(refundPrice,0)+#{money}
            and isnull(refundPrice,0)+#{money}>0
    </update>
    <update id="updateNetPayRefundMoney">
        update dbo.netPayRefundInfo set price = #{newMoney}
        where id = #{id} and price = #{oldMoney}
    </update>
    <select id="listPayRecord"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo$PayRecordVo">
        select nr.id,nr.money-isnull(nr.refundPrice,0.00) money,money maxMoney,nr.trade_no,nr.sub_number from dbo.netpay_record nr with(nolock)
        where nr.type= #{type} and cast(nr.dtime as date)>='2018-03-20' and  nr.sub_number = #{shouhouId}
         and payWay
          <choose>
              <when test="refundWay == '微信'"> in ('微信','微信APP')</when>
              <when test="refundWay == '兴业银行扫码'">in ('扫码枪','兴业银行扫码')</when>
              <otherwise> = #{refundWay}</otherwise>
          </choose>
          and nr.money-isnull(nr.refundPrice,0)>0
    </select>
    <select id="getLastNotCompleteRefund" resultType="com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo">
        SELECT top 1 id,shouhou_id,tuihuan_kind,inuser,areaid,comment,tui_way,bankname,bankfuming,banknumber,tuikuanM,tuikuanM1
                   ,zhejiaM,sub_id,basket_ids,inprice,coinM,smallproid,baitiaoM,kuBaiTiaoM,isValidt,check1,check1dtime,check1user
                   ,check2,check2dtime,check2user,check3,check3dtime,check3user,isdel,dtime,payOpenId
        FROM  shouhou_tuihuan st with(nolock) where isnull(st.isdel,0) = 0 and st.tuihuan_kind = #{tuihuanKind} and st.shouhou_id = #{shouhouId} and st.check3 is null order by st.id DESC
    </select>
    <select id="getShouHouMobile" resultType="java.lang.String">
        select mobile from shouhou with(nolock) where xianshi = 1 and id = #{shouhouId}
    </select>
    <select id="listSimpleHistory" resultType="com.jiuji.oa.afterservice.bigpro.vo.refund.TuihuanHistoryVo$TuihuanSimpleHistoryVo">
        select id,tuikuanM,dtime from shouhou_tuihuan st with(nolock)
        where isnull(st.isdel,0) = 0 and st.check3 is not null and st.tuihuan_kind = #{tuihuanKind}
            <if test="tuiKinds != null">
                and st.tui_kinds = #{tuiKinds}
            </if>
          <include refid="com.jiuji.oa.afterservice.refund.dao.ShouhouRefundMoneyMapper.andTuihuanMatchSubIdSql"></include>
        order by id desc
    </select>
    <select id="getHistory" resultType="com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo">
        select id,shouhou_id,tuihuan_kind,inuser,areaid,comment,tui_way,bankname,bankfuming,banknumber,tuikuanM,tuikuanM1
                 ,zhejiaM,sub_id,basket_ids,inprice,coinM,smallproid,baitiaoM,kuBaiTiaoM,isValidt,check1,check1dtime,check1user
                 ,check2,check2dtime,check2user,check3,check3dtime,check3user,isdel,dtime,payOpenId,pzid
        from shouhou_tuihuan st with(nolock)
        where isnull(st.isdel,0) = 0 and id = #{id}
    </select>
    <select id="getRefundUserInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.OpenIdInfoBo">
        select top 1 stui.id,open_type type,user_id userId,stui.user_avatar userAvatar,stui.nick_name nickName
        from shouhou_tuihuan_user_info stui with(nolock)
        where isnull(stui.is_del,0)=0 and stui.tuihuan_id_type = #{idType} and stui.tuihuan_id = #{tuihuanId}
        order by stui.id desc
    </select>
    <select id="listPayRecordByTuihuanId"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo$PayRecordVo">
        select nr.id,npr.price money,nr.id,nr.money-isnull(nr.refundPrice,0.00) maxMoney,nr.trade_no,nr.sub_number,npr.id refundId
        from dbo.netpay_record nr with(nolock)
        inner join dbo.netPayRefundInfo npr with(nolock) on npr.netRecordId = nr.id
        where npr.returnid = #{tuihuanId}
    </select>
    <select id="remainingCashMoney" resultType="java.math.BigDecimal">
        SELECT case when isnull(sum(s.sub_pay01),0) - isnull(sum(st.tuikuanM),0) &lt; 0 then  0 else isnull(sum(s.sub_pay01),0) - isnull(sum(st.tuikuanM),0) end money
        from shouying s with(nolock)
        left join  shouhou_tuihuan st with(nolock) on st.shouhou_id=s.sub_id and st.tuihuan_kind in(5,11)
                                                          and st.tui_way = '现金' and st.check3 = 1 and isnull(st.isdel,0) = 0
        where s.sub_id = #{shouhouId} and s.shouying_type in('售后小件','售后','订金3')
    </select>
</mapper>
