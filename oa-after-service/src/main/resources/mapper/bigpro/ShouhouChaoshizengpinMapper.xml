<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouChaoshizengpinMapper">

    <resultMap type="com.jiuji.oa.afterservice.bigpro.po.ShouhouChaoshizengpin" id="shouhouChaoshizengpinMap">
        <result property="id" column="id"/>
        <result property="ppriceid" column="ppriceid"/>
        <result property="shouhouid" column="shouhouid"/>
        <result property="inuser" column="inuser"/>
        <result property="dtime" column="dtime"/>
        <result property="zcount" column="zCount"/>
        <result property="costprice" column="costprice"/>
        <result property="type" column="_type"/>
    </resultMap>

    <select id="judgeZengpinValue" resultType="java.lang.String">
        select top 1 product_name + ' ' + ISNULL(product_color,'') AS pname from productinfo p WITH(NOLOCK)
        where p.ppriceid = #{ppriceid}
        <choose>
            <when test="csDays != null">
                and p.memberprice &lt;=(select top 1 isnull(pmoney,99999)
                from shouhou_chaoshiPeifu WITH(NOLOCK)
                where startDay &lt; #{csDays} and #{csDays} &lt;= endDay and ptype = 2 order by pmoney desc)
            </when>
            <otherwise>
                and p.memberprice &lt;= 199
            </otherwise>
        </choose>

    </select>

    <select id="judgeZengpinValue4Jiuji" resultType="java.lang.String">
        select top 1 product_name + ' ' + ISNULL(product_color,'') AS pname from productinfo p WITH(NOLOCK)
        where p.ppriceid = #{ppriceid} and p.memberprice &lt;= 200

    </select>

    <select id="getZengpinName" resultType="java.lang.String">
        select  product_name + ' ' + ISNULL(product_color,'')  AS pname  from productinfo p WITH(NOLOCK)
        where p.ppriceid = #{ppriceid}
    </select>
    <select id="listGift" resultType="java.lang.Integer">
        select top 1 id  from shouhou_chaoshizengpin WITH(NOLOCK) where shouhouid = #{shouhouid} and isnull(_type,0) =#{type};
    </select>
</mapper>