<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.DeleteDataRecordMapper">
    <!-- 批量插入 -->
    <insert id="batchInsertRecord" useGeneratedKeys="true" keyProperty="id" parameterType="java.util.List">
        INSERT INTO delete_data_record (table_name, delete_time, primary_key_id)
        VALUES
        <foreach collection="deleteDataRecords" item="item" index="index" separator=",">
            (#{item.tableName}, #{item.deleteTime}, #{item.primaryKeyId})
        </foreach>
    </insert>

</mapper>
