<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ProductinfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.Productinfo">
        <result column="productid" property="productid1"/>
        <result column="product_id" property="productId"/>
        <result column="ppriceid" property="ppriceid"/>
        <result column="product_name" property="productName"/>
        <result column="product_color" property="productColor"/>
        <result column="costprice" property="costprice"/>
        <result column="vipPrice" property="vipPrice"/>
        <result column="cid" property="cid"/>
        <result column="ismobile1" property="ismobile1"/>
        <result column="bpic" property="bpic"/>
        <result column="memberprice" property="memberprice"/>
        <result column="pricefd" property="pricefd"/>
        <result column="que" property="que"/>
        <result column="display" property="display"/>
        <result column="isdel" property="isdel"/>
        <result column="viewsWeek" property="viewsWeek"/>
        <result column="ppriceid1" property="ppriceid1"/>
        <result column="config" property="config"/>
        <result column="brandID" property="brandID"/>
        <result column="cidFamily" property="cidFamily"/>
        <result column="viewsweekr" property="viewsweekr"/>
        <result column="rank1" property="rank1"/>
        <result column="noPromotion" property="noPromotion"/>
        <result column="pLabel" property="pLabel"/>
        <result column="OEMPrice" property="OEMPrice"/>
        <result column="barCode" property="barCode"/>
        <result column="vip2price" property="vip2price"/>
        <result column="isbarCode" property="isbarCode"/>
        <result column="Scarcity" property="Scarcity"/>
        <result column="isSn" property="isSn"/>
        <result column="supportService" property="supportService"/>
    </resultMap>
    <select id="getProductListByPpids" resultMap="BaseResultMap">
        select * from productinfo with(nolock) where 1=1
        <if test="ppids != null and ppids.size > 0">
            and ppriceid in
            <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
                #{ppid}
            </foreach>
        </if>

    </select>

    <select id="searchProduct" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ProductSimpleRes">
        <choose>
            <when test="pid != null and pid > 0">
                SELECT  productid pid,ppriceid ppid, product_name productName,product_color productColor
                FROM dbo.productinfo with(nolock)
                WHERE productid=#{pid}
                <if test="isSmallProduct != null and isSmallProduct == true">
                    AND ISNULL(ismobile1,0)=0
                </if>
                <if test="isSmallProduct == null or isSmallProduct == false">
                    AND ISNULL(ismobile1,0)=1 AND product_color IS NOT NULL
                </if>
                ORDER BY rank1 ASC
                OFFSET 0 ROWS FETCH NEXT #{limit} ROWS ONLY
            </when>
            <otherwise>
                SELECT  id as pid,name as productName,'' as productColor,0 AS ppid FROM dbo.product with(nolock)
                WHERE 1=1
                <if test="isSmallProduct != null and isSmallProduct == true">
                    AND ISNULL(ismobile,0)=0
                </if>
                <if test="isSmallProduct == null or isSmallProduct == false">
                    AND ISNULL(ismobile,0)=1
                </if>
                <if test="world != null and world != ''">
                    AND name LIKE CONCAT('%',#{world},'%')
                </if>
                ORDER BY viewsWeek DESC
                OFFSET 0 ROWS FETCH NEXT #{limit} ROWS ONLY
            </otherwise>
        </choose>
    </select>

    <select id="searchProductKC" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ProductSimpleKcRes">
        SELECT TOP
        30 p.product_name as productName,
        p.pLabel,
        isnull( p.product_color, '' ) AS productColor,
        p.ppriceid as ppid,
        p.product_id as pid,
        p.memberprice as price,
        p.memberprice as memberPrice,
        isnull( k.lcount, 0 ) AS lcount,
        isnull( k.inprice, p.costprice ) AS inprice,
        p.cid
        FROM
        productinfo p WITH (nolock)
        LEFT JOIN (SELECT ppriceid, inprice, leftCount AS lcount FROM product_kc WITH (nolock)
        WHERE 1=1
        <if test="areaId != null and areaId != 0">
            and areaid = #{areaId}
        </if>
        ) k ON k.ppriceid= p.ppriceid1
        WHERE
        ISNULL(p.que,0)!= 2
        <if test="xtenant != null and xtenant == 0">
            AND p.ppriceid NOT IN ( 81683, 81682 )
        </if>
        <choose>
            <when test="ppid == null or ppid == 0">
                <if test="isWordPpid == null or  isWordPpid != 1">
                    AND product_name like CONCAT('%',#{word},'%')
                </if>
                <if test="isWordPpid != null and  isWordPpid == 1">
                    AND (product_name like CONCAT('%',#{word},'%') or p.ppriceid=#{word})
                </if>
                and (exists( select 1 from f_category_children(23) f where f.id=p.cid ))
            </when>
            <otherwise>
                AND p.ppriceid=#{ppid}
            </otherwise>
        </choose>
        ORDER BY
        lcount DESC
    </select>

    <select id="searchGiveProductKC" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ProductSimpleKcRes">
        SELECT TOP 30
        p.product_name as productName,
        p.pLabel,
        isnull( p.product_color, '' ) AS productColor,
        p.ppriceid as ppid,
        p.product_id as pid,
        p.memberprice as price,
        p.memberprice as memberPrice,
        isnull( k.lcount, 0 ) AS lcount,
        isnull( k.inprice, p.costprice ) AS inprice,
        p.cid,
        p.ismobile1,
        ISNULL(p.que,0) as que
        FROM
        productinfo p WITH (nolock)
        LEFT JOIN (SELECT ppriceid, inprice, leftCount AS lcount FROM product_kc WITH (nolock)
         where 1=1
        <if test="areaId != null and areaId != 0">
            and areaid = #{areaId}
        </if>
        ) k ON k.ppriceid= p.ppriceid1

        <where>
            1=1

            <choose>
                <when test="ppid == null or ppid == 0">
                    <!-- 排除维修配件-->
                    and ISNULL(p.que,0)!= 2 and p.ismobile1 = 0 and isnull( k.lcount, 0 ) <![CDATA[ > ]]> 0
                    and not exists ( select 1 from f_category_children(23) f where f.id=p.cid )
                    and p.memberprice <![CDATA[ <= ]]> 200 and isnull( k.inprice, p.costprice ) <![CDATA[ < ]]> 20

                    <if test="word != null and word != ''">
                        AND product_name like CONCAT('%',#{word},'%')
                    </if>

                </when>

                <otherwise>
                    AND p.ppriceid=#{ppid}
                </otherwise>
            </choose>

        </where>

        order by lcount DESC
    </select>

    <select id="getConfigByImei" resultType="java.lang.String">
        select config from productinfo with(nolock) where ppriceid =( select top 1 ppriceid from product_mkc with(nolock) where imei=#{imei})
    </select>

    <select id="getCidsByPpid" resultType="java.lang.String">
        select cidFamily from productinfo with(nolock) where ppriceid in
        <foreach collection="ppidList" item="ppid" open="(" close=")" separator=",">
            #{ppid}
        </foreach>
    </select>

    <select id="getProductByBarCode" resultMap="BaseResultMap">
        select top 1 * from productinfo p with(nolock) where 1=1 and p.barCode is not null and p.barCode LIKE CONCAT ( '%',#{barCode}, '%' )
    </select>
    <select id="isBargainProduct" resultType="java.lang.Boolean">
        select iif(exists(select 1 from productprice pp with(nolock) where isnull(pp.isdel,0)=0 and pp.noPromotion = 1 and pp.ppriceid = #{ppriceid}),1,0)
    </select>

    <select id="getProductwithBrandListByPpids"
            resultType="com.jiuji.oa.afterservice.bigpro.po.ProductinfowithBrand">
        select b.id brandId,b.name brandName,p.ppriceid brandId,p.product_name productName FROM productinfo p WITH(NOLOCK)
        LEFT JOIN brand b with(nolock) on p.brandID = b.id
        where p.ppriceid in
        <if test="ppids != null and ppids.size > 0">
            <foreach collection="ppids" index="index" item="ppid" open="(" separator="," close=")">
                #{ppid}
            </foreach>
        </if>
    </select>
    <select id="getProductIdByProductName" resultType="java.lang.Long">
        SELECT TOP 1 id from product p where p.name = #{name} order by p.id desc
    </select>
</mapper>
