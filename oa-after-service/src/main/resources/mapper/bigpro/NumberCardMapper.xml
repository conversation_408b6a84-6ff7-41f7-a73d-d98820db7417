<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.NumberCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.NumberCard">
        <id column="ID" property="id" />
        <result column="GName" property="GName" />
        <result column="Total" property="Total" />
        <result column="CardID" property="CardID" />
        <result column="State" property="State" />
        <result column="Isdel" property="Isdel" />
        <result column="StartTime" property="StartTime" />
        <result column="EndTime" property="EndTime" />
        <result column="AddTime" property="AddTime" />
        <result column="Input" property="Input" />
        <result column="limit" property="limit" />
        <result column="userid" property="userid" />
        <result column="use_count" property="useCount" />
        <result column="limit1" property="limit1" />
        <result column="limit2" property="limit2" />
        <result column="limitprice" property="limitprice" />
        <result column="limitType" property="limitType" />
        <result column="limitids" property="limitids" />
        <result column="areas" property="areas" />
        <result column="takeMethod" property="takeMethod" />
        <result column="limitWay" property="limitWay" />
        <result column="limintClint" property="limintClint" />
        <result column="ch999_id" property="ch999Id" />
        <result column="binduid" property="binduid" />
        <result column="noticetime" property="noticetime" />
        <result column="isdjq" property="isdjq" />
        <result column="authorizeid" property="authorizeid" />
        <result column="other_id" property="otherId" />
        <result column="areaids" property="areaids" />
        <result column="seller" property="seller" />
        <result column="isNoProfit" property="isNoProfit" />
        <result column="intime" property="intime" />
        <result column="lastUpdateTime" property="lastUpdateTime" />
        <result column="lastUpdateUser" property="lastUpdateUser" />
    </resultMap>

    <select id="getUserBindCoupon" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.CouponRes">
            SELECT
            GName as gName,
            Total as total,
            limitprice as limitPrice,
            CardID as cardId,
            StartTime as startTime,
            EndTime as endTime,
            isnull( limit, 0 ) AS limit,
            isnull( limit1, 0 ) AS limit1,
            isnull( limit2, 0 ) AS limit2
        FROM
            NumberCard with(nolock)
        WHERE 1=1
            <if test="userId != null and userId != 0">
               and userid =#{userId}
            </if>
            AND ( State = 0 OR limit = 2 )
            AND Isdel = 0
            AND GETDATE() BETWEEN starttime
            AND endtime
            AND limittype = 1
            AND limitids = '23'
        ORDER BY
            EndTime DESC,
            StartTime ASC
    </select>

    <select id="getOldLimitIds" resultType="java.lang.Integer">
     <if test="limitType != null and limitIds != null">
         <if test="limitType == 1">
             select ID from dbo.category with(nolock) where id in
         </if>
         <if test="limitType == 2"></if>
         select ID from dbo.brand with(nolock) where id in
         <if test="limitType == 3">
             select ID from dbo.product with(nolock) where id in
         </if>
         <if test="limitType == 4">
             select ppriceid ID from dbo.productprice with(nolock) where ppriceid in
         </if>
         <foreach collection="limitIds" item="limitId" index="index" open="(" close=")" separator=",">
             #{limitId}
         </foreach>
     </if>
    </select>

    <update id="updateNumberCardUseCount">
        update NumberCard set use_count=isnull(use_count,0)+1,State=1 where cardid=#{cardId}
    </update>



    <select id="getShouhouCardData" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.NumberCardRes" parameterType="map">
            CREATE TABLE #TempNumberCard (
                GName NVARCHAR(100),
                Total DECIMAL(18, 2),
                limitprice DECIMAL(18,2),
                CardID NVARCHAR(12),
                StartTime DATE,
                EndTime DATE,
                limit TINYINT,
                limit1 TINYINT,
                limit2 BIT,
                limitids NVARCHAR(2000),
                ch999_id INT,
                AddTime DATE,
                limitType TINYINT,
                excludePpIds NVARCHAR(4000),
                userid INT,
                team_id INT,
                State BIT,
                Isdel BIT,
                rulecode NVARCHAR(200)
            );
            INSERT INTO #TempNumberCard (
            GName, Total, limitprice, CardID, StartTime, EndTime,
            limit, limit1, limit2, limitids, ch999_id, AddTime,
            limitType, excludePpIds, userid, team_id, State, Isdel,rulecode
            )
            VALUES
            <foreach collection="tempCards" item="item" separator=",">
                (
                #{item.gName}, #{item.total}, #{item.limitPrice}, #{item.cardID},
                #{item.startTime}, #{item.endTime}, #{item.limit}, #{item.limit1}, #{item.limit2},
                #{item.limitIds}, #{item.ch999Id}, #{item.addTime}, #{item.limitType},
                #{item.excludePpIds}, #{item.userId}, #{item.teamId}, #{item.state}, #{item.isDel},#{item.rulecode}
                )
            </foreach>;
        seLect distinct n.GName,n.Total,n.limitprice,n.CardID,n.StartTime,n.EndTime,isnull(n.limit,0) as limit,
        isnull(n.limit1,0) as limit1,isnull(n.limit2,0) as limit2,n.limitids,n.ch999_id,n.AddTime,n.limittype as limitType,
        n.excludePpIds,n.rulecode
        from shouhou  sh with(nolock)
        inner join #TempNumberCard n  with(nolock)  on sh.userid=#{userId}
        and (n.userid = #{userId} or n.team_id in(select team_id from BBSXP_Users bu with(nolock) where bu.id = #{userId} and bu.team_id>0))
        inner join wxkcoutput wk with(nolock)  on wk.wxid=sh.id
        left join dbo.productinfo pi with(nolock ) on pi.ppriceid=wk.ppriceid
        left join category c  with(nolock) on exists(select 1 from productinfo p with(nolock) where p.ppriceid=wk.ppriceid and c.id=p.cid)
            <include refid="getKxYouhuimasWhere"/>

    DROP TABLE #TempNumberCard;

    </select>



    <select id="getKxYouhuimas" resultType="com.jiuji.oa.afterservice.bigpro.vo.res.NumberCardRes">
         seLect distinct n.GName,n.Total,n.limitprice,n.CardID,n.StartTime,n.EndTime,isnull(n.limit,0) as limit,
                        isnull(n.limit1,0) as limit1,isnull(n.limit2,0) as limit2,n.limitids,n.ch999_id,n.AddTime,n.limittype as limitType,
                        n.excludePpIds
        from shouhou  sh with(nolock)
        inner join NumberCard n  with(nolock)  on sh.userid=#{userId}
                  and (n.userid = #{userId} or n.team_id in(select team_id from BBSXP_Users bu with(nolock) where bu.id = #{userId} and bu.team_id>0))
        inner join wxkcoutput wk with(nolock)  on wk.wxid=sh.id
        left join dbo.productinfo pi with(nolock ) on pi.ppriceid=wk.ppriceid
        left join category c  with(nolock) on exists(select 1 from productinfo p with(nolock) where p.ppriceid=wk.ppriceid and c.id=p.cid)
        <include refid="getKxYouhuimasWhere"/>
    </select>

    <sql id="getKxYouhuimasWhere">
        <where>
            sh.id= #{shouhouId}
            and n.Isdel=0 and isnull(sh.isquji,0) != 1
            <if test="isSoft != null and isSoft == true">
                and n.ch999_id=-39 and isnull(sh.issoft,0)=1
            </if>
            and (n.State=0 or n.limit=2)
            <if test="isZiXiuManualByAll == false">
                AND ((n.limittype = 1 AND exists(select 1 from string_split(n.limitids,',') lids
                inner join string_split(c.[Path],',') cp on cast(cp.value as int) = cast(lids.value as int)
                and cast(cp.value as int) > 0 or c.id = cast(lids.value as int)) )
                or(
                n.limittype = 3
                and exists(
                select 1
                from string_split(n.limitids,
                ',') lids
                inner join
                string_split(c.[Path], ',') cp
                on cast(cp.value as int) = cast(lids.value as int)
                and cast(cp.value as int) > 0
                or pi.product_id = cast(lids.value as int)
                )
                )
                or (n.limittype = 4 and  CHARINDEX(cast(wk.ppriceid as varchar(50)),n.limitids) >0)
                or isnull(n.limittype,0) = 0
                OR n.ch999_id  = -20 )
            </if>
            <if test="isZiXiuManualByAll == true ">
                AND ((n.limittype = 4 and  CHARINDEX(cast(wk.ppriceid as varchar(50)),n.limitids) >0)
                or isnull(n.limittype,0) = 0
                OR n.ch999_id  = -20 )
            </if>
            and n.EndTime &gt;= GETDATE()
            and n.limitprice &lt;= (SELECT sum(price) from wxkcoutput with(nolock) where wxid = #{shouhouId} and stats != 3
            <if test="isJiujiXtenant == false ">
                and isnull(ppriceid,0) != 0
            </if>
            )
            order by n.EndTime desc,n.StartTime asc
        </where>
    </sql>

    <select id="getNumberCardByCardId" resultType="com.jiuji.oa.afterservice.bigpro.po.NumberCard">
        select * from NumberCard with(nolock) where CardID = #{cardId} and isnull(isdel,0) = 0
    </select>

    <select id="validateYouHuiMaSatisfyUseCondition" resultType="java.lang.Integer">
        select distinct w.id from wxkcoutput w with(nolock)
        left join productinfo p WITH(nolock) on w.ppriceid = p.ppriceid
        where w.wxid = #{shouhouId} and p.cidFamily like '%23%' and w.ppriceid in
        <foreach collection="limitIds" item="ppid" open="(" close=")" separator=",">
            #{ppid}
        </foreach>
    </select>

    <select id="getYxNumberCardByCardIdList" resultType="java.lang.String">
        SELECT DISTINCT nc.CardID from NumberCard nc WITH(nolock)
        WHERE nc.CardID IN
        <foreach collection="cardIds" item="cardId" open="(" close=")" separator=",">
            #{cardId}
        </foreach>
    </select>

</mapper>
