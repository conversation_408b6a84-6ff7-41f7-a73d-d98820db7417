<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.ShouhouTestAttrMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.ShouhouTestAttr">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="attrName" column="attr_name" jdbcType="VARCHAR"/>
            <result property="attrType" column="attr_type" jdbcType="VARCHAR"/>
            <result property="isRequired" column="is_required" jdbcType="TINYINT"/>
            <result property="attrGroupId" column="attr_group_id" jdbcType="INTEGER"/>
            <result property="sort" column="sort" jdbcType="INTEGER"/>
            <result property="remark" column="remark" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="isDelete" column="is_delete" jdbcType="TINYINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,attr_name,attr_type,
        is_required,attr_group_id,sort,
        remark,create_time,update_time,
        is_delete,shouhou_test_attr_rv
    </sql>

    <resultMap id="shouhouTestAttrResultMap" type="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestAttrRes">
        <result property="attrId" column="id"/>
        <result property="attrName" column="attrName"/>
        <result property="attrType" column="attrType"/>
        <result property="isRequired" column="isRequired"/>
        <result property="attrGroupId" column="attrGroupId"/>
        <result property="sort" column="sort"/>
        <collection property="attrItemList" ofType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestAttrItemRes">
            <result property="attrItemLable" column="itemLable" />
            <result property="attrItemValue" column="itemValue" />
        </collection>
    </resultMap>
    
    <select id="getTestAttrListByGroupId"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouTestAttrRes">
        select
            sta.id as attrId,
            sta.attr_name as attrName,
            sta.attr_type as attrType,
            sta.is_required as isRequired,
            sta.attr_group_id as attrGroupId,
            sta.sort as sort,
            stai.item_lable as itemLable,
            stai.item_value as itemValue
        from
            shouhou_test_attr sta with(nolock)
        left join shouhou_test_attr_item stai with(nolock) on
            sta.id = stai.attr_id and stai.is_delete = 0
        where sta.is_delete = 0
        and sta.attr_group_id = #{attrGroupId}
        order by sta.sort
    </select>
</mapper>
