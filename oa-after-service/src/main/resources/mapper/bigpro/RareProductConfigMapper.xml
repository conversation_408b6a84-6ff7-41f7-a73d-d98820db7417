<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.bigpro.dao.RareProductConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.bigpro.po.RareProductConfig">
        <id column="id" property="id" />
        <result column="productid" property="productid" />
        <result column="isnotice" property="isnotice" />
        <result column="inuser" property="inuser" />
        <result column="intime" property="intime" />
        <result column="limitArea" property="limitArea" />
        <result column="buyLimit" property="buyLimit" />
    </resultMap>


    <select id="getRareProductInfo" resultType="com.jiuji.oa.afterservice.bigpro.bo.rare.RareProductInfo">
        select r.*,p.product_name,p.product_color from dbo.rareProductConfig r with(nolock)
        left join dbo.productinfo p with(nolock) on r.productid = p.ppriceid
        where 1 = 1 and isnull(r.buyLimit,0)=0
    </select>

</mapper>
