<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.wuliu.dao.ShunfengNoInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.wuliu.po.ShunfengNoInfo">
        <id column="id" property="id" />
        <result column="mailno" property="mailno" />
        <result column="wuliuid" property="wuliuid" />
        <result column="destRouteLabel" property="destRouteLabel" />
        <result column="twoDimensionCode" property="twoDimensionCode" />
        <result column="sareaid" property="sareaid" />
        <result column="custid" property="custid" />
        <result column="adddate" property="adddate" />
        <result column="j_mobile" property="jMobile" />
        <result column="d_mobile" property="dMobile" />
        <result column="sign_time" property="signTime" />
        <result column="ckind" property="ckind" />
    </resultMap>

    <select id="getShunfengOrderInfoBySfNo" resultType="com.jiuji.oa.afterservice.bigpro.bo.wuliu.ShunfengOrderInfoBo">
        SELECT top 1 id,sareaid as sAreaId,custid as custId,adddate as addDate,j_mobile as jMobile,d_mobile as dMobile
        FROM dbo.shunfengNoInfo WITH(NOLOCK) WHERE mailno=#{sfNo}
    </select>
</mapper>
