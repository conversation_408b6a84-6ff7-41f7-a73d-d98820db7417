<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.historicalrefund.mapper.HistoricalRefundMapper">


    <select id="selectRefundInfoByMember" resultType="com.jiuji.oa.afterservice.historicalrefund.po.SaleRefundPo">
        SELECT TOP 10 *
        FROM (
                 SELECT a.*
                 FROM dbo.shouhou_tuihuan a with (nolock)
                 WHERE a.tuihuan_kind IN (1, 2, 3, 4, 5, 11)
                   and a.tuikuanM > 0
                   AND a.id &lt;&gt; #{userIdAndMobile.cannotContainId}
                   AND ISNULL(a.isdel, 0) = 0
                   AND EXISTS (
                         SELECT 1
                         FROM dbo.shouhou h with (nolock)
                         WHERE a.shouhou_id = h.id
                           and (h.userid=#{userIdAndMobile.userId} or h.mobile=#{userIdAndMobile.mobile})
                     )
                 UNION ALL
                 SELECT a.*
                 FROM dbo.shouhou_tuihuan a with (nolock)
                 WHERE a.tuihuan_kind IN (7, 9, 10)
                   and a.tuikuanM > 0
                   AND a.id &lt;&gt; #{userIdAndMobile.cannotContainId}
                   AND ISNULL(a.isdel, 0) = 0
                   AND EXISTS (
                         SELECT 1
                         FROM dbo.Smallpro small with (nolock)
                         WHERE a.smallproid = small.id
                           and (small.userid=#{userIdAndMobile.userId} or small.mobile=#{userIdAndMobile.mobile})
                     )
                 UNION ALL
                 SELECT a.*
                 FROM dbo.shouhou_tuihuan a with (nolock)
                 WHERE a.tuihuan_kind IN (6)
                   and a.tuikuanM > 0
                   AND a.id &lt;&gt; #{userIdAndMobile.cannotContainId}
                   AND ISNULL(a.isdel, 0) = 0
                   AND EXISTS (
                         SELECT 1
                         FROM dbo.sub s with (nolock)
                         WHERE a.sub_id = s.sub_id
                           and (s.userid=#{userIdAndMobile.userId} or s.sub_mobile=#{userIdAndMobile.mobile})
                     )
                 UNION ALL
                 SELECT a.*
                 FROM dbo.shouhou_tuihuan a with (nolock)
                 WHERE a.tuihuan_kind IN (8)
                   and a.tuikuanM > 0
                   AND a.id &lt;&gt; #{userIdAndMobile.cannotContainId}
                   AND ISNULL(a.isdel, 0) = 0
                   AND EXISTS (
                         SELECT 1
                         FROM dbo.recover_marketInfo r with (nolock)
                         WHERE a.sub_id = r.sub_id
                           and (r.userid=#{userIdAndMobile.userId} or r.sub_mobile=#{userIdAndMobile.mobile})
                     )
             ) t1
        ORDER BY t1.dtime DESC;
    </select>
<!--    <select id="selectRefundInfoByMember" resultType="com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo">-->
<!--        select top 10 * from (-->
<!--            select  a.*-->
<!--            from dbo.shouhou_tuihuan a with (nolock)-->
<!--            left join dbo.shouhou h with (nolock) on a.shouhou_id = h.id-->
<!--            where a.tuihuan_kind in (1, 2, 3, 4, 5, 11) and (h.userid=#{userIdAndMobile.userId} or h.mobile=#{userIdAndMobile.mobile}) and isnull(a.isdel,0)=0-->
<!--            union-->
<!--            select  a.*-->
<!--            from dbo.shouhou_tuihuan a with (nolock)-->
<!--             left join dbo.Smallpro small with (nolock) on a.smallproid = small.id-->
<!--            where a.tuihuan_kind in (7,9,10) and (small.userid=#{userIdAndMobile.userId} or small.mobile=#{userIdAndMobile.mobile}) and isnull(a.isdel,0)=0-->
<!--            union-->
<!--            select a.* from dbo.shouhou_tuihuan a with (nolock)-->
<!--             left join dbo.sub s with (nolock) on a.sub_id = s.sub_id-->
<!--            where a.tuihuan_kind in (6) and (s.userid=#{userIdAndMobile.userId} or s.sub_mobile=#{userIdAndMobile.mobile}) and isnull(a.isdel,0)=0-->
<!--            union-->
<!--            select a.* from dbo.shouhou_tuihuan a with (nolock)-->
<!--             left join dbo.recover_marketInfo r with (nolock) on a.sub_id = r.sub_id-->
<!--            where a.tuihuan_kind in (8) and (r.userid=#{userIdAndMobile.userId} or r.sub_mobile=#{userIdAndMobile.mobile}) and isnull(a.isdel,0)=0-->
<!--        ) t1 order by t1.dtime desc-->
<!--    </select>-->

    <select id="selectShouhouTuiHuanPoById" resultType="com.jiuji.oa.afterservice.historicalrefund.po.SaleRefundPo">
        select * from dbo.shouhou_tuihuan with(nolock ) where isnull(isdel,0)=0  and id=#{id}

    </select>
    <select id="selectShouhouTuiHuanPo" resultType="com.jiuji.oa.afterservice.historicalrefund.po.SaleRefundPo">
        select top 10 * from dbo.shouhou_tuihuan with(nolock )
        where isnull(isdel,0)=0 and tuikuanM > 0
        <if test="selectCondition.id != null">
            and id &lt;&gt; #{selectCondition.id}
        </if>
        <if test="selectCondition.areaId != null">
            and areaid=#{selectCondition.areaId}
        </if>
        <if test="selectCondition.inuser != null">
            and inuser=#{selectCondition.inuser}
        </if>
        <if test="selectCondition.payOpenId != null">
            and payOpenId=#{selectCondition.payOpenId}
        </if>
        order by dtime desc
    </select>
    <select id="selectChaeInfo" resultType="com.jiuji.oa.afterservice.historicalrefund.po.ChaeInfoDetailsPo">
        select a.areaid, isnull(b.all_xianjing, 0) - a.areaPrice chae, a.dayTime time
        from (select areaid, sum(areaPrice) areaPrice, dayTime
        from dbo.bankPriceCheck with (nolock)
        where areaid = #{chaeInfoCondition.areaId}
        and dayTime between #{chaeInfoCondition.sTime} and #{chaeInfoCondition.eTime}
        and type_ = 1
        group by areaid, dayTime) a
        join (select sum(isnull(all_xianjing, 0)) all_xianjing, areaid, gdate
        from dbo.day_report with (nolock)
        where areaid = #{chaeInfoCondition.areaId}
        and gdate between dateadd(day, -1, #{chaeInfoCondition.sTime}) and dateadd(day, -1, #{chaeInfoCondition.eTime})
        group by areaid, gdate) b
        on (a.areaid = b.areaid and a.dayTime = cast(dateadd(day, 1, b.gdate) as date))
        where a.areaPrice &lt; b.all_xianjing and a.areaid = #{chaeInfoCondition.areaId}
        order by a.dayTime desc
     </select>
    <select id="selectRecoveryArea" resultType="com.jiuji.oa.afterservice.historicalrefund.po.RecoveryRefundPo">
        SELECT TOP 10 rs.areaid,rs.sub_id,sum(rb.price) amount,rs.payopenid as openid,rb2.inuser as payUser,rs.pay_time as pay_date,rs.sub_pay
        FROM recover_sub rs with(nolock)
        inner join recover_basket rb with(nolock) on rs.sub_id = rb.sub_id
            outer apply (select top 1 inuser from recover_basket rb1 with(nolock) where isnull(rb1.isdel,0) = 0 and rb1.sub_id = rs.sub_id order by rb1.price desc) rb2
        where isnull(rb.isdel,0) = 0 and rs.sub_check = 3 and rs.pay_time > GETDATE() - 90 and rs.areaid = #{selectCondition.areaId}
        GROUP by rs.areaid,rs.sub_id,rs.payopenid,rs.pay_time,rb2.inuser,rs.sub_pay
        order by rs.pay_time DESC
    </select>
    <select id="selectRecoveryInuser" resultType="com.jiuji.oa.afterservice.historicalrefund.po.RecoveryRefundPo">
        SELECT TOP 10 rs.areaid,rs.sub_id,sum(rb.price) amount,rs.payopenid as openid,rb2.inuser as payUser,rs.pay_time as pay_date,rs.sub_pay
        FROM recover_sub rs with(nolock)
            inner join recover_basket rb with(nolock) on rs.sub_id = rb.sub_id
            outer apply (select top 1 inuser from recover_basket rb1 with(nolock) where isnull(rb1.isdel,0) = 0 and rb1.sub_id = rs.sub_id order by rb1.price desc) rb2
        where isnull(rb.isdel,0) = 0 and rs.sub_check = 3 and rs.pay_time > GETDATE() - 90 and rb2.inuser =  #{selectCondition.inuser}
        GROUP by rs.areaid,rs.sub_id,rs.payopenid,rs.pay_time,rb2.inuser,rs.sub_pay
        order by rs.pay_time DESC

    </select>
    <select id="selectRecoveryMember" resultType="com.jiuji.oa.afterservice.historicalrefund.po.RecoveryRefundPo">
        SELECT top 10 t.areaid,t.sub_id,sum(rb.price) amount,t.payopenid  as openid,rb2.inuser  as payUser,t.pay_time as pay_date,t.sub_pay
        FROM
            (
                SELECT rs.areaid,rs.sub_id,rs.payopenid,rs.payUser,rs.pay_time,rs.sub_pay
                FROM recover_sub rs with(nolock)
                where rs.sub_check = 3 and rs.pay_time > GETDATE() - 90 and rs.userid = #{userIdAndMobile.userId}
                UNION
                SELECT rs.areaid,rs.sub_id,rs.payopenid,rs.payUser,rs.pay_time,rs.sub_pay
                FROM recover_sub rs with(nolock)
                where rs.sub_check = 3 and rs.pay_time > GETDATE() - 90 and rs.sub_tel = #{userIdAndMobile.mobile}
            ) t
                inner join recover_basket rb with(nolock) on t.sub_id = rb.sub_id
            outer apply (select top 1 inuser from recover_basket rb1 with(nolock) where isnull(rb1.isdel,0) = 0 and rb1.sub_id = t.sub_id order by rb1.price desc) rb2
        GROUP by t.areaid,t.sub_id,t.payopenid,t.pay_time,rb2.inuser,t.sub_pay
        ORDER BY t.pay_time DESC
    </select>
    <select id="selectRecoverypayeeAccount" resultType="com.jiuji.oa.afterservice.historicalrefund.po.RecoveryRefundPo">
        SELECT TOP 10 rs.areaid,rs.sub_id,sum(rb.price) amount,rs.payopenid as openid,rb2.inuser as payUser,rs.pay_time as pay_date,rs.sub_pay
        FROM recover_sub rs with(nolock)
            inner join recover_basket rb with(nolock) on rs.sub_id = rb.sub_id
            outer apply (select top 1 inuser from recover_basket rb1 with(nolock) where isnull(rb1.isdel,0) = 0 and rb1.sub_id = rs.sub_id order by rb1.price desc) rb2
        where isnull(rb.isdel,0) = 0 and rs.sub_check = 3 and rs.pay_time > GETDATE() - 90 and rs.payopenid =  #{selectCondition.payOpenId}
        GROUP by rs.areaid,rs.sub_id,rs.payopenid,rs.pay_time,rb2.inuser,rs.sub_pay
        order by rs.pay_time DESC
    </select>
    <select id="selectRecoveryPo" resultType="com.jiuji.oa.afterservice.historicalrefund.po.SaleRefundPo">
        select  dtime,areaid,inuser,payopenid as payOpenId,pay_total as tuikuanM ,sub_tel as userMobile,userid as userId from dbo.recover_sub with(nolock ) where sub_id = #{id}
    </select>


    <select id="selectRecoveryInuserPrice" resultType="java.lang.String">
        select top 1 inuser from recover_basket rb with(nolock) where isnull(rb.isdel,0) = 0 and rb.sub_id =  #{id} order by rb.price desc
    </select>


</mapper>
