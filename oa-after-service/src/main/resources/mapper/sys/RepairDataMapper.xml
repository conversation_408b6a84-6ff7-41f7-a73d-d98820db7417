<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.sys.dao.RepairDataMapper">

    <update id="updateShouhouStat">
        update shouhou
        set stats = 1
        where id in
         <foreach collection="shouhouIds" item="shouhouId" separator="," open="(" close=")">
             #{shouhouId}
         </foreach> and
         userid = 76783 and isquji = 1 and stats = 0
    </update>
    <select id="selectShouhouStatId" resultType="cn.hutool.core.lang.Dict">
        SELECT s.id FROM shouhou s with(nolock) where s.userid = 76783 and s.isquji = 1 and stats = 0
    </select>
</mapper>