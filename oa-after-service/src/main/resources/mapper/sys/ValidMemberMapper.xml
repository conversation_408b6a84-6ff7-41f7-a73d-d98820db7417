<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.sys.dao.ValidMemberMapper">
    <select id="getMobileAndUserId" resultType="com.jiuji.oa.afterservice.sys.bo.ValidBO">
        <choose>
            <!--取机校验 需要查询的参数：-->
            <when test="businessType == 1">
                select TOP 1 id as shouhouId,sub_id as subId,mobile,userid from shouhou with(nolock) where xianshi = 1 and id
                = #{orderId}
            </when>
            <!--维修费用退款操作校验：-->
            <when test="businessType == 2">
                SELECT TOP 1 s.id as shouhouId,s.sub_id as subId,s.userid as userid,s.areaid areaId,t.id tuihuanId,mobile
                ,t.tuihuan_kind as tuihuanKind from shouhou s WITH(NOLOCK) LEFT
                JOIN shouhou_tuihuan t WITH(NOLOCK) ON s.id = t.shouhou_id WHERE s.id = #{orderId}
            </when>
            <!--小件退/换货操作校验：-->
            <when test="businessType == 3 or businessType == 4">
                select TOP 1 id as smallproId,mobile,userid,areaId,sub_id as subId from Smallpro with(nolock) where isnull(isdel,0) = 0 and id
                = #{orderId}
            </when>
            <otherwise>
                select null
            </otherwise>
        </choose>
    </select>
</mapper>