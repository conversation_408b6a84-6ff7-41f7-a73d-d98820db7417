<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.sys.dao.SysConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.sys.po.SysConfig">
        <id column="id" property="id" />
        <result column="dsc" property="dsc" />
        <result column="name" property="name" />
        <result column="value" property="value" />
        <result column="code" property="code" />
        <result column="xtenant" property="xtenant" />
        <result column="authId" property="authId" />
        <result column="areaids" property="areaids" />
        <result column="kemu" property="kemu" />
        <result column="rank" property="rank" />
        <result column="isdel" property="isdel" />
        <result column="fzhsType" property="fzhsType" />
    </resultMap>

    <select id="getAuthorizeList" resultType="com.jiuji.oa.afterservice.sys.bo.AuthModel">
        select * from authorize with(nolock) order by rank
    </select>

    <select id="getKemuConfigList" resultType="com.jiuji.oa.afterservice.sys.service.KemuModel">
        select id,kemu,code,dsc,ztid as ztId,fzhsType,defaultFzhs from dbo.kemuConfig with(nolock)
    </select>
    <select id="getWxIdByAreaId" resultType="java.lang.Integer">
        SELECT TOP 1 w.wxid FROM dbo.weixinPushConfig w with(nolock)
        INNER JOIN dbo.areainfo a with(nolock) ON a.authorizeid = w.authorizeid
        WHERE a.id = #{areaId} AND w.supportPlat &amp; a.displayPlatform = w.supportPlat
        ORDER BY w.disOrder desc
    </select>

    <insert id="sqlServerBatchInsert" useGeneratedKeys="false">
        insert into sysConfig ( dsc, name, value, code ,xtenant ) values
        <foreach collection="list" item="item" index="index" open="(" separator="),(" close=")">
            #{item.dsc}, #{item.name}, #{item.value}, #{item.code},#{item.xtenant}
        </foreach>
    </insert>

    <select id="checkShoppingBag" resultType="java.lang.Integer">
        select ppriceid from productinfo p with(nolock) where p.cid in (select ID from dbo.f_category_children(485))
    </select>
    <select id="listIncludeDelInCode" resultType="com.jiuji.oa.afterservice.sys.po.SysConfig">
        SELECT id, dsc, name, value, code, xtenant, authId, areaids, kemu, rank, isdel, fzhsType
        FROM sysConfig with(nolock)
        WHERE code IN
        <foreach collection="codeList" open="(" separator="," close=")" item="code">
            #{code}
        </foreach>
    </select>
    <select id="getByCodeAndXtenant" resultType="com.jiuji.oa.afterservice.sys.po.SysConfig">
        SELECT
            top 1 *
        FROM
            dbo.sysconfig with(nolock)
        where [code] = #{code}
          and xtenant = #{xtenant}
          and isnull(isdel,0) = 0
    </select>
</mapper>
