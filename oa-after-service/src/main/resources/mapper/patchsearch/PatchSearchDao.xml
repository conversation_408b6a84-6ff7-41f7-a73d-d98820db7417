<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.patchsearch.dao.PatchSearchDao">
    <select id="getBasketPage" parameterType="com.jiuji.oa.afterservice.patchsearch.vo.req.PatchSearchReq"
            resultType="com.jiuji.oa.afterservice.patchsearch.vo.res.PatchSearchRes">
        SELECT
        a.area area,
        s.sub_id subId,
        p.product_name + p.product_color product,
        s.sub_check subCheck,
        s.sub_date subDate,
        '销售订单' AS type
        FROM dbo.basket b with(nolock)
        LEFT JOIN dbo.sub s with(nolock) ON b.sub_id = s.sub_id
        LEFT JOIN dbo.productinfo p with(nolock) ON b.ppriceid = p.ppriceid
        LEFT JOIN dbo.tiemoCardUserLog l with(nolock) ON l.basket_id = b.basket_id
        LEFT JOIN dbo.tiemoCard c with(nolock) ON l.cardId = c.id
	    LEFT JOIN dbo.areainfo a with(nolock) ON s.areaid = a.id
        WHERE
        c.basket_idBind =#{req.basketId}
        AND ISNULL( s.sub_check, 0 ) <![CDATA[<>]]> 4 UNION
        SELECT
        a.area area,
        s.id AS subId,
        s.Name AS product,
        s.Stats AS subCheck,
        s.Indate AS subDate,
        '售后小件订单' AS type
        FROM dbo.Smallpro s WITH(NOLOCK)
        LEFT JOIN dbo.SmallproBill b WITH(NOLOCK) ON b.smallproID= s.id
        LEFT JOIN dbo.areainfo a WITH(NOLOCK) ON s.areaid = a.id
        WHERE
        b.basket_id = #{req.basketId}
        AND ISNULL( s.Stats, 0 ) <![CDATA[<>]]> 2
        AND s.Kind = 2
        and s.ServiceType = 4
    </select>
</mapper>
