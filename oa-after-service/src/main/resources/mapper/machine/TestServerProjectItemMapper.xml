<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.machine.dao.TestServerProjectItemMapper">
    <insert id="insertProjectItem" useGeneratedKeys="true" keyProperty="serverProjectItem.id">
        INSERT INTO dbo.machine_test_server_project_item_cfg
        (server_project_cfgId, item_name, item_describe, item_guide, item_rank, item_type, is_require, create_time, create_user, update_time, is_del, recommend_cids)
        VALUES(#{serverProjectItem.serverProjectCfgId},#{serverProjectItem.itemName}, #{serverProjectItem.itemDescribe}, #{serverProjectItem.itemGuide}
        ,(select count(1)+1 from dbo.machine_test_server_project_item_cfg mtspic with(nolock) where isnull(mtspic.is_del,0)=0 and mtspic.server_project_cfgId=#{serverProjectItem.serverProjectCfgId})
        , #{serverProjectItem.itemType}, #{serverProjectItem.isRequire}, GETDATE(), #{oaUser.userName}, GETDATE(), 0, #{serverProjectItem.recommendCids})

    </insert>
    <select id="selectUpdateProjectItemLog" resultType="java.lang.String">
        select ' 项目{id['+rtrim(a.id)+']'+a.workContent+'}'
        from (select  mtspic.id,iif(mtspic.item_name = #{serverProjectItem.itemName},'',' 项目名称由['+mtspic.item_name+']改为['+#{serverProjectItem.itemName}+']')
            +iif(mtspic.item_describe = #{serverProjectItem.itemDescribe},'',' 项目描述由['+mtspic.item_describe+']改为['+#{serverProjectItem.itemDescribe}+']')
            +iif(mtspic.item_guide = #{serverProjectItem.itemGuide},'',' 项目指南由['+mtspic.item_guide+']改为['+#{serverProjectItem.itemGuide}+']')
            +iif(mtspic.item_type = #{serverProjectItem.itemType},'',' 项目类型由['+rtrim(mtspic.item_type)+']改为['+rtrim(#{serverProjectItem.itemType})+']')
            +iif(mtspic.is_require = #{serverProjectItem.isRequire},'',' 是否必填由['+rtrim(mtspic.is_require)+']改为['+rtrim(#{serverProjectItem.isRequire})+']')
            +iif(mtspic.recommend_cids = #{serverProjectItem.recommendCids},'',' 推荐分类id由['+mtspic.recommend_cids+']改为['+#{serverProjectItem.recommendCids}+']')
            workContent
              from machine_test_server_project_item_cfg mtspic where mtspic.id = #{serverProjectItem.id}) a
        where a.workContent &lt;&gt; ''
    </select>
    <update id="updateProjectItem">
        UPDATE dbo.machine_test_server_project_item_cfg
        SET item_name=#{serverProjectItem.itemName}, item_describe=#{serverProjectItem.itemDescribe}, item_guide= #{serverProjectItem.itemGuide}
          , item_type=#{serverProjectItem.itemType}, is_require=#{serverProjectItem.isRequire},update_time=GETDATE(),recommend_cids=#{serverProjectItem.recommendCids}
        WHERE id= #{serverProjectItem.id}

    </update>
    <update id="deleteItemById">
        UPDATE dbo.machine_test_server_project_item_cfg
        SET is_del=1
        WHERE id=#{id} and isnull(is_del,0)=0

    </update>
    <insert id="insertProjectOption" useGeneratedKeys="true" keyProperty="insertOptions.id">
        INSERT INTO dbo.machine_test_server_project_item_option_cfg
        (server_project_item_cfgId, option_name, option_rank, create_time, create_user, update_time, is_del)
        VALUES
        <foreach collection="insertOptions" separator="," item="option">
            (#{projectId}, #{option.optionName}, #{option.optionRank},GETDATE(), #{oaUser.userName}, GETDATE(), 0)
        </foreach>


    </insert>
    <select id="selectUpdateItemOptionLog" resultType="java.lang.String">
        select  stuff((select ' 选项{id['+rtrim(a.id)+']'+a.workContent+'}' from (select mtspioc.id,case mtspioc.id
        <foreach collection="updateOptions"  item="option">
            when #{option.id} then iif(mtspioc.option_name = #{option.optionName},'',' 选项名称由['+rtrim(mtspioc.option_name)+']改为['+rtrim(#{option.optionName})+']')
                +iif(mtspioc.option_rank = #{option.optionRank},'',' 选项排序由['+rtrim(mtspioc.option_rank)+'改为['+rtrim(#{option.optionRank})+']')
        </foreach> else ' 已删除'  end workContent
        from dbo.machine_test_server_project_item_option_cfg mtspioc
        WHERE mtspioc.server_project_item_cfgId = #{projectId} and isnull(mtspioc.is_del,0)=0
        ) a where a.workContent &lt;&gt;'' for xml path('')), 1, 1, '') workContent
    </select>
    <update id="updateProjectOption">
        UPDATE dbo.machine_test_server_project_item_option_cfg
        SET
        <foreach collection="updateOptions" item="option" open="option_name=case id " close=" else option_name end,">
            when #{option.id} then #{option.optionName}
        </foreach>
        <foreach collection="updateOptions" item="option" open="option_rank=case id " close=" else option_rank end,">
            when #{option.id} then #{option.optionRank}
        </foreach> update_time= GETDATE(), is_del=
            <choose>
                <when test="updateOptions.isEmpty">1</when>
                <otherwise>
                    <foreach collection="updateOptions" item="option" separator="," open="iif(id in(" close="),0,1)">
                        #{option.id}
                    </foreach>
                </otherwise>
            </choose>

        WHERE server_project_item_cfgId = #{projectId}

    </update>
    <select id="listProjectItem" resultType="com.jiuji.oa.afterservice.machine.po.MachineTestServerProjectItemCfgPO">
        <!--有page 不能加 order by-->
        SELECT id, server_project_cfgId, item_name, item_describe, item_guide, item_rank, item_type, is_require, create_time, create_user, update_time, is_del, recommend_cids
        FROM dbo.machine_test_server_project_item_cfg mtspic with(nolock) where isnull(mtspic.is_del,0)=0
        <if test="query.serverProjectId != null">
            and mtspic.server_project_cfgId = #{query.serverProjectId}
        </if>
    </select>
    <select id="getDataForEdit" resultType="com.jiuji.oa.afterservice.machine.bo.TestServerProjectItemBO">
        SELECT id, server_project_cfgId, item_name, item_describe, item_guide, item_rank, item_type, isnull(is_require,0) isRequire,recommend_cids
        FROM dbo.machine_test_server_project_item_cfg mtspic with(nolock)
        where mtspic.id = #{id}

    </select>
    <select id="getOptionDataForEdit"
            resultType="com.jiuji.oa.afterservice.machine.bo.TestServerProjectItemBO$ItemOptionBO">
        SELECT id, option_name, option_rank
        FROM dbo.machine_test_server_project_item_option_cfg mtspioc with(nolock)
        where mtspioc.server_project_item_cfgId = #{projectId}
    </select>
</mapper>