<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.machine.dao.ElectronicCheckMapper">
    <insert id="insertServerRecord" useGeneratedKeys="true" keyProperty="startServer.serviceRecordId" keyColumn="id">
        INSERT INTO dbo.machine_test_server_record (server_cfgId, server_name, server_regulations, business_type,regulation_url, sound_record
        , sub_id, basket_id, create_time, create_user, start_time, complete_time, voice_record_file, total_step, current_step, update_time, is_del)
        SELECT id server_cfgId,server_name, server_regulations, business_type,regulation_url, sound_record,#{startServer.subId} sub_id,#{startServer.basketId} basket_id
             ,GETDATE() create_time,#{userName} create_user,GETDATE() start_time,null complete_time,null voice_record_file
             ,(select count(1) from machine_test_server_project_cfg with(nolock) where isnull(is_del,0)=0 and server_cfgId = #{startServer.serviceId}) total_step
             ,1 current_step,GETDATE() update_time,0 is_del
        FROM dbo.machine_test_server_cfg with(nolock) where id = #{startServer.serviceId}
    </insert>
    <insert id="insertServerProjectRecord" useGeneratedKeys="true" keyProperty="startServer.lastServiceProjectRecordId" keyColumn="id">
        INSERT INTO dbo.machine_test_server_project_record
        (server_project_cfgId, server_record_id, project_name, project_step, start_time, complete_time, create_time, create_user, update_time, is_del)
        SELECT id server_project_cfgId,#{startServer.serviceRecordId} server_record_id, project_name,ROW_NUMBER() OVER(order by project_rank asc) project_step
             ,iif(ROW_NUMBER() OVER(order by project_rank asc) = 1,GETDATE(),null) start_time,null complete_time,GETDATE() create_time
             ,#{userName} create_user,GETDATE() update_time,0 is_del
        FROM dbo.machine_test_server_project_cfg with(nolock) where isnull(is_del,0)=0 and server_cfgId = #{startServer.serviceId} order by project_rank asc

    </insert>
    <insert id="insertServerProjectItemRecord" useGeneratedKeys="true" keyProperty="startServer.lastServiceProjectItemRecordId" keyColumn="id">
        INSERT INTO dbo.machine_test_server_project_item_record
        (server_project_item_cfgId, server_project_record_id, item_name, item_describe, item_guide,recommend_cids, item_rank, item_type
        , is_require, comment, check_comment, check_fids, create_time, create_user, update_time, is_del)
        SELECT mtspic.id server_project_item_cfgId,mtspr.id server_project_record_id, mtspic.item_name, mtspic.item_describe
             , mtspic.item_guide,mtspic.recommend_cids, mtspic.item_rank, mtspic.item_type,mtspic.is_require,null comment,null check_comment,null check_fids
             ,GETDATE() create_time,#{userName} create_user,GETDATE() update_time,0 is_del
        FROM dbo.machine_test_server_project_item_cfg mtspic with(nolock)
        inner join machine_test_server_project_record mtspr with(nolock) on mtspr.server_project_cfgId =mtspic.server_project_cfgId
        where isnull(mtspic.is_del,0)=0 and mtspr.id in
        <foreach collection="prIds" separator="," open="(" close=")" item="prId">
            #{prId}
        </foreach>
        order by mtspr.id asc,mtspic.item_rank asc
    </insert>
    <insert id="insertServerProjectItemOptionRecord">
        INSERT INTO dbo.machine_test_server_project_item_option_record
        (server_project_item_option_cfgId, server_project_item_record_id, option_name, option_rank, is_selected, create_time, create_user, update_time, is_del)
        SELECT mtspioc.id server_project_item_option_cfgId,mtspir.id server_project_item_record_id,mtspioc.option_name,mtspioc.option_rank,0 is_selected
             ,GETDATE() create_time,#{userName} create_user,GETDATE() update_time,0 is_del
        FROM dbo.machine_test_server_project_item_option_cfg mtspioc with(nolock)
        inner join machine_test_server_project_item_record mtspir with(nolock) on mtspir.server_project_item_cfgId = mtspioc.server_project_item_cfgId
        where isnull(mtspioc.is_del,0)=0 and mtspir.id in
        <foreach collection="priIds" separator="," open="(" close=")" item="priId">
            #{priId}
        </foreach>
        order by mtspir.id asc,mtspioc.option_rank asc

    </insert>
    <update id="updateProjectComplete">
        update machine_test_server_project_record
        set complete_time = GETDATE(),update_time = GETDATE()
        where id = #{currStepId} and  server_record_id = #{serviceRecordId}
    </update>
    <update id="updateProjectItem">
        update machine_test_server_project_item_record
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="comment =case" suffix="end,">
                <foreach collection="items" item="item">
                    <if test="item.answer!=null and item.answer !=''">
                        when id=#{item.stepOptionId}
                        then #{item.answer}
                    </if>
                </foreach>
            </trim>
            <trim prefix="check_comment =case" suffix="end,">
                <foreach collection="items" item="item">
                    <if test="item.text!=null and item.text!=''">
                        when id=#{item.stepOptionId}
                        then #{item.text}
                    </if>
                </foreach>
            </trim>
            <trim prefix="check_fids =case" suffix="end,">
                <foreach collection="items" item="item">
                    <if test="item.imageFids!=null">
                        when id=#{item.stepOptionId}
                        then #{item.imageFids}
                    </if>
                </foreach>
            </trim>
            update_time = GETDATE()
        </trim>
        where  server_project_record_id = #{currStepId} and id in
        <foreach collection="items" item="item" separator="," open="(" close=")">
            #{item.stepOptionId}
        </foreach>
    </update>
    <update id="updateProjectItemOption">
        update machine_test_server_project_item_option_record
        set is_selected = case when id in
          <foreach collection="choiceIds" separator="," open="(" close=")" item="choiceId">
            #{choiceId}
          </foreach> then 1 else 0 end,update_time = GETDATE()
        where server_project_item_record_id in
        <foreach collection="items" item="item" open="(" close=")" separator=",">
             #{item.stepOptionId}
        </foreach>
    </update>
    <update id="updateServerRecord">
        update machine_test_server_record
        set
            <if test="isLastStep">
                <if test="answer.recordFid != null">
                    voice_record_file = #{answer.recordFid},
                </if>
                complete_time = GETDATE(),
            </if>
            current_step = #{answer.currStepId},update_time = GETDATE()
        where id = #{answer.serviceRecordId} and complete_time is null
    </update>
    <update id="updateServerRecordVoiceRecordFile">
        update machine_test_server_record
        set voice_record_file = #{answer.recordFid}
        where id = #{answer.serviceRecordId}
    </update>
    <update id="updateProjectStartTime">
        update machine_test_server_project_record
        set start_time = GETDATE()
        where id = #{currStepId} and server_record_id = #{serviceRecordId} and start_time is null
    </update>
    <sql id="sqlHasServerCnd">
        isnull(mtsc.is_del,0)=0 and isnull(mtsc.is_enable,1)=1
        and EXISTS(SELECT 1 from machine_test_server_pro_cfg mtspc with(nolock)
                   where isnull(mtspc.is_del,0)=0 and mtspc.server_cfgId = mtsc.id
                     and EXISTS(SELECT 1 from basket bt with(nolock)
                                                  inner join dbo.productprice pp with(nolock) on bt.ppriceid = pp.ppriceid
                                                  inner join dbo.product p with(nolock) on pp.productid = p.id
                                where isnull(bt.isdel,0)=0 and bt.basket_id = ${basketId} and bt.sub_id = #{subId}
                                  and (p.cid = mtspc.cid or exists(select 1 from category c with(nolock) where c.id=p.cid and c.path like CONCAT('%,',mtspc.cid,',%')))
                                  and (p.ismobile = mtspc.is_mobile or mtspc.is_mobile = 0 or mtspc.is_mobile = 2 and p.ismobile = 0)
                                  and (p.brandID = mtspc.brand_id or mtspc.brand_id = 0))
                       )
        and mtsc.business_type = #{type}
    </sql>
    <select id="getServer" resultType="com.jiuji.oa.afterservice.machine.bo.ServerRuleBO">
        SELECT top 1 mtsc.id serviceId, mtsc.server_name serverName, mtsc.server_regulations serverRegulations, mtsc.sound_record soundRecord
                   , mtsc.business_type subType,mtsc.regulation_url regulationUrl
        from machine_test_server_cfg mtsc with(nolock)
        where <include refid="sqlHasServerCnd"></include>
           order by update_time desc,create_time desc
    </select>
    <select id="getBasketInfo" resultType="com.jiuji.oa.afterservice.machine.bo.BasketInfoBO">
        SELECT bt.sub_id subId,p.name serverBasketInfo,s.userid userId,s.sub_to userName,s.sub_tel userMobile FROM basket bt with(nolock)
               inner join sub s with(nolock) on s.sub_id = bt.sub_id
               LEFT JOIN product p with(nolock) ON EXISTS (SELECT 1 FROM dbo.productprice pp with(nolock) where bt.ppriceid = pp.ppriceid and pp.productid = p.id)
        where isnull(bt.isdel,0)=0 and s.sub_check &lt;&gt; 4 and s.sub_id = #{subId} and bt.basket_id = #{basketId}
    </select>
    <select id="isRecord" resultType="java.lang.Boolean">
        select isnull(mtsr.sound_record,0) from machine_test_server_record mtsr with(nolock) where mtsr.id= #{serviceId}
    </select>
    <select id="listStep" resultType="com.jiuji.oa.afterservice.machine.vo.ServerProjectVO$OptionsVO">
        select mtspr.id stepId,mtspr.project_name subStepName,mtspr.project_step rank,mtspr.start_time startTime,mtspr.complete_time completeTime
        from machine_test_server_project_record mtspr with(nolock)
        where isnull(mtspr.is_del,0)=0 and mtspr.server_record_id = #{serviceRecordId} order by mtspr.project_step asc
    </select>
    <select id="listStepItem" resultType="com.jiuji.oa.afterservice.machine.vo.ServerItemsVO">
        select mtspir.id stepOptionId,mtspir.is_require required,mtspir.item_type type,mtspir.item_name name,mtspir.item_describe stepDescription
        ,mtspir.item_guide stepGuide,1 noteEnable,mtspir.comment answer,mtspir.recommend_cids recommendCids,mtspir.check_comment checkComment
        ,mtspir.check_fids checkFids,mtspir.server_project_record_id projectRecordId
        from machine_test_server_project_item_record mtspir with(nolock)
        where isnull(mtspir.is_del,0)=0
          <if test="currStepId != null">
              and mtspir.server_project_record_id = #{currStepId}
          </if>
          and exists(select 1 from machine_test_server_project_record mtspr with(nolock) where mtspr.server_record_id = #{serviceRecordId} and mtspr.id = mtspir.server_project_record_id)
        order by mtspir.item_rank asc
    </select>
    <select id="getCompleteServerRecordId" resultType="java.lang.Integer">
        select mtsr.id from machine_test_server_record mtsr with(nolock) where isnull(mtsr.is_del,0)=0
               and mtsr.server_cfgId = #{startServer.serviceId} and mtsr.sub_id = #{startServer.subId}
               and mtsr.basket_id = #{startServer.basketId} and mtsr.complete_time is not null
    </select>
    <select id="listStepItemOption" resultType="com.jiuji.oa.afterservice.machine.vo.ServerItemsVO$ChoiceVO">
        select mtspior.server_project_item_record_id stepOptionId,mtspior.id choiceId,mtspior.option_name [text],isnull(mtspior.is_selected,0) isSelect
        ,mtspior.option_rank rank
        from machine_test_server_project_item_option_record mtspior with(nolock) where mtspior.server_project_item_record_id in
        <foreach collection="itemIds" open="(" close=")" item="itemId" separator=",">
            #{itemId}
        </foreach>
        order by stepOptionId asc,rank asc;
    </select>
    <select id="getAnswerInfoByStepId" resultType="com.jiuji.oa.afterservice.machine.bo.AnswerStepInfoBO">
        select mtsr.total_step totalStep,iif(mtspr.project_step=mtsr.total_step,1,0) isLastStep,isnull(mtsr.sound_record,0) isRecord,mtsr.voice_record_file
             ,(select count(1) from machine_test_server_project_item_record mtspir with(nolock) where isnull(mtspir.is_del,0)=0 and isnull(mtspir.is_require,0) = 1 and mtspir.server_project_record_id = mtspr.id) requireProjectNum
             ,b.projectCompleteNum
        from machine_test_server_project_record mtspr with(nolock)
        inner join machine_test_server_record mtsr  with(nolock) on mtsr.id = mtspr.server_record_id
        inner join (select count(1) projectCompleteNum from machine_test_server_project_record mtspr with(nolock)
                     where  isnull(mtspr.is_del,0)=0 and mtspr.server_record_id = #{serverRecordId} and mtspr.complete_time is not null) b on 1=1
        where isnull(mtspr.is_del,0)=0 and isnull(mtsr.is_del,0)=0 and mtspr.id = #{currStepId}
    </select>
    <select id="getServerRecord" resultType="com.jiuji.oa.afterservice.machine.po.MachineTestServerRecordPO">
        SELECT id, server_cfgId, server_name, server_regulations, business_type, sound_record, sub_id, basket_id, create_time
             , create_user, start_time, complete_time, iif(right(voice_record_file,4)='.msg',null,voice_record_file) voiceRecordFile, total_step, current_step, update_time, is_del
        FROM dbo.machine_test_server_record with(nolock) where id = #{serviceRecordId}

    </select>
    <select id="getSubInfo" resultType="com.jiuji.oa.afterservice.machine.bo.MachineSubInfo">
        select s.userid userId,b.sub_id,b.basket_id,b.ppriceid ppid from basket b with(nolock)
                    inner join sub s with(nolock) on s.sub_id = b.sub_id
        where b.sub_id = #{subId} and b.basket_id = #{basketId}
    </select>
    <select id="getCompleteServerRecord"
            resultType="com.jiuji.oa.afterservice.machine.po.MachineTestServerRecordPO">
        SELECT top 1 id, server_cfgId, server_name, server_regulations, business_type, sound_record, sub_id, basket_id, create_time
             , create_user, start_time, complete_time, iif(right(voice_record_file,4)='.msg',null,voice_record_file) voiceRecordFile, total_step, current_step, update_time, is_del
        FROM dbo.machine_test_server_record mtsr with(nolock) where isnull(mtsr.is_del,0)=0 and mtsr.sub_id = #{subId}
            and mtsr.basket_id = #{basketId} and mtsr.business_type = #{type}  and complete_time is not null
        order by mtsr.create_time desc
    </select>
    <select id="getAppRoute" resultType="com.jiuji.oa.afterservice.machine.vo.AppRouteVO">
        SELECT basket_id basketId,b.ppriceid,s.sub_check subCheck
        from basket b  with(nolock)
        inner join sub s with(nolock) on s.sub_id = b.sub_id
        where isnull(b.isdel,0)=0 and b.sub_id = #{subId}
    </select>
    <select id="listHasCheckConfigPpid" resultType="java.lang.Integer">
        SELECT pp.ppriceid from dbo.productprice pp with(nolock)
        inner join dbo.product p with(nolock) on pp.productid = p.id
        inner join machine_test_server_cfg mtsc with(nolock) on isnull(mtsc.is_del,0)=0 and isnull(mtsc.is_enable,1)=1
        inner join machine_test_server_pro_cfg mtspc with(nolock) on isnull(mtspc.is_del,0)=0 and mtspc.server_cfgId = mtsc.id
        where (p.cid = mtspc.cid or exists(select 1 from category c with(nolock) where c.id=p.cid and c.path like CONCAT('%,',mtspc.cid,',%')))
        and (p.ismobile = mtspc.is_mobile or mtspc.is_mobile = 0 or mtspc.is_mobile = 2 and p.ismobile = 0)
        and (p.brandID = mtspc.brand_id or mtspc.brand_id = 0)
        and mtsc.business_type = #{type}
        and pp.ppriceid in
        <foreach collection="ppids" separator="," open="(" close=")" item="ppid">
            #{ppid}
        </foreach>
    </select>
    <select id="listCompleteServerRecord"
            resultType="com.jiuji.oa.afterservice.machine.po.MachineTestServerRecordPO">
        SELECT id, server_cfgId, server_name, server_regulations, business_type, sound_record, sub_id, basket_id, create_time
                   , create_user, start_time, complete_time, iif(right(voice_record_file,4)='.msg',null,voice_record_file) voiceRecordFile, total_step, current_step, update_time, is_del
        FROM dbo.machine_test_server_record mtsr with(nolock)
        where isnull(mtsr.is_del,0)=0 and mtsr.sub_id = #{subId}
            and mtsr.basket_id in
                <foreach collection="basketIds" item="basketId" open="(" close=")" separator=",">
                    #{basketId}
                </foreach>
                and mtsr.business_type = #{type}  and complete_time is not null
        order by mtsr.create_time desc
    </select>
    <select id="listProjectIds" resultType="java.lang.Integer">
        select mtspr.id from machine_test_server_project_record mtspr with(nolock)  where  mtspr.server_record_id = #{serviceRecordId} and isnull(mtspr.is_del,0)=0
    </select>
    <select id="listProjectItemIds" resultType="java.lang.Integer">
        select mtspir.id from machine_test_server_project_item_record mtspir with(nolock)  where  mtspir.server_project_record_id in
            <foreach collection="projectRecordIds" item="projectRecordId" open="(" close=")" separator=",">
                #{projectRecordId}
            </foreach>
           and isnull(mtspir.is_del,0)=0
    </select>

    <select id="serverRecordDetailedStatistics"
            resultType="com.jiuji.oa.afterservice.machine.vo.ServerRecordDetailedStatisticsVO">
        SELECT
            tsr.id,
            s.areaid AS areaId,
            tsr.server_name AS serverName,
            tsr.create_time AS createTime,
            tsr.create_user AS createUser,
            s.subtype AS subType,
            tsr.sub_id AS subId,
            p.product_name + ' ' + ISNULL(p.product_color,'') AS productName,
            p.cid,
            p.brandID AS brandId,
            tsr.start_time AS startTime ,
            tsr.complete_time AS completeTime,
            DATEDIFF(minute,tsr.start_time,tsr.complete_time) AS machineInspectionTime,
            tsr.voice_record_file AS voiceRecordFile,
            c.name AS cidName,
            a.area,
            '新机订单' AS subtypeName,
            bd.name AS brandName
        FROM
            dbo.machine_test_server_record tsr WITH ( nolock )
	        LEFT JOIN dbo.sub s WITH ( nolock ) ON tsr.sub_id = s.sub_id
            LEFT JOIN dbo.basket b WITH ( nolock ) ON tsr.basket_id = b.basket_id
            LEFT JOIN dbo.productinfo p WITH ( nolock ) ON p.ppriceid = b.ppriceid
            LEFT JOIN category c WITH ( nolock ) ON c.id = p.cid
            LEFT JOIN areainfo a WITH ( nolock ) ON a.id = s.areaid
            LEFT JOIN brand bd  with(nolock) on bd.id = p.brandID
        WHERE 1=1 and tsr.complete_time is not null and isnull(tsr.is_del,0) = 0
        <!--时间搜索状态 -->
        <if test="req.timeType != null and req.timeType > 0">
            <choose>
                <!--验机时间-->
                <when test="req.timeType == 1">
                    AND tsr.start_time  BETWEEN #{req.startTime} AND #{req.endTime}
                </when>
                <!--加单时间-->
                <when test="req.timeType == 2">
                    AND s.sub_date  BETWEEN #{req.startTime} AND #{req.endTime}
                </when>
                <!--订单完成时间 -->
                <when test="req.timeType == 3">
                    AND s.tradeDate1  BETWEEN #{req.startTime} AND #{req.endTime}
                </when>
            </choose>
        </if>
        <!--搜索状态-->
        <if test="req.productType != null and req.productType > 0">
            <choose>
                <!--商品查询-->
                <when test="req.productType == 1">
                    <choose>
                        <when test="req.searchValue != null and req.searchValue != '' ">
                            <if test="req.ids != null and req.ids.size()>0">
                                and tsr.id IN
                                <foreach collection="req.ids" item="id" open="(" close=")" separator=",">
                                    #{id}
                                </foreach>
                            </if>
                            <if test="req.ids == null and req.ppid == null">
                                and p.product_name like concat('%',#{req.searchValue},'%')
                            </if>
                            <if test="req.ppid != null">
                                and b.ppriceid = #{req.ppid}
                            </if>
                        </when>
                    </choose>
                </when>
                <!--验机人-->
                <when test="req.productType == 2">
                    AND tsr.create_user like concat('%',#{req.searchValue},'%')
                </when>
                <!--订单号 -->
                <when test="req.productType == 3">
                    AND s.sub_id = #{req.searchValue}
                </when>
            </choose>
        </if>
        <!--门店-->
        <if test="req.areaIdList != null and req.areaIdList.size() > 0">
            AND s.areaid IN
            <foreach collection="req.areaIdList" item="areaId" open="(" close=")" separator=",">
                #{areaId}
            </foreach>
        </if>
        <!--品牌-->
        <if test="req.brandIdList != null and req.brandIdList.size() > 0">
            AND p.brandID IN
            <foreach collection="req.brandIdList" item="brandId" open="(" close=")" separator=",">
                #{brandId}
            </foreach>
        </if>
        <!--分类-->
        <if test="req.cidList != null and req.cidList.size() > 0">
            AND p.cid IN
            <foreach collection="req.cidList" item="cid" open="(" close=")" separator=",">
                #{cid}
            </foreach>
        </if>
        <!--开始时长和结束时长-->
        <if test="req.startDuration != null and req.endDuration != null">
            AND DATEDIFF(minute,tsr.start_time,tsr.complete_time) BETWEEN #{req.startDuration} AND #{req.endDuration}
        </if>
    </select>

    <select id="getServerRecordName" resultType="com.jiuji.oa.afterservice.machine.vo.ServerRecordDetailedStatisticsVO">
        SELECT
            tsr.id,
            p.product_name + ' ' + ISNULL(p.product_color,'') AS productName
        FROM
            dbo.machine_test_server_record tsr WITH ( nolock )
            LEFT JOIN dbo.basket b WITH ( nolock ) ON tsr.basket_id = b.basket_id
            LEFT JOIN dbo.productinfo p WITH ( nolock ) ON p.ppriceid = b.ppriceid
        WHERE tsr.complete_time is not null and isnull(tsr.is_del,0) = 0
    </select>
</mapper>