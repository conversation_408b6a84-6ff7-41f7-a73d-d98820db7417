<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.machine.dao.TestServerCfgProjectMapper">
    <insert id="insertProject" useGeneratedKeys="true" keyProperty="serverProject.id">
        INSERT INTO dbo.machine_test_server_project_cfg
        (server_cfgId, project_name, project_rank, create_time, create_user, update_time, is_del)
        VALUES(#{serverProject.serverCfgId}, #{serverProject.projectName}
        ,(select isnull(max(mtspc.project_rank),0)+1 from dbo.machine_test_server_project_cfg mtspc  with(nolock) where isnull(mtspc.is_del,0)=0 and mtspc.server_cfgId= #{serverProject.serverCfgId})
        ,GETDATE(), #{userName}, GETDATE(), 0)
    </insert>
    <update id="updateProject">
        UPDATE dbo.machine_test_server_project_cfg
        SET project_name=#{serverProject.projectName},update_time= GETDATE()
        WHERE id= #{serverProject.id}

    </update>
    <update id="updateProjectItemRank">
        UPDATE dbo.machine_test_server_project_item_cfg
        SET item_rank= case id
        <foreach collection="projectItems" item="projectItem">
            when #{projectItem.id} then #{projectItem.itemRank}
        </foreach> else item_rank end
        WHERE id in
        <foreach collection="projectItems" open="(" close=")" separator="," item="projectItem">
            #{projectItem.id}
        </foreach>
        and server_project_cfgId = #{projectId}
    </update>
    <select id="selectUpdateProjectLog" resultType="java.lang.String">
        select ' 步骤{id['+rtrim(a.id)+']'+a.workContent+'}'
        from (select  mtspc.id,iif(mtspc.project_name = #{serverProject.projectName},'',' 步骤名称由['+mtspc.project_name+']改为['+#{serverProject.projectName}+']') workContent
                from machine_test_server_project_cfg mtspc with(nolock)  where mtspc.id = #{serverProject.id}) a
        where a.workContent &lt;&gt; ''
    </select>
    <select id="selectUpdateItemLog" resultType="java.lang.String">
        select  stuff((select ' 项目{id['+rtrim(a.id)+']'+a.workContent+'}' from (select mtspic.id,case mtspic.id
        <foreach collection="projectItems"  item="projectItem">
            when #{projectItem.id} then iif(mtspic.item_rank = #{projectItem.itemRank},'',' 排序由['+rtrim(mtspic.item_rank)+']改为['+rtrim(#{projectItem.itemRank})+']')
        </foreach> else '' end workContent
        from dbo.machine_test_server_project_item_cfg mtspic WHERE id in
        <foreach collection="projectItems" open="(" close=")" separator="," item="projectItem">
            #{projectItem.id}
        </foreach>
        and server_project_cfgId = #{projectId}
        ) a where a.workContent &lt;&gt;'' for xml path('')), 1, 1, '') workContent
    </select>
</mapper>