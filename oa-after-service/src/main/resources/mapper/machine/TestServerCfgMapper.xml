<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.machine.dao.TestServerCfgMapper">
    <insert id="insertServer" useGeneratedKeys="true" keyProperty="testServer.id">
        INSERT INTO dbo.machine_test_server_cfg
        (server_name, server_regulations, business_type,regulation_url, sound_record, create_time, create_user, update_time, is_del, is_enable)
        VALUES(#{testServer.serverName}, #{testServer.serverRegulations}, #{testServer.businessType},#{testServer.regulationUrl}, #{testServer.soundRecord}
            ,GETDATE(), #{createUser}, GETDATE(), 0, 1)
    </insert>
    <insert id="insertServerPros" useGeneratedKeys="true" keyProperty="insertServerPros.id">
        INSERT INTO dbo.machine_test_server_pro_cfg
        (server_cfgId, cid, brand_id, is_mobile, create_time, create_user, update_time, is_del)
        VALUES
               <foreach collection="insertServerPros" separator="," item="serverPro">
                   (#{serverId}, #{serverPro.cid}, #{serverPro.brandId}, #{serverPro.isMobile}, GETDATE(), #{userName}, GETDATE(), 0)
               </foreach>
    </insert>
    <select id="selectUpdateServerLog" resultType="java.lang.String">
        select ' 服务{id['+rtrim(a.id)+']'+a.workContent+'}'
        from (select  mtsc.id,iif(mtsc.server_name = #{testServer.serverName},'',' 服务名称由['+mtsc.server_name+']改为['+#{testServer.serverName}+']')
            +iif(mtsc.server_regulations = #{testServer.serverRegulations},'',' 服务条例由['+mtsc.server_regulations+']改为['+#{testServer.serverRegulations}+']')
            +iif(mtsc.regulation_url = #{testServer.regulationUrl},'',' 条例地址由['+mtsc.regulation_url+']改为['+#{testServer.regulationUrl}+']')
            +iif(mtsc.business_type = #{testServer.businessType},'',' 业务类型由['+rtrim(mtsc.business_type)+']改为['+rtrim(#{testServer.businessType})+']')
            +iif(mtsc.sound_record = #{testServer.soundRecord},'',' 是否录音由['+rtrim(mtsc.sound_record)+']改为['+rtrim(#{testServer.soundRecord})+']')
            workContent
              from dbo.machine_test_server_cfg mtsc where mtsc.id = #{testServer.id}) a
        where a.workContent &lt;&gt; ''
    </select>
    <update id="updateServer">
        UPDATE dbo.machine_test_server_cfg
        SET server_name=#{testServer.serverName}, server_regulations=#{testServer.serverRegulations}
          , business_type=#{testServer.businessType},regulation_url=#{testServer.regulationUrl}
          , sound_record=#{testServer.soundRecord}, update_time= GETDATE()
        WHERE id=#{testServer.id}
    </update>
    <select id="selectUpdateServerProLog" resultType="java.lang.String">
        select  stuff((select ' 适用机型{id['+rtrim(a.id)+']'+a.workContent+'}' from (select mtspc.id,case mtspc.id
        <foreach collection="updateServerPros" item="serverPro">
            when #{serverPro.id} then iif(mtspc.cid = #{serverPro.cid},'',' 分类id由['+rtrim(mtspc.cid)+']改为['+rtrim(#{serverPro.cid})+']')
            +iif(mtspc.brand_id = #{serverPro.brandId},'',' 品牌id由['+rtrim(mtspc.brand_id)+']改为['+rtrim(#{serverPro.brandId})+']')
            +iif(mtspc.is_mobile = #{serverPro.isMobile},'',' 大小件由['+rtrim(mtspc.is_mobile)+']改为['+rtrim(#{serverPro.isMobile})+']')
        </foreach> else ' 已删除' end workContent
        from dbo.machine_test_server_pro_cfg mtspc  with(nolock) WHERE mtspc.server_cfgId = #{serverId} and isnull(mtspc.is_del,0)=0
        ) a where a.workContent &lt;&gt;'' for xml path('')), 1, 1, '') workContent
    </select>
    <update id="updateServerPros">
        UPDATE dbo.machine_test_server_pro_cfg
        SET
        <foreach collection="updateServerPros" item="serverPro" open="cid=case id " close=" else cid end,">
            when #{serverPro.id} then #{serverPro.cid}
        </foreach>
        <foreach collection="updateServerPros" item="serverPro" open=" brand_id=case id " close=" else brand_id end,">
            when #{serverPro.id} then #{serverPro.brandId}
        </foreach>
            <foreach collection="updateServerPros" item="serverPro" open=" is_mobile= case id " close="  else is_mobile end,">
                when #{serverPro.id} then #{serverPro.isMobile}
            </foreach>update_time= GETDATE(), is_del=
                <choose>
                    <when test="updateServerPros.isEmpty">1</when>
                    <otherwise>
                        <foreach collection="updateServerPros" item="serverPro" separator="," open="iif(id in(" close="),0,1)">
                            #{serverPro.id}
                        </foreach>
                    </otherwise>
                </choose>
        WHERE server_cfgId = #{serverId}

    </update>
    <select id="listServer" resultType="com.jiuji.oa.afterservice.machine.po.MachineTestServerCfgPO">
        SELECT id, server_name, server_regulations, business_type, sound_record, create_time, create_user, update_time,isnull(is_enable,1) isEnable,isnull(is_del,0) isDel
        FROM dbo.machine_test_server_cfg with(nolock) where isnull(is_del,0)=0
    </select>
    <select id="getServerCfgForEditById" resultType="com.jiuji.oa.afterservice.machine.bo.TestServerCfgBO">
        SELECT mtsc.id, mtsc.server_name, mtsc.server_regulations, mtsc.business_type,mtsc.regulation_url, mtsc.sound_record
        FROM dbo.machine_test_server_cfg mtsc with(nolock) where isnull(mtsc.is_del,0)=0 and mtsc.id = #{id}
    </select>
    <select id="listServerProForEdit"
            resultType="com.jiuji.oa.afterservice.machine.bo.TestServerCfgBO$TestServerProBO">
        SELECT id, cid, brand_id, is_mobile
        FROM dbo.machine_test_server_pro_cfg mtspc with(nolock) where isnull(mtspc.is_del,0)=0 and mtspc.server_cfgId = #{serverId}
    </select>
    <select id="listServerProject" resultType="com.jiuji.oa.afterservice.machine.vo.ServerEditLeftVO">
        SELECT id,project_name name, project_rank rank
        FROM dbo.machine_test_server_project_cfg mtspc with(nolock)
        where isnull(mtspc.is_del,0)=0 and mtspc.server_cfgId = #{serverId}
        order by rank asc
    </select>
</mapper>