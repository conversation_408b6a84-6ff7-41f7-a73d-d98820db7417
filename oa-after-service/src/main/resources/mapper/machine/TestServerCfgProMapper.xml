<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.machine.dao.TestServerCfgProMapper">
    <select id="listPro" resultType="com.jiuji.oa.afterservice.machine.po.MachineTestServerProCfgPO">
        SELECT mtspc.id, mtspc.server_cfgId, mtspc.cid,c.Name cidName, mtspc.brand_id,bd.name brandName, mtspc.is_mobile, mtspc.create_time, mtspc.create_user, mtspc.update_time, mtspc.is_del
        from machine_test_server_pro_cfg mtspc with(nolock)
        left join category c with(nolock) on c.ID = mtspc.cid
        left join brand bd  with(nolock) on bd.id = mtspc.brand_id
        where isnull(mtspc.is_del,0)=0 and mtspc.server_cfgId = #{query.serverId}
    </select>
</mapper>