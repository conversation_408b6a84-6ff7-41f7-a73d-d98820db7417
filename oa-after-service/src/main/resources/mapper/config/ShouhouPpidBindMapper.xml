<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.config.dao.ShouhouPpidBindMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.config.po.ShouhouPpidBind">
        <id column="id" property="id" />
        <result column="ppid" property="ppid" />
        <result column="config_id" property="configId" />
        <result column="name" property="name" />
        <result column="out_put_limit" property="outPutLimit" />
        <result column="create_user" property="createUser" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="is_del" property="isDel" />
    </resultMap>
    <select id="listBindPpidInfoNotAutoOutPut"
            resultType="com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidBindVo">
        SELECT spb.id, spb.ppid, spb.config_id, spb.name, spb.out_put_limit, spb.create_user, spb.create_time, spb.update_time
             , spb.is_del, spb.negative, spb.auto_out_put,isnull(pk.lcount,0) lcount
        FROM dbo.shouhou_ppid_bind spb with(nolock)
        left join product_kc pk with(nolock) on pk.ppriceid = spb.ppid and pk.areaid = #{areaId}
        where
            exists(select 1 from shouhou_ppid_config spc with(nolock) where spc.ppid = #{ppid} AND spc.id = spb.config_id and spc.xtenant = #{xtenant})
            and isnull(spb.is_del,0) = 0 and isnull(spb.auto_out_put,0) =#{isAutoOut}
        order by spb.id desc
    </select>

</mapper>
