<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.config.dao.ShouhouPpidConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.config.po.ShouhouPpidConfig">
        <id column="id" property="id" />
        <result column="ppid" property="ppid" />
        <result column="name" property="name" />
        <result column="xtenant" property="xtenant" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="create_user" property="createUser" />
    </resultMap>

    <select id="getShouhouPpidConfigList" resultType="com.jiuji.oa.afterservice.config.vo.res.ShouhouPpidOutPutConfig">
        select * from shouhou_ppid_config with(nolock) where 1=1
        <if test="req.searchKey != null and req.searchKey != ''">
            <choose>
                <when test="way == 0">
                    and name like concat('%',#{req.searchKey},'%')
                </when>
                <otherwise>
                    and ppid = #{req.searchKey}
                </otherwise>
            </choose>
        </if>
        <if test="req.xtenant != null">
            and xtenant = #{req.xtenant}
        </if>
        <if test="req.status != null">
            and status = #{req.status}
        </if>
        <if test="req.startTime != null and req.startTime != '' and req.endTime != null and req.endTime != ''">
            and create_time between #{req.startTime} and #{req.endTime}
        </if>
    </select>

    <select id="getXtenantInfo" resultType="com.jiuji.oa.afterservice.config.vo.res.XtenantInfo">
        SELECT xtenant,t.printName FROM
        (
            SELECT a.xtenant,a.printName,ROW_NUMBER() OVER(PARTITION BY a.xtenant ORDER BY COUNT(1) DESC) rn
            FROM dbo.areainfo a with(nolock) WHERE a.ispass = 1
            GROUP BY a.xtenant,a.printName
        ) t
        WHERE t.rn = 1
    </select>
</mapper>
