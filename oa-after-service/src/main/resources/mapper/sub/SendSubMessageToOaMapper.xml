<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.sub.dao.SendSubMessageToOaMapper">

    <select id="huaweiOtherAreaSub" resultType="cn.hutool.core.lang.Dict">
        select ao.id huaweiAreaId,s.areaid,s.sub_id,s.sub_date,p.productid,p.product_name,p.product_color,k.imei,s.Inuser
        from sub s with(nolock)
        left join dbo.basket b with(nolock) on s.sub_id=b.sub_id
        left join dbo.productinfo p with(nolock) on b.ppriceid=p.ppriceid
        left join dbo.product_mkc k with(nolock) on k.basket_id=b.basket_id
        inner join areainfo ao with(nolock) on ao.xtenant = 2 and exists(select 1 from dbo.Ok3w_qudao o with(nolock) where o.id=k.insourceid2 and o.bindAreaId = ao.id)
        where s.sub_check != 4 and isnull(b.isdel,0) = 0
        and  DATEDIFF(MINUTE,s.sub_date,GETDATE()) &lt;= #{minutes}
        and  exists(select id from areainfo a with(nolock) where a.id = s.areaid and a.xtenant &lt;&gt; 2)
    </select>
    <select id="selectAreaUser" resultType="cn.hutool.core.lang.Dict">
        select top ${areaUserMax} u.ch999_id,u.zhiwu from ch999_user u with(nolock)
        LEFT JOIN zhiwu z with(nolock) ON u.zhiwuid = z.id
        where u.iszaizhi = 1
        and ISNULL(u.isshixi,0) &lt;&gt; 4
        and u.area1id = #{areaId}
        and u.zhiwu in
        <foreach collection="zhiwus" item="zhiwu" separator="," open="(" close=")">
            #{zhiwu}
        </foreach>
        order by z.leve,u.ch999_id
    </select>
</mapper>