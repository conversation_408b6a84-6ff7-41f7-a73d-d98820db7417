<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.sub.dao.SubMapper">

    <select id="getCommonCustomerBOBySubId" resultType="com.jiuji.oa.afterservice.sub.bo.CommonCustomerBO">
        select
            s.subtype as subtype,
            z.id as customerId,
            z.areaId as customerAreaId,
            z1.id as customerId1,
            z1.areaId as customerAreaId1
        from sub s with(nolock)
            left join (
                select * from
			        (select id,userid,areaId,row_number() over(partition by userid order by id desc) rn from zitidian with(nolock) where customerKinds=1) aa
			    where aa.rn=1
			) z  on (z.userid=s.userid)
			left join (select * from
				    (select id,userid,areaId,row_number() over(partition by userid order by id desc) rn from zitidian with(nolock) where customerKinds=2) aa
				where aa.rn=1
			) z1  on (z1.userid=s.userid)
        WHERE
            s.sub_id = #{subId}
    </select>

    <select id="getSubDJQPrice" resultType="java.math.BigDecimal">
        select sum(sub_pay05) from shouying WITH(NOLOCK)  where sub_id=#{subId}
    </select>

    <select id="getReturnDataTable" resultType="com.jiuji.oa.afterservice.bigpro.bo.ShouhouTuihuanReturnDataBo">
        <if test="returnType != null and returnType == 1">
            select tradeDate1, isnull(z.tel1,u.mobile) mobile,isnull(z.userid,s.userid) userid from sub s with(nolock)
            left join dbo.zitidian z with(nolock) on s.zitidianID=z.id left join dbo.BBSXP_Users u with(nolock) on
            u.ID=s.userid where sub_id=#{subId}
        </if>
        <if test="returnType != null and returnType == 5">
            select tradeDate1, isnull(z.tel1,sub_mobile) mobile,isnull(z.userid,s.userid) userid from sub s with(nolock)
            left join dbo.zitidian z with(nolock) on s.zitidianID=z.id where sub_id=#{subId}
        </if>
        <if test="returnType != null and returnType == 5">
            select tradeDate1, isnull(z.tel1,sub_mobile) mobile,isnull(z.userid,s.userid) userid from sub s with(nolock)
            left join dbo.zitidian z with(nolock) on s.zitidianID=z.id where sub_id=#{subId}
        </if>
        <if test="returnType != null and returnType == 3 and tmpDtCount != null and tmpDtCount > 0">

            <if test="ishuishou == 1">
                select tradeDate1,sub_mobile as mobile,userid from recover_marketinfo with(nolock) where sub_id=#{subId}
            </if>
            <if test="ishuishou == null or ishuishou == 0">
                <choose>
                    <when test="subId == null or subId == 0">
                        SELECT s.tradeDate1,isnull(z.tel1,sub_mobile) mobile,isnull(z.userid,s.userid) userid
                        FROM dbo.product_mkc k with(nolock)
                        LEFT JOIN dbo.basket b WITH(nolock) ON b.basket_id = k.basket_id
                        LEFT JOIN sub s with(nolock) ON b.sub_id = s.sub_id
                        left join dbo.zitidian z with(nolock) on s.zitidianID=z.id
                        WHERE k.id = #{mkcId} AND ISNULL(b.isdel,0) = 0 AND k.kc_check = 5 AND s.sub_check =3
                    </when>
                    <otherwise>
                        select tradeDate1, isnull(z.tel1,sub_mobile) mobile,isnull(z.userid,s.userid) userid
                        from sub s with(nolock)
                        left join dbo.zitidian z with(nolock) on s.zitidianID=z.id
                        where sub_id=#{subId}
                    </otherwise>
                </choose>
            </if>

        </if>
        <if test="returnType != null and returnType == 4 ">
            select mobile,userid from dbo.shouhou with(nolock) where id=#{subId}
        </if>
    </select>

    <select id="searchSubInfoByUserId" resultType="com.jiuji.oa.afterservice.bigpro.vo.MemberSubVO">
        SELECT s.sub_id, k.imei,p.product_name,p.product_color
                FROM dbo.sub s with(nolock) INNER JOIN dbo.basket b with(nolock) ON s.sub_id=b.sub_id
                INNER JOIN dbo.product_mkc k with(nolock) ON k.basket_id=b.basket_id
                INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid=k.ppriceid
                WHERE s.sub_check not in (4,8,9) AND b.ismobile=1 AND s.userid=#{userId}
                ORDER BY b.basket_id DESC
    </select>
    <select id="searchLpInfoByUserId" resultType="com.jiuji.oa.afterservice.bigpro.vo.MemberSubVO">
         SELECT TOP 50 s.sub_id, k.imei,'【良品】'+p.product_name as product_name ,p.product_color
                FROM dbo.recover_marketInfo s with(nolock) INNER JOIN dbo.recover_marketSubInfo b with(nolock) ON s.sub_id = b.sub_id
                INNER JOIN dbo.recover_mkc k with(nolock) ON k.to_basket_id = b.basket_id
                INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid = k.ppriceid
                WHERE s.sub_check not in (4,8,9) AND s.userid=#{userId}
                ORDER BY b.basket_id DESC
    </select>

    <select id="searchSubInfoByImei" resultType="com.jiuji.oa.afterservice.bigpro.vo.MemberSubVO">
        	SELECT * from (SELECT TOP 1 1 as type,s.sub_id, k.imei,'【新机】'+p.product_name as product_name ,p.product_color,p.brandID as brandId
                FROM dbo.sub s with(nolock) INNER JOIN dbo.basket b with(nolock) ON s.sub_id = b.sub_id
                INNER JOIN dbo.product_mkc k with(nolock) ON k.ppriceid = b.ppriceid
                INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid = k.ppriceid
				and k.imei = #{imei}
                WHERE s.sub_check not in (4,5,7,8,9)
            union all
            SELECT TOP 1 2 as type,s.sub_id, k.imei,'【良品】'+p.product_name as product_name ,p.product_color,p.brandID as brandId
                FROM dbo.recover_marketInfo s with(nolock) INNER JOIN dbo.recover_marketSubInfo b with(nolock) ON s.sub_id = b.sub_id
                INNER JOIN dbo.recover_mkc k with(nolock) ON k.to_basket_id = b.basket_id
                INNER JOIN dbo.productinfo p with(nolock) ON p.ppriceid = k.ppriceid
                WHERE s.sub_check not in (4,5,7,8,9)
				and k.imei = #{imei}
          ) t order by t.type desc
    </select>
    <select id="getServiceSub" resultType="com.jiuji.oa.afterservice.sub.po.Sub">
        SELECT s.tradedate FROM sub s with(nolock)
        inner JOIN basket b with(nolock) ON b.sub_id = s.sub_id
        WHERE b.giftid=#{basketId} AND ISNULL(b.isdel,0)= 0 AND b.ppriceid = ${ppid}                                                                                                     AND EXISTS(SELECT * FROM servicerecord with(nolock) WHERE basket_id = b.basket_id)
    </select>

    <select id="getShieldService" resultType="com.jiuji.oa.afterservice.bigpro.vo.MemberSubVO">
        SELECT DISTINCT TOP 50
        s.sub_id,
        be.union_pro_imei AS imei,
        p.product_name,
        p.product_color
        FROM
        sub s WITH ( nolock )
        LEFT JOIN dbo.basket_extend be WITH ( nolock )
        ON EXISTS ( SELECT 1 FROM basket b WITH ( nolock ) WHERE b.basket_id = be.basket_id AND b.sub_id = s.sub_id )
        LEFT JOIN dbo.productinfo p WITH ( nolock ) ON p.ppriceid= be.union_pro_ppid
        INNER JOIN ServiceRecord sr WITH ( nolock ) ON sr.sub_id = s.sub_id
        AND isnull( sr.basket_idBind, 0 ) = 0
        AND isnull( sr.bargain_basket_id_bind, 0 ) = 0
        WHERE
        s.sub_check = 3
        AND s.sub_mobile = #{key}
    </select>
</mapper>
