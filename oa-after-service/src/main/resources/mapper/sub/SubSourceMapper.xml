<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.sub.dao.SubSourceMapper">

    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.sub.po.SubSource">
            <id property="id" column="id" jdbcType="INTEGER"/>
            <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
            <result property="name" column="name" jdbcType="VARCHAR"/>
            <result property="isDel" column="is_del" jdbcType="TINYINT"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="createUsername" column="create_username" jdbcType="VARCHAR"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
            <result property="subSourceRv" column="sub_source_rv" jdbcType="TIMESTAMP"/>
            <result property="xtenant" column="xtenant" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,parent_id,name,
        is_del,create_time,create_username,
        update_time,xtenant,sort_value
    </sql>
    <select id="selectSubSourceByXtenant" resultType="com.jiuji.oa.afterservice.sub.po.SubSource">
        select
            id,
            isnull(parent_id,0) as parentId,
            name,
            is_del,
            create_time,
            create_username,
            update_time,
            xtenant,
            sort_value
        from sub_source with(nolock)
        where isnull(is_del,0) = 0
        and xtenant = #{xtenant}
    </select>
</mapper>
