<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.sub.dao.SubReturnCheckBeforeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.sub.po.SubReturnCheckBefore">
        <id column="id" property="id"/>
        <result column="sub_id" property="subId"/>
        <result column="sub_type" property="subType"/>
        <result column="comment" property="comment"/>
        <result column="check_user" property="checkUser"/>
        <result column="check_remark" property="checkRemark"/>
        <result column="is_del" property="isDel"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
    </resultMap>

    <select id="getReturnCheckListPage" resultType="com.jiuji.oa.afterservice.sub.vo.res.ReturnCheckListRes">
        select s.id,s.sub_id,s.sub_type,s.comment,s.check_user,s.check_remark,s.create_user as submitUser,s.create_time
        as submitTime,s.check_time,s.status,s.operate_type,
        a.area,s.area_id
        from sub_return_check_before s with(nolock)
        left join areainfo a with(nolock) on s.area_id = a.id
        where isnull(s.is_del,0) = 0
        <if test="req.subId != null and req.subId != 0">
            and s.sub_id = #{req.subId}
        </if>
        <if test="req.submitUser != null and req.submitUser != ''">
            and s.create_user = #{req.submitUser}
        </if>
        <if test="req.checkUser != null and req.checkUser != ''">
            and s.check_user = #{req.checkUser}
        </if>
        <if test="req.status != null and req.status != 0">
            and s.status = #{req.status}
            <if test="req.status != 1">
                and s.check_user = #{ch999User}
            </if>
        </if>
    </select>
    <select id="getLastCheckStatus" resultType="java.lang.Integer">
        SELECT top 1 b.status FROM dbo.sub_return_check_before b WITH(NOLOCK)
        WHERE b.sub_id = #{subId} AND b.sub_type = #{subType}
                  AND b.operate_type = #{operateType} AND b.create_user_id = #{createUserId} ORDER BY b.id DESC
    </select>
</mapper>
