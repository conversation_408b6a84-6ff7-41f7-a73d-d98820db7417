<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.webaddreservation.mapper.WebAddReservationMapper">

    <select id="getRepairPartsList"
            resultType="com.jiuji.oa.afterservice.webaddreservation.vo.RepairPartsInfoVO">
        select p2.productid productId,
               p2.ppriceid ppid,
               p2.product_name productName,
               p2.product_color productColor,
               p2.memberprice price,
               isnull((select leftCount from product_kc kc with(nolock) where kc.ppriceid = p2.ppriceid and areaid = #{areaId}),0) leftCount,
               isnull((select leftCount leftCount from product_kc kc with(nolock) where kc.ppriceid = p2.ppriceid and areaid = 16),0) dcLeftCount,
               p2.que
        from productinfo p1 with(nolock)
        join productinfo p2 with(nolock) on p1.productid = p2.productid
        where p1.ppriceid in
        <foreach collection="ppidList" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>
