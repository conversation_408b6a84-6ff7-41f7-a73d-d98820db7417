<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.jiuji.oa.afterservice.smallpro.yearcardtransfer.mapper.YearPackageTransferMapper">
    <select id="selectTransferDetailByCode"
            resultType="com.jiuji.oa.afterservice.smallpro.yearcardtransfer.dto.YearPackageTransferDetailDto">
        SELECT
            t.id,
            t.transfer_code transferCode,
            t.sender_id AS senderId,
            t.receiver_id AS receiverId,
            isnull(ru.realname,ru.UserName) AS receiverUserName,
            ru.mobile receiverMobile,
            t.status,
            t.start_time as startTime,
            t.end_time endTime,
            t.create_time createTime,
            tc.basket_idBind bindBasketId
        FROM year_package_transfer t with(nolock)
                 LEFT JOIN BBSXP_Users ru with(nolock) ON t.receiver_id = ru.id
                 LEFT JOIN tiemoCard tc with(nolock) ON t.origin_card_id = tc.id
        WHERE t.transfer_code = #{transferCode}
    </select>
</mapper>
