<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.electronicTicket.mapper.AfterElectronicTicketMapper">

    <select id="selectpiaoProductInfo"
            resultType="com.jiuji.oa.afterservice.electronicTicket.entity.PiaoProductInfo">
        select top 1 p.fileid, t.id as piaoid, t.name, t.customType, t.kind, t.flag
        from dbo.tax_piao t
            with (nolock)
         left join dbo.electronPiao p
        with (nolock) on t.id = p.piaoid
        where t.flag in (0, 1, 2, 3, 4, 8)
          and isnull(t.type_
            , 0) = 1
          and exists(select 1
            from dbo.piaoProductInfo f with (nolock)
            where t.id = f.piaoid
          and f.sub_id = #{id})

    </select>
</mapper>
