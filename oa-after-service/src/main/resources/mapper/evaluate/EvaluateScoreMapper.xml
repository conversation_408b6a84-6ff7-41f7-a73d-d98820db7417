<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.evaluate.dao.EvaluateScoreDao">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.evaluate.po.EvaluateScore">
        <id column="Id" property="id"/>
        <result column="EvaluateId" property="evaluateId"/>
        <result column="RelateCh999Id" property="relateCh999Id"/>
        <result column="Score" property="score"/>
        <result column="Job" property="job"/>
        <result column="sub_id" property="subId"/>
        <result column="type_" property="type"/>
        <result column="userid" property="userid"/>
        <result column="sub_check" property="subCheck"/>
        <result column="mobileCount" property="mobileCount"/>
        <result column="uPrices" property="uPrices"/>
        <result column="udate" property="udate"/>
        <result column="dtime" property="dtime"/>
        <result column="areaid" property="areaid"/>
        <result column="EvaluateTagComment" property="evaluateTagComment"/>
        <result column="EvaluateTagScore" property="evaluateTagScore"/>
        <result column="wuxiao" property="wuxiao"/>
        <result column="isAppeal" property="isAppeal"/>
        <result column="kpXishu" property="kpXishu"/>
    </resultMap>

    <insert id="insertEvaluateScore" useGeneratedKeys="true" keyProperty="po.id">
        insert into ${officeName}..EvaluateScore (RelateCh999Id ,Job ,sub_id ,type_ ,userid ,dtime,areaid)
        values (#{po.relateCh999Id},#{po.job},#{po.subId},#{po.type},#{po.userid},#{po.dtime},#{po.areaid})
    </insert>

    <select id="countCallCenter" resultType="com.jiuji.oa.afterservice.evaluate.bo.CallCenterEvaluateScoreBO">
        SELECT
        SUM(CASE WHEN es.Score > 3 THEN 1 ELSE 0 END) goodCount,
        SUM(CASE WHEN es.Score <![CDATA[<=]]> 3 AND es.Score > 0 THEN 1 ELSE 0 END) badCount,
        es.RelateCh999Id ch999Id
        FROM EvaluateScore es WITH(NOLOCK)
        INNER JOIN Evaluate e WITH(NOLOCK) ON es.EvaluateId = e.Id
        WHERE ISNULL(e.isInvalid,0) = 0
        AND e.dtime BETWEEN #{startTime} AND #{endTime}
        AND ISNULL(es.wuxiao,0) = 0
        AND es.RelateCh999Id in
        <foreach collection="ch999Ids" item="ch999Id" separator="," open="(" close=")">
            #{ch999Id}
        </foreach>
        GROUP BY es.RelateCh999Id
    </select>

    <select id="countCallCenterOnlineOrder" resultType="com.jiuji.oa.afterservice.evaluate.bo.OnlineOrderCountBO">
        SELECT COUNT(DISTINCT os.sub_id) count, u.ch999_id ch999Id
        FROM dbo.onlineSub os WITH(NOLOCK)
        INNER JOIN dbo.sub s WITH(NOLOCK) ON os.sub_id=s.sub_id
        INNER JOIN dbo.ch999_user u WITH(NOLOCK) ON s.Inuser = u.ch999_name
        WHERE s.sub_check=3
        AND s.tradeDate1 BETWEEN #{startTime} AND #{endTime}
        AND u.ch999_id in
        <foreach collection="ch999Ids" item="ch999Id" separator="," open="(" close=")">
            #{ch999Id}
        </foreach>
        GROUP BY u.ch999_id
    </select>

    <select id="listSoftTakeOrderByUserId" resultType="com.jiuji.oa.afterservice.evaluate.bo.SoftTakeOrderBO">
        SELECT TOP 200
            id,
            sub_id subId,
            ishuishou isHuishou,
            modidate modiDate,
            imei
        FROM dbo.msoft WITH(NOLOCK)
        WHERE userid = #{userId}
        ORDER BY id DESC
    </select>

    <select id="getPingjiaVoByOrderId"
            resultType="com.jiuji.oa.afterservice.bigpro.vo.res.YixiugeShouhouPingjiaVo">
        SELECT Id orderNo,CommendedScore orderRate,commendedRemark orderComment,dtime modifyTime FROM dbo.Evaluate WITH(NOLOCK)
        WHERE SubId = #{orderId} and EvaluateType = 3
    </select>


</mapper>
