<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.channel.dao.ShouhouChannelMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.channel.po.ShouhouChannel">
        <id column="id" property="id"/>
        <result column="channel_id" property="channelId"/>
        <result column="shouhou_title" property="shouhouTitle"/>
        <result column="brand" property="brand"/>
        <result column="work_time" property="workTime"/>
        <result column="longitude " property="longitude "/>
        <result column="latitude" property="latitude"/>
        <result column="check_period" property="checkPeriod"/>
        <result column="check_material" property="checkMaterial"/>
        <result column="delivery_days_with_pj" property="deliveryDaysWithPj"/>
        <result column="delivery_days_no_pj" property="deliveryDaysNoPj"/>
        <result column="material_in_guarantee" property="materialInGuarantee"/>
        <result column="delivery_charger" property="deliveryCharger"/>
        <result column="delivery_charger_id" property="deliveryChargerId"/>
        <result column="repaire_days_with_pj" property="repaireDaysWithPj"/>
        <result column="repaire_days_no_pj" property="repaireDaysNoPj"/>
        <result column="attention_item" property="attentionItem"/>
        <result column="cooperation_status" property="cooperationStatus"/>
    </resultMap>


    <select id="getChannelListPage" resultType="com.jiuji.oa.afterservice.channel.vo.res.ShouhouChannelListRes">
        select o.id,o.company,o.company_jc as companyJc,o.address,o.ispass,o.tel,channel.id as channelId,
        channel.brand,channel.work_time,channel.longitude,channel.latitude,channel.check_period,channel.check_material,channel.delivery_days_with_pj,
        channel.delivery_days_no_pj,channel.material_in_guarantee,channel.delivery_charger,channel.delivery_charger_id,channel.repaire_days_with_pj,channel.repaire_days_no_pj,
        channel.attention_item,channel.cooperation_status
        from Ok3w_qudao o with(nolock)
        left join shouhou_channel channel with(nolock) on o.id = channel.channel_id
        where o.kinds = 8
        <if test="req.cooperationStatus != null">
            and o.ispass = #{req.cooperationStatus}
        </if>
        <if test="req.company != null and req.company != ''">
            and o.company like CONCAT('%',#{req.company},'%')
        </if>
        <if test="req.cityId != null and req.cityId != ''">
            and o.cityid like CONCAT('%',#{req.shouhouTitle},'')
        </if>
        <if test="req.brandId != null and req.brandId != ''">
            and o.brandid = #{req.brandId}
        </if>

    </select>

    <select id="getBrandList" resultType="com.jiuji.oa.afterservice.channel.vo.res.BrandInfo">
        select id as brandId,name from brand with(nolock) where 1=1
        <if test="key != null and key != ''">
            <choose>
                <when test="isNumeric != null and isNumeric == true">
                    and name like CONCAT('%',#{key},'%') or id = #{key}
                </when>
                <otherwise>
                    and name like CONCAT('%',#{key},'%')
                </otherwise>
            </choose>
        </if>
    </select>
</mapper>
