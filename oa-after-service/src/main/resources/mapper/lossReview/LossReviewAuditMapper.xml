<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.lossReview.dao.LossReviewAuditMapper">

    <select id="getLossReviewAuditPage" resultType="com.jiuji.oa.afterservice.lossReview.vo.res.LossReviewAuditPageRes">
        select temp.* from (select distinct a.id,case when isnull(wx.inprice,0.0)+isnull(wx.refunded_price,0.0)-isnull(wx.price,0.0) &lt; 0 or wx.ppriceid &gt; 0 and isnull(wx.price1,0)=0 then 0.0
            else isnull(wx.inprice,0.0)+isnull(wx.refunded_price,0.0)-isnull(wx.price,0.0) end lossAmount,s.id as shouhouId,s.basket_id ,s.ishuish<PERSON>
        ,ISNUll(s.areaid,s.toareaid) as areaId,
        s.baoxiu , s.serviceType,s.ppriceid,s.name as productName,s.feiyong ,
        s.costprice, s.problem ,s.offtime,a.audit_status,a.check_user1,a.dtime1,a.check_user2,a.check_reason2,a.dtime2
        from shouhou s WITH (nolock)
        left JOIN dbo.wxkcoutput wx WITH(NOLOCK,index=index_wxid) ON  wx.wxid = s.id and (wx.stats &lt;&gt; 3 or wx.tui_status = 1)
        inner join loss_review_audit a WITH (nolock) on a.shouhou_id = s.id and a.is_deleted=0
        inner join loss_review_audit_detail d WITH (nolock) on a.id = d.loss_review_audit_id and d.is_deleted=0
        <where>
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 1 ">
                and s.id =#{req.searchValue}
            </if>
            <!--商品id-->
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 2 ">
                and s.name like '%' + #{req.searchValue}+'%'
            </if>
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 3 ">
                and s.inuser = #{req.searchValue}
            </if>
            <if test="req.searchValue != null and req.searchValue != '' and req.searchType == 4 ">
                and s.weixiuren = #{req.searchValue}
            </if>
            <if test="req.startTime != null and req.endTime != null">
                and s.offtime between #{req.startTime} and #{req.endTime}
            </if>
            <if test="req.areaIdList != null and req.areaIdList.size > 0">
                and ISNUll(s.areaid,s.toareaid) in
                <foreach collection="req.areaIdList" item="areaId" open="(" close=")" separator=",">
                    #{areaId}
                </foreach>
            </if>
            <if test="req.auditStatus != null and req.auditStatus.size > 0">
                and (( a.audit_status in
                <foreach collection="req.auditStatus" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>)
                <if test="req.exemptReviewFlag == 1 ">
                    or (a.check_user2 = '免审' and a.check_user1 is not null)
                </if>
                )
            </if>
            <if test="req.errorType != null and req.errorType.size > 0">
                and d.error_type in
                <foreach collection="req.errorType" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.baoxiu != null and req.baoxiu.size > 0">
                and s.baoxiu in
                <foreach collection="req.baoxiu" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.serviceType != null and req.serviceType.size > 0">
                and s.serviceType in
                <foreach collection="req.serviceType" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="req.specialQualityAssurance != null and req.specialQualityAssurance.size > 0">
                and wx.special_quality_assurance in
                <foreach collection="req.specialQualityAssurance" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>) as temp
        where temp.lossAmount>0
    </select>
    <select id="checkPunishSubExist" resultType="java.lang.Integer">
        select 1 from punishSub WITH(NOLOCK) where sub_id=#{punishSubId}
    </select>
</mapper>
