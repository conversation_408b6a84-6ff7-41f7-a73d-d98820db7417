<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jiuji.oa.afterservice.api.dao.TaxPiaoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.jiuji.oa.afterservice.api.po.TaxPiao">
        <id column="id" property="id"/>
        <result column="sub_id" property="subId"/>
        <result column="dtime" property="dtime"/>
        <result column="inuser" property="inuser"/>
        <result column="jingban" property="jingban"/>
        <result column="jingbandtime" property="jingbandtime"/>
        <result column="shuiprice" property="shuiprice"/>
        <result column="kind" property="kind"/>
        <result column="flag" property="flag"/>
        <result column="name" property="name"/>
        <result column="tel" property="tel"/>
        <result column="totalprice" property="totalprice"/>
        <result column="area" property="area"/>
        <result column="productname1" property="productname1"/>
        <result column="danwei1" property="danwei1"/>
        <result column="count1" property="count1"/>
        <result column="price1" property="price1"/>
        <result column="productname2" property="productname2"/>
        <result column="danwei2" property="danwei2"/>
        <result column="count2" property="count2"/>
        <result column="price2" property="price2"/>
        <result column="productname3" property="productname3"/>
        <result column="danwei3" property="danwei3"/>
        <result column="count3" property="count3"/>
        <result column="price3" property="price3"/>
        <result column="productname4" property="productname4"/>
        <result column="danwei4" property="danwei4"/>
        <result column="count4" property="count4"/>
        <result column="price4" property="price4"/>
        <result column="piao_date" property="piaoDate"/>
        <result column="piao_number" property="piaoNumber"/>
        <result column="userid" property="userid"/>
        <result column="cdtime" property="cdtime"/>
        <result column="kind2" property="kind2"/>
        <result column="swid" property="swid"/>
        <result column="dealTime" property="dealTime"/>
        <result column="shenheTime" property="shenheTime"/>
        <result column="wuliuid" property="wuliuid"/>
        <result column="takeWay" property="takeWay"/>
        <result column="raddress" property="raddress"/>
        <result column="type_" property="type"/>
        <result column="areaid" property="areaid"/>
        <result column="company" property="company"/>
        <result column="CreditCode" property="CreditCode"/>
        <result column="companyAddress" property="companyAddress"/>
        <result column="companyBank" property="companyBank"/>
        <result column="rareaId" property="rareaId"/>
        <result column="customType" property="customType"/>
        <result column="BatchID" property="BatchID"/>
        <result column="requestToken" property="requestToken"/>
        <result column="receiveEmail" property="receiveEmail"/>
        <result column="issend" property="issend"/>
        <result column="piaoRemark" property="piaoRemark"/>
        <result column="isweixin" property="isweixin"/>
        <result column="kehuName" property="kehuName"/>
        <result column="rcityid" property="rcityid"/>
        <result column="isAuto" property="isAuto"/>
        <result column="interfaceType" property="interfaceType"/>
        <result column="feeSubId" property="feeSubId"/>
    </resultMap>
    <select id="getInvoiceBySubId" resultType="com.jiuji.oa.afterservice.api.po.TaxPiao">
        select top 1 name,productname1,kind,totalprice,piao_date,areaid,cdtime,flag from tax_piao p with(nolock)
        where isnull(p.flag,0) not in (-1,5,6)
        and (p.sub_id=#{subId}
        or exists(select 1 from dbo.piaoProductInfo r with(nolock) where p.id=r.piaoid and r.sub_id=#{subId} ))
        order by id asc
    </select>

    <select id="getpiaoinfoBySubId" resultType="com.jiuji.oa.afterservice.api.po.TaxPiao">
        select top 1 name,productname1,kind,totalprice,piao_date,areaid,cdtime,flag from tax_piao p with(nolock)
	    left JOIN dbo.piaoProductInfo r with(nolock) on p.id=r.piaoid
        where isnull(p.flag,0) not in (-1,5,6)
        <if test="type != null">
            and isnull(p.type_,0) = #{type}
        </if>
        and (p.sub_id=#{subId} or r.sub_id=#{subId})
    </select>

    <select id="getpiaoinfoByUserIdAndTradedate" resultType="com.jiuji.oa.afterservice.api.po.TaxPiao">
        select top 1 name,productname1,kind,totalprice,piao_date,areaid,cdtime,flag from tax_piao with(nolock)
        where flag != -1 and userid=#{userId}
        and dtime between #{tradeDate} and dateadd(d,5,#{tradeDate})
        order by id desc
    </select>
    <select id="selectTaxPiaoByUserId" resultType="com.jiuji.oa.afterservice.bigpro.vo.TaxPiaoUser">
        select count(1) as invoiceCount,sum(totalprice) as invoiceTotalPrice from dbo.tax_piao with(nolock) where flag in (1,2,3,4) and userid = #{userId} and dtime>=getdate()-90
    </select>
</mapper>
