<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Copyright (c) 2006-2017, Yunnan Sanjiu Network technology Co., Ltd.
  ~
  ~ All rights reserved.
  -->
<!--日志级别以及优先级排序: OFF > FATAL > ERROR > WARN > INFO > DEBUG > TRACE > ALL -->
<!--Configuration后面的status，这个用于设置log4j2自身内部的信息输出，可以不设置，当设置成trace时，你会看到log4j2内部各种详细输出 -->
<!--monitorInterval：Log4j能够自动检测修改配置 文件和重新配置本身，设置间隔秒数 -->
<configuration status="WARN" monitorinterval="30">

    <Properties>
        <!-- OA2 组，日志名为 oajava-* -->
        <Property name="log.elk.service-name">oajava-after</Property>
        <Property name="log.elk.host">************</Property>
        <Property name="log.elk.port">10516</Property>
        <Property name="log.main-package">com.jiuji.oa</Property>
        <!-- 根据项目部署文件进行配置-->
        <Property name="log.path">${sys:user.home}/oa-after-service/logs</Property>

        <!-- 以下的配置项 无特殊情况 不需调整-->
        <Property name="log.filePattern">${log.path}/$${date:yyyy-MM-dd}/</Property>
        <Property name="log.pattern">%d{yyyy-MM-dd HH:mm:ss} %level %c:%L - %m%n</Property>
    </Properties>

    <!--先定义所有的appender -->
    <appenders>

        <!--这个输出控制台的配置 -->
        <console name="Console" target="SYSTEM_OUT">
            <!--输出日志的格式 -->
            <PatternLayout pattern="${log.pattern}"/>
        </console>

        <!--文件会打印出所有信息，这个log每次运行程序会自动清空，由append属性决定，这个也挺有用的，适合临时测试用 -->
        <File name="log" fileName="logs/test.log" append="false">
            <PatternLayout pattern="${log.pattern}"/>
        </File>

        <!-- 这个会打印出所有的info及以下级别的信息，每次大小超过size，则这size大小的日志会自动存入按年份-月份建立的文件夹下面并进行压缩，作为存档 -->
        <RollingFile name="RollingFileInfo" fileName="${log.path}/info.log"
                     filePattern="${log.filePattern}/info-%d{yyyy-MM-dd}-%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch） -->
            <ThresholdFilter level="info" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${log.pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="128 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="180"/>
        </RollingFile>

        <RollingFile name="RollingFileDebug" fileName="${log.path}/debug.log"
                     filePattern="${log.filePattern}/debug-%d{yyyy-MM-dd}-%i.log.gz">
            <!--控制台只输出level及以上级别的信息（onMatch），其他的直接拒绝（onMismatch） -->
            <ThresholdFilter level="debug" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${log.pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="128 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="180"/>
        </RollingFile>

        <RollingFile name="RollingFileWarn" fileName="${log.path}/warn.log"
                     filePattern="${log.filePattern}/warn-%d{yyyy-MM-dd}-%i.log.gz">
            <ThresholdFilter level="warn" onMatch="ACCEPT"
                             onMismatch="DENY"/>
            <PatternLayout pattern="${log.pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="128 MB"/>
            </Policies>
            <!-- DefaultRolloverStrategy属性如不设置，则默认为最多同一文件夹下7个文件，这里设置了20 -->
            <DefaultRolloverStrategy max="180"/>
        </RollingFile>

        <RollingFile name="RollingFileError" fileName="${log.path}/error.log"
                     filePattern="${log.filePattern}/error-%d{yyyy-MM-dd}-%i.log.gz">
            <ThresholdFilter level="error" onMatch="ACCEPT" onMismatch="DENY"/>
            <PatternLayout pattern="${log.pattern}"/>
            <Policies>
                <TimeBasedTriggeringPolicy modulate="true" interval="1"/>
                <SizeBasedTriggeringPolicy size="128 MB"/>
            </Policies>
            <DefaultRolloverStrategy max="180"/>
        </RollingFile>

        <Socket name="elkAppender" host="${log.elk.host}" port="${log.elk.port}" protocol="udp">
            <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="DENY"/>
            <JSONLog4j2Layout singleLine="false" locationInfo="true">
                <UserField>
                    <key>ServiceName</key>
                    <!--                    <value>oajava-orginfo</value>-->
                    <value>${log.elk.service-name}</value>
                </UserField>
                <UserField>
                    <key>TenantId</key>
                    <value>${sys:TenantId}</value>
                </UserField>
            </JSONLog4j2Layout>
        </Socket>

    </appenders>

    <!--然后定义logger，只有定义了logger并引入的appender，appender才会生效 -->
    <loggers>
        <!--过滤掉spring和mybatis的一些无用的DEBUG信息 -->
        <logger name="${log.main-package}" level="INFO"/>
        <logger name="com.baomidou" level="ERROR"/>
        <root level="info">
            <appender-ref ref="Console"/>
            <appender-ref ref="RollingFileInfo"/>
            <appender-ref ref="RollingFileWarn"/>
            <appender-ref ref="RollingFileError"/>
            <appender-ref ref="elkAppender"/>
        </root>
    </loggers>

</configuration>
