consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.99fsm.com/
  upload:
    url: http://**************:9333
instance-zone: 10139
jiuji:
  sys:
    moa: https://moa.99fsm.com
    pc: https://oa.99fsm.com
    xtenant: 10139
  xtenant: 139000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10139:jLWvc675Ig84@***********:27017,***********:27017,***********:27017/ch999oa__10139
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  oa_core:
    dbname: oa_core__10139
    password: EcrIoUp9sQjB
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10139
  oa_nc:
    dbname: oa_nc__10139
    password: ip0qXB6lw8Ni
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10139
  oa_log:
    dbname: oa_log__10139
    password: 'FMtR9hWAaYBr'
    url: tidb.serv.hd.saas.ch999.cn:9383
    username: oa_log__10139
office:
  sys:
    xtenant: 10139
rabbitmq:
  master:
    password: dlYYL
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10139
    vhost: oaAsync__10139
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: YAknG
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10139
    vhost: oa__10139
  oaAsync:
    password: dlYYL
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10139
    vhost: oaAsync__10139
  printer:
    password: pJeEF
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10139
    vhost: printer__10139
redis:
  oa:
    host: ***********
    password: google99
    port: 6398
    url: google99@***********:6398
sms:
  send:
    email:
      url: http://sms.99fsm.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.99fsm.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10139
sqlserver:
  after_write:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  ch999oanew:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  ch999oanewReport:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  ch999oanewHis:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: b1utgQXOSTI3
    port: 1433
    username: office__10139
  oanewWrite:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  office:
    dbname: office__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: b1utgQXOSTI3
    port: 1433
    username: office__10139
  officeWrite:
    dbname: office__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: b1utgQXOSTI3
    port: 1433
    username: office__10139
  smallpro_write:
    dbname: ch999oanew__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "ExJ13KT8rF2L"
    port: 1433
    username: ch999oanew__10139
  web999:
    dbname: web999__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 2n64O#osC^vE
    port: 1433
    username: web999__10139
  ipaddress:
    dbname: ipaddress__10139
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: C4XcomM7gNuj
    port: 1433
    username: ipaddress__10139
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.99fsm.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.99fsm.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333

after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'

apollo:
  url: http://**************:8080
  file: application-after.yml

