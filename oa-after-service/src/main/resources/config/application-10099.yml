consul:
  host: ***********
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.gphone.vip/
  upload:
    url: http://**************:9333
instance-zone: 10099
jiuji:
  sys:
    moa: https://moa.gphone.vip
    pc: https://oa.gphone.vip
    xtenant: 10099
  xtenant: 99000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10099:dWJBy0gw6DMR@***********:27017,***********:27017,***********:27017/ch999oa__10099
  url1: **********************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10099
    password: OaXVZ5IAC3Hx
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10099
  oa_nc:
    dbname: oa_nc__10099
    password: o5KSTxwvaDWq
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10099
office:
  sys:
    xtenant: 10099
rabbitmq:
  master:
    password: aIsDD
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10099
    vhost: oaAsync__10099
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: Ldinj
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10099
    vhost: oa__10099
  oaAsync:
    password: aIsDD
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10099
    vhost: oaAsync__10099
  printer:
    password: RbeoT
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10099
    vhost: printer__10099
redis:
  oa:
    host: ***********
    password: google99
    port: 6386
    url: google99@***********:6386
sms:
  send:
    email:
      url: http://sms.gphone.vip/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.gphone.vip/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10099
sqlserver:
  after_write:
    dbname: ch999oanew__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "kzqdGroznmOr"
    port: 1433
    username: ch999oanew__10099
  ch999oanew:
    dbname: ch999oanew__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "kzqdGroznmOr"
    port: 1433
    username: ch999oanew__10099
  ch999oanewReport:
    dbname: ch999oanew__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "kzqdGroznmOr"
    port: 1433
    username: ch999oanew__10099
  ch999oanewHis:
    dbname: ch999oanew__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "kzqdGroznmOr"
    port: 1433
    username: ch999oanew__10099
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: jIkMPN4jcHA1
    port: 1433
    username: office__10099
  oanewWrite:
    dbname: ch999oanew__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "kzqdGroznmOr"
    port: 1433
    username: ch999oanew__10099
  office:
    dbname: office__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: jIkMPN4jcHA1
    port: 1433
    username: office__10099
  officeWrite:
    dbname: office__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: jIkMPN4jcHA1
    port: 1433
    username: office__10099
  smallpro_write:
    dbname: ch999oanew__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "kzqdGroznmOr"
    port: 1433
    username: ch999oanew__10099
  web999:
    dbname: web999__10099
    host: sqlserver.serv.hd.saas.ch999.cn
    password: ntRRKXS5Qw29
    port: 1433
    username: web999__10099
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.gphone.vip/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.gphone.vip/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'