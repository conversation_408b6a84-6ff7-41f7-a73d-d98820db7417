consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.doubibi.com/
  upload:
    url: http://**************:9333
instance-zone: 10106
jiuji:
  sys:
    moa: https://moa.doubibi.com
    pc: https://oa.doubibi.com
    xtenant: 10106
  xtenant: 106000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10106:HghMc4IVdxX4@***********:27017,***********:27017,***********:27017/ch999oa__10106
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10106
    password: CFKOypJSikzk
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10106
  oa_nc:
    dbname: oa_nc__10106
    password: Umro8G0U1lDQ
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10106
office:
  sys:
    xtenant: 10106
rabbitmq:
  master:
    password: bseTJ
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10106
    vhost: oaAsync__10106
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: wkMPW
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10106
    vhost: oa__10106
  oaAsync:
    password: bseTJ
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10106
    vhost: oaAsync__10106
  printer:
    password: szIhd
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10106
    vhost: printer__10106
redis:
  oa:
    host: ***********
    password: google99
    port: 6392
    url: google99@***********:6392
sms:
  send:
    email:
      url: http://sms.doubibi.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.doubibi.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10106
sqlserver:
  after_write:
    dbname: ch999oanew__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "adDpaVF9cz48"
    port: 1433
    username: ch999oanew__10106
  ch999oanew:
    dbname: ch999oanew__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "adDpaVF9cz48"
    port: 1433
    username: ch999oanew__10106
  ch999oanewReport:
    dbname: ch999oanew__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "adDpaVF9cz48"
    port: 1433
    username: ch999oanew__10106
  ch999oanewHis:
    dbname: ch999oanew__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "adDpaVF9cz48"
    port: 1433
    username: ch999oanew__10106
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: #owqQ3nOFsOV
    port: 1433
    username: office__10106
  oanewWrite:
    dbname: ch999oanew__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "adDpaVF9cz48"
    port: 1433
    username: ch999oanew__10106
  office:
    dbname: office__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: #owqQ3nOFsOV
    port: 1433
    username: office__10106
  officeWrite:
    dbname: office__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: #owqQ3nOFsOV
    port: 1433
    username: office__10106
  smallpro_write:
    dbname: ch999oanew__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "adDpaVF9cz48"
    port: 1433
    username: ch999oanew__10106
  web999:
    dbname: web999__10106
    host: sqlserver.serv.hd.saas.ch999.cn
    password: 2to8BvNmr^0F
    port: 1433
    username: web999__10106
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.doubibi.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.doubibi.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'