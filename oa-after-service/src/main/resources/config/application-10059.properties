## sqlserver:after_write
sqlserver.after_write.host=**************
sqlserver.after_write.port=1433
sqlserver.after_write.dbname=ch999oanew__10059
sqlserver.after_write.username=ch999oanew__10059
sqlserver.after_write.password=ch999oanewRNebB

## sqlserver:ch999oanew
sqlserver.ch999oanew.host=**************
sqlserver.ch999oanew.port=1433
sqlserver.ch999oanew.dbname=ch999oanew__10059
sqlserver.ch999oanew.username=ch999oanew__10059
sqlserver.ch999oanew.password=ch999oanewRNebB
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=**************
sqlserver.ch999oanewReport.port=1433
sqlserver.ch999oanewReport.dbname=ch999oanew__10059
sqlserver.ch999oanewReport.username=ch999oanew__10059
sqlserver.ch999oanewReport.password=ch999oanewRNebB

## sqlserver:office
sqlserver.office.host=**************
sqlserver.office.port=1433
sqlserver.office.dbname=office__10059
sqlserver.office.username=office__10059
sqlserver.office.password=officeKsRTr

## sqlserver:web99
sqlserver.web999.host=**************
sqlserver.web999.port=1433
sqlserver.web999.dbname=web999__10059
sqlserver.web999.username=web999__10059
sqlserver.web999.password=web999lQkXF

## mysql:oa_core
mysql.oa_core.url=**************:3306
mysql.oa_core.dbname=oa_core__10059
mysql.oa_core.username=oa_core__10059
mysql.oa_core.password=oa_coreDXa

## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=**************
sqlserver.ch999oanewHis.port=1433
sqlserver.ch999oanewHis.dbname=ch999oanew__10059
sqlserver.ch999oanewHis.username=ch999oanew__10059
sqlserver.ch999oanewHis.password=ch999oanewRNebB

## midl
consul.host=**************
consul.port=8500

## redis
redis.oa.host=**************
redis.oa.port=6402
redis.oa.password=google99
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}

## rabbitmq
rabbitmq.oaAsync.url=**************
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.vhost=oaAsync__10059
rabbitmq.oaAsync.username=oaAsync__10059
rabbitmq.oaAsync.password=xOGaN

mongodb.url1=*********************************************************************
url.source.path=i18n/url
logging.config.path=classpath:log/log4j2-saas.xml

## image
image.upload.url=
image.del.url=
image.select.url=

spring.cloud.consul.discovery.instance-zone=10059
instance-zone=10059


after.lmstfy.host= lmstfy.service.ch999.cn
after.lmstfy.token= 01GP2BFHSGQESTJ151AFN4TP7J