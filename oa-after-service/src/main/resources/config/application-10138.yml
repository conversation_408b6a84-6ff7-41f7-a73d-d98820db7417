consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.myiphone.vip/
  upload:
    url: http://**************:9333
instance-zone: 10138
jiuji:
  sys:
    moa: https://moa.myiphone.vip
    pc: https://oa.myiphone.vip
    xtenant: 10138
  xtenant: 138000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10138:r6MJElq2AhW3@**************:27017,**************:27017,**************:27017/ch999oa__10138
  url1: *******************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  oa_core:
    dbname: oa_core__10138
    password: #aBILZ#6BXiz
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_core__10138
  oa_nc:
    dbname: oa_nc__10138
    password: LHO8Z6ERHdMB
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_nc__10138
office:
  sys:
    xtenant: 10138
rabbitmq:
  master:
    password: xKDBH
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10138
    vhost: oaAsync__10138
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: jJzbS
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oa__10138
    vhost: oa__10138
  oaAsync:
    password: xKDBH
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10138
    vhost: oaAsync__10138
  printer:
    password: gslhr
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: printer__10138
    vhost: printer__10138
redis:
  oa:
    host: **************
    password: google99
    port: 6408
    url: google99@**************:6408
sms:
  send:
    email:
      url: http://sms.myiphone.vip/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.myiphone.vip/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10138
sqlserver:
  after_write:
    dbname: ch999oanew__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "3ak9Xf5woxgg"
    port: 1433
    username: ch999oanew__10138
  ch999oanew:
    dbname: ch999oanew__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "3ak9Xf5woxgg"
    port: 1433
    username: ch999oanew__10138
  ch999oanewReport:
    dbname: ch999oanew__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "3ak9Xf5woxgg"
    port: 1433
    username: ch999oanew__10138
  ch999oanewHis:
    dbname: ch999oanew__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "3ak9Xf5woxgg"
    port: 1433
    username: ch999oanew__10138
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: AFRa#R5YzEhh
    port: 1433
    username: office__10138
  oanewWrite:
    dbname: ch999oanew__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "3ak9Xf5woxgg"
    port: 1433
    username: ch999oanew__10138
  office:
    dbname: office__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: AFRa#R5YzEhh
    port: 1433
    username: office__10138
  officeWrite:
    dbname: office__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: AFRa#R5YzEhh
    port: 1433
    username: office__10138
  smallpro_write:
    dbname: ch999oanew__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "3ak9Xf5woxgg"
    port: 1433
    username: ch999oanew__10138
  web999:
    dbname: web999__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: JOb2Kw5CqMBb
    port: 1433
    username: web999__10138
  ipaddress:
    dbname: ipaddress__10138
    host: sqlserver.serv.xn.saas.ch999.cn
    password: 5b1fLWGoWXOd
    port: 1433
    username: ipaddress__10138
url:
  delImgUrl: http://**************:5083
  oa-push-info: http://inwcf.myiphone.vip/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.myiphone.vip/
  source:
      path: i18n/url
  uploadImgUrl: http://**************:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'