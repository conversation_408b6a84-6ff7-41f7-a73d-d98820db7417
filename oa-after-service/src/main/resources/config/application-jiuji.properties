# data1 sqlserver
sqlserver.data1.host=master.web.sqlserver.service.ch999.cn
sqlserver.data1.port=1433
## data2 sqlserver
sqlserver.data2.host=data2.ch999.cn
sqlserver.data2.port=1433
## business db
sqlserver.data7.host=data7.ch999.cn
sqlserver.data7.port=1433
# data6 sqlserver
sqlserver.data6.host=data6.ch999.cn
sqlserver.data6.port=1433
## oareadnodes db
sqlserver.oareadnodes.host=readnodes.oamssql.serv.ch999.cn
sqlserver.oareadnodes.port=1433
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.oareadnodes.host}
sqlserver.ch999oanew.port=${sqlserver.oareadnodes.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=ch999oanewGreen
sqlserver.ch999oanew.password=SMns^!N@)!@y@#$dOpV3
## sqlserver:ch999oanew2
sqlserver.ch999oanew2.host=*************
sqlserver.ch999oanew2.port=1433
sqlserver.ch999oanew2.dbname=ch999oanew
sqlserver.ch999oanew2.username=oaUserChwM
sqlserver.ch999oanew2.password=o!@$^#!OnFkMgf
sqlserver.ch999oanew2.is-enable=true

## sqlserver:office2
sqlserver.office2.host=*************
sqlserver.office2.port=1433
sqlserver.office2.dbname=office
sqlserver.office2.username=officeUserCg
sqlserver.office2.password=P$%*Jgd@#%!Bj$
sqlserver.office2.is-enable=true
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.oareadnodes.host}
sqlserver.ch999oanewReport.port=${sqlserver.oareadnodes.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=ch999oanewGreen
sqlserver.ch999oanewReport.password=SMns^!N@)!@y@#$dOpV3
## sqlserver:office
sqlserver.office.host=${sqlserver.oareadnodes.host}
sqlserver.office.port=${sqlserver.oareadnodes.port}
sqlserver.office.dbname=office
sqlserver.office.username=officeGreen
sqlserver.office.password=MhS@#$KhEW#$Hgs#$WsA
## sqlserver:web99
sqlserver.web999.host=${sqlserver.data1.host}
sqlserver.web999.port=${sqlserver.data1.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=web999Green
sqlserver.web999.password=A7b!3e@5h#Y9k$1mP2^q
## sqlserver:after_write
sqlserver.after_write.host=${sqlserver.data6.host}
sqlserver.after_write.port=${sqlserver.data6.port}
sqlserver.after_write.dbname=ch999oanew
sqlserver.after_write.username=9jioa
sqlserver.after_write.password=9ji!D#%!$AWWFG!#
## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=his.sqlserver.ch999.cn
sqlserver.ch999oanewHis.port=1433
sqlserver.ch999oanewHis.dbname=ch999oahis
sqlserver.ch999oanewHis.username=ch999oahis
sqlserver.ch999oanewHis.password=VS35!Q7@CDGwFF91
## sqlserver:ipaddress
sqlserver.ipaddress.host=*************
sqlserver.ipaddress.port=1433
sqlserver.ipaddress.dbname=ipaddress
sqlserver.ipaddress.username=ipaddress
sqlserver.ipaddress.password=google__
## mysql
mysql.url=master.main.mysql.service.ch999.cn:4306

redis.oa.host=master.main.redis.service.ch999.cn
redis.oa.port=6379
redis.oa.password=JiujiOa2020
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
## \u65E5\u5FD7
logging.config.path=classpath:log/log4j2-jiuji.xml
## mongoDB
mongodb.url1=*****************************************************
## spring.autoconfigure.exclude
#\uFFFD\u2CBF\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u013C\uFFFD\uFFFD\uFFFD\u05B7
url.source.path=i18n/url,i18n/constants
# rabbitMq-oaAsync
rabbitmq.oaAsync.url=*************
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.username=oaAsync
rabbitmq.oaAsync.password=oaAsyncpwd
rabbitmq.oaAsync.vhost=oaAsync
# rabbitMq-oa
rabbitmq.oa.url=master.main.rabbitmq.service.ch999.cn
rabbitmq.oa.port=5672
rabbitmq.oa.username=oa
rabbitmq.oa.password=ch999
rabbitmq.oa.vhost=oa
## mysql:oa_core
mysql.oa_core.url=master.main.mysql8.service.ch999.cn:3306
mysql.oa_core.dbname=oa_core
mysql.oa_core.username=oa_core
mysql.oa_core.password=oa_core00&*(
## mysql:oa_nc
mysql.oa_nc.url=master.main.mysql8.service.ch999.cn:3306
mysql.oa_nc.dbname=oa_nc
mysql.oa_nc.username=oa_nc
mysql.oa_nc.password=oa_nc#$%
## mysql:oa_log
mysql.oa_log.url=main.tidb.ch999.cn:9383
mysql.oa_log.dbname=oa_log
mysql.oa_log.username=oa_log
mysql.oa_log.password=ueSCNX3dIoM

## image
image.upload.url=http://*************:9333
image.del.url=http://*************:5083
image.select.url=https://img2.ch999img.com/

consul.host=*************
consul.port=8500
spring.cloud.consul.discovery.instance-zone=9ji
instance-zone=9ji

after.lmstfy.host=lmstfy.service.ch999.cn
after.lmstfy.token=01GP2BFHSGQESTJ151AFN4TP7J


apollo.url=http://*************:8010,http://**************:8010,http://**************:8010
apollo.file=application-after.yml
yi.dong.yi_xiu_ge.url=https://yn.10086.cn/service/openapi/opensource/api.json
yi.dong.yi_xiu_ge.app_id=8710150
yi.dong.yi_xiu_ge.secret=OS!672ybt)70G_hP(YOV%54G[p{0b%22

dynamic.datasource.oanew_his_write.url=*****************************************************************************************_${spring.application.name}
dynamic.datasource.oanew_his_write.username=ch999oahisManage
dynamic.datasource.oanew_his_write.password=ch999oahisManage
dynamic.datasource.oanew_his_write.type=${sqlserver.datasource.type}
dynamic.datasource.oanew_his_write.driver-class-name=${sqlserver.datasource.driver-class-name}

mqtt.host=tcp://iot.9xun.com:1883
mqtt.username=admin
mqtt.clientId=oa-afterservice-${random.value}
mqtt.password=ch999
mqtt.timeout=1000
mqtt.keepalive=20  
mqtt.topic=afterservice-default-topic

jiuji.sys.moa=https://moa.9ji.com
jiuji.sys.pc=https://oa.9ji.com
jiuji.sys.web=https://www.9ji.com
jiuji.sys.inWcf=http://inwcf.ch999.cn
jiuji.sys.oaWcf=http://oawcf2.ch999.cn

