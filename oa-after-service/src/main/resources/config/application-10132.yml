consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.chixin.cn/
  upload:
    url: http://**************:9333
instance-zone: 10132
jiuji:
  sys:
    moa: https://moa.chixin.cn
    pc: https://oa.chixin.cn
    inWcf: http://inwcf.chixin.cn
    oaWcf: http://inwcf2.chixin.cn
    xtenant: 10132
  xtenant: 132000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10132:9eDGdnjHcCsg@***********:27017,***********:27017,***********:27017/ch999oa__10132
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  oa_core:
    dbname: oa_core__10132
    password: Vyc9LxukAHCn
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10132
  oa_nc:
    dbname: oa_nc__10132
    password: wWNeK3xetq8q
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10132
  oa_log:
    dbname: oa_log__10132
    password: 'ptuuPfjQxznf'
    url: tidb.serv.hd.saas.ch999.cn:9383
    username: oa_log__10132
office:
  sys:
    xtenant: 10132
rabbitmq:
  master:
    password: AqoNX
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10132
    vhost: oaAsync__10132
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: yblxB
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10132
    vhost: oa__10132
  oaAsync:
    password: AqoNX
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10132
    vhost: oaAsync__10132
  printer:
    password: qJtXB
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10132
    vhost: printer__10132
redis:
  oa:
    host: ***********
    password: google99
    port: 6397
    url: google99@***********:6397
sms:
  send:
    email:
      url: http://sms.chixin.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.chixin.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10132
sqlserver:
  after_write:
    dbname: ch999oanew__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "CDpqU0adYGsI"
    port: 1433
    username: ch999oanew__10132
  ch999oanew:
    dbname: ch999oanew__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "CDpqU0adYGsI"
    port: 1433
    username: ch999oanew__10132
  ch999oanewReport:
    dbname: ch999oanew__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "CDpqU0adYGsI"
    port: 1433
    username: ch999oanew__10132
  ch999oanewHis:
    dbname: ch999oanew__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "CDpqU0adYGsI"
    port: 1433
    username: ch999oanew__10132
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: IluzRCkOLVl2
    port: 1433
    username: office__10132
  oanewWrite:
    dbname: ch999oanew__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "CDpqU0adYGsI"
    port: 1433
    username: ch999oanew__10132
  office:
    dbname: office__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: IluzRCkOLVl2
    port: 1433
    username: office__10132
  officeWrite:
    dbname: office__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: IluzRCkOLVl2
    port: 1433
    username: office__10132
  smallpro_write:
    dbname: ch999oanew__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "CDpqU0adYGsI"
    port: 1433
    username: ch999oanew__10132
  web999:
    dbname: web999__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 8BairFpYnd4W
    port: 1433
    username: web999__10132
  ipaddress:
    dbname: ipaddress__10132
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: Ucm6qU#YkZF0
    port: 1433
    username: ipaddress__10132
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.chixin.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.chixin.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333

after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'

apollo:
  url: http://**************:8080
  file: application-after.yml

mqtt:
  host: tcp://iot.9xun.com:1883
  topic: afterservice-default-topic
  clientId: oa-afterservice-${random.value}
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20


