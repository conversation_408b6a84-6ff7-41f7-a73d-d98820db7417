consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.stsmt.com/
  upload:
    url: http://**************:9333
instance-zone: 10128
jiuji:
  sys:
    moa: https://moa.stsmt.com
    pc: https://oa.stsmt.com
    xtenant: 10128
  xtenant: 128000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10128:f2sKYGP71UcO@************:27017,************:27017,************:27017/ch999oa__10128
  url1: *************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10128
    password: v0I35LAJZhhd
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_core__10128
  oa_nc:
    dbname: oa_nc__10128
    password: y9ey9^CUHQTK
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_nc__10128
office:
  sys:
    xtenant: 10128
rabbitmq:
  master:
    password: XSvWz
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10128
    vhost: oaAsync__10128
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: qkMmh
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oa__10128
    vhost: oa__10128
  oaAsync:
    password: XSvWz
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10128
    vhost: oaAsync__10128
  printer:
    password: bLglE
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: printer__10128
    vhost: printer__10128
redis:
  oa:
    host: ************
    password: google99
    port: 6384
    url: google99@************:6384
sms:
  send:
    email:
      url: http://sms.stsmt.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.stsmt.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10128
sqlserver:
  after_write:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  ch999oanew:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  ch999oanewReport:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  ch999oanewHis:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: qSy9i7FhCR5j
    port: 1433
    username: office__10128
  oanewWrite:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  office:
    dbname: office__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: qSy9i7FhCR5j
    port: 1433
    username: office__10128
  officeWrite:
    dbname: office__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: qSy9i7FhCR5j
    port: 1433
    username: office__10128
  smallpro_write:
    dbname: ch999oanew__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "fhv^4jr7xhuZ"
    port: 1433
    username: ch999oanew__10128
  web999:
    dbname: web999__10128
    host: sqlserver.serv.hn.saas.ch999.cn
    password: 4fuqeHPfb3Os
    port: 1433
    username: web999__10128
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.stsmt.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.stsmt.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'