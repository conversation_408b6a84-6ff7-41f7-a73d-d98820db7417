consul:
  host: ***********
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.919wx.com/
  upload:
    url: http://**************:9333
instance-zone: 10109
jiuji:
  sys:
    moa: https://moa.919wx.com
    pc: https://oa.919wx.com
    xtenant: 10109
  xtenant: 109000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10109:IaWQJDpBcGtE@***********:27017,***********:27017,***********:27017/ch999oa__10109
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10109
    password: B4x5zqsWpDne
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10109
  oa_nc:
    dbname: oa_nc__10109
    password: kd7QdeDfBuRR
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10109
office:
  sys:
    xtenant: 10109
rabbitmq:
  master:
    password: lVLDE
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10109
    vhost: oaAsync__10109
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: FQsYD
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10109
    vhost: oa__10109
  oaAsync:
    password: lVLDE
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10109
    vhost: oaAsync__10109
  printer:
    password: JGhjY
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10109
    vhost: printer__10109
redis:
  oa:
    host: ***********
    password: google99
    port: 6395
    url: google99@***********:6395
sms:
  send:
    email:
      url: http://sms.919wx.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.919wx.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10109
sqlserver:
  after_write:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  ch999oanew:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  ch999oanewReport:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  ch999oanewHis:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: mIG3Ilbd6DVy
    port: 1433
    username: office__10109
  oanewWrite:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  office:
    dbname: office__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: mIG3Ilbd6DVy
    port: 1433
    username: office__10109
  officeWrite:
    dbname: office__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: mIG3Ilbd6DVy
    port: 1433
    username: office__10109
  smallpro_write:
    dbname: ch999oanew__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "9ZZPrmUfqQ4G"
    port: 1433
    username: ch999oanew__10109
  web999:
    dbname: web999__10109
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: xk8kTAOuVIFV
    port: 1433
    username: web999__10109
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.919wx.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.919wx.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'