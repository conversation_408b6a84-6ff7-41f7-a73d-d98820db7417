consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.hnjst.com/
  upload:
    url: http://**************:9333
instance-zone: 10096
jiuji:
  sys:
    moa: https://moa.hnjst.com
    pc: https://oa.hnjst.com
    xtenant: 10096
  xtenant: 96000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10096:AF1Wr5w86Rrf@************:27017,************:27017,************:27017/ch999oa__10096
  url1: *************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10096
    password: hIp0KliqbRLt
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_core__10096
  oa_nc:
    dbname: oa_nc__10096
    password: SVhRHQ4OBd74
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_nc__10096
office:
  sys:
    xtenant: 10096
rabbitmq:
  master:
    password: RKGiA
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10096
    vhost: oaAsync__10096
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: Yljfw
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oa__10096
    vhost: oa__10096
  oaAsync:
    password: RKGiA
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10096
    vhost: oaAsync__10096
  printer:
    password: vNTqq
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: printer__10096
    vhost: printer__10096
redis:
  oa:
    host: ************
    password: google99
    port: 6383
    url: google99@************:6383
sms:
  send:
    email:
      url: http://sms.hnjst.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.hnjst.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10096
sqlserver:
  after_write:
    dbname: ch999oanew__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "NCk4WldE1FjK"
    port: 1433
    username: ch999oanew__10096
  ch999oanew:
    dbname: ch999oanew__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "NCk4WldE1FjK"
    port: 1433
    username: ch999oanew__10096
  ch999oanewReport:
    dbname: ch999oanew__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "NCk4WldE1FjK"
    port: 1433
    username: ch999oanew__10096
  ch999oanewHis:
    dbname: ch999oanew__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "NCk4WldE1FjK"
    port: 1433
    username: ch999oanew__10096
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: 8m6VMzNpBUvU
    port: 1433
    username: office__10096
  oanewWrite:
    dbname: ch999oanew__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "NCk4WldE1FjK"
    port: 1433
    username: ch999oanew__10096
  office:
    dbname: office__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: 8m6VMzNpBUvU
    port: 1433
    username: office__10096
  officeWrite:
    dbname: office__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: 8m6VMzNpBUvU
    port: 1433
    username: office__10096
  smallpro_write:
    dbname: ch999oanew__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "NCk4WldE1FjK"
    port: 1433
    username: ch999oanew__10096
  web999:
    dbname: web999__10096
    host: sqlserver.serv.hn.saas.ch999.cn
    password: 6e8WIrHniIri
    port: 1433
    username: web999__10096
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.hnjst.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.hnjst.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'