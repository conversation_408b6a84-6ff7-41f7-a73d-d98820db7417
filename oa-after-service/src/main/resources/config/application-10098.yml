consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.xinzhonglian.cn/
  upload:
    url: http://**************:9333
instance-zone: 10098
jiuji:
  sys:
    moa: https://moa.xinzhonglian.cn
    pc: https://oa.xinzhonglian.cn
    xtenant: 10098
  xtenant: 98000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10098:6Y9PR7Sctiqh@***********:27017,***********:27017,***********:27017/ch999oa__10098
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  oa_core:
    dbname: oa_core__10098
    password: FwcbJitLf^jD
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10098
  oa_nc:
    dbname: oa_nc__10098
    password: u#47w8oJfvBY
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10098
  oa_log:
    dbname: oa_log__10098
    password: 'NJ1oZl4AwslY'
    url: tidb.serv.hd.saas.ch999.cn:9383
    username: oa_log__10098
office:
  sys:
    xtenant: 10098
rabbitmq:
  master:
    password: WicCJ
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10098
    vhost: oaAsync__10098
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: hjPDO
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10098
    vhost: oa__10098
  oaAsync:
    password: WicCJ
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10098
    vhost: oaAsync__10098
  printer:
    password: TgSwm
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10098
    vhost: printer__10098
redis:
  oa:
    host: ***********
    password: google99
    port: 6385
    url: google99@***********:6385
sms:
  send:
    email:
      url: http://sms.xinzhonglian.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.xinzhonglian.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10098
sqlserver:
  after_write:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  ch999oanew:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  ch999oanewReport:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  ch999oanewHis:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: 00cfgHBcF0pP
    port: 1433
    username: office__10098
  oanewWrite:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  office:
    dbname: office__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: 00cfgHBcF0pP
    port: 1433
    username: office__10098
  officeWrite:
    dbname: office__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: 00cfgHBcF0pP
    port: 1433
    username: office__10098
  smallpro_write:
    dbname: ch999oanew__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "68ZKCHzYqb6F"
    port: 1433
    username: ch999oanew__10098
  web999:
    dbname: web999__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: sv3SFZ4g8Nfj
    port: 1433
    username: web999__10098
  ipaddress:
    dbname: ipaddress__10098
    host: sqlserver.serv.hd.saas.ch999.cn
    password: y2DNWzMpm4Kb
    port: 1433
    username: ipaddress__10098
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.xinzhonglian.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.xinzhonglian.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333

after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'

apollo:
  url: http://**************:8080
  file: application-after.yml

