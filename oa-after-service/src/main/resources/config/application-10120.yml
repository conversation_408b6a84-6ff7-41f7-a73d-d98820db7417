consul:
  host: **************
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://scdxt.img.saas.ch999.cn/
  upload:
    url: http://**************:9333
instance-zone: 10120
jiuji:
  sys:
    moa: https://scdxt.moa.saas.ch999.cn
    pc: https://scdxt.oa.saas.ch999.cn
    xtenant: 10120
  xtenant: 120000
logging:
  config:
    path: classpath:log/log4j2-dev.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10120:LucdX2Cj3R7W@**************:27017,**************:27017,**************:27017/ch999oa__10120
  url1: *******************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10120
    password: IaHtF7SE3gs9
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_core__10120
  oa_nc:
    dbname: oa_nc__10120
    password: GEPiuu2XMT#e
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_nc__10120
office:
  sys:
    xtenant: 10120
rabbitmq:
  master:
    password: GISZD
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10120
    vhost: oaAsync__10120
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: jpTLn
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oa__10120
    vhost: oa__10120
  oaAsync:
    password: GISZD
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10120
    vhost: oaAsync__10120
  printer:
    password: efQuW
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: printer__10120
    vhost: printer__10120
redis:
  oa:
    host: **************
    password: google99
    port: 6412
    url: google99@**************:6412
sms:
  send:
    email:
      url: http://scdxt.sms.saas.ch999.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://scdxt.sms.saas.ch999.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10120
sqlserver:
  after_write:
    dbname: ch999oanew__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "PHbwaEcLbJqT"
    port: 1433
    username: ch999oanew__10120
  ch999oanew:
    dbname: ch999oanew__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "PHbwaEcLbJqT"
    port: 1433
    username: ch999oanew__10120
  ch999oanewReport:
    dbname: ch999oanew__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "PHbwaEcLbJqT"
    port: 1433
    username: ch999oanew__10120
  ch999oanewHis:
    dbname: ch999oanew__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "PHbwaEcLbJqT"
    port: 1433
    username: ch999oanew__10120
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: qiPpNDqNuK4S
    port: 1433
    username: office__10120
  oanewWrite:
    dbname: ch999oanew__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "PHbwaEcLbJqT"
    port: 1433
    username: ch999oanew__10120
  office:
    dbname: office__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: qiPpNDqNuK4S
    port: 1433
    username: office__10120
  officeWrite:
    dbname: office__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: qiPpNDqNuK4S
    port: 1433
    username: office__10120
  smallpro_write:
    dbname: ch999oanew__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "PHbwaEcLbJqT"
    port: 1433
    username: ch999oanew__10120
  web999:
    dbname: web999__10120
    host: sqlserver.serv.xn.saas.ch999.cn
    password: wv4KWPv7nk2a
    port: 1433
    username: web999__10120
url:
  delImgUrl: http://**************:5083
  oa-push-info: http://scdxt.inwcf.saas.ch999.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://scdxt.img.saas.ch999.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://**************:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'