consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.tttx.vip/
  upload:
    url: http://**************:9333
instance-zone: 10117
jiuji:
  sys:
    moa: https://moa.tttx.vip
    pc: https://oa.tttx.vip
    xtenant: 10117
  xtenant: 117000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10117:JXFAnLpuALR7@***********:27017,***********:27017,***********:27017/ch999oa__10117
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10117
    password: gUDLOWzgd^U0
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10117
  oa_nc:
    dbname: oa_nc__10117
    password: 6uWY3qZJKw4V
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10117
office:
  sys:
    xtenant: 10117
rabbitmq:
  master:
    password: NMRLU
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10117
    vhost: oaAsync__10117
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: vZhMs
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10117
    vhost: oa__10117
  oaAsync:
    password: NMRLU
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10117
    vhost: oaAsync__10117
  printer:
    password: EuQtA
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10117
    vhost: printer__10117
redis:
  oa:
    host: ***********
    password: google99
    port: 6391
    url: google99@***********:6391
sms:
  send:
    email:
      url: http://sms.tttx.vip/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.tttx.vip/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10117
sqlserver:
  after_write:
    dbname: ch999oanew__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "v6Mn9BPinucQ"
    port: 1433
    username: ch999oanew__10117
  ch999oanew:
    dbname: ch999oanew__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "v6Mn9BPinucQ"
    port: 1433
    username: ch999oanew__10117
  ch999oanewReport:
    dbname: ch999oanew__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "v6Mn9BPinucQ"
    port: 1433
    username: ch999oanew__10117
  ch999oanewHis:
    dbname: ch999oanew__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "v6Mn9BPinucQ"
    port: 1433
    username: ch999oanew__10117
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: FGLfUM7dsUlL
    port: 1433
    username: office__10117
  oanewWrite:
    dbname: ch999oanew__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "v6Mn9BPinucQ"
    port: 1433
    username: ch999oanew__10117
  office:
    dbname: office__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: FGLfUM7dsUlL
    port: 1433
    username: office__10117
  officeWrite:
    dbname: office__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: FGLfUM7dsUlL
    port: 1433
    username: office__10117
  smallpro_write:
    dbname: ch999oanew__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "v6Mn9BPinucQ"
    port: 1433
    username: ch999oanew__10117
  web999:
    dbname: web999__10117
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: tKgByAfCiMRo
    port: 1433
    username: web999__10117
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.tttx.vip/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.tttx.vip/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'