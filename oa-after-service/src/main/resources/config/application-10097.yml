consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.lyjt.vip/
  upload:
    url: http://**************:9333
instance-zone: 10097
jiuji:
  sys:
    moa: https://moa.lyjt.vip
    pc: https://oa.lyjt.vip
    xtenant: 10097
  xtenant: 97000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10097:RQoWd6Y41640@***********:27017,***********:27017,***********:27017/ch999oa__10097
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10097
    password: 7jl6aHeY1WI9
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10097
  oa_nc:
    dbname: oa_nc__10097
    password: TDVGWnkwnciT
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10097
office:
  sys:
    xtenant: 10097
rabbitmq:
  master:
    password: kqwSU
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10097
    vhost: oaAsync__10097
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: Weryb
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10097
    vhost: oa__10097
  oaAsync:
    password: kqwSU
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10097
    vhost: oaAsync__10097
  printer:
    password: NozDF
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10097
    vhost: printer__10097
redis:
  oa:
    host: ***********
    password: google99
    port: 6393
    url: google99@***********:6393
sms:
  send:
    email:
      url: http://sms.lyjt.vip/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.lyjt.vip/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10097
sqlserver:
  after_write:
    dbname: ch999oanew__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "pzTKNOxVaDEk"
    port: 1433
    username: ch999oanew__10097
  ch999oanew:
    dbname: ch999oanew__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "pzTKNOxVaDEk"
    port: 1433
    username: ch999oanew__10097
  ch999oanewReport:
    dbname: ch999oanew__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "pzTKNOxVaDEk"
    port: 1433
    username: ch999oanew__10097
  ch999oanewHis:
    dbname: ch999oanew__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "pzTKNOxVaDEk"
    port: 1433
    username: ch999oanew__10097
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: arYKek16YVV7
    port: 1433
    username: office__10097
  oanewWrite:
    dbname: ch999oanew__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "pzTKNOxVaDEk"
    port: 1433
    username: ch999oanew__10097
  office:
    dbname: office__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: arYKek16YVV7
    port: 1433
    username: office__10097
  officeWrite:
    dbname: office__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: arYKek16YVV7
    port: 1433
    username: office__10097
  smallpro_write:
    dbname: ch999oanew__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: "pzTKNOxVaDEk"
    port: 1433
    username: ch999oanew__10097
  web999:
    dbname: web999__10097
    host: sqlserver.serv.hb.saas.ch999.cn
    password: vzffZHIaHJqq
    port: 1433
    username: web999__10097
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.lyjt.vip/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.lyjt.vip/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'