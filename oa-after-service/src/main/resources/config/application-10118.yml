consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.yongbokeji.cn/
  upload:
    url: http://**************:9333
instance-zone: 10118
jiuji:
  sys:
    moa: https://moa.yongbokeji.cn
    pc: https://oa.yongbokeji.cn
    xtenant: 10118
  xtenant: 118000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10118:3lqV9IM06CDz@***********:27017,***********:27017,***********:27017/ch999oa__10118
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname:
    password:
    url: :3306
    username:
  oa_core:
    dbname: oa_core__10118
    password: 3Cj8#6pUJsIG
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10118
  oa_nc:
    dbname: oa_nc__10118
    password: 2fac1khcc9xu
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10118
office:
  sys:
    xtenant: 10118
rabbitmq:
  master:
    password: dBJEG
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10118
    vhost: oaAsync__10118
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: ZXIRK
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10118
    vhost: oa__10118
  oaAsync:
    password: dBJEG
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10118
    vhost: oaAsync__10118
  printer:
    password: UuoaN
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10118
    vhost: printer__10118
redis:
  oa:
    host: ***********
    password: google99
    port: 6390
    url: google99@***********:6390
sms:
  send:
    email:
      url: http://sms.yongbokeji.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.yongbokeji.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10118
sqlserver:
  after_write:
    dbname: ch999oanew__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "IEMMmE#xmdDd"
    port: 1433
    username: ch999oanew__10118
  ch999oanew:
    dbname: ch999oanew__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "IEMMmE#xmdDd"
    port: 1433
    username: ch999oanew__10118
  ch999oanewReport:
    dbname: ch999oanew__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "IEMMmE#xmdDd"
    port: 1433
    username: ch999oanew__10118
  ch999oanewHis:
    dbname: ch999oanew__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "IEMMmE#xmdDd"
    port: 1433
    username: ch999oanew__10118
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: dmAD1xBnhPqL
    port: 1433
    username: office__10118
  oanewWrite:
    dbname: ch999oanew__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "IEMMmE#xmdDd"
    port: 1433
    username: ch999oanew__10118
  office:
    dbname: office__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: dmAD1xBnhPqL
    port: 1433
    username: office__10118
  officeWrite:
    dbname: office__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: dmAD1xBnhPqL
    port: 1433
    username: office__10118
  smallpro_write:
    dbname: ch999oanew__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "IEMMmE#xmdDd"
    port: 1433
    username: ch999oanew__10118
  web999:
    dbname: web999__10118
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: sNXNfjHbXDlh
    port: 1433
    username: web999__10118
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.yongbokeji.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.yongbokeji.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'
mqtt:
  host: tcp://iot.9xun.com:1883
  topic: afterservice-default-topic
  clientId: oa-afterservice-${random.value}
  username: client
  password: ch999
  timeout: 10000
  keepalive: 20
