consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.jingjie100.com/
  upload:
    url: http://**************:9333
instance-zone: 10102
jiuji:
  sys:
    moa: https://moa.jingjie100.com
    pc: https://oa.jingjie100.com
    xtenant: 10102
  xtenant: 102000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10102:oWtCwoSqaDrd@***********:27017,***********:27017,***********:27017/ch999oa__10102
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10102
    password: lRtjOus6FRwp
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10102
  oa_nc:
    dbname: oa_nc__10102
    password: FIpEw6nlBpo#
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10102
office:
  sys:
    xtenant: 10102
rabbitmq:
  master:
    password: NwGbv
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10102
    vhost: oaAsync__10102
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: DXUXK
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10102
    vhost: oa__10102
  oaAsync:
    password: NwGbv
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10102
    vhost: oaAsync__10102
  printer:
    password: cRmec
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10102
    vhost: printer__10102
redis:
  oa:
    host: ***********
    password: google99
    port: 6390
    url: google99@***********:6390
sms:
  send:
    email:
      url: http://sms.jingjie100.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.jingjie100.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10102
sqlserver:
  after_write:
    dbname: ch999oanew__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "sr^Gffd5ARJ8"
    port: 1433
    username: ch999oanew__10102
  ch999oanew:
    dbname: ch999oanew__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "sr^Gffd5ARJ8"
    port: 1433
    username: ch999oanew__10102
  ch999oanewReport:
    dbname: ch999oanew__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "sr^Gffd5ARJ8"
    port: 1433
    username: ch999oanew__10102
  ch999oanewHis:
    dbname: ch999oanew__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "sr^Gffd5ARJ8"
    port: 1433
    username: ch999oanew__10102
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: HUayBHTsH7UC
    port: 1433
    username: office__10102
  oanewWrite:
    dbname: ch999oanew__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "sr^Gffd5ARJ8"
    port: 1433
    username: ch999oanew__10102
  office:
    dbname: office__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: HUayBHTsH7UC
    port: 1433
    username: office__10102
  officeWrite:
    dbname: office__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: HUayBHTsH7UC
    port: 1433
    username: office__10102
  smallpro_write:
    dbname: ch999oanew__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "sr^Gffd5ARJ8"
    port: 1433
    username: ch999oanew__10102
  web999:
    dbname: web999__10102
    host: sqlserver.serv.hd.saas.ch999.cn
    password: XGgIyc3FFaXE
    port: 1433
    username: web999__10102
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.jingjie100.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.jingjie100.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'