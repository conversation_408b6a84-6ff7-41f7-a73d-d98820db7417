## host
data1=************
data2=************
data3=************
data4=************
data5=************
web1=************
web2=************
linux1=************
linux2=************
vm1=************
## \u6570\u636E\u5E93 sqlserver
sqlserver.data2.host=oa.sqlserver.serv.iteng.com
sqlserver.data2.port=1433
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data2.host}
sqlserver.ch999oanew.port=${sqlserver.data2.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=huateng_ch999oanew
sqlserver.ch999oanew.password=cpPE2Zv5JNId#EQ
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data2.host}
sqlserver.ch999oanewReport.port=${sqlserver.data2.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=huateng_ch999oanew
sqlserver.ch999oanewReport.password=cpPE2Zv5JNId#EQ
## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=${sqlserver.data2.host}
sqlserver.ch999oanewHis.port=${sqlserver.data2.port}
sqlserver.ch999oanewHis.dbname=ch999oanew
sqlserver.ch999oanewHis.username=huateng_ch999oanew
sqlserver.ch999oanewHis.password=cpPE2Zv5JNId#EQ
## sqlserver:after_write
sqlserver.after_write.host=${sqlserver.data2.host}
sqlserver.after_write.port=${sqlserver.data2.port}
sqlserver.after_write.dbname=ch999oanew
sqlserver.after_write.username=huateng_ch999oanew
sqlserver.after_write.password=cpPE2Zv5JNId#EQ
## sqlserver:office
sqlserver.office.host=${sqlserver.data2.host}
sqlserver.office.port=${sqlserver.data2.port}
sqlserver.office.dbname=office
sqlserver.office.username=huateng_office
sqlserver.office.password=noYJ*szhN^9a%w9
# data1
sqlserver.data1.host=web.sqlserver.serv.iteng.com
sqlserver.data1.port=1433
## sqlserver:web99
sqlserver.web999.host=${sqlserver.data1.host}
sqlserver.web999.port=${sqlserver.data1.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=huateng_web999
sqlserver.web999.password=P6$y9e3o^CrCIjf
## mysql
mysql.url=master.mysql.serv.iteng.com:3306

consul.host=${data4}
consul.port=8500
redis.oa.host=oa.redis.serv.iteng.com
redis.oa.port=6379
redis.oa.password=google99
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
## rabbitMq
rabbitmq.oaAsync.url=master.rabbitmq.serv.iteng.com
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.username=oaAsync
rabbitmq.oaAsync.password=oaAsyncpwd
rabbitmq.oaAsync.vhost=oaAsync
## logConfig
logging.config.path=classpath:log/log4j2-iteng.xml
## mongodb
mongodb.url1=mongodb://ch999oa:google@${data3}:27017/ch999oa
mongodb.url2=mongodb://ch999oa:google@${data4}:27017/ch999oa
mongodb.url3=mongodb://ch999oa:google@${data5}:27017/ch999oa
#\uFFFD\u2CBF\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u013C\uFFFD\uFFFD\uFFFD\u05B7
url.source.path=i18n/url,i18n/constants,i18n/logContent
## mysql:oa_core
mysql.oa_core.url=${mysql.url}
mysql.oa_core.dbname=oa_core
mysql.oa_core.username=oa_core
mysql.oa_core.password=OaCore2019!@#
## image
image.upload.url=http://master.weedfs.serv.iteng.com:9333
image.del.url=http://volume.weedfs.serv.iteng.com:5083
image.select.url=https://img.iteng.com/

spring.cloud.consul.discovery.instance-zone=iteng
instance-zone=iteng

after.lmstfy.host=lmstfy.service.ch999.cn
after.lmstfy.token=01GP2BFHTHASM3TSYV4ZGKVKKF


apollo.url=http://************:8081,http://************:8081,http://************:8081
apollo.file=application-after.yml


mqtt.host=tcp://************:1883
mqtt.username=admin
mqtt.clientinId=oa-afterservice-${random.value}
mqtt.password=ch999
mqtt.timeout=1000
mqtt.keepalive=20
mqtt.topic=afterservice-default-topic