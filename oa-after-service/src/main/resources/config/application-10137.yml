consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://cdyp.img.saas.ch999.cn/
  upload:
    url: http://**************:9333
instance-zone: 10137
jiuji:
  sys:
    moa: https://cdyp.moa.saas.ch999.cn
    pc: https://cdyp.oa.saas.ch999.cn
    xtenant: 10137
  xtenant: 137000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10137:fhOnO5fNvGXS@************:27017,************:27017,************:27017/ch999oa__10137
  url1: *************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10137
    password: KGJO7pyC1yn1
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_core__10137
  oa_nc:
    dbname: oa_nc__10137
    password: KPbyfnd5RSC4
    url: mysql.serv.hn.saas.ch999.cn:3306
    username: oa_nc__10137
office:
  sys:
    xtenant: 10137
rabbitmq:
  master:
    password: xdSTI
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10137
    vhost: oaAsync__10137
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: aKpVA
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oa__10137
    vhost: oa__10137
  oaAsync:
    password: xdSTI
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: oaAsync__10137
    vhost: oaAsync__10137
  printer:
    password: YBnzE
    port: 5672
    url: rabbitmq.serv.hn.saas.ch999.cn
    username: printer__10137
    vhost: printer__10137
redis:
  oa:
    host: ************
    password: google99
    port: 6386
    url: google99@************:6386
sms:
  send:
    email:
      url: http://cdyp.sms.saas.ch999.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://cdyp.sms.saas.ch999.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10137
sqlserver:
  after_write:
    dbname: ch999oanew__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "QlYIhWu4vgpe"
    port: 1433
    username: ch999oanew__10137
  ch999oanew:
    dbname: ch999oanew__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "QlYIhWu4vgpe"
    port: 1433
    username: ch999oanew__10137
  ch999oanewReport:
    dbname: ch999oanew__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "QlYIhWu4vgpe"
    port: 1433
    username: ch999oanew__10137
  ch999oanewHis:
    dbname: ch999oanew__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "QlYIhWu4vgpe"
    port: 1433
    username: ch999oanew__10137
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: BXb5HReWeJ9U
    port: 1433
    username: office__10137
  oanewWrite:
    dbname: ch999oanew__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "QlYIhWu4vgpe"
    port: 1433
    username: ch999oanew__10137
  office:
    dbname: office__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: BXb5HReWeJ9U
    port: 1433
    username: office__10137
  officeWrite:
    dbname: office__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: BXb5HReWeJ9U
    port: 1433
    username: office__10137
  smallpro_write:
    dbname: ch999oanew__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: "QlYIhWu4vgpe"
    port: 1433
    username: ch999oanew__10137
  web999:
    dbname: web999__10137
    host: sqlserver.serv.hn.saas.ch999.cn
    password: 4hS2pvwK6cvr
    port: 1433
    username: web999__10137
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://cdyp.inwcf.saas.ch999.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://cdyp.img.saas.ch999.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'