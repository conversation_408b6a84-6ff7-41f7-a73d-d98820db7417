consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.yfsd.co/
  upload:
    url: http://**************:9333
instance-zone: 10108
jiuji:
  sys:
    moa: https://moa.yfsd.co
    pc: https://oa.yfsd.co
    xtenant: 10108
  xtenant: 108000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10108:1roz0gAh7wcV@***********:27017,***********:27017,***********:27017/ch999oa__10108
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10108
    password: GGeHiyTygfev
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10108
  oa_nc:
    dbname: oa_nc__10108
    password: HYn#DOtIfhCc
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10108
office:
  sys:
    xtenant: 10108
rabbitmq:
  master:
    password: hdBKP
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10108
    vhost: oaAsync__10108
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: CJPsm
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10108
    vhost: oa__10108
  oaAsync:
    password: hdBKP
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10108
    vhost: oaAsync__10108
  printer:
    password: nbVYq
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10108
    vhost: printer__10108
redis:
  oa:
    host: ***********
    password: google99
    port: 6393
    url: google99@***********:6393
sms:
  send:
    email:
      url: http://sms.yfsd.co/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.yfsd.co/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10108
sqlserver:
  after_write:
    dbname: ch999oanew__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "EycdJFmwfXOq"
    port: 1433
    username: ch999oanew__10108
  ch999oanew:
    dbname: ch999oanew__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "EycdJFmwfXOq"
    port: 1433
    username: ch999oanew__10108
  ch999oanewReport:
    dbname: ch999oanew__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "EycdJFmwfXOq"
    port: 1433
    username: ch999oanew__10108
  ch999oanewHis:
    dbname: ch999oanew__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "EycdJFmwfXOq"
    port: 1433
    username: ch999oanew__10108
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: wTzKa7ma9jbq
    port: 1433
    username: office__10108
  oanewWrite:
    dbname: ch999oanew__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "EycdJFmwfXOq"
    port: 1433
    username: ch999oanew__10108
  office:
    dbname: office__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: wTzKa7ma9jbq
    port: 1433
    username: office__10108
  officeWrite:
    dbname: office__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: wTzKa7ma9jbq
    port: 1433
    username: office__10108
  smallpro_write:
    dbname: ch999oanew__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "EycdJFmwfXOq"
    port: 1433
    username: ch999oanew__10108
  web999:
    dbname: web999__10108
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: NdEmFbz0shUE
    port: 1433
    username: web999__10108
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.yfsd.co/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.yfsd.co/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'