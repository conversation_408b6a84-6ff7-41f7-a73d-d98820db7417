## host
data1=************ 
data2=************
data3=************ 
data4=************ 
data5=************
web1=************ 
web2=************
linux1=************ 
linux2=************
vm1=************ 
logserver=************ 
## æ°æ®åº sqlserver
sqlserver.data2.host=oa.sqlserver.serv.zlf.co
sqlserver.data2.port=1433
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data2.host}
sqlserver.ch999oanew.port=${sqlserver.data2.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=jiahai_ch999oanew
sqlserver.ch999oanew.password=ZYEny7J!QtU4DGjw

## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data2.host}
sqlserver.ch999oanewReport.port=${sqlserver.data2.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=jiahai_ch999oanew
sqlserver.ch999oanewReport.password=ZYEny7J!QtU4DGjw
## sqlserver:ch999oanewHis
sqlserver.ch999oanewHis.host=${sqlserver.data2.host}
sqlserver.ch999oanewHis.port=${sqlserver.data2.port}
sqlserver.ch999oanewHis.dbname=ch999oanew
sqlserver.ch999oanewHis.username=jiahai_ch999oanew
sqlserver.ch999oanewHis.password=ZYEny7J!QtU4DGjw
## sqlserver:after_write
sqlserver.after_write.host=${sqlserver.data2.host}
sqlserver.after_write.port=${sqlserver.data2.port}
sqlserver.after_write.dbname=ch999oanew
sqlserver.after_write.username=jiahai_ch999oanew
sqlserver.after_write.password=ZYEny7J!QtU4DGjw

## sqlserver:office
sqlserver.office.host=${sqlserver.data2.host}
sqlserver.office.port=${sqlserver.data2.port}
sqlserver.office.dbname=office
sqlserver.office.username=jiahai_office
sqlserver.office.password=bwhG1BFc@u2#Yq2t
# data1
sqlserver.data1.host=web.sqlserver.serv.zlf.co
sqlserver.data1.port=1433
## sqlserver:web99
sqlserver.web999.host=${sqlserver.data1.host}
sqlserver.web999.port=${sqlserver.data1.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=jiahai_web999
sqlserver.web999.password=FL2nBkultoNdkcWQ
## sqlserver:ipaddress
sqlserver.ipaddress.host=web.sqlserver.serv.zlf.co
sqlserver.ipaddress.port=1433
sqlserver.ipaddress.dbname=ipaddress
sqlserver.ipaddress.username=jiahai_ipaddress
sqlserver.ipaddress.password=7K$RaZpMqHdSIhcF
## mysql
mysql.url=master.mysql.serv.zlf.co:3306

redis.oa.host=oa.redis.serv.zlf.co
redis.oa.port=6379
redis.oa.password=google99
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
# rabbitMq-oaAsync
rabbitmq.oaAsync.url=************
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.username=oaAsync
rabbitmq.oaAsync.password=oaAsyncpwd
rabbitmq.oaAsync.vhost=oaAsync
logging.config.path=classpath:log/log4j2-zlf.xml
## mongodb
mongodb.url1=***************************************************
mongodb.url2=mongodb://ch999oa:google@${data4}:27017/ch999oa
mongodb.url3=mongodb://ch999oa:google@${data5}:27017/ch999oa
#å¤é¨é¾æ¥æä»¶å°å
url.source.path=i18n/url,i18n/constants
## mysql:oa_core
mysql.oa_core.url=${mysql.url}
mysql.oa_core.dbname=oa_core
mysql.oa_core.username=oa_core
mysql.oa_core.password=OaCore2019#$%
## mysql:oa_log
mysql.oa_log.url=main.tidb.serv.zlf.co:8381
mysql.oa_log.dbname=oa_log
mysql.oa_log.username=oa_log
mysql.oa_log.password=B5MpMel3MPA
## image
image.upload.url=http://master.weedfs.serv.zlf.co:9333
image.del.url=http://volume.weedfs.serv.zlf.co:5083
image.select.url=https://img.zlf.co/
## mongoDB
consul.host=************
consul.port=8500
spring.cloud.consul.discovery.instance-zone=zlf
instance-zone=zlf

apollo.url=http://************:8081,http://************:8081,http://************:8081
apollo.file=application-after.yml


after.lmstfy.host=lmstfy.service.ch999.cn
after.lmstfy.token=01GP2BFHSGQESTJ151AFN4TP7J

mqtt.host=tcp://iot.9xun.com:1883
mqtt.clientId=oa-afterservice-${random.value}
mqtt.username=client
mqtt.password=ch999
mqtt.timeout=1000
mqtt.keepalive=20  
mqtt.topic=afterservice-default-topic

jiuji.sys.moa=https://moa.zlf.co
jiuji.sys.pc=https://oa.zlf.co
jiuji.sys.inWcf=http://inwcf.zlf.co
jiuji.sys.oaWcf=http://inwcf2.zlf.co

