# data1
sqlserver.data1.host=***************
sqlserver.data1.port=1433
## sqlserver:web99
sqlserver.web999.host=${sqlserver.data1.host}
sqlserver.web999.port=${sqlserver.data1.port}
sqlserver.web999.dbname=web999
sqlserver.web999.username=devops
sqlserver.web999.password=devops00
## sqlserver:office
sqlserver.office.host=${sqlserver.data1.host}
sqlserver.office.port=${sqlserver.data1.port}
sqlserver.office.dbname=office
sqlserver.office.username=devops
sqlserver.office.password=devops00
## sqlserver:ch999oanew
sqlserver.ch999oanew.host=${sqlserver.data1.host}
sqlserver.ch999oanew.port=${sqlserver.data1.port}
sqlserver.ch999oanew.dbname=ch999oanew
sqlserver.ch999oanew.username=devops
sqlserver.ch999oanew.password=devops00
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=${sqlserver.data1.host}
sqlserver.ch999oanewReport.port=${sqlserver.data1.port}
sqlserver.ch999oanewReport.dbname=ch999oanew
sqlserver.ch999oanewReport.username=devops
sqlserver.ch999oanewReport.password=devops00
## sqlserver:after_write
sqlserver.after_write.host=${sqlserver.data1.host}
sqlserver.after_write.port=${sqlserver.data1.port}
sqlserver.after_write.dbname=ch999oanew
sqlserver.after_write.username=devops
sqlserver.after_write.password=devops00
## redisConfig
redis.oa.host=**************
redis.oa.port=6379
redis.oa.password=google99
redis.oa.url=:${redis.oa.password}@${redis.oa.host}:${redis.oa.port}
## \uFFFD\uFFFD\u05BE\uFFFD\u013C\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u00B7\uFFFD\uFFFD
logging.config.path=classpath:log/log4j2-dev.xml
## mongoDB
mongodb.url1=*****************************************************
#\uFFFD\u2CBF\uFFFD\uFFFD\uFFFD\uFFFD\uFFFD\u013C\uFFFD\uFFFD\uFFFD\u05B7
url.source.path=i18nTest/url
#rabbitMq
rabbitmq.oaAsync.url=**************
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.username=oaAsync
rabbitmq.oaAsync.password=oaAsyncpwd
rabbitmq.oaAsync.publisher-confirm=true
rabbitmq.oaAsync.publisher-returns=true
rabbitmq.oaAsync.connection-timeout=20000
rabbitmq.oaAsync.vhost=oaAsync
#consul
consul.host=**************
consul.port=8500
instance.zone=10000
spring.cloud.consul.discovery.instance-zone=10000
spring.cloud.consul.discovery.default-query-tag=zone=10000
## mysql
mysql.url=master.main.mysql.service.ch999.cn:4306
## mysql:oa_core
mysql.oa_core.url=${mysql.url}
mysql.oa_core.dbname=oa_core
mysql.oa_core.username=oa_core
mysql.oa_core.password=OaCore2019!@#
## image
image.upload.url=http://master.weedfs.serv.iteng.com:9333
image.del.url=http://volume.weedfs.serv.iteng.com:5083
image.select.url=https://img.iteng.com/
spring.cloud.client.ipaddress=***********