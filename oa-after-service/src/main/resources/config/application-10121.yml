consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.fbzj.com.cn/
  upload:
    url: http://**************:9333
instance-zone: 10121
jiuji:
  sys:
    moa: https://moa.fbzj.com.cn
    pc: https://oa.fbzj.com.cn
    xtenant: 10121
  xtenant: 121000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10121:Kmimr8ugTlAL@***********:27017,***********:27017,***********:27017/ch999oa__10121
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10121
    password: 1llkAanpQ8iI
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10121
  oa_nc:
    dbname: oa_nc__10121
    password: 6LAlfbMxwGrV
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10121
office:
  sys:
    xtenant: 10121
rabbitmq:
  master:
    password: OigSv
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10121
    vhost: oaAsync__10121
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: zulkt
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10121
    vhost: oa__10121
  oaAsync:
    password: OigSv
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10121
    vhost: oaAsync__10121
  printer:
    password: sQcUh
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10121
    vhost: printer__10121
redis:
  oa:
    host: ***********
    password: google99
    port: 6400
    url: google99@***********:6400
sms:
  send:
    email:
      url: http://sms.fbzj.com.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.fbzj.com.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10121
sqlserver:
  after_write:
    dbname: ch999oanew__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "K9^Wk#3wi#bT"
    port: 1433
    username: ch999oanew__10121
  ch999oanew:
    dbname: ch999oanew__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "K9^Wk#3wi#bT"
    port: 1433
    username: ch999oanew__10121
  ch999oanewReport:
    dbname: ch999oanew__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "K9^Wk#3wi#bT"
    port: 1433
    username: ch999oanew__10121
  ch999oanewHis:
    dbname: ch999oanew__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "K9^Wk#3wi#bT"
    port: 1433
    username: ch999oanew__10121
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: pUHc5PSgszLo
    port: 1433
    username: office__10121
  oanewWrite:
    dbname: ch999oanew__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "K9^Wk#3wi#bT"
    port: 1433
    username: ch999oanew__10121
  office:
    dbname: office__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: pUHc5PSgszLo
    port: 1433
    username: office__10121
  officeWrite:
    dbname: office__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: pUHc5PSgszLo
    port: 1433
    username: office__10121
  smallpro_write:
    dbname: ch999oanew__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "K9^Wk#3wi#bT"
    port: 1433
    username: ch999oanew__10121
  web999:
    dbname: web999__10121
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: LELyWOym5adZ
    port: 1433
    username: web999__10121
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.fbzj.com.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.fbzj.com.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'