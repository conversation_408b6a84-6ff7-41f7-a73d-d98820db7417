consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.huanqiumobile.com/
  upload:
    url: http://**************:9333
instance-zone: 10130
jiuji:
  sys:
    moa: https://moa.huanqiumobile.com
    pc: https://oa.huanqiumobile.com
    xtenant: 10130
  xtenant: 130000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10130:1xO8dOBdQXCd@***********:27017,***********:27017,***********:27017/ch999oa__10130
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10130
    password: KqbMMMOkQAGK
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10130
  oa_nc:
    dbname: oa_nc__10130
    password: ig0NJlDSeqOY
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10130
office:
  sys:
    xtenant: 10130
rabbitmq:
  master:
    password: wSPjZ
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10130
    vhost: oaAsync__10130
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: Vumib
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10130
    vhost: oa__10130
  oaAsync:
    password: wSPjZ
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10130
    vhost: oaAsync__10130
  printer:
    password: bfFtY
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10130
    vhost: printer__10130
redis:
  oa:
    host: ***********
    password: google99
    port: 6394
    url: google99@***********:6394
sms:
  send:
    email:
      url: http://sms.huanqiumobile.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.huanqiumobile.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10130
sqlserver:
  after_write:
    dbname: ch999oanew__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "u8dF2GijtDkF"
    port: 1433
    username: ch999oanew__10130
  ch999oanew:
    dbname: ch999oanew__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "u8dF2GijtDkF"
    port: 1433
    username: ch999oanew__10130
  ch999oanewReport:
    dbname: ch999oanew__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "u8dF2GijtDkF"
    port: 1433
    username: ch999oanew__10130
  ch999oanewHis:
    dbname: ch999oanew__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "u8dF2GijtDkF"
    port: 1433
    username: ch999oanew__10130
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 9Kk1zP83rekK
    port: 1433
    username: office__10130
  oanewWrite:
    dbname: ch999oanew__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "u8dF2GijtDkF"
    port: 1433
    username: ch999oanew__10130
  office:
    dbname: office__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 9Kk1zP83rekK
    port: 1433
    username: office__10130
  officeWrite:
    dbname: office__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: 9Kk1zP83rekK
    port: 1433
    username: office__10130
  smallpro_write:
    dbname: ch999oanew__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: "u8dF2GijtDkF"
    port: 1433
    username: ch999oanew__10130
  web999:
    dbname: web999__10130
    host: sqlserver2.serv.hd.saas.ch999.cn
    password: jY27IrmMiBp0
    port: 1433
    username: web999__10130
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.huanqiumobile.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.huanqiumobile.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'