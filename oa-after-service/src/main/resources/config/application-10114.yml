consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.sjc99.com/
  upload:
    url: http://**************:9333
instance-zone: 10114
jiuji:
  sys:
    moa: https://moa.sjc99.com
    pc: https://oa.sjc99.com
    xtenant: 10114
  xtenant: 114000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10114:IY9WlU9sNVDD@***********:27017,***********:27017,***********:27017/ch999oa__10114
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10114
    password: vrkhftwi6kl2
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10114
  oa_nc:
    dbname: oa_nc__10114
    password: 4XbVjTkmpuDZ
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10114
office:
  sys:
    xtenant: 10114
rabbitmq:
  master:
    password: zQUsA
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10114
    vhost: oaAsync__10114
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: ghwrD
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10114
    vhost: oa__10114
  oaAsync:
    password: zQUsA
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10114
    vhost: oaAsync__10114
  printer:
    password: vYpNe
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10114
    vhost: printer__10114
redis:
  oa:
    host: ***********
    password: google99
    port: 6397
    url: google99@***********:6397
sms:
  send:
    email:
      url: http://sms.sjc99.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.sjc99.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10114
sqlserver:
  after_write:
    dbname: ch999oanew__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "KnvrARGPmheM"
    port: 1433
    username: ch999oanew__10114
  ch999oanew:
    dbname: ch999oanew__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "KnvrARGPmheM"
    port: 1433
    username: ch999oanew__10114
  ch999oanewReport:
    dbname: ch999oanew__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "KnvrARGPmheM"
    port: 1433
    username: ch999oanew__10114
  ch999oanewHis:
    dbname: ch999oanew__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "KnvrARGPmheM"
    port: 1433
    username: ch999oanew__10114
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: QQUHyBkjAtYE
    port: 1433
    username: office__10114
  oanewWrite:
    dbname: ch999oanew__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "KnvrARGPmheM"
    port: 1433
    username: ch999oanew__10114
  office:
    dbname: office__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: QQUHyBkjAtYE
    port: 1433
    username: office__10114
  officeWrite:
    dbname: office__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: QQUHyBkjAtYE
    port: 1433
    username: office__10114
  smallpro_write:
    dbname: ch999oanew__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "KnvrARGPmheM"
    port: 1433
    username: ch999oanew__10114
  web999:
    dbname: web999__10114
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: ksecH2nrgGr6
    port: 1433
    username: web999__10114
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.sjc99.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.sjc99.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'