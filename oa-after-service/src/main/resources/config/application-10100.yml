consul:
  host: ***********
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.cnjinzhen.com/
  upload:
    url: http://**************:9333
instance-zone: 10100
jiuji:
  sys:
    moa: https://moa.cnjinzhen.com
    pc: https://oa.cnjinzhen.com
    xtenant: 10100
  xtenant: 100000
logging:
  config:
    path: classpath:log/log4j2-dev.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10100:0FbNYveB3ZR1@***********:27017,***********:27017,***********:27017/ch999oa__10100
  url1: **********************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10100
    password: vGh9vlwIJxCB
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10100
  oa_nc:
    dbname: oa_nc__10100
    password: A^F5wcbCoRNV
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10100
office:
  sys:
    xtenant: 10100
rabbitmq:
  master:
    password: KZZpz
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10100
    vhost: oaAsync__10100
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: aZBoP
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10100
    vhost: oa__10100
  oaAsync:
    password: KZZpz
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10100
    vhost: oaAsync__10100
  printer:
    password: KxaPj
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10100
    vhost: printer__10100
redis:
  oa:
    host: ***********
    password: google99
    port: 6387
    url: google99@***********:6387
sms:
  send:
    email:
      url: http://sms.cnjinzhen.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.cnjinzhen.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10100
sqlserver:
  after_write:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  ch999oanew:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  ch999oanewReport:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  ch999oanewHis:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: XBXVIdHzTISe
    port: 1433
    username: office__10100
  oanewWrite:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  office:
    dbname: office__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: XBXVIdHzTISe
    port: 1433
    username: office__10100
  officeWrite:
    dbname: office__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: XBXVIdHzTISe
    port: 1433
    username: office__10100
  smallpro_write:
    dbname: ch999oanew__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "1XcVcdTqgHEA"
    port: 1433
    username: ch999oanew__10100
  web999:
    dbname: web999__10100
    host: sqlserver.serv.hd.saas.ch999.cn
    password: JlKZq0iHdVzY
    port: 1433
    username: web999__10100
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.cnjinzhen.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.cnjinzhen.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'