consul:
  host: **************
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://lsdxt.img.saas.ch999.cn/
  upload:
    url: http://**************:9333
instance-zone: 10119
jiuji:
  sys:
    moa: https://lsdxt.moa.saas.ch999.cn
    pc: https://lsdxt.oa.saas.ch999.cn
    xtenant: 10119
  xtenant: 119000
logging:
  config:
    path: classpath:log/log4j2-dev.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10119:G5SIq3bWUMcX@**************:27017,**************:27017,**************:27017/ch999oa__10119
  url1: *******************************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10119
    password: fgP9ldIUnKWL
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_core__10119
  oa_nc:
    dbname: oa_nc__10119
    password: 58xLxT9nLe1X
    url: mysql.serv.xn.saas.ch999.cn:3306
    username: oa_nc__10119
office:
  sys:
    xtenant: 10119
rabbitmq:
  master:
    password: igaeQ
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10119
    vhost: oaAsync__10119
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: DVsQD
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oa__10119
    vhost: oa__10119
  oaAsync:
    password: igaeQ
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: oaAsync__10119
    vhost: oaAsync__10119
  printer:
    password: rEjrW
    port: 5672
    url: rabbitmq.serv.xn.saas.ch999.cn
    username: printer__10119
    vhost: printer__10119
redis:
  oa:
    host: **************
    password: google99
    port: 6410
    url: google99@**************:6410
sms:
  send:
    email:
      url: http://lsdxt.sms.saas.ch999.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://lsdxt.sms.saas.ch999.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10119
sqlserver:
  after_write:
    dbname: ch999oanew__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "EzYtij^JO#AJ"
    port: 1433
    username: ch999oanew__10119
  ch999oanew:
    dbname: ch999oanew__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "EzYtij^JO#AJ"
    port: 1433
    username: ch999oanew__10119
  ch999oanewReport:
    dbname: ch999oanew__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "EzYtij^JO#AJ"
    port: 1433
    username: ch999oanew__10119
  ch999oanewHis:
    dbname: ch999oanew__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "EzYtij^JO#AJ"
    port: 1433
    username: ch999oanew__10119
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: #VBZFxMuceTz
    port: 1433
    username: office__10119
  oanewWrite:
    dbname: ch999oanew__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "EzYtij^JO#AJ"
    port: 1433
    username: ch999oanew__10119
  office:
    dbname: office__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: #VBZFxMuceTz
    port: 1433
    username: office__10119
  officeWrite:
    dbname: office__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: #VBZFxMuceTz
    port: 1433
    username: office__10119
  smallpro_write:
    dbname: ch999oanew__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: "EzYtij^JO#AJ"
    port: 1433
    username: ch999oanew__10119
  web999:
    dbname: web999__10119
    host: sqlserver.serv.xn.saas.ch999.cn
    password: 2uvfXY#tk2qG
    port: 1433
    username: web999__10119
url:
  delImgUrl: http://**************:5083
  oa-push-info: http://lsdxt.inwcf.saas.ch999.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://lsdxt.img.saas.ch999.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://**************:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'