consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.hxbxtx.com/
  upload:
    url: http://**************:9333
instance-zone: 10112
jiuji:
  sys:
    moa: https://moa.hxbxtx.com
    pc: https://oa.hxbxtx.com
    xtenant: 10112
  xtenant: 112000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10112:va3dI3PFK5uS@***********:27017,***********:27017,***********:27017/ch999oa__10112
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10112
    password: k87qD03^^qWA
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_core__10112
  oa_nc:
    dbname: oa_nc__10112
    password: EkV#jOYuFqGY
    url: mysql.serv.hd.saas.ch999.cn:3306
    username: oa_nc__10112
office:
  sys:
    xtenant: 10112
rabbitmq:
  master:
    password: SKSBF
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10112
    vhost: oaAsync__10112
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: Tstxn
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oa__10112
    vhost: oa__10112
  oaAsync:
    password: SKSBF
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: oaAsync__10112
    vhost: oaAsync__10112
  printer:
    password: RIDLU
    port: 5672
    url: rabbitmq.serv.hd.saas.ch999.cn
    username: printer__10112
    vhost: printer__10112
redis:
  oa:
    host: ***********
    password: google99
    port: 6389
    url: google99@***********:6389
sms:
  send:
    email:
      url: http://sms.hxbxtx.com/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.hxbxtx.com/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10112
sqlserver:
  after_write:
    dbname: ch999oanew__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Fdqk7ZpbEydw"
    port: 1433
    username: ch999oanew__10112
  ch999oanew:
    dbname: ch999oanew__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Fdqk7ZpbEydw"
    port: 1433
    username: ch999oanew__10112
  ch999oanewReport:
    dbname: ch999oanew__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Fdqk7ZpbEydw"
    port: 1433
    username: ch999oanew__10112
  ch999oanewHis:
    dbname: ch999oanew__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Fdqk7ZpbEydw"
    port: 1433
    username: ch999oanew__10112
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: gEmChiiuh^Ce
    port: 1433
    username: office__10112
  oanewWrite:
    dbname: ch999oanew__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Fdqk7ZpbEydw"
    port: 1433
    username: ch999oanew__10112
  office:
    dbname: office__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: gEmChiiuh^Ce
    port: 1433
    username: office__10112
  officeWrite:
    dbname: office__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: gEmChiiuh^Ce
    port: 1433
    username: office__10112
  smallpro_write:
    dbname: ch999oanew__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: "Fdqk7ZpbEydw"
    port: 1433
    username: ch999oanew__10112
  web999:
    dbname: web999__10112
    host: sqlserver.serv.hd.saas.ch999.cn
    password: d0XgOTNI7lJI
    port: 1433
    username: web999__10112
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.hxbxtx.com/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.hxbxtx.com/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'