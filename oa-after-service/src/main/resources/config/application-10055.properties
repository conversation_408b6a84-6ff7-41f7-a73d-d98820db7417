## sqlserver:ch999oan<PERSON>
sqlserver.ch999oanew.host=**************
sqlserver.ch999oanew.port=1433
sqlserver.ch999oanew.dbname=ch999oanew__10055
sqlserver.ch999oanew.username=ch999oanew__10055
sqlserver.ch999oanew.password=ch999oanewPGtuI
## sqlserver:ch999oanewReport
sqlserver.ch999oanewReport.host=**************
sqlserver.ch999oanewReport.port=1433
sqlserver.ch999oanewReport.dbname=ch999oanew__10055
sqlserver.ch999oanewReport.username=ch999oanew__10055
sqlserver.ch999oanewReport.password=ch999oanewPGtuI


## sqlserver:office
sqlserver.office.host=**************
sqlserver.office.port=1433
sqlserver.office.dbname=office__10055
sqlserver.office.username=office__10055
sqlserver.office.password=officeIIckU

sqlserver.oaOffice.host=**************
sqlserver.oaOffice.port=1433
sqlserver.oaOffice.dbname=office__10055
sqlserver.oaOffice.username=office__10055
sqlserver.oaOffice.password=officeIIckU

## sqlserver:officeWrite
sqlserver.officeWrite.host=**************
sqlserver.officeWrite.port=1433
sqlserver.officeWrite.dbname=office__10055
sqlserver.officeWrite.username=office__10055
sqlserver.officeWrite.password=officeIIckU

## mysql:oa_nc
mysql.oa_nc.url=**************:3306
mysql.oa_nc.dbname=oa_nc__10055
mysql.oa_nc.username=oa_nc__10055
mysql.oa_nc.password=oa_ncaCn


## midl
consul.host=**************
consul.port=8500

## redis
redis.oa.host=**************
redis.oa.port=6394
redis.oa.password=google99
redis.oa.url=${redis.oa.password}@${redis.oa.host}:${redis.oa.port}

## rabbitmq
rabbitmq.printer.url=**************
rabbitmq.printer.port=5672
rabbitmq.printer.vhost=printer__10055
rabbitmq.printer.username=printer__10055
rabbitmq.printer.password=XmYyd

rabbitmq.oaAsync.url=**************
rabbitmq.oaAsync.port=5672
rabbitmq.oaAsync.vhost=oaAsync__10055
rabbitmq.oaAsync.username=oaAsync__10055
rabbitmq.oaAsync.password=ucFMW

mongodb.url1=*******************************************************************************
logging.config.path=classpath:log/log4j2-saas.xml
## image
image.upload.url=
image.del.url=
image.select.url=https://img.xahztxgc.com/

jiuji.sys.xtenant=10055
jiuji.sys.moa=https://moa.xahztxgc.com
jiuji.sys.pc=https://oa.xahztxgc.com
jiuji.sys.m=https://m.xahztxgc.com

spring.cloud.consul.discovery.instance-zone=10055
instance-zone=10055

sqlserver.datasource.max-pool-size=20
mysql.datasource.max-pool-size=20
mqtt.host=tcp://************:2883
mqtt.topic=oa2/nc-segments
mqtt.clientinid=nc-segments-${random.value}
mqtt.qoslevel=1
mqtt.username=nc-segments
mqtt.password=password@312
mqtt.timeout=10000
mqtt.keepalive=20
mqtt.adminUrl=http://************:8081/api/v4
mqtt.adminUser=admin
mqtt.adminPassword=public

after.lmstfy.host= lmstfy.service.ch999.cn
after.lmstfy.token= 01GP2BFHSGQESTJ151AFN4TP7J
