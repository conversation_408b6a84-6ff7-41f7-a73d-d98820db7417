consul:
  host: 127.0.0.1
  port: 8500
image:
  del:
    url: http://**************:5083
  select:
    url: https://img.91hjh.cn/
  upload:
    url: http://**************:9333
instance-zone: 10124
jiuji:
  sys:
    moa: https://moa.91hjh.cn
    pc: https://oa.91hjh.cn
    xtenant: 10124
  xtenant: 124000
logging:
  config:
    path: classpath:log/log4j2-saas.xml
messages:
  basename: i18n/abstractInfo,i18n/saas
mongodb:
  ch999oa:
    url: ch999oa__10124:PQNbmHqAAI3H@***********:27017,***********:27017,***********:27017/ch999oa__10124
  url1: **********************************************************************************************************
mysql:
  datasource:
    max-pool-size: 20
  manage_training:
    dbname: 
    password: 
    url: :3306
    username: 
  oa_core:
    dbname: oa_core__10124
    password: VUJepcjK9ylX
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_core__10124
  oa_nc:
    dbname: oa_nc__10124
    password: 7MY#lfJjLluD
    url: mysql.serv.hb.saas.ch999.cn:3306
    username: oa_nc__10124
office:
  sys:
    xtenant: 10124
rabbitmq:
  master:
    password: aCilM
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10124
    vhost: oaAsync__10124
  msgcenter:
    password: ch999
    port: 35672
    url: storemq.ch999.cn
    username: msgcenter
    vhost: msgcenter
  oa:
    password: ABqZD
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oa__10124
    vhost: oa__10124
  oaAsync:
    password: aCilM
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: oaAsync__10124
    vhost: oaAsync__10124
  printer:
    password: hehhn
    port: 5672
    url: rabbitmq.serv.hb.saas.ch999.cn
    username: printer__10124
    vhost: printer__10124
redis:
  oa:
    host: ***********
    password: google99
    port: 6402
    url: google99@***********:6402
sms:
  send:
    email:
      url: http://sms.91hjh.cn/email/email.aspx
    in:
      url: http://office/Handler/api.ashx
  url: http://sms.91hjh.cn/?test=
spring:
  cloud:
    consul:
      discovery:
        instance-zone: 10124
sqlserver:
  after_write:
    dbname: ch999oanew__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "#loTGwKa2D28"
    port: 1433
    username: ch999oanew__10124
  ch999oanew:
    dbname: ch999oanew__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "#loTGwKa2D28"
    port: 1433
    username: ch999oanew__10124
  ch999oanewReport:
    dbname: ch999oanew__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "#loTGwKa2D28"
    port: 1433
    username: ch999oanew__10124
  ch999oanewHis:
    dbname: ch999oanew__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "#loTGwKa2D28"
    port: 1433
    username: ch999oanew__10124
  datasource:
    max-pool-size: 20
  oaOffice:
    dbname: office__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: Xd8t2NRLCfRy
    port: 1433
    username: office__10124
  oanewWrite:
    dbname: ch999oanew__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "#loTGwKa2D28"
    port: 1433
    username: ch999oanew__10124
  office:
    dbname: office__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: Xd8t2NRLCfRy
    port: 1433
    username: office__10124
  officeWrite:
    dbname: office__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: Xd8t2NRLCfRy
    port: 1433
    username: office__10124
  smallpro_write:
    dbname: ch999oanew__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: "#loTGwKa2D28"
    port: 1433
    username: ch999oanew__10124
  web999:
    dbname: web999__10124
    host: sqlserver2.serv.hb.saas.ch999.cn
    password: lIoFH2ccXGtX
    port: 1433
    username: web999__10124
url:
  delImgUrl: http://weedfs.xn.saas.ch999.cn:5083
  oa-push-info: http://inwcf.91hjh.cn/ajax.ashx?act=oaMessagePush&content=%s&ch999ids=%s&link=%s
  selectImgUrl: https://img.91hjh.cn/
  source:
      path: i18n/url
  uploadImgUrl: http://weedfs.xn.saas.ch999.cn:9333
after:
  lmstfy:
    host: 'lmstfy.service.ch999.cn'
    token: '01GP2BFHSGQESTJ151AFN4TP7J'