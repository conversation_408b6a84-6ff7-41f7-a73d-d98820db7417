package com.jiuji.oa.afterservice.batchreturn.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.comparator.CompareUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.map.MapBuilder;
import cn.hutool.core.util.*;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jiuji.oa.afterservice.batchreturn.bo.BatchReturnSubBO;
import com.jiuji.oa.afterservice.batchreturn.dao.BatchReturnDao;
import com.jiuji.oa.afterservice.batchreturn.dao.BatchReturnMkcDao;
import com.jiuji.oa.afterservice.batchreturn.enums.BatchReturnStatusEnum;
import com.jiuji.oa.afterservice.batchreturn.enums.BatchReturnWayEnum;
import com.jiuji.oa.afterservice.batchreturn.enums.EcustomerAccountKindsEnum;
import com.jiuji.oa.afterservice.batchreturn.po.BatchReturn;
import com.jiuji.oa.afterservice.batchreturn.po.BatchReturnMkc;
import com.jiuji.oa.afterservice.batchreturn.service.BatchReturnService;
import com.jiuji.oa.afterservice.batchreturn.vo.BatchReturnInfoVO;
import com.jiuji.oa.afterservice.batchreturn.vo.BatchReturnMkcVO;
import com.jiuji.oa.afterservice.batchreturn.vo.req.BatchReturnQueryReq;
import com.jiuji.oa.afterservice.batchreturn.vo.req.BatchReturnReviewReq;
import com.jiuji.oa.afterservice.batchreturn.vo.req.BatchReturnTakeReq;
import com.jiuji.oa.afterservice.batchreturn.vo.res.BatchReturnPageVO;
import com.jiuji.oa.afterservice.batchreturn.vo.res.HandleReturnRes;
import com.jiuji.oa.afterservice.batchreturn.wrapper.BatchReturnWrapper;
import com.jiuji.oa.afterservice.bigpro.bo.OpenIdInfoBo;
import com.jiuji.oa.afterservice.bigpro.bo.OpenValidInfoBo;
import com.jiuji.oa.afterservice.bigpro.dao.tuihuan.ShouhouTuiHuanMapper;
import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouRefundService;
import com.jiuji.oa.afterservice.bigpro.service.SmsService;
import com.jiuji.oa.afterservice.bigpro.service.kemu.KemuService;
import com.jiuji.oa.afterservice.bigpro.service.tuihuan.TuiHuanService;
import com.jiuji.oa.afterservice.bigpro.vo.refund.TuihuanFormVo;
import com.jiuji.oa.afterservice.bigpro.vo.res.ShouhouRefundDetailVo;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.source.LogContentSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.HttpClientUtil;
import com.jiuji.oa.afterservice.common.util.OaVerifyUtil;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.customeraccount.service.CustomerAccountService;
import com.jiuji.oa.afterservice.finance.VoucherRecordBuilder;
import com.jiuji.oa.afterservice.finance.po.VoucherRecord;
import com.jiuji.oa.afterservice.finance.service.VoucherRecordService;
import com.jiuji.oa.afterservice.other.bo.SaveMoneyInfoBO;
import com.jiuji.oa.afterservice.other.dao.AreainfoMapper;
import com.jiuji.oa.afterservice.other.dao.BasketMapper;
import com.jiuji.oa.afterservice.other.enums.SaveMoneyEkindEnum;
import com.jiuji.oa.afterservice.other.po.Basket;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.other.service.BasketService;
import com.jiuji.oa.afterservice.other.service.ShouhouTuihuanService;
import com.jiuji.oa.afterservice.refund.bo.BuyPriceSubInfoBo;
import com.jiuji.oa.afterservice.refund.bo.NotRefundBo;
import com.jiuji.oa.afterservice.refund.bo.RefundDataBo;
import com.jiuji.oa.afterservice.refund.service.RefundMoneyService;
import com.jiuji.oa.afterservice.refund.service.kind.ParentTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.service.way.ThirdOriginWayService;
import com.jiuji.oa.afterservice.refund.service.way.WechatAlipaySecondsRefundService;
import com.jiuji.oa.afterservice.refund.utils.RefundMoneyUtil;
import com.jiuji.oa.afterservice.refund.vo.res.way.Refund;
import com.jiuji.oa.afterservice.refund.vo.res.way.ThirdOriginRefundVo;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProRelativePathConstant;
import com.jiuji.oa.afterservice.smallpro.vo.req.oaApiReq.OaApiReq;
import com.jiuji.oa.afterservice.stock.dao.ProductMkcMapper;
import com.jiuji.oa.afterservice.stock.po.ProductMkc;
import com.jiuji.oa.afterservice.stock.service.ProductMkcService;
import com.jiuji.oa.afterservice.sub.dao.SubMapper;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.loginfo.batchreturn.client.BatchReturnLogClient;
import com.jiuji.oa.loginfo.common.pojo.AddLogReq;
import com.jiuji.oa.loginfo.mkc.client.MkcLogClient;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.WorkLogUtil;
import com.jiuji.tc.utils.constants.IntConstant;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.enums.coupon.BusinessTypeEnum;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @since 2020/3/12
 */
@Service
@Slf4j
public class BatchReturnServiceImpl
        extends ServiceImpl<BatchReturnDao, BatchReturn>
        implements BatchReturnService {

    @Resource
    AreainfoMapper areainfoMapper;
    @Resource
    SubMapper subMapper;
    @Resource
    VoucherRecordService voucherRecordService;
    @Resource
    BatchReturnMkcDao batchReturnMkcDao;
    @Resource
    BatchReturnLogClient batchReturnLogClient;
    @Resource
    LogContentSource logContentSource;
    @Resource
    SubLogsCloud subLogsCloud;
    @Resource
    MkcLogClient mkcLogClient;
    @Resource
    AreaInfoClient areaInfoClient;
    @Resource
    ProductMkcService productMkcService;
    @Resource
    ProductMkcMapper productMkcMapper;
    @Resource
    SubService subService;
    @Resource
    BasketMapper basketMapper;
    @Resource
    InwcfUrlSource inwcfUrlSource;
    @Resource
    CustomerAccountService customerAccountService;
    @Resource
    private SysConfigClient sysConfigClient;
    @Autowired
    private ShouhouRefundService shouhouRefundService;
    @Autowired
    private TuiHuanService tuiHuanService;
    @Autowired
    ShouhouTuiHuanMapper tuiHuanMapper;
    private static final String SUB_LOG_KEY = "subLogs";
    private static final String BATCH_LOG_KEY = "batch_log_key";
    @Resource
    private ThirdOriginWayService thirdOriginWayService;
    @Resource
    private RefundMoneyService refundMoneyService;
    @Resource
    private AbstractCurrentRequestComponent currentRequestComponent;
    @Resource
    private ShouhouTuihuanService shouhouTuihuanService;
    @Resource
    private BasketService basketService;

    @Override
    public List<BatchReturnSubBO> getBatchReturnSubBO(Integer subId) {
        return baseMapper.getBatchReturnSubInfo(subId);
    }



    @Override
    public BuyPriceSubInfoBo getBuyPriceAndSubInfo(Integer orderId, BatchReturn batchReturn, TuihuanKindEnum tuihuanKindEnum, List<BatchReturnMkcVO> batchReturnMkcVOS) {
        BigDecimal yifuMoney = BigDecimal.ZERO;

        int tradeType = IntConstant.ZERO;
        LocalDateTime tradeDate = null;
        String peizhi = org.apache.commons.lang3.StringUtils.EMPTY;
        Integer piaoType = IntConstant.ZERO;
        Integer subId = batchReturn.getSubId();
        if (Objects.isNull(subId)) {
            subId = 0;
        }
        List<Integer> basketIdList = batchReturnMkcVOS.stream().map(BatchReturnMkcVO::getBasketId).distinct().collect(Collectors.toList());
        List<RefundDataBo> refundDataBos =  this.baseMapper.listRefundDataBySubId(basketIdList);
        if (CollectionUtils.isEmpty(refundDataBos)) {
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG,"获取不到购买的订单信息,库存状态或订单状态不对");
            return null;
        }
        BuyPriceSubInfoBo buyPriceSubInfoBo = new BuyPriceSubInfoBo();
        RefundDataBo refundDataBo = refundDataBos.get(NumberConstant.ZERO);
        BigDecimal price = batchReturnMkcVOS.stream().map(BatchReturnMkcVO::getOriginalPrice).reduce(BigDecimal.ZERO, BigDecimal::add);
        Integer userId = refundDataBo.getUserId();
     // #endregion
        tradeDate = refundDataBo.getTradeDate1();

        tradeType = 1;
        //优品
        if (Objects.equals(refundDataBo.getType(),22)) {
            tradeType = 2;
        }
        peizhi = refundDataBo.getProductPeizhi();
        yifuMoney = refundDataBo.getYifuMoney();
        // 获取发票信息
        Integer fapiaoKind = shouhouTuihuanService.getFapiaoKindBySubId(subId, Convert.toInt(Boolean.FALSE));
        if (fapiaoKind != null) {
            piaoType = fapiaoKind;
        }
        buyPriceSubInfoBo.setBusinessType(BusinessTypeEnum.SALE_ORDER.getCode());

        buyPriceSubInfoBo.setTradeDate(tradeDate);
        // 设置交易完成时间, 三方获取交易完成之后的退订
        SpringContextUtil.getRequest().ifPresent(req -> req.setAttribute(RequestAttrKeys.GROUP_REFUND_ORDER_TRADEDATE, buyPriceSubInfoBo.getTradeDate()));
        //扣减商品对应的不可退金额记录
        NotRefundBo notRefundBo = RefundMoneyUtil.getNotRefundMoney(Optional.ofNullable(tuihuanKindEnum).orElse(TuihuanKindEnum.BATCH_TK), BusinessTypeEnum.SALE_ORDER.getCode(), orderId);
        Map<Integer, Tuple> splitBasketMap = RefundMoneyUtil.splitNotRefundPrice(new HashSet<>(basketIdList), notRefundBo, orderId);
        int splitNotRefundIndex = 1;
        BigDecimal splitNotRefund =  basketIdList.stream().map(item -> {
            Tuple tuple = splitBasketMap.get(item);
            if (tuple == null) {
                return BigDecimal.ZERO;
            }
            try {
                Object value = tuple.get(splitNotRefundIndex);
                return value == null ? BigDecimal.ZERO : new BigDecimal(value.toString());
            } catch (IndexOutOfBoundsException e) {
                return BigDecimal.ZERO; // 索引越界时返回0
            }
        } ).reduce(BigDecimal.ZERO, BigDecimal::add);

        price = NumberUtil.max(ObjectUtil.defaultIfNull(price.subtract(splitNotRefund), BigDecimal.ZERO), BigDecimal.ZERO);
        // 详情
        buyPriceSubInfoBo.setOrderId(orderId);
        buyPriceSubInfoBo.setAreaId(refundDataBo.getAreaId());
        buyPriceSubInfoBo.setSubCheck(refundDataBo.getSubCheck());
        buyPriceSubInfoBo.setTotalPrice(price.setScale(2,RoundingMode.HALF_UP));
        buyPriceSubInfoBo.setYifuMoney(yifuMoney);
        buyPriceSubInfoBo.setUserId(userId);
        buyPriceSubInfoBo.setTradeType(tradeType);
        buyPriceSubInfoBo.setPeizhi(peizhi);
        buyPriceSubInfoBo.setPiaoType(piaoType);
        return buyPriceSubInfoBo;
    }


    @Override
    public R<Integer> checkTake(Integer subId, List<BatchReturnSubBO> boList, List<BatchReturnMkcVO> mkcIdList) {
        if (CollectionUtils.isEmpty(boList) || CollectionUtils.isEmpty(mkcIdList)) {
            // -2 存在空数据
            return R.success("请选择可退大件",-2);
        }
        if (!CollUtil.containsAll(boList.stream().map(BatchReturnSubBO::getMkcId).collect(Collectors.toList()),
                mkcIdList.stream().map(BatchReturnMkcVO::getMkcId).collect(Collectors.toList()))) {
            // -1 存在不可退商品  bo 没有包含所有mkc的元素
            return R.success("存在不可退商品",-1);
        }
        BatchReturn lastNotComplete = baseMapper.getLastNotComplete(subId);
        if (lastNotComplete != null) {
            // -3 该订单有退款中的订单
            return R.success("该订单有退款中的订单,批量退款单号为：" + lastNotComplete.getId(),-3);
        }
        //增加正常退款的信息判断
        ShouhouTuiHuanPo tuiHuan = tuiHuanMapper.getLastNotCompleteBySubId(subId);
        if(tuiHuan != null){
            return R.success("该订单有退款中的订单,售后单号为：" + ObjectUtil.defaultIfNull(tuiHuan.getShouhouId(),tuiHuan.getSmallproid()),-4);
        }
        //获取订单信息
        AreaInfo subAreaInfo = subService.lambdaQuery().eq(Sub::getSubId, subId).select(Sub::getAreaId).list().stream()
                .filter(Objects::nonNull).findFirst().map(Sub::getAreaId).map(areaInfoClient::getAreaInfoById)
                .map(areaR -> CommonUtils.getResultData(areaR, userMsg -> {
                    throw new CustomizeException(StrUtil.format("门店信息获取异常, 原因: {}", userMsg));
                }))
                .orElseThrow(() -> new CustomizeException("订单门店信息获取异常"));

        OaUserBO inUser =  currentRequestComponent.getCurrentStaffId();
        if(ObjectUtil.notEqual(XtenantEnum.getXtenant(), subAreaInfo.getXtenant())){
            throw new CustomizeException("该订单不属于当前门店租户体系, 不允许退款");
        }
        Boolean isAuth = Convert.toBool(CommonUtils.getResultData(sysConfigClient.getValueByCode(SysConfigConstant.AUTHORIZE),
                userMsg -> {
                    throw new CustomizeException(StrUtil.format("获取授权配置异常, 原因: {}", userMsg));
                }));
        if(Boolean.TRUE.equals(isAuth) && ObjectUtil.notEqual(inUser.getAuthorizeId(), subAreaInfo.getAuthorizeId())){
            throw new CustomizeException("该订单不属于当前门店授权体系, 不允许退款");
        }
        if (mkcIdList.size() == boList.size()) {
            return R.success("可以退款",1);
        } else {
            return R.success("可以退款",2);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer takeReturn(OaUserBO inUser, LocalDateTime tradeDate1, Integer takeWay, BatchReturnTakeReq takeReq) {
        BatchReturn po = new BatchReturn();
        po.setSubId(takeReq.getSubId());
        po.setRemark(takeReq.getRemark());
        po.setTakeCh999Id(inUser.getUserId());
        po.setTakeWay(takeWay);
        po.setAreaId(inUser.getAreaId());
        po.setUserName(takeReq.getUserName());
        po.setUserTel(takeReq.getUserTel());
        po.setBuyTime(tradeDate1);
        po.setReturnWay(ObjectUtil.defaultIfNull(takeReq.getReturnWay(),BatchReturnWayEnum.BALANCE.getMessage()));
        int row = baseMapper.insert(po);
        if (row < 1) {
            throw new CustomizeException("插入批量退换表数据0条");
        }
        //计算退款金额
        BigDecimal returnPrice = takeReq.getMkcInfo().stream().map(BatchReturnMkcVO::getPrice).filter(ObjectUtil::isNotNull)
                .reduce((price1, price2) -> price1.add(price2)).orElse(BigDecimal.ZERO);

        Integer id = po.getId();
        List<BatchReturnMkc> batchReturnMkcs = takeReq.getMkcInfo()
                .stream()
                .map(tmp -> {
                    BatchReturnMkc tmpMkc = new BatchReturnMkc();
                    BeanUtils.copyProperties(tmp, tmpMkc);
                    tmpMkc.setPrice(tmpMkc.getPrice().setScale(4, RoundingMode.HALF_DOWN));
                    tmpMkc.setOriginalPrice(tmpMkc.getOriginalPrice().setScale(4, RoundingMode.HALF_DOWN));
                    tmpMkc.setBatchReturnId(id);
                    return tmpMkc;
                }).collect(Collectors.toList());
        int pageSize = NumberConstant.TWO_HUNDRED.intValue();
        int totalPage =  PageUtil.totalPage(batchReturnMkcs.size(), pageSize);
        for (int i = 0; i < totalPage; i++) {
            batchReturnMkcDao.insertBatch(CollUtil.page(i,pageSize,batchReturnMkcs));
        }
        // 新版  批量退款 提交的时候不进行退换表的写入
        Boolean isNewBatchRefund = refundMoneyService.enable(takeReq.getSubId(), Collections.singletonList(TuihuanKindEnum.BATCH_TK.getCode()), inUser.getAreaId());
        if(!isNewBatchRefund){
            row = insertTuihuan(inUser, returnPrice, po);
            if(row<1){
                throw new CustomizeException("插入退换表数据0条");
            }
        }
        return id;
    }

    private int insertTuihuan(OaUserBO inUser, BigDecimal returnPrice, BatchReturn po) {
        //目前九机没在用不需要考虑退九机币
        //插入售后退换表数据 xxk
        return baseMapper.insertTuihuan(po, TuihuanKindEnum.BATCH_TK.getCode(),returnPrice, inUser);
    }

    @Override
    public R<BatchReturnInfoVO> getInfoVOById(Integer id) {
        BatchReturn batchReturn = getById(id);
        if (batchReturn == null) {
            return R.error(ResultCode.NO_DATA, "批量退款不存在");
        }
        List<Integer> relatedUserId = getRelatedCh999Id(batchReturn);
        List<OaUserBO> userBOList = baseMapper.getUserNames(relatedUserId);
        Map<Integer, String> userNameMap = userBOList.stream()
                .collect(Collectors.toMap(OaUserBO::getUserId, OaUserBO::getUserName));
        List<BatchReturnMkcVO> mkcVOS = batchReturnMkcDao.listMkcVoByBatchReturnId(id);

        R<AreaInfo> areaInfoR = areaInfoClient.getAreaInfoById(batchReturn.getAreaId());
        AreaInfo areaInfo = null;
        if (areaInfoR.getCode() == 0) {
            areaInfo = areaInfoR.getData();
        }

        R<List<String>> logsR = batchReturnLogClient.list(id, false);
        List<String> logs = new ArrayList<>();
        if (logsR != null && logsR.getCode() == 0) {
            logs = logsR.getData();
        }
        BatchReturnInfoVO batchReturnInfo = BatchReturnWrapper.toInfoVo(batchReturn, mkcVOS, areaInfo, logs, userNameMap);
        LambdaQueryWrapper<ShouhouTuiHuanPo> tuiHuanWrapper = Wrappers.lambdaQuery();
        ShouhouTuiHuanPo tuiHuan = tuiHuanService.getOne(tuiHuanWrapper.eq(ShouhouTuiHuanPo::getShouhouId,batchReturnInfo.getId())
                .eq(ShouhouTuiHuanPo::getTuihuanKind,TuihuanKindEnum.BATCH_TK.getCode()).orderByDesc(ShouhouTuiHuanPo::getId),false);
        R<BatchReturnInfoVO> result = R.success(batchReturnInfo);
        //设置退款方式列表
        R<List<String>> returnWaysR = shouhouRefundService.listRefundWay(batchReturn.getSubId(), TuihuanKindEnum.TK.getCode());
        if(returnWaysR.isSuccess()){
            batchReturnInfo.setRefundWays(returnWaysR.getData());
            List<Refund> refunds = refundMoneyService.listAllShouYing(batchReturnInfo.getSubId(), TuihuanKindEnum.TDJ);
            if (CollectionUtils.isNotEmpty(refunds)) {
                //找到判断如果存在不是第三方就不显示
                List<Refund> refundList = refunds.stream().filter(item -> !(item instanceof ThirdOriginRefundVo)).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(refundList)) {
                    Optional.ofNullable(tuiHuan).ifPresent(obj -> {
                        Set<String> set = new HashSet<>();
                        refunds.forEach(item -> {
                            String refundTypeName = item.getReturnWayName();
                            set.add(refundTypeName + "返回");
                        });
                        //只有一种三方的情况下才显示
                        if (set.size() == 1) {
                            batchReturnInfo.getRefundWays().addAll(set);
                        }
                    });
                }
            }
        }else{
           result.addBusinessLog(returnWaysR.getUserMsg());
           batchReturnInfo.setRefundWays(Collections.emptyList());
        }

        if(ObjectUtil.isNull(tuiHuan)){
            return result;
        }
        batchReturnInfo.setTuihuanId(tuiHuan.getId());
        //设置退款方式
        shouhouRefundService.setRefundWayValid((url, info) -> {
            batchReturnInfo.setOpenIdUrl(url);
            batchReturnInfo.setOpenIdInfo(info);
        }, OpenValidInfoBo.from(tuiHuan).setOrderBusinessTypeEnum(OpenValidInfoBo.OrderBusinessTypeEnum.ORDER).setSubId(batchReturn.getSubId()));
        //原路径退款设置支付编号信息
        if (StrUtil.endWith(tuiHuan.getTuiWay(), ShouhouRefundService.REFUND_WAY_ORIGIN)) {
            batchReturnInfo.setPayRecords(shouhouRefundService.listPayRecordByTuihuanId(tuiHuan.getId()));
        }
        return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateBatchReturn(OaUserBO inUser, BatchReturn batchReturn, BatchReturnTakeReq req) {
        //原路径退款的校验
        assertCheckUpdate(batchReturn,req,inUser);
        //记录更新日志
        StringJoiner logJoiner = new StringJoiner(",");
        WorkLogUtil.getFieldModifiedLog(BatchReturn.class,batchReturn,req).stream().forEach(logJoiner::add);
        LambdaUpdateWrapper<ShouhouTuiHuanPo> tuihuanUpdateWrapper = Wrappers.lambdaUpdate();
        batchReturn.setReturnWay(req.getReturnWay());
        tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getTuiWay,req.getReturnWay());
        batchReturn.setBankCardNum(req.getBankCardNum());
        tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getBanknumber,req.getBankCardNum());
        batchReturn.setBankName(req.getBankName());
        tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getBankname,req.getBankName());
        batchReturn.setBankUserName(req.getBankUserName());
        tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getBankfuming,req.getBankUserName());
        batchReturn.setRemark(req.getRemark());
        batchReturn.setUserName(req.getUserName());
        batchReturn.setUserTel(req.getUserTel());
        //退款的科目
        batchReturn.setKemu(req.getKemu());
        if(!XtenantEnum.isJiujiXtenant() && StrUtil.isNotBlank(req.getKemu())){
            tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getKemuTui, req.getKemu());
        }
        boolean updateInfoSuccess = update(batchReturn, new LambdaQueryWrapper<BatchReturn>()
                .eq(BatchReturn::getId,batchReturn.getId()).eq(BatchReturn::getStatus,batchReturn.getStatus()));
        boolean result = false;
        List<BatchReturnMkcVO> mkcInfo = req.getMkcInfo();
        BigDecimal returnPrice = BigDecimal.ZERO;
        BigDecimal zheJiaM = BigDecimal.ZERO;
        if (updateInfoSuccess && CollUtil.isNotEmpty(mkcInfo)) {
            MapBuilder<Integer, BatchReturnMkcVO> mapBuilder = MapBuilder.create();
            batchReturnMkcDao.listMkcVoByBatchReturnId(batchReturn.getId()).forEach(mkc->mapBuilder.put(mkc.getId(),mkc));
            Map<Integer, BatchReturnMkcVO> map = mapBuilder.build();
            for (BatchReturnMkcVO mkc : mkcInfo) {
                //记录更新日志
                List<String> mkcModifiedLogs = WorkLogUtil.getFieldModifiedLog(BatchReturnMkcVO.class, map.get(mkc.getId()), mkc);
                if(!mkcModifiedLogs.isEmpty()){
                    logJoiner.add(StrUtil.format("商品{}: ",mkc.getProductName()));
                    mkcModifiedLogs.forEach(logJoiner::add);
                }

                returnPrice = returnPrice.add(ObjectUtil.defaultIfNull(mkc.getPrice(),BigDecimal.ZERO));
                zheJiaM = zheJiaM.add(mkc.getDisPrice());
            }
            int pageSize = NumberConstant.ONE_THOUSAND.intValue();
            int totalPage =  PageUtil.totalPage(mkcInfo.size(), pageSize);
            for (int i = 0; i < totalPage; i++) {
                result = retBool(batchReturnMkcDao.updatePriceBatch(CollUtil.page(i,pageSize,mkcInfo)));
            }

            // 更新退款总价, 组合退, 无需更新
            tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getTuikuanM,returnPrice).set(ShouhouTuiHuanPo::getTuikuanM1,returnPrice)
                    .set(ShouhouTuiHuanPo::getZhejiaM,zheJiaM);
        }
        // 判断是否可以用组合
        boolean enable = refundMoneyService.enable(batchReturn.getSubId(), Collections.singletonList(TuihuanKindEnum.BATCH_TK.getCode()), batchReturn.getAreaId());
        if(updateInfoSuccess && ! enable){
            // 组合退,无需处理
            ShouhouTuiHuanPo tuiHuan = shouhouRefundService.getLastNotCompleteRefund(batchReturn.getId(), TuihuanKindEnum.BATCH_TK);
            if(tuiHuan == null && isVersion2Before(batchReturn)){
                //历史数据处理
                insertTuihuan(inUser,returnPrice,batchReturn);
                tuiHuan = shouhouRefundService.getLastNotCompleteRefund(batchReturn.getId(), TuihuanKindEnum.BATCH_TK);
            }
            //更新退换表信息
            tuihuanUpdateWrapper.eq(ShouhouTuiHuanPo::getId,tuiHuan.getId());
            tuiHuanMapper.update(null,tuihuanUpdateWrapper);
            savePayRecords(inUser, req, logJoiner, tuiHuan);
        }

        if(logJoiner.length()>0){
            logJoiner.add("保存修改");
            batchReturnLogClient.addLog(buildLogReq(batchReturn.getId(),logJoiner.toString(),true));
        }
        return result;
    }

    /**
     * 保存支付记录
     * @param inUser
     * @param req
     * @param logJoiner
     * @param tuiHuan
     */
    private void savePayRecords(OaUserBO inUser, BatchReturnTakeReq req, StringJoiner logJoiner, ShouhouTuiHuanPo tuiHuan) {
        if(CollUtil.isEmpty(req.getPayRecords())){
            return;
        }

        //获取所有的退款记录
        List<ShouhouRefundDetailVo.PayRecordVo> dbNetPays = shouhouRefundService.listPayRecordByTuihuanId(tuiHuan.getId());
        for (TuihuanFormVo.PayRecordVo payRecord : req.getPayRecords()) {
            Optional<ShouhouRefundDetailVo.PayRecordVo> dbRecordOpt = dbNetPays.stream()
                    .filter(hnp -> Objects.equals(hnp.getTradeNo(), payRecord.getTradeNo())).findFirst();
            String overMoneyMsg = StrUtil.format("{}交易号退款金额超额", payRecord.getTradeNo());
            if (dbRecordOpt.isPresent()) {
                //之前已经存在进行更新退款金额,并记录日志
                ShouhouRefundDetailVo.PayRecordVo dbPayRecord = dbRecordOpt.get();
                if(CompareUtil.compare(dbPayRecord.getMoney(),payRecord.getMoney()) != 0){
                    logJoiner.add(StrUtil.format("交易{}退款金额由【{}】修改为【{}】",dbPayRecord.getTradeNo(),dbPayRecord.getMoney(),payRecord.getMoney()));
                    if(shouhouRefundService.updateNetPayRefundMoney(payRecord.getMoney(),dbPayRecord.getRefundId(),dbPayRecord.getMoney())<1){
                        throw new CustomizeException("其他人已修改退款金额,请刷新页面后再重新操作");
                    }
                }
            } else if (shouhouRefundService.insertNetPayRefundInfo(payRecord, tuiHuan.getId(), NumberConstant.ONE, inUser) < 1) {
                //插入原路径退款信息 有一个插入失败全部回滚
                throw new CustomizeException(overMoneyMsg);
            }
        }
        //未勾选的直接删除,并恢复支付记录的退款金额,并记录删除日志
        dbNetPays.stream().filter(dnp->req.getPayRecords().stream().noneMatch(pr->Objects.equals(dnp.getTradeNo(),pr.getTradeNo())))
        .forEach(dnp->{
            logJoiner.add(StrUtil.format("删除交易{}的退款记录",dnp.getTradeNo()));
            if(shouhouRefundService.deleteNetPayRefundById(dnp.getRefundId(),dnp.getId())>0){
            }else{
                throw new CustomizeException(StrUtil.format("删除{}交易的退款失败",dnp.getTradeNo()));
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean review(OaUserBO inUser, BatchReturn batchReturn, BatchReturnReviewReq req) {
        assertCheckForReview(req,inUser,batchReturn);
        if (!req.getIsPass()) {
            return handleCancel(inUser, batchReturn, req);
        }
        AddLogReq logReq = buildLogReq(batchReturn.getId(),null,true);
        if (req.getRemark() == null) {
            req.setRemark("");
        }
        LambdaUpdateWrapper<ShouhouTuiHuanPo> tuihuanUpdateWrapper = Wrappers.lambdaUpdate();
        if (req.getType() == 1) {
            batchReturn.setReview1Ch999Id(inUser.getUserId());
            batchReturn.setReview1Time(LocalDateTime.now());
            tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getCheck1,1).set(ShouhouTuiHuanPo::getCheck1dtime,LocalDateTime.now())
            .set(ShouhouTuiHuanPo::getCheck1user,inUser.getUserName()).isNull(ShouhouTuiHuanPo::getCheck1);
            logReq.setComment("审批1，备注：" + req.getRemark());
        } else if (req.getType() == 2) {
            batchReturn.setReview2Ch999Id(inUser.getUserId());
            batchReturn.setReview2Time(LocalDateTime.now());
            tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getCheck2,1).set(ShouhouTuiHuanPo::getCheck2dtime,LocalDateTime.now())
                    .set(ShouhouTuiHuanPo::getCheck2user,inUser.getUserName()).isNull(ShouhouTuiHuanPo::getCheck2);
            logReq.setComment("审批2，备注：" + req.getRemark());
        }
        boolean updateSuccess = update(batchReturn, new LambdaQueryWrapper<BatchReturn>()
                .eq(BatchReturn::getId,batchReturn.getId()).eq(BatchReturn::getStatus,batchReturn.getStatus()));

        if (!updateSuccess) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        //同步审核到到售后退换表
        tuihuanUpdateWrapper.eq(ShouhouTuiHuanPo::getShouhouId,batchReturn.getId())
                .eq(ShouhouTuiHuanPo::getTuihuanKind,TuihuanKindEnum.BATCH_TK.getCode())
                .apply("isnull(isdel,0)=0");
        List<ShouhouTuiHuanPo> shouhouTuiHuanPoList = tuiHuanMapper.selectList(tuihuanUpdateWrapper);
        if(isVersion2Before(batchReturn) && shouhouRefundService.getLastNotCompleteRefund(batchReturn.getId(), TuihuanKindEnum.BATCH_TK) == null){
            BigDecimal returnPrice = batchReturnMkcDao.listMkcVoByBatchReturnId(batchReturn.getId()).stream().map(BatchReturnMkcVO::getPrice).filter(Objects::nonNull)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            //历史数据处理
            insertTuihuan(inUser,returnPrice,batchReturn);
        }
        updateSuccess = tuiHuanMapper.update(null,tuihuanUpdateWrapper)>0;
        if(!updateSuccess){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return false;
        }
        R<Boolean> logRes = batchReturnLogClient.addLog(logReq);
        //点击一审之后，走一遍自动审核逻辑
        if (req.getType() == 1) {
            if (shouhouTuiHuanPoList.size() > 0){
                ShouhouTuiHuanPo shouhouTuiHuanPo = shouhouTuiHuanPoList.get(0);
                ShouhouTuihuanService shouhouTuihuanService = SpringUtil.getBean(ShouhouTuihuanService.class);
                R<Integer> result = shouhouTuihuanService.autoTuihuanCheck(shouhouTuiHuanPo.getId());
                if (result.isSuccess() && ShouhouRefundDetailVo.ProcessStatus.CHECK2.getCode().equals(result.getData())){
                    batchReturn.setReview2Ch999Id(inUser.getUserId());
                    batchReturn.setReview2Time(LocalDateTime.now());
                    update(batchReturn, new LambdaQueryWrapper<BatchReturn>()
                            .eq(BatchReturn::getId,batchReturn.getId()).eq(BatchReturn::getStatus,batchReturn.getStatus()));
                    AddLogReq addLogReq = buildLogReq(batchReturn.getId(),null,true);
                    addLogReq.setComment("审批2，备注：" + req.getRemark());
                    batchReturnLogClient.addLog(addLogReq);
                }
            }

        }
        return logRes.getCode() == 0 && logRes.getData();
    }

    public boolean isVersion2Before(BatchReturn batchReturn) {
        return batchReturn.getTakeTime().isBefore(LocalDateTime.parse("2021-12-15 00:00:00", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }

    private AddLogReq buildLogReq(Integer businessId,String comment,boolean isShow) {
        OaUserBO inUser = SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId();
        AddLogReq logReq = new AddLogReq();
        logReq.setId(businessId);
        logReq.setCh999Id(inUser.getUserId());
        logReq.setInUser(inUser.getUserName());
        logReq.setShowAll(isShow);
        logReq.setComment(comment);
        return logReq;
    }

    /**
     * 处理退款订单
     * @param inUser
     * @param batchReturn
     * @param req
     * @return List<ProductMkc> newMkc 0,List<BatchReturnSubBO> allMkcOfSub 1,List<BatchReturnMkc> toReturnMkcList 2
     */
    private Tuple handleReturnSub(OaUserBO inUser, BatchReturn batchReturn, BatchReturnReviewReq req){
        List<BatchReturnSubBO> allMkcOfSub = baseMapper.getBatchReturnSubInfoAll(batchReturn.getSubId());
        LambdaQueryWrapper<BatchReturnMkc> mkcToReturn = new LambdaQueryWrapper<BatchReturnMkc>()
                .eq(BatchReturnMkc::getBatchReturnId, batchReturn.getId());
        if(allMkcOfSub.isEmpty()){
            throw new CustomizeException("商品已经通过其他方式退了");
        }
        List<BatchReturnMkc> toReturnMkcList = batchReturnMkcDao.selectList(mkcToReturn);

        boolean isWholeSub = checkReturnWholeSub(allMkcOfSub, toReturnMkcList);
        //写入日志
        String logContent = StrUtil.format("退款类型:{}", isWholeSub ? "整单退" : "部分退");
        addLogToRequest(buildLogReq(batchReturn.getId(),logContent,false));
        List<ProductMkc> newMkc;
        if (isWholeSub) {
            newMkc = handleReturnWholeSub(inUser, batchReturn, toReturnMkcList,req);
        } else {
            newMkc = handleReturnPartSub(inUser, batchReturn, allMkcOfSub, toReturnMkcList);
        }
        // 以上操作：
        // 1. 订单状态修改：拆单、或整单更新
        // 2. 拆分basket
        // 3. 将退回的mkc，转为售后机

        return new Tuple(newMkc,allMkcOfSub,toReturnMkcList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<HandleReturnRes> handleReturn(OaUserBO inUser, BatchReturn batchReturn, BatchReturnReviewReq req) {
        assertCheckForReview(req,inUser, batchReturn);
        if (!req.getIsPass()) {
            boolean cancelResult = handleCancel(inUser, batchReturn, req);
            if(!cancelResult){
                return R.error("批量退款取消失败");
            }
            return R.success("批量退款取消成功",new HandleReturnRes());
        }
        //退款办理的科目验证 银行转账才验证科目
        if(Objects.equals(BatchReturnWayEnum.BANK.getMessage(),batchReturn.getReturnWay()) && StrUtil.isBlank(batchReturn.getKemu())
            && ObjectUtil.notEqual(CommonUtils.toShotWebXtenant(XtenantEnum.getXtenant()), ShortXtenantEnum.ZLF.getCode())){
            throw new CustomizeException(StrUtil.format("{}退款,科目不能为空",batchReturn.getReturnWay()));
        }
        // 校验退款金额是否小于最大可退金额
        boolean existsGtBuyPrice = baseMapper.existsGtBuyPrice(batchReturn.getId());
        if(existsGtBuyPrice){
            return R.error("退款金额不能大于最大可退金额");
        }
        //List<ProductMkc> newMkc 0,List<BatchReturnSubBO> allMkcOfSub 1,List<BatchReturnMkc> toReturnMkcList 2
        Tuple tuple = handleReturnSub(inUser, batchReturn, req);
        // 以下操作：
        // 1. 处理凭证
        // 2. 处理MKC日志
        // 3. 更新批量退款状态
        // 4. 更新退款金额
        List<ProductMkc> newMkc = tuple.get(0);
        List<BatchReturnSubBO> allMkcOfSub =tuple.get(1);
        List<BatchReturnMkc> toReturnMkcList = tuple.get(2);
        if (CollectionUtils.isEmpty(newMkc)) {
            log.error("更新订单状态，更新basket状态，更新mkc状态  失败");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return R.error("更新订单状态，更新basket状态，更新mkc状态失败");
        }
        //获取所有的退款记录
        ShouhouTuiHuanPo tuiHuan = Optional.ofNullable(shouhouRefundService.getLastNotCompleteRefund(batchReturn.getId(), TuihuanKindEnum.BATCH_TK))
                .orElseGet(() -> {
                    if (isVersion2Before(batchReturn)) {
                        BigDecimal returnPrice = toReturnMkcList.stream().map(BatchReturnMkc::getPrice).filter(Objects::nonNull)
                                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        //历史数据处理 单独事务 避免循环死锁
                        new MultipleTransaction().execute(DataSourceConstants.DEFAULT, () -> insertTuihuan(inUser, returnPrice, batchReturn)).commit();
                        return shouhouRefundService.getLastNotCompleteRefund(batchReturn.getId(), TuihuanKindEnum.BATCH_TK);
                    }
                    return null;
                });
        //获取售后退换信息
        shouhouRefundService.listPayRecordByTuihuanId(tuiHuan.getId()).forEach(dbPayRecord->{
            if(shouhouRefundService.updateNetPayRefundPrice(dbPayRecord.getId(),dbPayRecord.getTradeNo(),dbPayRecord.getMoney())<1){
                throw new CustomizeException(StrUtil.format("交易号{}退款金额已更新,请刷新页面重新调整退款金额",dbPayRecord.getTradeNo()));
            }
        });
        // 添加凭证节点
//        CommonCustomerBO customerBO = subMapper.getCommonCustomerBOBySubId(batchReturn.getSubId());
        // mkc添加日志
        List<AddLogReq> listAddReq = new ArrayList<>();
        // 在原始mkc_id中备注：售后退货，新mkc_id：xxxxxx
        List<AddLogReq> oldMkcLog = newMkc.stream().map(tmp ->buildLogReq(tmp.getOldMkcId(), StrUtil.format("售后退货，新mkc_id：{}" , tmp.getId()), true))
                .collect(Collectors.toList());
        // 新mkc_id中备注：售后退货：xxxxx（退款单号，超链），mkc_id:xxxxxx，退货原因：xxxxxx（接件备注）
        List<AddLogReq> newMkcLog = newMkc.stream().map(tmp -> buildLogReq(tmp.getId(),
                StrUtil.format("售后退货：{}，原mkc_id：{}，退货原因：{}", batchReturn.getId(), tmp.getOldMkcId(), batchReturn.getRemark()),true))
                .collect(Collectors.toList());
        listAddReq.addAll(oldMkcLog);
        listAddReq.addAll(newMkcLog);

        R<Boolean> mkcLogReq = mkcLogClient.batchAdd(listAddReq);
        if (mkcLogReq.getCode() != 0 || Boolean.FALSE.equals(mkcLogReq.getData())) {
            throw new CustomizeException(StrUtil.format("添加mck日志异常, 原因: {}", mkcLogReq.getUserMsg()));
        }

        BatchReturn batchReturnToUpdate = new BatchReturn()
                .setToSubId(batchReturn.getToSubId())
                .setReturnCh999Id(inUser.getUserId());

        LambdaQueryWrapper<BatchReturn> updateQ = new LambdaQueryWrapper<BatchReturn>()
                .eq(BatchReturn::getId, batchReturn.getId());
        VoucherRecord voucherRecord = insertVoucherRecord(batchReturn);
        if(req.getIsmyHandleReturnWay()){
            //当前处理退款方式 其他退款方式调用接口
            LambdaUpdateWrapper<ShouhouTuiHuanPo> tuihuanUpdateWrapper = Wrappers.lambdaUpdate();
            tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getCheck3,Boolean.TRUE).set(ShouhouTuiHuanPo::getCheck3dtime,LocalDateTime.now())
                    .set(ShouhouTuiHuanPo::getCheck3user,inUser.getUserName()).set(ShouhouTuiHuanPo::getVoucherId,voucherRecord.getId())
                    .eq(ShouhouTuiHuanPo::getId,tuiHuan.getId()).isNull(ShouhouTuiHuanPo::getCheck3);
            tuiHuanService.update(tuihuanUpdateWrapper);
            tuiHuan.setCheck3(Boolean.TRUE);
            tuiHuan.setCheck3dtime(LocalDateTime.now());
            tuiHuan.setCheck3user(inUser.getUserName());
            batchReturnToUpdate.setReturnTime(LocalDateTime.now()).setStatus(BatchReturnStatusEnum.DONE.getCode());
            // 更新记录成功
            batchReturnToUpdate.setVoucherId(voucherRecord.getId());
            updateQ.eq(BatchReturn::getStatus,BatchReturnStatusEnum.TAKE.getCode());
            boolean updateInfoSuccess = update(batchReturnToUpdate, updateQ);
            if (!updateInfoSuccess) {
                throw new CustomizeException("更新批量退款记录失败");
            }
        }
        // todo 银行跟余额办理, 需要c#那边提供批量接口
        if (BatchReturnWayEnum.BALANCE.getMessage().equals(batchReturn.getReturnWay())) {
            // 插入银行流水
            Integer userId = allMkcOfSub.get(0).getUserId();
            BigDecimal allReturnAmount = toReturnMkcList.stream()
                    .map(BatchReturnMkc::getPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            Boolean flag = customerAccountService.addCustomerAccount(userId,
                    EcustomerAccountKindsEnum.ECUSTOMERACCOUNTKINDS5,
                    inUser.getUserName(), "批量退款，退款单号：" + batchReturn.getId(), batchReturn.getId(), BigDecimal.ZERO,
                    allReturnAmount);
            /*if (!flag) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                log.error("插入银行流水失败");
                return false;
            }*/
            // #fixme 接口修改
            String url = inwcfUrlSource.getMoneySave();
            R<String> valueR = sysConfigClient.getValueByCode(SysConfigConstant.IN_WCF_HOST);
            String host = valueR.getData();
            if (StringUtils.isEmpty(host)){
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                throw new CustomizeException("获取退款url地址失败");
            }
            url = host + SmallProRelativePathConstant.MONEY_SAVE;
            //获取退款地址
            List<String> activeProfile = SpringContextUtil.getActiveProfileList();
            // 加上风控
            Integer xTenant = Optional.ofNullable(sysConfigClient.getValueByCode(SysConfigConstant.XTENANT))
                    .map(R::getData).map(Integer::valueOf).orElseThrow(() -> new CustomizeException("获取租户ID出错"));
            if (xTenant < NumberConstant.ONE_THOUSAND) {
//            if (!activeProfile.stream().anyMatch(e -> e.equalsIgnoreCase("jiuji")) && url.contains("ch999")) {
                throw new CustomizeException("退余额链接错误");
            }
            Map<String, String> headers = new HashMap<>(1);
            headers.put("Content-Type", "application/json");
            SaveMoneyInfoBO saveMoneyInfoBO = new SaveMoneyInfoBO();
            saveMoneyInfoBO.setUserid(allMkcOfSub.get(0).getUserId())
                    .setAmount(allReturnAmount.doubleValue())
                    .setEkind(SaveMoneyEkindEnum.SAVE_MONEY_EKIND_8.getCode())
                    .setSubid(batchReturn.getSubId())
                    .setAreaid(batchReturn.getAreaId())
                    .setMemo(StrUtil.format("批量退款办理{${}$}", batchReturn.getId()));
            //SaveMoneyInfoReqBO saveMoneyInfoReqBO = new SaveMoneyInfoReqBO().setItem(saveMoneyInfoBO);
            OaApiReq oaApiReq = getOaApiReq(saveMoneyInfoBO);
            oaApiReq.setData(null);
            Map<String, Object> stringObjectMap = CommenUtil.transBean2Map(oaApiReq);
            stringObjectMap.put("timestamp", oaApiReq.getTimeStamp());
            stringObjectMap.put("Data", saveMoneyInfoBO);
            HashMap<String, Object> map = new HashMap<>(1);
            map.put("item", stringObjectMap);
            String params = JSONObject.toJSONString(map);

            try {
                String kcResult = HttpClientUtil.post(url, params, headers);
                R r = JSON.parseObject(JSON.parse(kcResult).toString(), R.class);
                if (!r.isSuccess()) {
                    throw new CustomizeException("退余额失败，返回结果为：" + kcResult);
                }
            }catch (CustomizeException e){
                throw e;
            } catch (Exception e) {
                //io异常, 避免重复打款的问题
                RRExceptionHandler.logError("余额打款", req, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }
        } else if (BatchReturnWayEnum.BANK.getMessage().equals(batchReturn.getReturnWay())) {
            // 插入银行流水
            Integer userId = allMkcOfSub.get(0).getUserId();
            BigDecimal allReturnAmount = toReturnMkcList.stream()
                    .map(BatchReturnMkc::getPrice)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            Boolean flag = customerAccountService.addCustomerAccount(userId,
                    EcustomerAccountKindsEnum.ECUSTOMERACCOUNTKINDS5,
                    inUser.getUserName(), "批量退款，退款单号：" + batchReturn.getId(), batchReturn.getId(), allReturnAmount,
                    allReturnAmount);
            /*if (!flag) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                log.error("插入银行流水失败");
                return false;
            }*/
        }else{
            //退款操作
            LambdaUpdateWrapper<ShouhouTuiHuanPo> tuihuanUpdateWrapper = Wrappers.lambdaUpdate();
            tuiHuanService.update(tuihuanUpdateWrapper.set(ShouhouTuiHuanPo::getVoucherId, voucherRecord.getId())
                    .eq(ShouhouTuiHuanPo::getId, tuiHuan.getId()));
            httpReturnSucess(voucherRecord, tuiHuan, batchReturnToUpdate, updateQ);
        }
        //批量写入订单日志
        HttpServletRequest request = SpringContextUtil.getRequest().orElseThrow(() -> new CustomizeException("只允许通过http请求调用"));
        if(request.getAttribute(SUB_LOG_KEY)!=null){
            R<Boolean> resSubLog = subLogsCloud.addSubLogBatch(Convert.toList(SubLogsNewReq.class, request.getAttribute(SUB_LOG_KEY)));
            if (resSubLog.getCode() != 0 || !Boolean.TRUE.equals(resSubLog.getData())) {
                throw new CustomizeException("增加订单日志异常");
            }
        }
        if(request.getAttribute(BATCH_LOG_KEY) != null){
            Convert.toList(AddLogReq.class, request.getAttribute(BATCH_LOG_KEY)).forEach(batchReturnLogClient::addLog);
        }

        return R.success("批量退款办理成功", new HandleReturnRes().setTuiHuanPo(tuiHuan));
    }

    /**
     * http 请求退款成功的操作
     * @param voucherRecord
     * @param tuiHuan
     * @param batchReturnToUpdate
     * @param updateQ
     */
    public void httpReturnSucess(VoucherRecord voucherRecord, ShouhouTuiHuanPo tuiHuan, BatchReturn batchReturnToUpdate, LambdaQueryWrapper<BatchReturn> updateQ) {
        batchReturnToUpdate.setVoucherId(voucherRecord.getId());
        update(batchReturnToUpdate, updateQ);
    }

    private VoucherRecord insertVoucherRecord(BatchReturn batchReturn) {
        Integer accountSetId = areainfoMapper.getAccountSetIdByAreaId(batchReturn.getAreaId());
        VoucherRecord voucherRecord = VoucherRecordBuilder
                .getBatchReturnVoucher(batchReturn.getId(), batchReturn.getAreaId(), accountSetId);
        boolean addVoucherSuccess = voucherRecordService.save(voucherRecord);
        if (!addVoucherSuccess) {
            throw new CustomizeException("插入凭证失败");
        }
        return voucherRecord;
    }

    private OaApiReq getOaApiReq(SaveMoneyInfoBO saveMoneyInfoReqBO) {
        OaApiReq oaApiReq = new OaApiReq();
        oaApiReq.setTimeStamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        oaApiReq.setData(saveMoneyInfoReqBO);
        String secret = batchReturnMkcDao.getSecretByCode(SecretEnum.BALANCE_OPERATE.getCode());
        oaApiReq.setSign(OaVerifyUtil.createSign(secret, oaApiReq));
        return oaApiReq;
    }

    private List<ProductMkc> handleReturnWholeSub(OaUserBO inUser
            , BatchReturn batchReturn
            , List<BatchReturnMkc> toReturnMkcList, BatchReturnReviewReq brrReq) {
        batchReturn.setToSubId(batchReturn.getSubId());
        Integer updateSubInfo = DecideUtil.iif(Boolean.TRUE.equals(brrReq.getIsmyHandleReturnWay()),()->baseMapper.updateSubToReturn(batchReturn.getSubId()),
                ()->{
                    //单独提交事务手动回滚
                    AtomicReference<Integer> usiN = new AtomicReference<>(0);
                    new MultipleTransaction().execute(DataSourceConstants.DEFAULT,()->usiN.set(baseMapper.updateSubToReturn(batchReturn.getSubId()))).commit();
                    return usiN.get();
                });
        if (updateSubInfo <= 0) {
            log.info("更新订单未退款状态失败");
            return new ArrayList<>();
        }
        // 订单补充日志
        SubLogsNewReq req = new SubLogsNewReq();
        req.setInUser(inUser.getUserName());
        req.setSubId(batchReturn.getSubId());
        req.setShowType(true);
        req.setType(1);
        req.setComment(StrUtil.indexedFormat(logContentSource.getBatchReturnSub(batchReturn.getId()),batchReturn.getId()));
        addSubLogToRequest(req);
        // 更新退款金额
        Map<Integer, List<BatchReturnMkc>> basketMap =
                toReturnMkcList.stream().collect(Collectors.groupingBy(BatchReturnMkc::getBasketId));
        basketMap.entrySet().forEach(e -> {
            Integer basketId = e.getKey();
            List<BatchReturnMkc> basketList = e.getValue();
            Basket basket = new Basket();
            basket.setBasketId(basketId);
            basket.setReturnPrice(basketList.get(0).getPrice());
            LambdaQueryWrapper<Basket> basketUpdateQuery = new LambdaQueryWrapper<Basket>()
                    .eq(Basket::getBasketId, basket.getBasketId()).eq(Basket::getSubId,batchReturn.getSubId());
            int updateBasket = basketMapper.update(basket, basketUpdateQuery);
            if (updateBasket <= 0) {
                log.error("更新原basket 退款金额失败,原basketId:{}", basketId);
            }
        });

        return toNewProductMkc(batchReturn, toReturnMkcList);
    }

    private void addSubLogToRequest(SubLogsNewReq req) {
        Optional<HttpServletRequest> requestOpt = SpringContextUtil.getRequest();
        if (!requestOpt.isPresent()){
            throw new CustomizeException("只允许通过http请求调用");
        }
        HttpServletRequest request = requestOpt.get();
        if(request.getAttribute(SUB_LOG_KEY) == null){
            request.setAttribute(SUB_LOG_KEY,new HashSet<>());
        }
        Set<SubLogsNewReq> subLogs = (Set<SubLogsNewReq>) request.getAttribute(SUB_LOG_KEY);
        subLogs.add(req);
    }

    private void addLogToRequest(AddLogReq req) {
        Optional<HttpServletRequest> requestOpt = SpringContextUtil.getRequest();
        if (!requestOpt.isPresent()){
            throw new CustomizeException("只允许通过http请求调用");
        }
        HttpServletRequest request = requestOpt.get();
        if(request.getAttribute(BATCH_LOG_KEY) == null){
            request.setAttribute(BATCH_LOG_KEY,new HashSet<>());
        }
        Set<AddLogReq> addLogs = (Set<AddLogReq>) request.getAttribute(BATCH_LOG_KEY);
        addLogs.add(req);
    }

    private List<ProductMkc> handleReturnPartSub(OaUserBO inUser
            , BatchReturn batchReturn
            , List<BatchReturnSubBO> allMkcOfSub
            , List<BatchReturnMkc> toReturnMkcList) {
        Sub oldSub = subService.getById(batchReturn.getSubId());
        BigDecimal basketPrice = toReturnMkcList.stream()
                .map(BatchReturnMkc::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        Sub splitSub = subService.splitSub(oldSub, 9, basketPrice, basketPrice);
        if (splitSub == null) {
            log.error("拆单失败");
            return new ArrayList<>();
        }
        batchReturn.setToSubId(splitSub.getSubId());
        //需要退款的basket
        Map<Integer, List<BatchReturnSubBO>> basketMkcList = allMkcOfSub.stream()
                .collect(Collectors.groupingBy(BatchReturnSubBO::getBasketId));
        //订单里面的basket
        Map<Integer, List<BatchReturnMkc>> toReturnBasketMkcList = toReturnMkcList.stream()
                .collect(Collectors.groupingBy(BatchReturnMkc::getBasketId));

        List<BatchReturnMkc> mkcList = new ArrayList<>();
        for (Integer basketId : toReturnBasketMkcList.keySet()) {
            List<BatchReturnMkc> tmpMkc = handleBasket(oldSub, splitSub, basketId
                    , basketMkcList.get(basketId), toReturnBasketMkcList.get(basketId));
            if (CollectionUtils.isEmpty(tmpMkc)) {
                log.error("拆分basket失败,处理mkc失败,basketId:{}", basketId);
                return new ArrayList<>();
            }
            mkcList.addAll(tmpMkc);
        }
        // 修改原来订单的yingfuM  yifuM jidianM youhuiM
        subService.lambdaUpdate().eq(Sub::getSubId, oldSub.getSubId())
                .set(Sub::getYingfuM, oldSub.getYingfuM() )
                .set(Sub::getYifuM, oldSub.getYifuM() )
                .set(Sub::getJidianM, oldSub.getJidianM() )
                .set(Sub::getYouhui1M, oldSub.getYouhui1M())
                .update();
        //修改新订单的 jidianM youhuiM
        subService.lambdaUpdate().eq(Sub::getSubId, splitSub.getSubId())
                .set(Sub::getJidianM, splitSub.getJidianM() )
                .set(Sub::getYouhui1M, splitSub.getYouhui1M())
                .update();
        // 订单补充日志
        SubLogsNewReq req = new SubLogsNewReq();
        req.setInUser(inUser.getUserName());
        req.setSubId(batchReturn.getSubId());
        req.setShowType(true);
        req.setType(1);
        req.setComment(logContentSource.getBatchReturnSub(batchReturn.getId()));
        addSubLogToRequest(req);

        // 订单补充日志
        SubLogsNewReq reqOld = new SubLogsNewReq();
        reqOld.setInUser(inUser.getUserName());
        reqOld.setSubId(batchReturn.getSubId());
        reqOld.setShowType(true);
        reqOld.setType(1);
        //原订单
        reqOld.setComment(StrUtil.indexedFormat(logContentSource.getBatchReturnSubOld(batchReturn.getToSubId()),batchReturn.getToSubId()));
        addSubLogToRequest(reqOld);

        SubLogsNewReq reqNewSub = new SubLogsNewReq();
        reqNewSub.setInUser(inUser.getUserName());
        reqNewSub.setSubId(batchReturn.getToSubId());
        reqNewSub.setShowType(true);
        reqNewSub.setType(1);
        // 新订单
        reqNewSub.setComment(StrUtil.indexedFormat(logContentSource.getBatchReturnSubNew(batchReturn.getSubId()),batchReturn.getSubId()));
        addSubLogToRequest(reqNewSub);
        return toNewProductMkc(batchReturn, mkcList);
    }

    private List<BatchReturnMkc> handleBasket(Sub oldSub, Sub newSub
            , Integer basketId
            , List<BatchReturnSubBO> allMkcOfBasket
            , List<BatchReturnMkc> toReturnMkcOfBasket) {
        //整个basket退
        if (allMkcOfBasket.size() == toReturnMkcOfBasket.size()) {
            Basket basket = new Basket();
            basket.setSubId(Long.valueOf(newSub.getSubId()));
            LambdaQueryWrapper<Basket> updateQuery = new LambdaQueryWrapper<Basket>()
                    .eq(Basket::getBasketId, basketId);
            //更新退款金额
            basket.setReturnPrice(toReturnMkcOfBasket.get(0).getPrice());
            int basketUpdate = basketMapper.update(basket, updateQuery);
            if (basketUpdate <= 0) {
                log.error("转basket到 拆单失败，basketId：" + basketId);
                return new ArrayList<>();
            }
            //订单信息更新
            Basket basketSplit = basketMapper.selectById(basketId);
            updateOrderInfo(oldSub, newSub, toReturnMkcOfBasket, basketSplit);
//            return toReturnMkcOfBasket.stream()
//                    .map(BatchReturnMkc::getMkcId)
//                    .collect(Collectors.toList());
            return toReturnMkcOfBasket;
        } else {
            //退部分basket
            Basket basket = basketMapper.selectById(basketId);
//            BigDecimal allReturn = toReturnMkcOfBasket.stream()
//                    .map(BatchReturnMkc::getPrice)
//                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //拆分basket
            Basket newBasket = new Basket();
            BeanUtils.copyProperties(basket, newBasket);
            newBasket.setBasketId(null)
                    .setSubId(Long.valueOf(newSub.getSubId()))
                    .setBasketCount(toReturnMkcOfBasket.size())
                    .setPrice(basket.getPrice());
            newBasket.setReturnPrice(toReturnMkcOfBasket.get(0).getPrice());
            int insertBasket = basketMapper.insert(newBasket);
            if (insertBasket <= 0) {
                log.error("拆分basket失败");
                return new ArrayList<>();
            }
            //更新订单信息
            updateOrderInfo(oldSub, newSub, toReturnMkcOfBasket, basket);

            // 原basket，更新count
            Basket oldBasketToUpdate = new Basket();
            oldBasketToUpdate.setBasketCount(basket.getBasketCount() - toReturnMkcOfBasket.size());
            // 原basket更新退款金额
            //oldBasketToUpdate.setReturnPrice(basketPrice);
            LambdaQueryWrapper<Basket> basketUpdateQuery = new LambdaQueryWrapper<Basket>()
                    .eq(Basket::getBasketId, basket.getBasketId());
            int updateBasket = basketMapper.update(oldBasketToUpdate, basketUpdateQuery);
            if (updateBasket <= 0) {
                log.error("更新原basket count失败,原basketId:{}", basketId);
                return new ArrayList<>();
            }

            // 需要退的mkc，指向新的basket
            ProductMkc productMkc = new ProductMkc();
            productMkc.setBasketId(newBasket.getBasketId());
            List<Integer> mkcId = toReturnMkcOfBasket.stream()
                    .map(BatchReturnMkc::getMkcId)
                    .collect(Collectors.toList());
            LambdaQueryWrapper<ProductMkc> mkcUpdateQuery = new LambdaQueryWrapper<ProductMkc>()
                    .in(ProductMkc::getId, mkcId);
            int updateMkc = productMkcMapper.update(productMkc, mkcUpdateQuery);
            if (updateMkc <= 0) {
                log.error("拆分mkc到新basket失败,原basketId {}", basketId);
                return new ArrayList<>();
            }

            // 返回需要退的mkcid，进行转现。
            return toReturnMkcOfBasket;
        }
    }

    /**
     * 订单修改
     * @param oldSub
     * @param newSub
     * @param toReturnMkcOfBasket
     * @param basket
     */
    private static void updateOrderInfo(Sub oldSub, Sub newSub, List<BatchReturnMkc> toReturnMkcOfBasket, Basket basket) {
        // 修改原来订单的yingfuM  yifuM jidianM youhuiM
        BigDecimal basketSize = BigDecimal.valueOf(Optional.ofNullable(toReturnMkcOfBasket).map(List::size).orElse(0));
        BigDecimal yingFuM = Optional.ofNullable(oldSub.getYingfuM()).orElse(BigDecimal.ZERO);
        BigDecimal youhuiPrice = Optional.ofNullable(basket.getYouhuiPrice()).orElse(BigDecimal.ZERO);
        BigDecimal price2 = Optional.ofNullable(basket.getPrice2()).orElse(BigDecimal.ZERO);
        BigDecimal jifenPrice = Optional.ofNullable(basket.getJifenPrice()).orElse(BigDecimal.ZERO);
        yingFuM = yingFuM.subtract(price2.multiply(basketSize));
        BigDecimal youhuiM = Optional.ofNullable(oldSub.getYouhui1M()).orElse(BigDecimal.ZERO);
        youhuiM = youhuiM.subtract(youhuiPrice.multiply(BigDecimal.valueOf(toReturnMkcOfBasket.size())));
        BigDecimal jidianM = Optional.ofNullable(oldSub.getJidianM()).orElse(BigDecimal.ZERO);
        jidianM = jidianM.subtract(jifenPrice.multiply(BigDecimal.valueOf(toReturnMkcOfBasket.size())));
        oldSub.setYingfuM(yingFuM).setYifuM(yingFuM).setYouhui1M(youhuiM).setJidianM(jidianM);
        //修改新订单的 jidianM youhuiM
        BigDecimal youhuiMnew = Optional.ofNullable(newSub.getYouhui1M()).orElse(BigDecimal.ZERO);
        youhuiMnew = youhuiMnew.add(youhuiMnew.multiply(BigDecimal.valueOf(toReturnMkcOfBasket.size())));
        BigDecimal jidianMnew = Optional.ofNullable(newSub.getJidianM()).orElse(BigDecimal.ZERO);
        jidianMnew = jidianMnew.add(jidianMnew.multiply(BigDecimal.valueOf(toReturnMkcOfBasket.size())));
        newSub.setYouhui1M(youhuiM).setJidianM(jidianM);
    }

    /**
     * 对需要转销售的退回机，进行更新。
     *
     * @param batchReturn
     * @param oldMkcList
     * @return MKC 其中 oldMkcId 为旧机MKC值。
     */
    @Override
    public List<ProductMkc> toNewProductMkc(BatchReturn batchReturn, List<BatchReturnMkc> oldMkcList) {
//        LambdaQueryWrapper<ProductMkc> mkcQuery = new LambdaQueryWrapper<ProductMkc>()
//                .in(ProductMkc::getId, oldMkcIdList);
        if (CollectionUtils.isEmpty(oldMkcList)) {
            log.error("回收的MKC为空");
            return new ArrayList<>();
        }
        List<Integer> oldMkcIdList = oldMkcList.stream()
                .map(BatchReturnMkc::getMkcId)
                .collect(Collectors.toList());
        Map<Integer, BatchReturnMkc> returnMkcMap = oldMkcList.stream()
                .collect(Collectors.toMap(BatchReturnMkc::getMkcId, tmp -> tmp));

        List<ProductMkc> allMkc = CommenUtil.bigDataInQuery(oldMkcIdList,productMkcMapper::listByMkcId);
//        List<ProductMkc> allMkc = productMkcService.list(mkcQuery);
        LocalDateTime nowTime = LocalDateTime.now();
        List<ProductMkc> newMkc = allMkc.stream()
                .map(tmp ->
                        new ProductMkc()
                                .setOldMkcId(tmp.getId())
                                .setPpriceid(tmp.getPpriceid())
                                .setInsourceid(5)
                                .setInuser("系统")
                                .setKcCheck(6)
                                .setAreaid(batchReturn.getAreaId())
                                .setFrareaid(batchReturn.getAreaId())
                                .setOrigareaid(batchReturn.getAreaId())
                                .setInprice(tmp.getInprice())
                                .setInbeihuoprice(tmp.getInbeihuoprice())
                                .setInbeihuodate(LocalDateTime.now())
                                .setImei(tmp.getImei())
                                .setImeidate(tmp.getImeidate())
                                .setInbeihuo(2)
                                .setCaigoulock(true)
                                .setVoucherId(tmp.getVoucherId())
                                .setInPzid(tmp.getInPzid())
                                .setProtectPrice(tmp.getProtectPrice())
                                .setFanli(tmp.getFanli())
                                .setModifyPrice(tmp.getModifyPrice())
                                .setInsourceid2(tmp.getInsourceid2())
                                .setInnerPrice(tmp.getInnerPrice())
                ).collect(Collectors.toList());
        for (ProductMkc tmpMkc : newMkc) {
            boolean saveSuccess = productMkcService.save(tmpMkc);
            if (!saveSuccess) {
                log.error("转回收机保存失败");
                return new ArrayList<>();
            }
            returnMkcMap.get(tmpMkc.getOldMkcId()).setToMkcId(tmpMkc.getId());
        }
        int pageSize = NumberConstant.ONE_THOUSAND.intValue();
        int totalPage =  PageUtil.totalPage(returnMkcMap.values().size(), pageSize);
        int updateToMkc = 0;
        for (int i = 0; i < totalPage; i++) {
            updateToMkc = batchReturnMkcDao.updateToMkcBatch(CollUtil.page(i,pageSize,returnMkcMap.values()
                    .stream().collect(Collectors.toList())));
        }

        if (updateToMkc <= 0) {
            log.error("更新 记录中，转新机失败");
            return new ArrayList<>();
        }
        return newMkc;
    }

    private boolean checkReturnWholeSub(List<BatchReturnSubBO> allMkcOfSub, List<BatchReturnMkc> toReturnMkcList) {
        if(!CollUtil.containsAll(allMkcOfSub.stream().map(BatchReturnSubBO::getMkcId).collect(Collectors.toList()),
                toReturnMkcList.stream().map(BatchReturnMkc::getMkcId).collect(Collectors.toList()))){
            throw new CustomizeException("商品已通过其他方式退款");
        }
        boolean haveSmall = allMkcOfSub.stream().anyMatch(tmp -> tmp.getIsMobile() == null || !tmp.getIsMobile());
        if (haveSmall) {
            return false;
        }
        return allMkcOfSub.size() == toReturnMkcList.size();
    }


    @Override
    public boolean handleCancel(OaUserBO inUser, BatchReturn batchReturn, BatchReturnReviewReq req) {
        BatchReturn updateTo = new BatchReturn();
        updateTo.setStatus(BatchReturnStatusEnum.CANCELLED.getCode());
        LambdaQueryWrapper<BatchReturn> updateQ = new LambdaQueryWrapper<BatchReturn>()
                .eq(BatchReturn::getId, batchReturn.getId());
        boolean updateSuccess = update(updateTo, updateQ);
        if (!updateSuccess) {
            return false;
        }
        if (StringUtils.isBlank(req.getRemark())) {
            req.setRemark("");
        }
        AddLogReq logReq = new AddLogReq();
        logReq.setId(batchReturn.getId());
        logReq.setCh999Id(inUser.getUserId());
        logReq.setInUser(inUser.getUserName());
        logReq.setComment("取消批量办理，原因：" + req.getRemark());
        logReq.setShowAll(true);
        Boolean isNewBatchRefund = refundMoneyService.enable(batchReturn.getSubId(), Collections.singletonList(TuihuanKindEnum.BATCH_TK.getCode()), batchReturn.getAreaId());
        if(!isNewBatchRefund){
            tuiHuanMapper.updateToDel(batchReturn.getId(),inUser.getUserName(),TuihuanKindEnum.BATCH_TK.getCode());
        } else {
            Optional.ofNullable(refundMoneyService.getRefund(null, req.getId(), TuihuanKindEnum.BATCH_TK))
                    .ifPresent(refund-> refundMoneyService.cancelRefund(refund.getId()));
        }
        R<Boolean> logRes = batchReturnLogClient.addLog(logReq);
        return logRes.getCode() == 0 && logRes.getData();
    }

    @Override
    public Page<BatchReturnPageVO> page(BatchReturnQueryReq req) {
        Page<BatchReturnPageVO> page = new Page<>(req.getCurrent(), req.getSize());
        page.setDesc("br.id");
        //结束时间调整下包含最后一天
        if(req.getEndTime() != null && req.getEndTime().isEqual(req.getEndTime().truncatedTo(ChronoUnit.DAYS))){
            req.setEndTime(req.getEndTime().plusDays(1).minusNanos(1));
        }
        return baseMapper.page(page, req);
    }

    @Override
    public String getSecretByCode(Integer code) {
        AtomicReference<String> secret = new AtomicReference<>(batchReturnMkcDao.getSecretByCode(code));
        if(StrUtil.isBlank(secret.get())){
            //插入一条数据
            secret.set(IdUtil.fastSimpleUUID());
            //独立事务提交,防止不必要回滚
            new MultipleTransaction().execute(DataSourceConstants.CH999_OA_NEW,()->{
                if(batchReturnMkcDao.insertSecret(code,secret.get())<1){
                    //没插入成功,数据库已经存在,重新查询
                    secret.set(batchReturnMkcDao.getSecretByCode(code));
                }
            }).commit();

        }
        return secret.get();
    }

    private List<Integer> getRelatedCh999Id(BatchReturn po) {
        List<Integer> userIdList = new ArrayList<>();
        CollectionUtils.addIgnoreNull(userIdList, po.getTakeCh999Id());
        CollectionUtils.addIgnoreNull(userIdList, po.getReview1Ch999Id());
        CollectionUtils.addIgnoreNull(userIdList, po.getReview2Ch999Id());
        CollectionUtils.addIgnoreNull(userIdList, po.getReturnCh999Id());
        return userIdList;
    }

    private void assertCheckUpdate(BatchReturn batchReturn, BatchReturnTakeReq req, OaUserBO oaUser) {
        try {
            String bankWay = BatchReturnWayEnum.BANK.getMessage();
            boolean isNotKemuReturnWay = BatchReturnWayEnum.listNotKemuReturnWay().contains(req.getReturnWay());
            boolean isDealKemu = isNotKemuReturnWay || bankWay.equals(req.getReturnWay());
            if(isNotKemuReturnWay){
                //现金,余额清理科目
                req.setKemu(null);
            }else if(!isDealKemu){
                //非手动处理的科目,自动获取
                req.setKemu(SpringUtil.getBean(KemuService.class).getKemuByReturnWay(req.getReturnWay(),batchReturn.getAreaId()));
            }
                //验证地区是否一致
            AreaInfoClient areaInfoClient = SpringUtil.getBean(AreaInfoClient.class);
            AreaInfo areaInfo = areaInfoClient.getAreaInfoById(batchReturn.getAreaId()).getData();
            Assert.isTrue(Objects.equals(batchReturn.getAreaId(), oaUser.getAreaId()), StrUtil.format("地区不符，请切换至{}再操作！", areaInfo.getArea()));
            //支付配置验证 是否为原路径退款
            boolean isPayOnline = shouhouRefundService.isPayOnline(req.getReturnWay(),oaUser);
            //非原路径退款 支付记录必须为空
            Assert.isFalse(!isPayOnline && CollUtil.isNotEmpty(req.getPayRecords()), "非原路径退款,支付记录必须为空");
            Assert.isFalse(isPayOnline && CollUtil.isEmpty(req.getPayRecords()), "原路径退款,退款交易号不能为空");
            BigDecimal totalReturnPrice = req.getMkcInfo().stream().map(BatchReturnMkcVO::getPrice).reduce(BigDecimal::add)
                    .orElse(BigDecimal.ZERO);
            //原路径退款,退款金额必须与配件的金额相等
            Assert.isFalse(CollUtil.isNotEmpty(req.getPayRecords())&& req.getPayRecords().stream().map(TuihuanFormVo.PayRecordVo::getMoney).reduce(BigDecimal::add)
                        .orElse(BigDecimal.ZERO).compareTo(totalReturnPrice) != 0, "原路径退款,退款金额必须与配件的金额相等");
            //退款金额必须小于最大可退金额
            BigDecimal maxTotalReturnPrice = batchReturnMkcDao.listMkcVoByBatchReturnId(batchReturn.getId()).stream().map(BatchReturnMkcVO::getOriginalPrice)
                    .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            Assert.isTrue(totalReturnPrice.compareTo(maxTotalReturnPrice) <= 0,"退款金额必须小于等于最大可退金额");
            Assert.isFalse(baseMapper.existsGtBuyPrice(batchReturn.getId()),"商品的退款金额必须小于购买金额");
            Assert.isFalse(bankWay.equals(req.getReturnWay()) && StrUtil.isBlank(req.getBankCardNum()),"银行转账卡号不能为空");
            //自动获取的科目不能为空 银行转账科目在审批的时候进行验证 不再进行校验 由财务那边处理
            //Assert.isFalse(!isDealKemu && StrUtil.isBlank(req.getKemu()), StrUtil.format("{}科目不能为空",req.getReturnWay()));
            Assert.isTrue(req.getMkcInfo().stream().allMatch(mkc-> CompareUtil.compare(mkc.getPrice(),BigDecimal.ZERO)>=0),"商品退款金额必须大于等于零");
            Assert.isTrue(req.getPayRecords().stream().allMatch(mkc-> CompareUtil.compare(mkc.getMoney(),BigDecimal.ZERO)>=0),"支付记录的退款金额必须大于等于零");
            // todo 已经提交组合退, 不允许金额动变动
            List<ShouhouTuihuan> tuihuanList = shouhouTuihuanService.lambdaQuery().eq(ShouhouTuihuan::getShouhouId, batchReturn.getId())
                    .eq(ShouhouTuihuan::getTuihuanKind, TuihuanKindEnum.BATCH_TK.getCode())
                    .and(CommenUtil.isNullOrEq(ShouhouTuihuan::getIsdel, 0))
                    .eq(ShouhouTuihuan::getTuiKinds, ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode())
                    .select(ShouhouTuihuan::getId)
                    .list();
            if(CollUtil.isNotEmpty(tuihuanList)){
                throw new CustomizeException("已经提交组合退, 不允许修改");
            }
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        //校验通过
    }

    private void assertCheckForReview(BatchReturnReviewReq req, OaUserBO oaUser, BatchReturn batchReturn) {
        try {
            Boolean isPass = Optional.ofNullable(req.getIsPass()).orElse(Boolean.TRUE);
            Assert.isFalse(req.getType() ==1 && isPass
                    && !RankEnum.hasAuthority(oaUser.getRank(),RankEnum.SHOUHOU_TUIHUAN_CHECK2),"没有批量退款审核1的权限,权值:63");
            Assert.isFalse(req.getType() ==2 && isPass
                    && !RankEnum.hasAuthority(oaUser.getRank(),RankEnum.BATCH_TUIHUAN_CHECK2),"没有批量退款审核2的权限,权值:65");
            boolean isReturn = req.getType() == 99;
            // 退款办理,与组合退权值控制一致,  组合退, 这里不用验证
            boolean enable = refundMoneyService.enable(batchReturn.getSubId(), Collections.singletonList(TuihuanKindEnum.BATCH_TK.getCode()), batchReturn.getAreaId());
            if(isReturn && isPass && ! enable){
                ShouhouRefundDetailVo.ProcessStatus notWorkProcessStatus = ParentTuiHuanKindService.getNotWorkProcessStatus(oaUser.getRank(),
                        RefundMoneyService.getTuiGroupByTuiWay(batchReturn.getReturnWay()).getCode(), batchReturn.getReturnWay());
                if (notWorkProcessStatus != null){
                    throw new CustomizeException(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.NOT_WORK_RANK).stream()
                            .collect(Collectors.joining(StringPool.SPACE)));
                }
                //退款方式判断
                ShouhouTuiHuanPo tuiHuanPo = shouhouRefundService.getLastNotCompleteRefund(batchReturn.getId(), TuihuanKindEnum.BATCH_TK);
                if(tuiHuanPo != null){
                    //微信支付宝秒退验证
                    AtomicReference<OpenIdInfoBo> infoRef = new AtomicReference<>();
                    shouhouRefundService.setRefundWayValid((url, info) -> {
                        infoRef.set(info);
                    }, OpenValidInfoBo.from(tuiHuanPo).setOrderBusinessTypeEnum(OpenValidInfoBo.OrderBusinessTypeEnum.ORDER).setSubId(batchReturn.getSubId()));
                    if(ShouhouRefundService.WECHAT_REFUND_WAY.equals(tuiHuanPo.getTuiWay())){
                        Assert.isFalse(Objects.isNull(infoRef.get()),"请先绑定微信验证信息！");
                        //微信秒退
                        SpringUtil.getBean(WechatAlipaySecondsRefundService.class).wechatCheckLimit(tuiHuanPo.getTuikuanM(), oaUser.getRank(), infoRef.get().getUserId())
                                .filter(r -> !r.isSuccess())
                                .ifPresent(r -> {
                                    throw new CustomizeException(r);
                                });
                    } else if(ShouhouRefundService.ALIPAY_REFUND_WAY.equals(tuiHuanPo.getTuiWay())) {
                        Assert.isFalse(Objects.isNull(infoRef.get()),"请先绑定支付宝验证信息！");
                        //支付宝秒退
                        SpringUtil.getBean(WechatAlipaySecondsRefundService.class).alipayCheckLimit(tuiHuanPo.getTuikuanM(), oaUser.getRank(), infoRef.get().getUserId())
                                .filter(r -> !r.isSuccess())
                                .ifPresent(r -> {
                                    throw new CustomizeException(r);
                                });
                    }

                }
            }
        } catch (IllegalArgumentException e) {
            throw new CustomizeException(e.getMessage());
        }
        //校验通过
    }

}
