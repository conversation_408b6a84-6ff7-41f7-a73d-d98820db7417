package com.jiuji.oa.afterservice.statistics.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.*;
import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.ContentType;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.jiuji.cloud.org.vo.enums.RoleTermModuleEnum;
import com.jiuji.oa.afterservice.bigpro.statistics.service.ShouhouStatisticsService;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.config.properties.JiujiSystemProperties;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.util.BusinessUtil;
import com.jiuji.oa.afterservice.common.util.ExcelUtils;
import com.jiuji.oa.afterservice.common.util.ExcelWriterUtil;
import com.jiuji.oa.afterservice.common.vo.CheckDataViewScopeReq;
import com.jiuji.oa.afterservice.common.vo.res.CommonDataRes;
import com.jiuji.oa.afterservice.statistics.bo.person.ShouHouPersonStatisticsColumn;
import com.jiuji.oa.afterservice.statistics.bo.person.UserDepartInfoBO;
import com.jiuji.oa.afterservice.statistics.dao.PersonStatisticsMapper;
import com.jiuji.oa.afterservice.statistics.dao.PersonStatisticsV2Mapper;
import com.jiuji.oa.afterservice.statistics.enums.PersonStatisticsTypeEnum;
import com.jiuji.oa.afterservice.statistics.enums.SmallproStatisticsFilterEnum;
import com.jiuji.oa.afterservice.statistics.service.PersonStatisticsService;
import com.jiuji.oa.afterservice.statistics.vo.req.PersonStatisticsReq;
import com.jiuji.oa.afterservice.statistics.vo.res.PersonStatisticsAppVO;
import com.jiuji.oa.afterservice.statistics.vo.res.PersonStatisticsVO;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.oa.orginfo.userinfo.client.UserInfoClient;
import com.jiuji.oa.orginfo.userinfo.vo.Ch999UserVo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.EnumUtil;
import com.jiuji.tc.utils.enums.EnumVO;
import com.jiuji.tc.utils.transaction.MultipleTransaction;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.DefaultTypedTuple;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.StopWatch;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * description: <  >
 * translation: <  >
 * date : 2020-10-10 14:09
 *
 * <AUTHOR> leee41
 **/
@Service
@RequiredArgsConstructor
@Slf4j
public class PersonStatisticsServiceImpl implements PersonStatisticsService {

    private final PersonStatisticsMapper personStatisticsMapper;
    private final ShouhouStatisticsService shouhouStatisticsService;
    @Qualifier("pushMessageExecutor")
    private final ThreadPoolTaskExecutor executor;
    private final JiujiSystemProperties jiujiSystemProperties;
    private final SysConfigClient sysConfigClient;
    private final AbstractCurrentRequestComponent currentRequestComponent;
    public static final String STOP_WATCH_FORMAT = "{}计时:{}";
    private final StringRedisTemplate stringRedisTemplate;

    /**
     * 个人售后统计
     *
     * @param req req
     */
    @Override
    public List<PersonStatisticsVO> personStatistics(PersonStatisticsReq req) {
        OaUserBO oaUserBO = currentRequestComponent.getCurrentStaffId();
        if(CollUtil.isEmpty(req.getTypeCodes()) && CollUtil.isEmpty(req.getTypes())){
            throw new CustomizeException("统计类型不能为空");
        }
        //获取不同版本的mapper
        PersonStatisticsMapper personStatisticsMapper = DecideUtil.iif(PersonStatisticsReq.PersonStatisticsVersionEnum.VERSION_2.getCode().equals(req.getVersion())
                , SpringUtil.getBean(PersonStatisticsV2Mapper.class),this.personStatisticsMapper);
        //参数类型处理
        Optional.ofNullable(req.getTypeCodes()).map(typeCodes -> typeCodes.stream().map(code -> EnumUtil.getEnumByCode(PersonStatisticsTypeEnum.class,code))
                .filter(Objects::nonNull).collect(Collectors.toSet())).ifPresent(req::setTypes);
        //参数时间处理
        if (Objects.equals(null, req.getStart())
                || Objects.equals(null, req.getEnd())
                || req.getStart().isAfter(req.getEnd())
                || req.getStart().getYear() < 2003
                || req.getEnd().getYear() < 2003
                || req.getEnd().isAfter(req.getStart().plusMonths(3L))
        ) {
            req.setStart(LocalDateTime.now().plusMonths(-1L));
            req.setEnd(LocalDateTime.now());
        }
        final int count = req.getTypes().size();
        if (count == 0) {
            return new ArrayList<>();
        }
        R<Boolean> authApartR = sysConfigClient.getAuthApart(oaUserBO.getXTenant());
        R<Boolean> departManageR = sysConfigClient.isDepartManageAreaId(oaUserBO.getAreaId(),oaUserBO.getXTenant());
        if (authApartR.getCode() == ResultCode.SUCCESS && departManageR.getCode() == ResultCode.SUCCESS){
            if (authApartR.getData() && !departManageR.getData()){
                req.setAuthorizeId(oaUserBO.getAuthorizeId());
                req.setAuthPart(Boolean.TRUE);
            }
        }
        if (!(Objects.equals(null, req.getAreaCodes()) || req.getAreaCodes().isEmpty())) {
            List<Long> areaIds = MultipleTransaction.query(DataSourceConstants.CH999_OA_NEW_REPORT,()->
                    this.shouhouStatisticsService.getAreaIdsByCodes(req.getAreaCodes().stream().filter(s -> s.startsWith("a"))
                            .collect(Collectors.toSet())));
            areaIds.addAll(req.getAreaCodes().stream().filter(s ->
                    !s.startsWith("a")).map(Long::valueOf).collect(Collectors.toList()));
            req.setAreaIds(areaIds);
        }
        CompletableFuture<List<PersonStatisticsVO>> base = CompletableFuture.supplyAsync(() ->
                invokeWithStopWatch(StrUtil.format("个人业绩统计base数据计时:selectWithNoJoin1"),()->personStatisticsMapper.selectWithNoJoin1(req)), executor);
        List<CompletableFuture<List<PersonStatisticsVO>>> completableFutures =
                req.getTypes().stream().flatMap(typeEnum -> this.makeFutures(typeEnum, req).stream()).collect(Collectors.toList());
        List<PersonStatisticsVO> rtn = base.join();
        rtn.removeIf(personStatisticsVO -> Objects.equals(null, personStatisticsVO.getYuanGong()));
        completableFutures.forEach(resFuture -> resFuture.join().forEach(res ->
                rtn.stream().filter(target ->
                        target.getYuanGong().equals(res.getYuanGong())).forEach(filter ->
                        this.copyPropertiesAndReturn(res, filter))
        ));
        //离职员工且统计数据为空,移除该数据
        rtn.removeIf(personStatistics -> !personStatistics.getYouXiao() && !Boolean.TRUE.equals(personStatistics.getIsZaizhi()));
        PersonStatisticsVO sum = this.sumAll(rtn);
        sum.setDiQu("合计");
        rtn.add(sum);
        //个人手机销量的统计 按需统计
        statisticsGeRengShouJiXiaoLiang(req,rtn);
        rtn.forEach(this::dealData);
        return rtn;
    }

    /**
     * 统计个人手机销量
     * @param req
     * @param rtn
     */
    private void statisticsGeRengShouJiXiaoLiang(PersonStatisticsReq req, List<PersonStatisticsVO> rtn) {
        if(PersonStatisticsReq.PersonStatisticsVersionEnum.VERSION_2.getCode().equals(req.getVersion())){
            //新版本 统计手机销量 ps.getRuanJianJieJianLiang() 由于软件接件率不再需要统计,直接去掉了
        }
    }

    private List<PersonStatisticsVO> personStatisticsMaoli(PersonStatisticsReq req) {
        //获取不同版本的mapper
        PersonStatisticsMapper personStatisticsMapper = DecideUtil.iif(PersonStatisticsReq.PersonStatisticsVersionEnum.VERSION_2.getCode().equals(req.getVersion())
                , SpringUtil.getBean(PersonStatisticsV2Mapper.class),this.personStatisticsMapper);

        CompletableFuture<List<PersonStatisticsVO>> base = CompletableFuture.supplyAsync(() ->
                invokeWithStopWatch(StrUtil.format("个人业绩统计base数据计时:selectWithNoJoin1"),()->personStatisticsMapper.selectWithNoJoin1(req)), executor);
        List<PersonStatisticsVO> rtn = base.join();
        List<CompletableFuture<List<PersonStatisticsVO>>> completableFutures = new ArrayList<>();
        completableFutures.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,"","selectWithNoJoin3")
                ,()->personStatisticsMapper.selectWithNoJoin8(req)), executor));
        completableFutures.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,"","selectWithNoJoin3")
                ,()->personStatisticsMapper.selectWithNoJoin16(req)), executor));

        completableFutures.forEach(resFuture -> resFuture.join().forEach(res ->
                rtn.stream().filter(target ->
                        target.getYuanGong().equals(res.getYuanGong())).forEach(filter ->
                        this.copyPropertiesAndReturn(res, filter))
        ));
        //离职员工且统计数据为空,移除该数据
        rtn.removeIf(personStatistics -> !personStatistics.getYouXiao() && !Boolean.TRUE.equals(personStatistics.getIsZaizhi()));
        return rtn;
    }

    private BigDecimal statisticsWeixiuMaoli(PersonStatisticsReq req) {
        String maoliKey = StrUtil.format(RedisKeys.SGOUHOU_PERSON_MAOLI_KEY, LocalDateTimeUtil.format(req.getStart(), DatePattern.PURE_DATE_PATTERN), LocalDateTimeUtil.format(req.getEnd(), DatePattern.PURE_DATE_PATTERN));
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(maoliKey))) {
            Object obj = stringRedisTemplate.opsForHash().get(maoliKey, req.getWeixiuren());
            return Convert.toBigDecimal(obj, BigDecimal.ZERO);
        }
        String yuangong = req.getWeixiuren();
        req.setWeixiuren("");
        List<PersonStatisticsVO> rtn = personStatisticsMaoli(req);
        req.setWeixiuren(yuangong);
        Map<String, String> maoliMap = rtn.stream().collect(Collectors.toMap(PersonStatisticsVO::getYuanGong, v -> {
            //维修毛利 客服业绩->维修毛利 + 【维修业绩->维修毛利】 + 【维修业绩->自接自修毛利】
            BigDecimal jieJianRenWeiXiuMaoLi = Optional.ofNullable(v.getJieJianRenWeiXiuMaoLi()).orElse(BigDecimal.ZERO);
            BigDecimal weiXiuMaoLi = Optional.ofNullable(v.getWeiXiuMaoLi()).orElse(BigDecimal.ZERO);
            BigDecimal ziJieZiXiuMaoLi = Optional.ofNullable(v.getZiJieZiXiuMaoLi()).orElse(BigDecimal.ZERO);
            BigDecimal maoli = jieJianRenWeiXiuMaoLi.add(weiXiuMaoLi).add(ziJieZiXiuMaoLi);
            return Convert.toStr(maoli, "");
        }, (v1, v2) -> v2));
        stringRedisTemplate.opsForHash().putAll(maoliKey, maoliMap);
        stringRedisTemplate.expire(maoliKey, 1, TimeUnit.DAYS);
        return Convert.toBigDecimal(maoliMap.get(yuangong),BigDecimal.ZERO);
    }

    private PersonStatisticsAppVO statisticsWeixiuMaoliRank(PersonStatisticsReq personStatisticsReq) {
        PersonStatisticsReq req = ObjectUtil.cloneByStream(personStatisticsReq);
        //获取不同版本的mapper
        PersonStatisticsMapper personStatisticsMapper = DecideUtil.iif(PersonStatisticsReq.PersonStatisticsVersionEnum.VERSION_2.getCode().equals(req.getVersion())
                , SpringUtil.getBean(PersonStatisticsV2Mapper.class),this.personStatisticsMapper);
        List<UserDepartInfoBO> userDepartInfoList = personStatisticsMapper.selectUserDepartInfo(req);
        UserDepartInfoBO userDepartInfo = userDepartInfoList.stream().filter(v -> req.getWeixiuren().equals(v.getUserName())).findFirst().orElse(null);
        if (Objects.isNull(userDepartInfo)) {
            return new PersonStatisticsAppVO();
        }
        String maoliRankKey = StrUtil.format(RedisKeys.SGOUHOU_PERSON_MAOLI_RANK_KEY, LocalDateTimeUtil.format(req.getStart(), DatePattern.PURE_DATE_PATTERN), LocalDateTimeUtil.format(req.getEnd(), DatePattern.PURE_DATE_PATTERN));
        String allRankKey = maoliRankKey + "all";
        if (Boolean.TRUE.equals(stringRedisTemplate.hasKey(allRankKey))) {
            return getRankByCache(userDepartInfo,maoliRankKey);
        }
        userDepartInfoList = personStatisticsMapper.selectUserDepartInfo(new PersonStatisticsReq());

        req.setWeixiuren("");
        List<PersonStatisticsVO> personStatisticsList = personStatisticsMaoli(req);
        Map<String, BigDecimal> maoliMap = personStatisticsList.stream()
                .collect(Collectors.toMap(PersonStatisticsVO::getYuanGong, v ->
                        Optional.ofNullable(v.getJieJianRenWeiXiuMaoLi()).orElse(BigDecimal.ZERO)
                                .add(Optional.ofNullable(v.getWeiXiuMaoLi()).orElse(BigDecimal.ZERO))
                                .add(Optional.ofNullable(v.getZiJieZiXiuMaoLi()).orElse(BigDecimal.ZERO)), (v1, v2) -> v2));
        userDepartInfoList.forEach(v -> v.setMaoli(maoliMap.getOrDefault(v.getUserName(),BigDecimal.ZERO)));
        Map<String, List<UserDepartInfoBO>> areaMap = new HashMap<>();
        areaMap.put("all", userDepartInfoList);
        flushedRankCache(maoliRankKey, areaMap);
        Map<String, List<UserDepartInfoBO>> bigAreaMap = userDepartInfoList.stream()
                .filter(v -> Objects.nonNull(v.getBigArea()))
                .collect(Collectors.groupingBy(UserDepartInfoBO::getBigArea));
        flushedRankCache(maoliRankKey, bigAreaMap);
        Map<String, List<UserDepartInfoBO>> smallAreaMap = userDepartInfoList.stream()
                .filter(v -> Objects.nonNull(v.getSmallArea()))
                .collect(Collectors.groupingBy(UserDepartInfoBO::getSmallArea));
        flushedRankCache(maoliRankKey, smallAreaMap);
        Map<String, List<UserDepartInfoBO>> levelMap = userDepartInfoList.stream()
                .filter(v -> Objects.nonNull(v.getShouhouLevel()))
                .collect(Collectors.groupingBy(v -> Convert.toStr(v.getShouhouLevel())));
        flushedRankCache(maoliRankKey, levelMap);
        return getRankByCache(userDepartInfo,maoliRankKey);
    }

    private PersonStatisticsAppVO getRankByCache(UserDepartInfoBO userDepartInfo,String maoliRankKey) {
        Long allRank = getCacheRank(maoliRankKey + "all", userDepartInfo.getUserName());
        Long bigAreaRank = getCacheRank(maoliRankKey + userDepartInfo.getBigArea(), userDepartInfo.getUserName());
        Long smallAreaRank = getCacheRank(maoliRankKey + userDepartInfo.getSmallArea(), userDepartInfo.getUserName());
        Long levelAreaRank = getCacheRank(maoliRankKey + userDepartInfo.getShouhouLevel(), userDepartInfo.getUserName());
        PersonStatisticsAppVO personStatistics = new PersonStatisticsAppVO();
        personStatistics.setAllRank(allRank != null ? allRank + 1: 0);
        personStatistics.setBigAreaRank(bigAreaRank != null ? bigAreaRank + 1: 0);
        personStatistics.setSmallAreaRank(smallAreaRank != null ? smallAreaRank + 1: 0);
        personStatistics.setLevelRank(levelAreaRank != null ? levelAreaRank + 1: 0);
        return personStatistics;
    }

    private Long getCacheRank(String key, String userName) {
        return stringRedisTemplate.opsForZSet().reverseRank(key, userName);
    }

    private void flushedRankCache(String maoliRankKey, Map<String, List<UserDepartInfoBO>> areaMap) {
        areaMap.forEach((k,v) -> {
            String key = maoliRankKey + k;
            Set<ZSetOperations.TypedTuple<String>> typedTuple = v.stream().map(x -> new DefaultTypedTuple<String>(x.getUserName(), Convert.toDouble(x.getMaoli(), 0.0))).collect(Collectors.toSet());
            stringRedisTemplate.opsForZSet().add(key, typedTuple);
            stringRedisTemplate.expire(key, 1, TimeUnit.HOURS);
        });
    }

    @Override
    public R<Dict> statisticsEnumsV2() {
        Dict result = Dict.create();
        List<PersonStatisticsTypeEnum> statisticsTypeSelects = Arrays.asList(PersonStatisticsTypeEnum.KE_FU_YE_JI
                , PersonStatisticsTypeEnum.JI_SHU_RUAN_JIAN_LIANG,PersonStatisticsTypeEnum.WEI_XIU_YE_JI);
        result.set("statisticsType", Arrays.stream(PersonStatisticsTypeEnum.values())
                .filter(v -> ShouHouPersonStatisticsColumn.TitleColumn.SHOUHOU_YE_JI.getStatisticsCodes().contains(v.getCode()))
                .map(v -> new EnumVO().setValue(v.getCode()).setSelected(statisticsTypeSelects.contains(v)).setLabel(v.getMessage())).collect(Collectors.toList()))
                .set("areaKind", Arrays.stream(SmallproStatisticsFilterEnum.values()).filter(v -> Objects.equals(v.getFlag(), NumberConstant.TWO))
                        .map(v -> new EnumVO().setLabel(v.getMessage()).setValue(ObjectUtil.defaultIfNull(v.getCode(),0))
                                .setSelected(v == SmallproStatisticsFilterEnum.SMALLPRO_STATISTICS_FILTER_AREA_KIND_ALL))
                        .collect(Collectors.toList()));
        return R.success(result);
    }

    @Override
    public R<CommonDataRes<PersonStatisticsVO>> personStatisticsV2(PersonStatisticsReq req) {
        //角色数据查询
        R dataViewRes = BusinessUtil.checkDataViewScope(CheckDataViewScopeReq.builder().moduleEnum(RoleTermModuleEnum.AFTER_SALES)
                .getStartTimeFun(req::getStart)
                .getEndTimeFun(req::getEnd)
                .setStartTimeFun(req::setStart)
                .setEndTimeFun(req::setEnd)
                .build(), null);
        if (!dataViewRes.isSuccess()) {
            return R.error(dataViewRes.getUserMsg());
        }
        req.setVersion(PersonStatisticsReq.PersonStatisticsVersionEnum.VERSION_2.getCode());
        List<PersonStatisticsVO> personStatisticsList = personStatistics(req);
        CommonDataRes<PersonStatisticsVO> result = new CommonDataRes<>();
        result.setList(personStatisticsList);
        result.setColumns(ShouHouPersonStatisticsColumn.buildColumns(req));
        return R.success(result);
    }

    @Override
    public R<PersonStatisticsAppVO> personStatisticsApp(PersonStatisticsReq req) {
        req.setVersion(PersonStatisticsReq.PersonStatisticsVersionEnum.VERSION_2.getCode());
        req.setTypeCodes(Arrays.stream(PersonStatisticsTypeEnum.values()).map(PersonStatisticsTypeEnum::getCode).collect(Collectors.toSet()));
        //参数时间处理
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime firstDay = now.with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
        if (Objects.equals(null, req.getStart())
                || Objects.equals(null, req.getEnd())
                || req.getStart().isAfter(req.getEnd())) {
            req.setStart(firstDay);
            req.setEnd(now);
        }
        CompletableFuture<PersonStatisticsAppVO> rankFuture = CompletableFuture.supplyAsync(() ->
                invokeWithStopWatch(StrUtil.format("个人业绩统计排名查询:statisticsWeixiuMaoliRank"),()->statisticsWeixiuMaoliRank(req)), executor);
        List<PersonStatisticsVO> personStatisticsList = getAppPersonStatistics(req);
        PersonStatisticsVO personStatistics = personStatisticsList.stream().filter(v -> req.getWeixiuren().equals(v.getYuanGong())).findFirst().orElse(new PersonStatisticsVO());
        PersonStatisticsAppVO result = new PersonStatisticsAppVO();
        //维修毛利 客服业绩->维修毛利 + 【维修业绩->维修毛利】 + 【维修业绩->自接自修毛利】
        BigDecimal jieJianRenWeiXiuMaoLi = Optional.ofNullable(personStatistics.getJieJianRenWeiXiuMaoLi()).orElse(BigDecimal.ZERO);
        BigDecimal weiXiuMaoLi = Optional.ofNullable(personStatistics.getWeiXiuMaoLi()).orElse(BigDecimal.ZERO);
        BigDecimal ziJieZiXiuMaoLi = Optional.ofNullable(personStatistics.getZiJieZiXiuMaoLi()).orElse(BigDecimal.ZERO);
        BigDecimal maoLi = jieJianRenWeiXiuMaoLi.add(weiXiuMaoLi).add(ziJieZiXiuMaoLi);
        result.setWeixiuMaoLi(maoLi);
        //硬件总接件量
        result.setZongJieJianLiang(personStatistics.getZongJieJianLiang());
        //维修量 总修好量 - 外送修好量
        int weixiuLiang = Optional.ofNullable(personStatistics.getZongXiuHaoLiang()).orElse(0) - Optional.ofNullable(personStatistics.getWaiSongXiuHaoLiang()).orElse(0);
        result.setWeixiuLiang(weixiuLiang);
        result.setHuanWaiPingMaoLi(personStatistics.getHuanWaiPingMaoLi());
        result.setHouGaiXianDaShouLv(personStatistics.getHouGaiXianDaShouLv());
        result.setAnXinBaoDaShouLv(personStatistics.getAnXinBaoDaShouLv());
        result.setDianChiXianDaShouLv(personStatistics.getDianChiXianDaShouLv());
        result.setSuiPingXianDaShouLv(personStatistics.getSuiPingXianDaShouLv());
        //环比
        //计算截止当前时间前一天的数据
        BigDecimal huanbiMaoli = BigDecimal.ZERO;
        //当前时间是当月的第1天不计算
        if (!now.toLocalDate().equals(firstDay.toLocalDate())) {
            LocalDateTime endDay = now.minusDays(1).with(LocalTime.MAX);
            req.setStart(firstDay);
            req.setEnd(endDay);
            BigDecimal currMaoLi = statisticsWeixiuMaoli(req);
            LocalDateTime preStart = firstDay.minusMonths(1).with(TemporalAdjusters.firstDayOfMonth()).with(LocalTime.MIN);
            LocalDateTime preEnd = endDay.minusMonths(1).with(LocalTime.MAX);
            req.setStart(preStart);
            req.setEnd(preEnd);
            BigDecimal preMaoli = statisticsWeixiuMaoli(req);
            if (BigDecimal.ZERO.compareTo(preMaoli) != 0) {
                huanbiMaoli = currMaoLi.subtract(preMaoli).divide(preMaoli, NumberConstant.FOUR, RoundingMode.HALF_UP);
            }
        }
        result.setHuanbiLv(huanbiMaoli);

        LocalDateTime firstDay1 = firstDay.minusMonths(1);
        LocalDateTime lastDay1 = firstDay1.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
        req.setStart(firstDay1);
        req.setEnd(lastDay1);
        result.setLastMonthWeixiuMaoLi(statisticsWeixiuMaoli(req));
        LocalDateTime firstDay2 = firstDay1.minusMonths(1);
        LocalDateTime lastDay2 = firstDay2.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
        req.setStart(firstDay2);
        req.setEnd(lastDay2);
        result.setPreviousMonthWeixiuMaoLi(statisticsWeixiuMaoli(req));
        LocalDateTime firstDay3 = firstDay2.minusMonths(1);
        LocalDateTime lastDay3 = firstDay3.with(TemporalAdjusters.lastDayOfMonth()).with(LocalTime.MAX);
        req.setStart(firstDay3);
        req.setEnd(lastDay3);
        result.setPreviousThreeMonthWeixiuMaoLi(statisticsWeixiuMaoli(req));
        //排名
        PersonStatisticsAppVO personStatisticsRank = rankFuture.join();
        result.setAllRank(personStatisticsRank.getAllRank());
        result.setBigAreaRank(personStatisticsRank.getBigAreaRank());
        result.setSmallAreaRank(personStatisticsRank.getSmallAreaRank());
        result.setLevelRank(personStatisticsRank.getLevelRank());
        return R.success(result);
    }

    private List<PersonStatisticsVO> getAppPersonStatistics(PersonStatisticsReq req) {
        //获取不同版本的mapper
        PersonStatisticsMapper personStatisticsMapper = DecideUtil.iif(PersonStatisticsReq.PersonStatisticsVersionEnum.VERSION_2.getCode().equals(req.getVersion())
                , SpringUtil.getBean(PersonStatisticsV2Mapper.class),this.personStatisticsMapper);
        //参数类型处理
        Optional.ofNullable(req.getTypeCodes()).map(typeCodes -> typeCodes.stream().map(code -> EnumUtil.getEnumByCode(PersonStatisticsTypeEnum.class,code))
                .filter(Objects::nonNull).collect(Collectors.toSet())).ifPresent(req::setTypes);
        //参数时间处理
        if (Objects.equals(null, req.getStart())
                || Objects.equals(null, req.getEnd())
                || req.getStart().isAfter(req.getEnd())) {
            req.setStart(LocalDateTime.now().plusMonths(-1L));
            req.setEnd(LocalDateTime.now());
        }
        final int count = req.getTypes().size();
        if (count == 0) {
            return new ArrayList<>();
        }
        CompletableFuture<List<PersonStatisticsVO>> base = CompletableFuture.supplyAsync(() ->
                invokeWithStopWatch(StrUtil.format("个人业绩统计base数据计时:selectWithNoJoin1"),()->personStatisticsMapper.selectWithNoJoin1(req)), executor);
        List<CompletableFuture<List<PersonStatisticsVO>>> completableFutures =
                req.getTypes().stream().flatMap(typeEnum -> this.makeFutures(typeEnum, req).stream()).collect(Collectors.toList());
        List<PersonStatisticsVO> rtn = base.join();
        rtn.removeIf(personStatisticsVO -> Objects.equals(null, personStatisticsVO.getYuanGong()));
        completableFutures.forEach(resFuture -> resFuture.join().forEach(res ->
                rtn.stream().filter(target ->
                        target.getYuanGong().equals(res.getYuanGong())).forEach(filter ->
                        this.copyPropertiesAndReturn(res, filter))
        ));
        //离职员工且统计数据为空,移除该数据
        rtn.removeIf(personStatistics -> !personStatistics.getYouXiao() && !Boolean.TRUE.equals(personStatistics.getIsZaizhi()));
        //个人手机销量的统计 按需统计
        statisticsGeRengShouJiXiaoLiang(req,rtn);
        rtn.forEach(this::dealData);
        return rtn;
    }

    @Override
    public void personExport(PersonStatisticsReq req, HttpServletResponse response) {
        R<CommonDataRes<PersonStatisticsVO>> dataR = personStatisticsV2(req);
        CommonDataRes<PersonStatisticsVO> commonDataRes = dataR.getData();
        if(!dataR.isSuccess() || commonDataRes == null){
            ServletUtil.write(response, JSON.toJSONString(dataR), ContentType.JSON.getValue());
            return;
        }
        ByteArrayOutputStream out;
        try (ExcelWriter writer = ExcelUtil.getWriterWithSheet(commonDataRes.getColumns().stream().findFirst()
                .map(CommonDataRes.ColumnsBean::getTitle).orElse(null))){
            ExcelWriterUtil.writeHeadAndData(writer,commonDataRes);
            out = new ByteArrayOutputStream();
            writer.flush(out,true);
        }
        //String fileName = StrUtil.format("{}{}.xls", "售后个人统计", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHH")))
        String fileName = ExcelUtils.getExportFileName("售后个人业绩统计");
        ServletUtil.write(response, IoUtil.toStream(out.toByteArray()), FileUtil.getMimeType(fileName), fileName);
    }

    @Override
    public R<?> personRecent(Integer ch999Id, Integer months) {
        UserInfoClient userInfoClient = SpringUtil.getBean(UserInfoClient.class);
        Ch999UserVo ch999UserInfo = Optional.ofNullable(userInfoClient.getCh999UserInfo(ch999Id))
                .map(r -> {
                    if (!r.isSuccess()){
                        throw new CustomizeException(r.getUserMsg());
                    }
                    return r.getData();
                })
                .orElseThrow(() -> new CustomizeException("员工不存在!"));

        PersonStatisticsReq req = new PersonStatisticsReq();
        req.setWeixiuren(ch999UserInfo.getCh999Name());
        LocalDateTime end = LocalDate.now().atStartOfDay().withDayOfMonth(1);
        req.setStart(end.minusMonths(months));
        req.setEnd(end.minusNanos(1));
        PersonStatisticsV2Mapper personStatisticsV2Mapper = SpringUtil.getBean(PersonStatisticsV2Mapper.class);
        //修不好量
        BigDecimal xiuBuHaoLiang = BigDecimal.valueOf(invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT, PersonStatisticsTypeEnum.WEI_XIU_YE_JI.getMessage(), "selectWithNoJoin15")
                , () -> personStatisticsV2Mapper.selectWithNoJoin15(req)).stream().mapToInt(PersonStatisticsVO::getXiuBuHaoLiang).sum());
        //总修好量
        BigDecimal zongXiuHaoLiang = BigDecimal.valueOf(invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT, PersonStatisticsTypeEnum.WEI_XIU_YE_JI.getMessage(), "selectWithNoJoin12")
                , () -> personStatisticsV2Mapper.selectWithNoJoin12(req)).stream().mapToInt(PersonStatisticsVO::getZongXiuHaoLiang).sum());

        return R.success(Dict.create().set("giveUpRepair",xiuBuHaoLiang.divide(zongXiuHaoLiang.compareTo(BigDecimal.ZERO)>0 ? zongXiuHaoLiang : BigDecimal.ONE)
                .setScale(2,RoundingMode.HALF_UP)));
    }

    private void dealData(PersonStatisticsVO vo) {
        if (!Objects.equals(vo.getZongXiuHaoLiang(), null)) {
            if (!Objects.equals(vo.getXiuBuHaoLiang(), null)) {
                vo.setXiuBuHaoLv(vo.getZongXiuHaoLiang() == 0
                        ? BigDecimal.valueOf(0.00d)
                        : BigDecimal.valueOf(vo.getXiuBuHaoLiang())
                        .divide(BigDecimal.valueOf(vo.getZongXiuHaoLiang()), 4, RoundingMode.HALF_DOWN));
            }
            if (!Objects.equals(vo.getFanXiuLiang(), null)) {
                vo.setFanXiuLv(vo.getZongXiuHaoLiang() == 0
                        ? BigDecimal.valueOf(0.00d)
                        : BigDecimal.valueOf(vo.getFanXiuLiang())
                        .divide(BigDecimal.valueOf(vo.getZongXiuHaoLiang()), 4, RoundingMode.HALF_DOWN));
            }
        }
        if (!Objects.equals(vo.getShouHouDianChiXianLiang(), null)
                && !Objects.equals(vo.getShouHouDianChiChuKuLiang(), null)) {
            vo.setDianChiXianDaShouLv(vo.getShouHouDianChiXianLiang() == 0 || vo.getShouHouDianChiChuKuLiang() == 0
                    ? BigDecimal.valueOf(0.00d)
                    : BigDecimal.valueOf(vo.getShouHouDianChiXianLiang())
                    .divide(BigDecimal.valueOf(vo.getShouHouDianChiChuKuLiang()), 4, RoundingMode.HALF_DOWN));
        }
        if (!Objects.equals(vo.getRuanJianJieJianLiang(), null)
                && !Objects.equals(vo.getGeRengShouJiXiaoLiang(), null)) {
            vo.setRuanJianJieJianLv(vo.getRuanJianJieJianLiang() == 0 || vo.getGeRengShouJiXiaoLiang() == 0
                    ? BigDecimal.valueOf(0.00d)
                    : BigDecimal.valueOf(vo.getRuanJianJieJianLiang())
                    .divide(BigDecimal.valueOf(vo.getGeRengShouJiXiaoLiang()), 4, RoundingMode.HALF_DOWN));
        }
        if (!Objects.equals(vo.getShouHouSuiPingXianLiang(), null)
                && !Objects.equals(vo.getShouHouPingMuChuKuLiang(), null)) {
            vo.setSuiPingXianDaShouLv(vo.getShouHouSuiPingXianLiang() == 0 || vo.getShouHouPingMuChuKuLiang() == 0
                    ? BigDecimal.valueOf(0.00d)
                    : BigDecimal.valueOf(vo.getShouHouSuiPingXianLiang())
                    .divide(BigDecimal.valueOf(vo.getShouHouPingMuChuKuLiang()), 4, RoundingMode.HALF_DOWN));
        }
        if (!Objects.equals(vo.getShouHouHouGaiXianLiang(), null)
                && !Objects.equals(vo.getShouHouHouGaiChuKuLiang(), null)) {
            vo.setHouGaiXianDaShouLv(vo.getShouHouHouGaiXianLiang() == 0 || vo.getShouHouHouGaiChuKuLiang() == 0
                    ? BigDecimal.valueOf(0.00d)
                    : BigDecimal.valueOf(vo.getShouHouHouGaiXianLiang())
                    .divide(BigDecimal.valueOf(vo.getShouHouHouGaiChuKuLiang()), 4, RoundingMode.HALF_DOWN));
        }
        if (!Objects.equals(vo.getAnXinBaoXiaoLiang(), null)
                && !Objects.equals(vo.getAnXinBaoChuKuLiang(), null)) {
            vo.setAnXinBaoDaShouLv(vo.getAnXinBaoXiaoLiang() == 0 || vo.getAnXinBaoChuKuLiang() == 0
                    ? BigDecimal.valueOf(0.00d)
                    : BigDecimal.valueOf(vo.getAnXinBaoXiaoLiang())
                    .divide(BigDecimal.valueOf(vo.getAnXinBaoChuKuLiang()), 4, RoundingMode.HALF_DOWN));
        }
    }

    private List<CompletableFuture<List<PersonStatisticsVO>>> makeFutures(PersonStatisticsTypeEnum typeEnum, PersonStatisticsReq req) {
        List<CompletableFuture<List<PersonStatisticsVO>>> rtn = new ArrayList<>();
        //获取不同版本的mapper
        PersonStatisticsMapper personStatisticsMapper = DecideUtil.iif(PersonStatisticsReq.PersonStatisticsVersionEnum.VERSION_2.getCode().equals(req.getVersion())
                , SpringUtil.getBean(PersonStatisticsV2Mapper.class),this.personStatisticsMapper);

        switch (typeEnum) {
            case JI_SHU_WEI_XIU_FU_WU_KE_FU_TONG_JI:
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin2")
                        ,()->personStatisticsMapper.selectWithNoJoin2(req,jiujiSystemProperties.getOfficeName())), executor));
                break;
            case KE_FU_YE_JI:
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin3")
                        ,()->personStatisticsMapper.selectWithNoJoin3(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin4")
                        ,()->personStatisticsMapper.selectWithNoJoin4(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin5")
                        ,()->personStatisticsMapper.selectWithNoJoin5(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin6")
                        ,()->personStatisticsMapper.selectWithNoJoin6(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin7")
                        ,()->personStatisticsMapper.selectWithNoJoin7(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin8")
                        ,()->personStatisticsMapper.selectWithNoJoin8(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin9")
                        ,()->personStatisticsMapper.selectWithNoJoin9(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin10")
                        ,()->personStatisticsMapper.selectWithNoJoin10(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin11")
                        ,()->personStatisticsMapper.selectWithNoJoin11(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin23")
                        ,()->personStatisticsMapper.selectWithNoJoin23(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin22")
                        ,()->personStatisticsMapper.selectWithNoJoin22(req)), executor));
                break;
            case JI_SHU_RUAN_JIAN_LIANG:
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin11")
                        ,()->personStatisticsMapper.selectWithNoJoin11(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin27")
                        ,()->personStatisticsMapper.selectWithNoJoin27(req)), executor));
                break;
            case WEI_XIU_YE_JI:
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin12")
                        ,()->personStatisticsMapper.selectWithNoJoin12(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin13")
                        ,()->personStatisticsMapper.selectWithNoJoin13(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin14")
                        ,()->personStatisticsMapper.selectWithNoJoin14(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin15")
                        ,()->personStatisticsMapper.selectWithNoJoin15(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin16")
                        ,()->personStatisticsMapper.selectWithNoJoin16(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin17")
                        ,()->personStatisticsMapper.selectWithNoJoin17(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin18")
                        ,()->personStatisticsMapper.selectWithNoJoin18(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin20")
                        ,()->personStatisticsMapper.selectWithNoJoin20(req)), executor));
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin21")
                        ,()->personStatisticsMapper.selectWithNoJoin21(req)), executor));
                break;
            case SHOU_HOU_XIAN_TONG_JI:
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin25")
                        ,()->personStatisticsMapper.selectWithNoJoin25(req)), executor));
                break;
            case CE_SHI_REN_YUAN_YE_JI:
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin19")
                        ,()->personStatisticsMapper.selectWithNoJoin19(req)), executor));
                break;
            case SHANG_MENG_AN_ZHUANG_YE_JI:
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin24")
                        ,()->personStatisticsMapper.selectWithNoJoin24(req)), executor));
                break;
            case HUAN_WAI_PING_YE_WU:
                rtn.add(CompletableFuture.supplyAsync(() -> invokeWithStopWatch(StrUtil.format(STOP_WATCH_FORMAT,typeEnum.getMessage(),"selectWithNoJoin26")
                        ,()->personStatisticsMapper.selectWithNoJoin26(req)), executor));
                break;
            default:
        }
        return rtn;
    }

    private <T> T invokeWithStopWatch(String stopWatchName, Supplier<T> supplier) {
        StopWatch stopWatch = new StopWatch(stopWatchName);
        stopWatch.start();
        try {
            return MultipleTransaction.query(DataSourceConstants.CH999_OA_NEW_REPORT,()->supplier.get());
        } finally {
            stopWatch.stop();
            log.info(stopWatch.prettyPrint());
        }
    }

    private void copyPropertiesAndReturn(PersonStatisticsVO res, PersonStatisticsVO target) {
        if (!Objects.equals(null, res.getRuanJianPingJiaZongLiang())
                && !res.getRuanJianPingJiaZongLiang().equals(0)) {
            target.setRuanJianPingJiaZongLiang(res.getRuanJianPingJiaZongLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getRuanJianPingJiaPingJunFen())
                && res.getRuanJianPingJiaPingJunFen().compareTo(BigDecimal.ZERO) > 0) {
            target.setRuanJianPingJiaPingJunFen(res.getRuanJianPingJiaPingJunFen());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getRuanJianPingJiaChaPingLiang())
                && !res.getRuanJianPingJiaChaPingLiang().equals(0)) {
            target.setRuanJianPingJiaChaPingLiang(res.getRuanJianPingJiaChaPingLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouWeiXiuPingJiaZongLiang())
                && res.getShouHouWeiXiuPingJiaZongLiang().equals(0)) {
            target.setShouHouWeiXiuPingJiaZongLiang(res.getShouHouWeiXiuPingJiaZongLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouWeiXiuPingJiaPingJunFen())
                && res.getShouHouWeiXiuPingJiaPingJunFen().compareTo(BigDecimal.ZERO) > 0) {
            target.setShouHouWeiXiuPingJiaPingJunFen(res.getShouHouWeiXiuPingJiaPingJunFen());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouWeiXiuPingJiaChaPingLiang())
                && !res.getShouHouWeiXiuPingJiaChaPingLiang().equals(0)) {
            target.setShouHouWeiXiuPingJiaChaPingLiang(res.getShouHouWeiXiuPingJiaChaPingLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouJiShuKeFuPingJiaZongLiang())
                && !res.getShouHouJiShuKeFuPingJiaZongLiang().equals(0)) {
            target.setShouHouJiShuKeFuPingJiaZongLiang(res.getShouHouJiShuKeFuPingJiaZongLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouJiShuKeFuPingJiaPingJunFen())
                && res.getShouHouJiShuKeFuPingJiaPingJunFen().compareTo(BigDecimal.ZERO) > 0) {
            target.setShouHouJiShuKeFuPingJiaPingJunFen(res.getShouHouJiShuKeFuPingJiaPingJunFen());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouJiShuKeFuPingJiaChaPingLiang())
                && !res.getShouHouJiShuKeFuPingJiaChaPingLiang().equals(0)) {
            target.setShouHouJiShuKeFuPingJiaChaPingLiang(res.getShouHouJiShuKeFuPingJiaChaPingLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getZongJieJianLiang())
                && !res.getZongJieJianLiang().equals(0)) {
            target.setZongJieJianLiang(res.getZongJieJianLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getZaiBaoLiang())
                && !res.getZaiBaoLiang().equals(0)) {
            target.setZaiBaoLiang(res.getZaiBaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getBuZaiBaoLiang())
                && !res.getBuZaiBaoLiang().equals(0)) {
            target.setBuZaiBaoLiang(res.getBuZaiBaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getWaiXiuLiang())
                && !res.getWaiXiuLiang().equals(0)) {
            target.setWaiXiuLiang(res.getWaiXiuLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getJiuJiFuWuLiang())
                && !res.getJiuJiFuWuLiang().equals(0)) {
            target.setJiuJiFuWuLiang(res.getJiuJiFuWuLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShangMenAnZhuangLiang())
                && !res.getShangMenAnZhuangLiang().equals(0)) {
            target.setShangMenAnZhuangLiang(res.getShangMenAnZhuangLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShangMenAnZhuangMaoLi())
                && res.getShangMenAnZhuangMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setShangMenAnZhuangMaoLi(res.getShangMenAnZhuangMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getJieJianRenWeiXiuMaoLi())
                && res.getJieJianRenWeiXiuMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setJieJianRenWeiXiuMaoLi(res.getJieJianRenWeiXiuMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getJieJianRenJiuJianMaoLi())
                && res.getJieJianRenJiuJianMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setJieJianRenJiuJianMaoLi(res.getJieJianRenJiuJianMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getJieJianRenZhiHuanJiuJianMaoLi())
                && res.getJieJianRenZhiHuanJiuJianMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setJieJianRenZhiHuanJiuJianMaoLi(res.getJieJianRenZhiHuanJiuJianMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getJieJianRenZhiHuanLiang())
                && !res.getJieJianRenZhiHuanLiang().equals(0)) {
            target.setJieJianRenZhiHuanLiang(res.getJieJianRenZhiHuanLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getRuanJianAnZhuangLiang())
                && !res.getRuanJianAnZhuangLiang().equals(0)) {
            target.setRuanJianAnZhuangLiang(res.getRuanJianAnZhuangLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getGeRengShouJiXiaoLiang())
                && !res.getGeRengShouJiXiaoLiang().equals(0)) {
            target.setGeRengShouJiXiaoLiang(res.getGeRengShouJiXiaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getRuanJianJieJianLiang())
                && !res.getRuanJianJieJianLiang().equals(0)) {
            target.setRuanJianJieJianLiang(res.getRuanJianJieJianLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getRuanJianWaiXiuMaoLi())
                && res.getRuanJianWaiXiuMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setRuanJianWaiXiuMaoLi(res.getRuanJianWaiXiuMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getZongXiuHaoLiang())
                && !res.getZongXiuHaoLiang().equals(0)) {
            target.setZongXiuHaoLiang(res.getZongXiuHaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getZhiHuanLiang())
                && !res.getZhiHuanLiang().equals(0)) {
            target.setZhiHuanLiang(res.getZhiHuanLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShangMenLiang())
                && !res.getShangMenLiang().equals(0)) {
            target.setShangMenLiang(res.getShangMenLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getYingPanLiang())
                && !res.getYingPanLiang().equals(0)) {
            target.setYingPanLiang(res.getYingPanLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getWaiSongXiuHaoLiang())
                && !res.getWaiSongXiuHaoLiang().equals(0)) {
            target.setWaiSongXiuHaoLiang(res.getWaiSongXiuHaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getGengPeiXiuHaoLiang())
                && !res.getGengPeiXiuHaoLiang().equals(0)) {
            target.setGengPeiXiuHaoLiang(res.getGengPeiXiuHaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getXinPianXiuHaoLiang())
                && !res.getXinPianXiuHaoLiang().equals(0)) {
            target.setXinPianXiuHaoLiang(res.getXinPianXiuHaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getFanXiuLiang())
                && !res.getFanXiuLiang().equals(0)) {
            target.setFanXiuLiang(res.getFanXiuLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getXiuBuHaoLiang())
                && !res.getXiuBuHaoLiang().equals(0)) {
            target.setXiuBuHaoLiang(res.getXiuBuHaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getWeiXiuMaoLi())
                && res.getWeiXiuMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setWeiXiuMaoLi(res.getWeiXiuMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getZiJieZiXiuMaoLi())
                && res.getZiJieZiXiuMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setZiJieZiXiuMaoLi(res.getZiJieZiXiuMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getWeiXiuRenJiuJianMaoLi())
                && res.getWeiXiuRenJiuJianMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setWeiXiuRenJiuJianMaoLi(res.getWeiXiuRenJiuJianMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getWeiXiuRenZhiHuanJiuJianMaoLi())
                && res.getWeiXiuRenZhiHuanJiuJianMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setWeiXiuRenZhiHuanJiuJianMaoLi(res.getWeiXiuRenZhiHuanJiuJianMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getCeShiZongLiang())
                && !res.getCeShiZongLiang().equals(0)) {
            target.setCeShiZongLiang(res.getCeShiZongLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getCeShiBuTongGuoLiang())
                && !res.getCeShiBuTongGuoLiang().equals(0)) {
            target.setCeShiBuTongGuoLiang(res.getCeShiBuTongGuoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getWuFaCeShiLiang())
                && !res.getWuFaCeShiLiang().equals(0)) {
            target.setWuFaCeShiLiang(res.getWuFaCeShiLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getXiuBuHaoLv())
                && res.getXiuBuHaoLv().compareTo(BigDecimal.ZERO) > 0) {
            target.setXiuBuHaoLv(res.getXiuBuHaoLv());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getFanXiuLv())
                && res.getFanXiuLv().compareTo(BigDecimal.ZERO) > 0) {
            target.setFanXiuLv(res.getFanXiuLv());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getWuMaoLiXinPianXiuHaoLiang())
                && !res.getWuMaoLiXinPianXiuHaoLiang().equals(0)) {
            target.setWuMaoLiXinPianXiuHaoLiang(res.getWuMaoLiXinPianXiuHaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getWuMaoLiGengPeiXiuHaoLiang())
                && !res.getWuMaoLiGengPeiXiuHaoLiang().equals(0)) {
            target.setWuMaoLiGengPeiXiuHaoLiang(res.getWuMaoLiGengPeiXiuHaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getWuMaoLiYingPanXiuHaoLiang())
                && !res.getWuMaoLiYingPanXiuHaoLiang().equals(0)) {
            target.setWuMaoLiYingPanXiuHaoLiang(res.getWuMaoLiYingPanXiuHaoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getFeiBaoWaiSongJiHuanHuoLiang())
                && !res.getFeiBaoWaiSongJiHuanHuoLiang().equals(0)) {
            target.setFeiBaoWaiSongJiHuanHuoLiang(res.getFeiBaoWaiSongJiHuanHuoLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getYuYueJieJianLiang())
                && !res.getYuYueJieJianLiang().equals(0)) {
            target.setYuYueJieJianLiang(res.getYuYueJieJianLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouDianChiXianLiang())
                && !res.getShouHouDianChiXianLiang().equals(0)) {
            target.setShouHouDianChiXianLiang(res.getShouHouDianChiXianLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouDianChiXianJinE())
                && res.getShouHouDianChiXianJinE().compareTo(BigDecimal.ZERO) > 0) {
            target.setShouHouDianChiXianJinE(res.getShouHouDianChiXianJinE());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouDianChiChuKuLiang())
                && !res.getShouHouDianChiChuKuLiang().equals(0)) {
            target.setShouHouDianChiChuKuLiang(res.getShouHouDianChiChuKuLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouDianChiXianChuXianChengBen())
                && res.getShouHouDianChiXianChuXianChengBen().compareTo(BigDecimal.ZERO) > 0) {
            target.setShouHouDianChiXianChuXianChengBen(res.getShouHouDianChiXianChuXianChengBen());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouSuiPingXianLiang())
                && !res.getShouHouSuiPingXianLiang().equals(0)) {
            target.setShouHouSuiPingXianLiang(res.getShouHouSuiPingXianLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouSuiPingXianJinE())
                && res.getShouHouSuiPingXianJinE().compareTo(BigDecimal.ZERO) > 0) {
            target.setShouHouSuiPingXianJinE(res.getShouHouSuiPingXianJinE());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouPingMuChuKuLiang())
                && !res.getShouHouPingMuChuKuLiang().equals(0)) {
            target.setShouHouPingMuChuKuLiang(res.getShouHouPingMuChuKuLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getShouHouSuiPingXianChuXianChengBen())
                && res.getShouHouSuiPingXianChuXianChengBen().compareTo(BigDecimal.ZERO) > 0) {
            target.setShouHouSuiPingXianChuXianChengBen(res.getShouHouSuiPingXianChuXianChengBen());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getHuanWaiPingLiang())
                && !res.getHuanWaiPingLiang().equals(0)) {
            target.setHuanWaiPingLiang(res.getHuanWaiPingLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getHuanWaiPingFanXiuLiang())
                && !res.getHuanWaiPingFanXiuLiang().equals(0)) {
            target.setHuanWaiPingFanXiuLiang(res.getHuanWaiPingFanXiuLiang());
            target.setYouXiao(Boolean.TRUE);
        }
        if (!Objects.equals(null, res.getHuanWaiPingMaoLi())
                && res.getHuanWaiPingMaoLi().compareTo(BigDecimal.ZERO) > 0) {
            target.setHuanWaiPingMaoLi(res.getHuanWaiPingMaoLi());
            target.setYouXiao(Boolean.TRUE);
        }
        // 通过反射来获取变量,新增的变量,自动累加,历史处理字段不管
        Arrays.stream(ReflectUtil.getFields(PersonStatisticsVO.class))
                // 非静态文件且为number类型 已经处理的不管
                .filter(f -> !ModifierUtil.isStatic(f) && Number.class.isAssignableFrom(f.getType()) && PersonStatisticsVO.isAutoYouXiao(f.getName()))
                .forEach(f -> {
                   Number reValue = (Number)ReflectUtil.getFieldValue(res,f);
                    if(reValue != null && reValue.doubleValue() > 0){
                        ReflectUtil.setFieldValue(target,f,reValue);
                        target.setYouXiao(Boolean.TRUE);
                    }
                });
    }

    private PersonStatisticsVO sumAll(List<PersonStatisticsVO> vos) {
        PersonStatisticsVO.PersonStatisticsVOBuilder builder = PersonStatisticsVO.builder();
        //占比的合计在: dealData方法中进行处理,此处不需要处理
        // 通过反射来获取变量,新增的变量,自动累加,历史处理字段不管
        Arrays.stream(ReflectUtil.getFields(PersonStatisticsVO.class))
                // 非静态文件且为number类型 已经处理的不管
                .filter(f -> !ModifierUtil.isStatic(f) && Number.class.isAssignableFrom(f.getType()) && PersonStatisticsVO.isAutoYouXiao(f.getName()))
                .forEach(f -> vos.stream().map(psv -> (Number)ReflectUtil.getFieldValue(psv,f)).filter(Objects::nonNull).reduce(NumberUtil::add)
                            .ifPresent(sum -> ReflectUtil.invoke(builder,f.getName(), Convert.convert(f.getType(), sum))));
        return builder
                .ruanJianPingJiaZongLiang(vos.stream().map(PersonStatisticsVO::getRuanJianPingJiaZongLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .ruanJianPingJiaPingJunFen(vos.stream().map(PersonStatisticsVO::getRuanJianPingJiaPingJunFen).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .ruanJianPingJiaChaPingLiang(vos.stream().map(PersonStatisticsVO::getRuanJianPingJiaChaPingLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shouHouWeiXiuPingJiaZongLiang(vos.stream().map(PersonStatisticsVO::getShouHouWeiXiuPingJiaZongLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shouHouWeiXiuPingJiaPingJunFen(vos.stream().map(PersonStatisticsVO::getShouHouWeiXiuPingJiaPingJunFen).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .shouHouWeiXiuPingJiaChaPingLiang(vos.stream().map(PersonStatisticsVO::getShouHouWeiXiuPingJiaChaPingLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shouHouJiShuKeFuPingJiaZongLiang(vos.stream().map(PersonStatisticsVO::getShouHouJiShuKeFuPingJiaZongLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shouHouJiShuKeFuPingJiaPingJunFen(vos.stream().map(PersonStatisticsVO::getShouHouJiShuKeFuPingJiaPingJunFen).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .shouHouJiShuKeFuPingJiaChaPingLiang(vos.stream().map(PersonStatisticsVO::getShouHouJiShuKeFuPingJiaChaPingLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .zongJieJianLiang(vos.stream().map(PersonStatisticsVO::getZongJieJianLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .zaiBaoLiang(vos.stream().map(PersonStatisticsVO::getZaiBaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .buZaiBaoLiang(vos.stream().map(PersonStatisticsVO::getBuZaiBaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .waiXiuLiang(vos.stream().map(PersonStatisticsVO::getWaiXiuLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .jiuJiFuWuLiang(vos.stream().map(PersonStatisticsVO::getJiuJiFuWuLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shangMenAnZhuangLiang(vos.stream().map(PersonStatisticsVO::getShangMenAnZhuangLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shangMenAnZhuangMaoLi(vos.stream().map(PersonStatisticsVO::getShangMenAnZhuangMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .jieJianRenWeiXiuMaoLi(vos.stream().map(PersonStatisticsVO::getJieJianRenWeiXiuMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .jieJianRenJiuJianMaoLi(vos.stream().map(PersonStatisticsVO::getJieJianRenJiuJianMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .jieJianRenZhiHuanJiuJianMaoLi(vos.stream().map(PersonStatisticsVO::getJieJianRenZhiHuanJiuJianMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .jieJianRenZhiHuanLiang(vos.stream().map(PersonStatisticsVO::getJieJianRenZhiHuanLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .ruanJianAnZhuangLiang(vos.stream().map(PersonStatisticsVO::getRuanJianAnZhuangLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .ruanJianJieJianLiang(vos.stream().map(PersonStatisticsVO::getRuanJianJieJianLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .geRengShouJiXiaoLiang(vos.stream().filter(vo ->
                        !Objects.equals(null, vo.getRuanJianJieJianLiang())
                                && !vo.getRuanJianJieJianLiang().equals(0)).map(PersonStatisticsVO::getGeRengShouJiXiaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .ruanJianWaiXiuMaoLi(vos.stream().map(PersonStatisticsVO::getRuanJianWaiXiuMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .zongXiuHaoLiang(vos.stream().map(PersonStatisticsVO::getZongXiuHaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .zhiHuanLiang(vos.stream().map(PersonStatisticsVO::getZhiHuanLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shangMenLiang(vos.stream().map(PersonStatisticsVO::getShangMenLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .yingPanLiang(vos.stream().map(PersonStatisticsVO::getYingPanLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .waiSongXiuHaoLiang(vos.stream().map(PersonStatisticsVO::getWaiSongXiuHaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .gengPeiXiuHaoLiang(vos.stream().map(PersonStatisticsVO::getGengPeiXiuHaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .xinPianXiuHaoLiang(vos.stream().map(PersonStatisticsVO::getXinPianXiuHaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .fanXiuLiang(vos.stream().map(PersonStatisticsVO::getFanXiuLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .xiuBuHaoLiang(vos.stream().map(PersonStatisticsVO::getXiuBuHaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .weiXiuMaoLi(vos.stream().map(PersonStatisticsVO::getWeiXiuMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .ziJieZiXiuMaoLi(vos.stream().map(PersonStatisticsVO::getZiJieZiXiuMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .weiXiuRenJiuJianMaoLi(vos.stream().map(PersonStatisticsVO::getWeiXiuRenJiuJianMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .weiXiuRenZhiHuanJiuJianMaoLi(vos.stream().map(PersonStatisticsVO::getWeiXiuRenZhiHuanJiuJianMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .ceShiZongLiang(vos.stream().map(PersonStatisticsVO::getCeShiZongLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .ceShiBuTongGuoLiang(vos.stream().map(PersonStatisticsVO::getCeShiBuTongGuoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .wuFaCeShiLiang(vos.stream().map(PersonStatisticsVO::getWuFaCeShiLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .xiuBuHaoLv(vos.stream().map(PersonStatisticsVO::getXiuBuHaoLv).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .fanXiuLv(vos.stream().map(PersonStatisticsVO::getFanXiuLv).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .wuMaoLiXinPianXiuHaoLiang(vos.stream().map(PersonStatisticsVO::getWuMaoLiXinPianXiuHaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .wuMaoLiGengPeiXiuHaoLiang(vos.stream().map(PersonStatisticsVO::getWuMaoLiGengPeiXiuHaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .wuMaoLiYingPanXiuHaoLiang(vos.stream().map(PersonStatisticsVO::getWuMaoLiYingPanXiuHaoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .feiBaoWaiSongJiHuanHuoLiang(vos.stream().map(PersonStatisticsVO::getFeiBaoWaiSongJiHuanHuoLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .yuYueJieJianLiang(vos.stream().map(PersonStatisticsVO::getYuYueJieJianLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shouHouDianChiXianLiang(vos.stream().map(PersonStatisticsVO::getShouHouDianChiXianLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shouHouDianChiXianJinE(vos.stream().map(PersonStatisticsVO::getShouHouDianChiXianJinE).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .shouHouDianChiChuKuLiang(vos.stream().map(PersonStatisticsVO::getShouHouDianChiChuKuLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shouHouDianChiXianChuXianChengBen(vos.stream().map(PersonStatisticsVO::getShouHouDianChiXianChuXianChengBen).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .shouHouSuiPingXianLiang(vos.stream().map(PersonStatisticsVO::getShouHouSuiPingXianLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shouHouSuiPingXianJinE(vos.stream().map(PersonStatisticsVO::getShouHouSuiPingXianJinE).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .shouHouPingMuChuKuLiang(vos.stream().map(PersonStatisticsVO::getShouHouPingMuChuKuLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .shouHouSuiPingXianChuXianChengBen(vos.stream().map(PersonStatisticsVO::getShouHouSuiPingXianChuXianChengBen).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .huanWaiPingLiang(vos.stream().map(PersonStatisticsVO::getHuanWaiPingLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .huanWaiPingFanXiuLiang(vos.stream().map(PersonStatisticsVO::getHuanWaiPingFanXiuLiang).filter(Objects::nonNull).reduce(Integer::sum).orElse(0))
                .huanWaiPingMaoLi(vos.stream().map(PersonStatisticsVO::getHuanWaiPingMaoLi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .youXiao(Boolean.TRUE)
                .build();

    }


}
