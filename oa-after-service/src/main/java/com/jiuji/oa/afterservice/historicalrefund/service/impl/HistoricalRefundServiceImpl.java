package com.jiuji.oa.afterservice.historicalrefund.service.impl;


import com.jiuji.oa.afterservice.bigpro.po.refund.ShouhouTuiHuanPo;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.enums.TuihuanKindEnum;
import com.jiuji.oa.afterservice.historicalrefund.abstractFactory.RecoveryRefundFactory;
import com.jiuji.oa.afterservice.historicalrefund.abstractFactory.SaleRefundFactory;
import com.jiuji.oa.afterservice.historicalrefund.enums.HistoricalRefundEnum;
import com.jiuji.oa.afterservice.historicalrefund.enums.RecoverySubPayEnum;
import com.jiuji.oa.afterservice.historicalrefund.enums.RefundOrderTypeEnum;
import com.jiuji.oa.afterservice.historicalrefund.mapper.HistoricalRefundMapper;
import com.jiuji.oa.afterservice.historicalrefund.po.ChaeInfoDetailsPo;
import com.jiuji.oa.afterservice.historicalrefund.po.RecoveryRefundPo;
import com.jiuji.oa.afterservice.historicalrefund.po.SaleRefundPo;
import com.jiuji.oa.afterservice.historicalrefund.service.HistoricalRefundService;
import com.jiuji.oa.afterservice.historicalrefund.service.RefundService;
import com.jiuji.oa.afterservice.historicalrefund.vo.*;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.refund.po.ShouhouTuihuanDetailPo;
import com.jiuji.oa.afterservice.refund.service.ShouhouTuihuanDetailService;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.foundation.db.annotation.DS;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HistoricalRefundServiceImpl extends HistoricalRefundCommonServiceImpl implements HistoricalRefundService {

    @Resource
    private RecoveryRefundFactory recoveryRefundFactory;
    @Resource
    private SaleRefundFactory saleRefundFactory;
    @Resource
    private AreainfoService areainfoService;
    @Resource
    private HistoricalRefundMapper refundMapper;
    @Resource
    private ShouhouTuihuanDetailService tuihuanDetailService;


    private static final Long SEVEN_DAY=7L;


    @DS(DataSourceConstants.CH999_OA_NEW)
    @Override
    public R<RefundInfoShowVO> findRefundInfo(SelectRefundInfoCondition selectRefundInfoCondition) {
        RefundInfoShowVO refundInfoShowVO = new RefundInfoShowVO();
        SaleRefundPo shouhouTuiHuanPo = Optional.ofNullable(getShouhouTuiHuan(selectRefundInfoCondition)).orElse(new SaleRefundPo());
        refundInfoShowVO.setShowPayOpenIdFlag(StringUtils.isNotEmpty(shouhouTuiHuanPo.getPayOpenId()));
        CompletableFuture<List<HistoricalRefundInfoVO>> cf1 = CompletableFuture.supplyAsync(() -> createHistoricalRefundInfoVO(selectRefundInfoCondition));
        CompletableFuture<List<HistoricalRefundInfoVO>> cf2 = CompletableFuture.supplyAsync(() -> createRecoveryHistoricalRefundInfoVO(selectRefundInfoCondition));
        CompletableFuture<List<HistoricalRefundInfoVO>> listCompletableFuture = cf1.thenCombine(cf2, (a, b) -> {
            List<HistoricalRefundInfoVO> combineList = new ArrayList<>(a);
            //回收的数据进行一次处理
            if(CollectionUtils.isNotEmpty(b)){
                List<HistoricalRefundInfoVO> filterList = b.stream().filter(item -> item.getAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
                combineList.addAll(filterList);
            }
            return combineList.stream()
                    .filter(item-> item.getDtime()!=null)
                    .distinct()
                    .sorted(Comparator.comparing(HistoricalRefundInfoVO::getDtime).reversed())
                    .limit(10)
                    .collect(Collectors.toList());
        });
        List<HistoricalRefundInfoVO> joinList = listCompletableFuture.join();
        //设置订单跳转连接
        setOrderUrl(joinList);
        refundInfoShowVO.setList(joinList);
        return R.success(refundInfoShowVO);
    }
    @DS(DataSourceConstants.CH999_OA_NEW)
    @Override
    public R<ChaeInfoShowVO> findChaeInfo(SelectChaeInfoCondition chaeInfoCondition) {
        //获取当前时间
        LocalDate now = LocalDate.now();
        List<ChaeInfoDetailsPo> detailsList = new ArrayList<>();
        detailsList.add(new ChaeInfoDetailsPo(now.minusDays(1L)));
        detailsList.add(new ChaeInfoDetailsPo(now.minusDays(2L)));
        detailsList.add(new ChaeInfoDetailsPo(now.minusDays(3L)));
        detailsList.add(new ChaeInfoDetailsPo(now.minusDays(4L)));
        detailsList.add(new ChaeInfoDetailsPo(now.minusDays(5L)));
        detailsList.add(new ChaeInfoDetailsPo(now.minusDays(6L)));
        detailsList.add(new ChaeInfoDetailsPo(now.minusDays(7L)));
        //获取期天之前
        chaeInfoCondition.setSTime(now.minusDays(SEVEN_DAY));
        chaeInfoCondition.setETime(now);
        SelectRefundInfoCondition selectRefundInfoCondition = new SelectRefundInfoCondition();
        selectRefundInfoCondition.setRefundId(chaeInfoCondition.getRefundId());
        selectRefundInfoCondition.setRefundIdType(chaeInfoCondition.getRefundIdType());
        SaleRefundPo shouhouTuiHuan = getShouhouTuiHuan(selectRefundInfoCondition);
        Integer areaid = shouhouTuiHuan.getAreaid();
        chaeInfoCondition.setAreaId(areaid);
        List<ChaeInfoDetailsPo> chaeInfoDetails = refundMapper.selectChaeInfo(chaeInfoCondition);
        if(CollectionUtils.isNotEmpty(chaeInfoDetails)){
            Map<LocalDate, ChaeInfoDetailsPo> map = chaeInfoDetails.stream().collect(Collectors.toMap(ChaeInfoDetailsPo::getTime, Function.identity(), (n1, n2) -> n2));
            detailsList.forEach(item->{
                ChaeInfoDetailsPo details = Optional.ofNullable(map.get(item.getTime())).orElse(new ChaeInfoDetailsPo());
                BigDecimal chae = details.getChae();
                if(chae!=null){
                    item.setChae(chae);
                } else {
                    item.setChae(BigDecimal.ZERO);
                }
            });
        }
        BigDecimal sum = detailsList.stream().map(ChaeInfoDetailsPo::getChae)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        ChaeInfoShowVO chaeInfoShowVO = new ChaeInfoShowVO();
        chaeInfoShowVO.setChaeInfoDetailsList(detailsList);
        chaeInfoShowVO.setSum(sum);
        Areainfo areainfo = Optional.ofNullable(areainfoService.getByIdSqlServer(areaid)).orElse(new Areainfo());
        chaeInfoShowVO.setArea(areainfo.getArea());
        return R.success(chaeInfoShowVO);
    }



    /**
     * 获取回收数据
     * @param selectRefundInfoCondition
     * @return
     */
    private List<HistoricalRefundInfoVO> createRecoveryHistoricalRefundInfoVO(SelectRefundInfoCondition selectRefundInfoCondition){
        RefundService refundService = recoveryRefundFactory.createRefundService();
        Integer historicalRefundType = selectRefundInfoCondition.getHistoricalRefundType();
        List<RecoveryRefundPo> list = new ArrayList<>();
        if( HistoricalRefundEnum.CONFIRM_TYPE_ONE.getCode().equals(historicalRefundType)){
            List<RecoveryRefundPo> tuihuanList = getChildList(refundService.findRefundInfoAll(selectRefundInfoCondition),RecoveryRefundPo.class);
            list = tuihuanList.stream().sorted(Comparator.comparing(RecoveryRefundPo::getPayDate).reversed()).collect(Collectors.toList());
        }
        if( HistoricalRefundEnum.CONFIRM_TYPE_TWO.getCode().equals(historicalRefundType)){
            list = getChildList(refundService.findRefundInfoByAreaId(selectRefundInfoCondition),RecoveryRefundPo.class);
        }
        if( HistoricalRefundEnum.CONFIRM_TYPE_THREE.getCode().equals(historicalRefundType)){
            list = getChildList(refundService.findRefundInfoByInUser(selectRefundInfoCondition),RecoveryRefundPo.class);
        }
        if( HistoricalRefundEnum.CONFIRM_TYPE_FOUR.getCode().equals(historicalRefundType)){
            list = getChildList(refundService.findRefundInfoByMember(selectRefundInfoCondition),RecoveryRefundPo.class);
        }
        if( HistoricalRefundEnum.CONFIRM_TYPE_FIVE.getCode().equals(historicalRefundType)){
            list = getChildList(refundService.findRefundInfoByAccountNumber(selectRefundInfoCondition),RecoveryRefundPo.class);
        }
        List<HistoricalRefundInfoVO> refundInfoVoArrayList = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return refundInfoVoArrayList;
        }
        //收集门店id
        List<Integer> areaIdList = list.stream().map(RecoveryRefundPo::getAreaid).collect(Collectors.toList());
        Map<Integer, Areainfo> areaMap = areainfoService.getAreaMap(areaIdList);
        refundInfoVoArrayList = list.parallelStream().map((RecoveryRefundPo item)->{
            HistoricalRefundInfoVO historicalRefundInfoVO = new HistoricalRefundInfoVO();
            String area = Optional.ofNullable(areaMap.get(item.getAreaid())).orElse(new Areainfo()).getArea();
            historicalRefundInfoVO.setAmount(item.getAmount())
                    .setArea(area)
                    .setDtime(item.getPayDate())
                    .setInuser(item.getPayUser())
                    .setOrderNo(item.getSubId())
                    .setType("回收支付")
                    .setPayOpenId(item.getPayOpenId())
                    .setPayType(RecoverySubPayEnum.getMessageByCode(item.getSubPay()))
                    .setCheckStr("已完成")
                    .setOrderType(RefundOrderTypeEnum.REFUND_ORDER_TYPE_RECOVERY.getMessage());
            return historicalRefundInfoVO;
        }).collect(Collectors.toList());
        return refundInfoVoArrayList;
    }






    /**
     * 获取查询数据
     * @param selectRefundInfoCondition
     * @return
     */
    private List<HistoricalRefundInfoVO> createHistoricalRefundInfoVO(SelectRefundInfoCondition selectRefundInfoCondition){
        RefundService refundService = saleRefundFactory.createRefundService();
        Integer historicalRefundType = selectRefundInfoCondition.getHistoricalRefundType();
        List<SaleRefundPo> list = new ArrayList<>();
        if( HistoricalRefundEnum.CONFIRM_TYPE_ONE.getCode().equals(historicalRefundType)){
            List<SaleRefundPo> tuihuanList = getChildList(refundService.findRefundInfoAll(selectRefundInfoCondition),SaleRefundPo.class);
            list = tuihuanList.stream().sorted(Comparator.comparing(SaleRefundPo::getDtime).reversed()).collect(Collectors.toList());
        }
        if( HistoricalRefundEnum.CONFIRM_TYPE_TWO.getCode().equals(historicalRefundType)){
            list = getChildList(refundService.findRefundInfoByAreaId(selectRefundInfoCondition),SaleRefundPo.class);
        }
        if( HistoricalRefundEnum.CONFIRM_TYPE_THREE.getCode().equals(historicalRefundType)){
            list = getChildList(refundService.findRefundInfoByInUser(selectRefundInfoCondition),SaleRefundPo.class);
        }
        if( HistoricalRefundEnum.CONFIRM_TYPE_FOUR.getCode().equals(historicalRefundType)){
            list = getChildList(refundService.findRefundInfoByMember(selectRefundInfoCondition),SaleRefundPo.class);
        }
        if( HistoricalRefundEnum.CONFIRM_TYPE_FIVE.getCode().equals(historicalRefundType)){
            list = getChildList(refundService.findRefundInfoByAccountNumber(selectRefundInfoCondition),SaleRefundPo.class);
        }
        List<HistoricalRefundInfoVO> refundInfoVOArrayList = new ArrayList<>();
        if(CollectionUtils.isEmpty(list)){
            return refundInfoVOArrayList;
        }
        //收集门店id
        List<Integer> areaIdList = list.stream().map(SaleRefundPo::getAreaid).collect(Collectors.toList());
        Map<Integer, Areainfo> areaMap = areainfoService.getAreaMap(areaIdList);
        //获取组合退详情
        Map<Integer, List<ShouhouTuihuanDetailPo>> mapCombinationRegressionMap = getMapCombinationRegression(list);
        refundInfoVOArrayList = list.parallelStream().map((SaleRefundPo item)->{
            HistoricalRefundInfoVO historicalRefundInfoVO = new HistoricalRefundInfoVO();
            String area = Optional.ofNullable(areaMap.get(item.getAreaid())).orElse(new Areainfo()).getArea();
            OrderInfo orderInfo = getOrderInfo(item);
            historicalRefundInfoVO.setAmount(item.getTuikuanM())
                    .setArea(area)
                    .setDtime(item.getDtime())
                    .setInuser(item.getInuser())
                    .setOrderNo(orderInfo.getOrderNo())
                    .setType(orderInfo.getType())
                    .setPayOpenId(item.getPayOpenId())
                    .setPayType(item.getTuiWay())
                    .setCheckStr(Optional.ofNullable(item.getCheck3()).orElse(Boolean.FALSE)?"已完成":"进行中")
                    .setOrderType(orderInfo.getOrderType());
            //判断订单是否为组合退(兼容组合退相关数据)
            List<ShouhouTuihuanDetailPo> tuihuanDetailPoList = mapCombinationRegressionMap.get(item.getId());
            if(ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode().equals(item.getTuiKinds()) && CollectionUtils.isNotEmpty(tuihuanDetailPoList)){
                StringJoiner payType = new StringJoiner(",");
                StringJoiner payOpenId = new StringJoiner(",");
                tuihuanDetailPoList.forEach(detail->{
                    String tuiWay = detail.getTuiWay();
                    payType.add(tuiWay);
                    String detailPayOpenId = detail.getPayOpenId();
                    if(StringUtils.isNotEmpty(detailPayOpenId)){
                        payOpenId.add(tuiWay+":"+detailPayOpenId);
                    } else {
                        payOpenId.add(tuiWay);
                    }

                });
                if(StringUtils.isNotEmpty(payOpenId.toString())){
                    historicalRefundInfoVO.setPayOpenId(payOpenId.toString());
                }
                if(StringUtils.isNotEmpty(payType.toString())){
                    historicalRefundInfoVO.setPayType(payType.toString());
                }
            }

            return historicalRefundInfoVO;
        }).collect(Collectors.toList());
        return refundInfoVOArrayList;
    }

    /**
     * 获取组合退相关详情
     * @param list
     * @return
     */
    private Map<Integer,List<ShouhouTuihuanDetailPo>> getMapCombinationRegression(List<SaleRefundPo> list){
        Map<Integer,List<ShouhouTuihuanDetailPo>> map = new HashMap<>();
        if(CollectionUtils.isEmpty(list)){
            return map;
        }
        //过滤出退换id
        List<Integer> idList = list.stream()
                .filter(item -> ShouhouTuiHuanPo.TuiKindsEnum.GROUP_TUI.getCode().equals(item.getTuiKinds()))
                .map(SaleRefundPo::getId)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(idList)){
            return map;
        }
        List<ShouhouTuihuanDetailPo> tuihuanDetailPos = tuihuanDetailService.lambdaQuery().in(ShouhouTuihuanDetailPo::getFkTuihuanId, idList).list();
        if(CollectionUtils.isEmpty(tuihuanDetailPos)){
            return map;
        }
        return tuihuanDetailPos.stream().collect(Collectors.groupingBy(ShouhouTuihuanDetailPo::getFkTuihuanId));
    }

    /**
     * 获取订单信息
     * @param item
     * @return
     */
    private OrderInfo getOrderInfo(SaleRefundPo item){
        OrderInfo orderInfo = new OrderInfo();
        Integer tuihuanKind = item.getTuihuanKind();
        //设置类型
        Optional.ofNullable(TuihuanKindEnum.getByCode(tuihuanKind)).ifPresent(obj->orderInfo.setType(obj.getMessage()));
        //售后单的获取方式
        if(SHOU_HOU_LIST.contains(tuihuanKind)){
            orderInfo.setOrderNo(item.getShouhouId())
                    .setOrderType(RefundOrderTypeEnum.REFUND_ORDER_TYPE_MAINTENANCE.getMessage());

        }
        //小件获取方式
        if(SMAPLLPRO_LIST.contains(tuihuanKind)){
            orderInfo.setOrderNo(item.getSmallproid())
                    .setOrderType(RefundOrderTypeEnum.REFUND_ORDER_TYPE_SMALL_PIECE.getMessage());
        }
        //新机单获取方式
        if(TuihuanKindEnum.TDJ.getCode().equals(tuihuanKind)){
            orderInfo.setOrderNo(item.getSubId())
                    .setOrderType(RefundOrderTypeEnum.REFUND_ORDER_TYPE_SALE.getMessage());
        }
        //良品单获取方式
        if(TuihuanKindEnum.TDJ_LP.getCode().equals(tuihuanKind)){
            orderInfo.setOrderNo(item.getSubId())
                    .setOrderType(RefundOrderTypeEnum.REFUND_ORDER_TYPE_GOOD_PRODUCT.getMessage());
        }
        return orderInfo;
    }
    
}
