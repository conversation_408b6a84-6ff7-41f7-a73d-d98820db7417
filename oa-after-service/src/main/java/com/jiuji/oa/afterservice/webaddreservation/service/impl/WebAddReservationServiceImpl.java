package com.jiuji.oa.afterservice.webaddreservation.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.service.ProductinfoService;
import com.jiuji.oa.afterservice.cloud.service.WebCloud;
import com.jiuji.oa.afterservice.cloud.vo.ProductServiceOpeningVO;
import com.jiuji.oa.afterservice.cloud.vo.web.AccessoriesVO;
import com.jiuji.oa.afterservice.cloud.vo.web.FixPartsDTO;
import com.jiuji.oa.afterservice.cloud.vo.web.FixPartsVO;
import com.jiuji.oa.afterservice.cloud.vo.web.ProRelateInfoServiceVO;
import com.jiuji.oa.afterservice.common.constant.DataSourceConstants;
import com.jiuji.oa.afterservice.common.constant.RedisKeys;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.enums.XtenantEnum;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.webaddreservation.mapper.WebAddReservationMapper;
import com.jiuji.oa.afterservice.webaddreservation.service.WebAddReservationService;
import com.jiuji.oa.afterservice.webaddreservation.vo.CreateOrderInfoVO;
import com.jiuji.oa.afterservice.webaddreservation.vo.OrderInfoConditionVO;
import com.jiuji.oa.afterservice.webaddreservation.vo.RepairPartsInfoVO;
import com.jiuji.oa.afterservice.webaddreservation.vo.RepairPartsVO;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.foundation.db.annotation.DS;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 */
@Service
public class WebAddReservationServiceImpl implements WebAddReservationService {

    private static final String AFTER_SERVICE_TROUBLE_DES = "售后服务";

    @Resource
    private ProductinfoService productinfoService;

    @Resource
    private WebAddReservationMapper webAddReservationMapper;

    @Resource
    private WebCloud webCloud;

    @Override
    public R<CreateOrderInfoVO> getCreateOrderInfoVO(OrderInfoConditionVO orderInfoConditionVO) {
        R<CreateOrderInfoVO> result = R.success(null);
        checkAndSetCreateOrderParam(orderInfoConditionVO, result);
        if(!result.isSuccess()){
            return result;
        }
        CreateOrderInfoVO createOrderInfoVO = new CreateOrderInfoVO();
        //组装到返回结果中
        result.setData(createOrderInfoVO);
        Productinfo productinfo = productinfoService.getProductinfoByPpid(orderInfoConditionVO.getPpid());
        if(productinfo == null){
            result.setCode(ResultCode.SERVER_ERROR);
            result.setUserMsg(StrUtil.format("查询不到ppid[{}]的商品信息", orderInfoConditionVO.getPpid()));
            return result;
        }
        createOrderInfoVO.setProductColor(productinfo.getProductColor())
                .setProductName(productinfo.getProductName());

        List<RepairPartsVO> repairPartsList = getRepairPartsList(orderInfoConditionVO.getAreaId(), orderInfoConditionVO.getInquiryInformationPpids());
        Result<AccessoriesVO> accessories = webCloud.getAccessories(productinfo.getProductId(),null);
        if (Objects.equals(0, accessories.getCode()) && Objects.nonNull(accessories.getData()) && CollectionUtils.isNotEmpty(accessories.getData().getFixPartsList())) {
            List<Integer> troubleIdList = Arrays.stream(orderInfoConditionVO.getTroubleIds().split("-")).filter(StringUtils::isNumeric).map(Integer::parseInt).collect(Collectors.toList());
            List<FixPartsVO> fixPartsList = accessories.getData().getFixPartsList();
            Map<Integer, FixPartsDTO> productTroubleDesMap = fixPartsList.stream()
                    .flatMap(v -> v.getFixPartsDTO().stream().filter(f -> troubleIdList.contains(f.getTroubleId())))
                    .collect(Collectors.toMap(FixPartsDTO::getPpid, Function.identity(),(k1, k2) -> k1));
            //Map<Integer, String> productTroubleDesMap = fixPartsList.stream().flatMap(v -> v.getFixPartsDTO().stream().filter(f -> troubleIdList.contains(f.getTroubleId()))).collect(Collectors.toMap(FixPartsDTO::getPpid, FixPartsDTO::getTroubleDes,(k1,k2) -> k1))
            repairPartsList.forEach(v -> {
                if (Boolean.TRUE.equals(v.getServiceFlag())) {
                    v.setTroubleDes(AFTER_SERVICE_TROUBLE_DES);
                    v.setRepParentTitle(AFTER_SERVICE_TROUBLE_DES);
                } else {
                    FixPartsDTO fixParts = productTroubleDesMap.getOrDefault(v.getPpid(), LambdaBuild.create(FixPartsDTO.class)
                            .set(FixPartsDTO::setTroubleDes, v.getProductName())
                            .set(FixPartsDTO::setRepParentTitle, v.getProductName())
                            .build());
                    v.setTroubleDes(fixParts.getTroubleDes());
                    v.setTroubleId(fixParts.getTroubleId());
                    v.setRepParentId(fixParts.getRepParentId());
                    v.setRepParentTitle(fixParts.getRepParentTitle());
                }
            });
        }

        //查询服务价格
        List<Integer> ppids = repairPartsList.stream().filter(v -> Boolean.FALSE.equals(v.getServiceFlag())).map(RepairPartsVO::getPpid).collect(Collectors.toList());
        Result<Map<Integer, List<ProRelateInfoServiceVO>>> serviceList = webCloud.getServiceList(productinfo.getProductId(), ppids);
        Map<Integer, BigDecimal> serviceMap = null;
        if (Objects.equals(0, accessories.getCode()) && Objects.nonNull(accessories.getData())) {
            Map<Integer, List<ProRelateInfoServiceVO>> serviceData = serviceList.getData();
            serviceMap = serviceData.values().stream().flatMap(Collection::stream).collect(Collectors.toMap(ProRelateInfoServiceVO::getPpriceid, ProRelateInfoServiceVO::getMemberprice, (k1,k2) -> k1));
        }
        Map<Integer, BigDecimal> finalServiceMap = serviceMap;
        repairPartsList.forEach(v -> {
            if (Boolean.TRUE.equals(v.getServiceFlag()) && Objects.nonNull(finalServiceMap) && finalServiceMap.containsKey(v.getPpid())) {
                v.setPrice(finalServiceMap.get(v.getPpid()));
                v.getFixPartsList().forEach(x -> x.setPrice(finalServiceMap.get(x.getPpid())));
            }
        });

        createOrderInfoVO.setPartsList(repairPartsList);
        return result;
    }

    private void checkAndSetCreateOrderParam(OrderInfoConditionVO orderInfoConditionVO, R<CreateOrderInfoVO> result) {
        String inquiryCode = orderInfoConditionVO.getInquiryCode();
        if(StrUtil.isNotBlank(inquiryCode)){
            StringRedisTemplate stringRedisTemplate = SpringUtil.getBean(StringRedisTemplate.class);
            String inquiryValue = stringRedisTemplate.opsForValue().get(StrUtil.format(RedisKeys.WEB_SHOUHOU_YUYUE_INQUIRY_FORMAT,
                    inquiryCode));
            //解析参数并进行替换
            if(StrUtil.isNotBlank(inquiryValue)){
                Map<String, String> inquiryParamMap = HttpUtil.decodeParamMap(inquiryValue, Charset.defaultCharset());
                orderInfoConditionVO.setPpid(Convert.toInt(inquiryParamMap.get("ppid"), orderInfoConditionVO.getPpid()));
                orderInfoConditionVO.setInquiryInformationPpids(inquiryParamMap.get("gz"));
                orderInfoConditionVO.setTroubleIds(inquiryParamMap.get("faults"));
                result.put("inquiryParam", inquiryParamMap);
            }else{
                result.setCode(ResultCode.SERVER_ERROR);
                result.setUserMsg(StrUtil.format("询价编码[{}]已失效", inquiryCode));
                return;
            }
            SpringContextUtil.addRequestKeyMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG, "询价编码原始参数: {}", inquiryValue);
        }
        if(ObjectUtil.defaultIfNull(orderInfoConditionVO.getPpid(),0)<=0){
            result.setCode(ResultCode.SERVER_ERROR);
            result.setUserMsg("ppid不能为空");
            return;
        }
        return;
    }

    @Override
    @DS(DataSourceConstants.CH999_OA_NEW)
    public List<RepairPartsVO> getRepairPartsList(Integer areaId, String ppids) {
        List<RepairPartsVO> repairPartsLsit = new ArrayList<>();
        if (StringUtils.isBlank(ppids) || areaId == null) {
            return repairPartsLsit;
        }
        List<Integer> ppidList = Arrays.stream(ppids.split(",")).filter(StringUtils::isNumeric).map(Integer::parseInt).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ppidList)) {
            return repairPartsLsit;
        }
        List<RepairPartsInfoVO> list = webAddReservationMapper.getRepairPartsList(areaId, ppidList);
        //特殊ppid处理
        if(ppidList.contains(0)){
            RepairPartsInfoVO repairPartsVO = new RepairPartsInfoVO();
            repairPartsVO.setPpid(0);
            repairPartsVO.setProductId(0);
            repairPartsVO.setPrice(BigDecimal.ZERO);
            repairPartsVO.setProductName("其他故障");
            repairPartsVO.setServiceFlag(false);
            repairPartsVO.setQue(0);
            repairPartsVO.setLeftCount(0);
            repairPartsVO.setDcLeftCount(0);
            list.add(repairPartsVO);
        }
        if (CollectionUtils.isNotEmpty(list)) {
            list = list.stream().filter(v -> ppidList.contains(v.getPpid()) || Objects.equals(0, v.getQue())).peek(v -> v.setServiceFlag(false)).collect(Collectors.toList());
            Map<Integer, List<RepairPartsInfoVO>> productMapList = list.stream().collect(Collectors.groupingBy(RepairPartsInfoVO::getProductId));
            List<RepairPartsInfoVO> repairPartsInfoLsit = list.stream().filter(v -> ppidList.contains(v.getPpid())).collect(Collectors.toList());
            repairPartsLsit = repairPartsInfoLsit.stream()
                    .map(v -> LambdaBuild.create(new RepairPartsVO())
                            .set(RepairPartsVO::setPpid, v.getPpid())
                            .set(RepairPartsVO::setProductName, v.getProductName())
                            .set(RepairPartsVO::setProductId, v.getProductId())
                            .set(RepairPartsVO::setProductColor, v.getProductColor())
                            .set(RepairPartsVO::setLeftCount, v.getLeftCount())
                            .set(RepairPartsVO::setPrice, v.getPrice())
                            .set(RepairPartsVO::setDcLeftCount, v.getDcLeftCount())
                            .set(RepairPartsVO::setServiceFlag, false)
                            .set(RepairPartsVO::setFixPartsList, productMapList.get(v.getProductId())).build())
                    .collect(Collectors.toList());
            //标识配件是商品还是服务
            List<Integer> sourcePpidList = list.stream().map(RepairPartsInfoVO::getPpid).collect(Collectors.toList());
            List<Integer> targetPpidList = DecideUtil.iif(XtenantEnum.isJiujiXtenant(),
                    () -> Optional.of(sourcePpidList).filter(CollUtil::isNotEmpty)
                            .map(x -> webCloud.openingGetServices(x.stream()
                                    .map(StrUtil::toString).collect(Collectors.joining(",")), XtenantEnum.getXtenant()))
                            .filter(r -> r.getCode() == ResultCode.SUCCESS).map(Result::getData).map(psos -> psos.stream()
                                    .filter(pso -> Objects.nonNull(pso.getPpid()))
                                    .map(ProductServiceOpeningVO::getPpid).collect(Collectors.toList()))
                            .orElse(Collections.emptyList()), Collections::emptyList);
            if (CollectionUtils.isNotEmpty(targetPpidList)) {
                repairPartsLsit.forEach(v -> {
                    v.setServiceFlag(targetPpidList.contains(v.getPpid()));
                    v.getFixPartsList().forEach(x -> x.setServiceFlag(targetPpidList.contains(x.getPpid())));
                });
            }
        }
        return repairPartsLsit;
    }
}
