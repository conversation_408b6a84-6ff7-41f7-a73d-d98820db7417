package com.jiuji.oa.afterservice.batchreturn.vo.res;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.jiuji.oa.afterservice.batchreturn.vo.req.ShouHouStatisticsHttpReq;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.ShouHouStatisticsEnum;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.vo.res.CommonDataRes;
import com.jiuji.oa.orginfo.areainfo.client.AreaInfoClient;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.enums.CodeMessageEnumInterface;
import com.jiuji.tc.utils.enums.EnumUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 售后统计列控制
 * <AUTHOR>
 * @since 2022/2/25 13:33
 */
public class ShouHouStatisticsColumnJiuji {

    public static List<CommonDataRes.ColumnsBean> buildColumns(ShouHouStatisticsHttpReq req){
        //动态变量
        AreaInfoClient areaInfoClient = SpringUtil.getBean(AreaInfoClient.class);
        Dict varDict = Dict.create();
        Optional.ofNullable(SpringUtil.getBean(AbstractCurrentRequestComponent.class).getCurrentStaffId()).map(OaUserBO::getAreaId)
                .map(areaInfoClient::getAreaInfoById).filter(R::isSuccess).map(R::getData).map(AreaInfo::getPrintName)
                .ifPresent(printName -> varDict.set("printName",printName));

        return Arrays.stream(TitleColumn.values()).filter(TitleColumn::getShow)
                .map(title -> {
                    //处理tab
                    CommonDataRes.ColumnsBean columnsBean = CommonDataRes.ColumnsBean.of(title.message, String.valueOf(title.getCode()));
                    //处理报表表头
                    List<FieldColumnEnum> titleFields = Arrays.stream(FieldColumnEnum.values())
                            //当前tab的显示的表头
                            .filter(fc -> Objects.equals(fc.getTitleColumn(), title)).filter(fc -> Boolean.TRUE.equals(fc.getShow()))
                            //满足统计条件的表头
                            .filter(fc -> fc.getStatisticsEnum() == null || req.getShouHouStatisticsEnums().contains(fc.getStatisticsEnum()))
                            .collect(Collectors.toList());
                    List<CommonDataRes.ColumnsBean> childs = wrapTreeColumn(titleFields,varDict,null, NumberConstant.ONE_HUNDRED);
                    columnsBean.setSubTitles(childs);
                    return columnsBean;
                })
                .collect(Collectors.toList());
    }


    private static List<CommonDataRes.ColumnsBean> wrapTreeColumn(List<FieldColumnEnum> titleFields, Dict varDict, FieldColumnEnum parentNode, int maxDeep) {
        if (CollUtil.isEmpty(titleFields) || maxDeep < 1) {
            return Collections.emptyList();
        }
        List<FieldColumnEnum> notCurrChildNodes = titleFields.stream().filter(ttf -> !Objects.equals(ttf.getParent(), parentNode)).collect(Collectors.toList());
        return titleFields.stream().filter(tf -> Objects.equals(tf.getParent(), parentNode))
                .map(tf -> CommonDataRes.ColumnsBean.of(StrUtil.format(tf.getMessage(),varDict), tf.getCode())
                        .setWidth(StrUtil.str(DecideUtil.iif(StrUtil.contains(tf.getMessage(),"{"),()->16*(StrUtil.length(tf.getMessage())+1)+20,()->tf.getWidth()), Charset.defaultCharset()))
                        .setDescription(Optional.ofNullable(EnumUtil.getEnumByCode(FieldOtherInfoEnum.class,tf)).map(FieldOtherInfoEnum::getDescription).orElse(null))
                        .setSubTitles(wrapTreeColumn(notCurrChildNodes, varDict, tf, maxDeep - 1)))
                .collect(Collectors.toList());
    }

    /**
     * 售后业绩统计 售后管控统计 维修单周期管控
     */
    @Getter
    @AllArgsConstructor
    public enum TitleColumn implements CodeMessageEnumInterface {
        SHOUHOU_YE_JI(1,"售后业绩统计",CollUtil.newHashSet(0, 3, 5, 6, 7, 13, 19),true)
        ,SHOUHOU_GUAN_KONG(2,"售后管控统计",CollUtil.newHashSet(2, 8, 9),true)
        ,SHOUHOU_ZHOU_QI(3,"维修单周期管控",null,false)

        ;

        /**
         * 编码
         */
        private Integer code;
        /**
         * 编码对应信息
         */
        private String message;
        /**
         * 包含的统计枚举
         */
        private Set<Integer> statisticsCodes;
        /**
         * 是否显示
         */
        private Boolean show;
    }


    /**
     * 字段列分组
     */
    @Getter
    public enum FieldColumnEnum implements CodeMessageEnumInterface {
        /**售后业绩统计*/
        YE_JI_AREA("area","地区",null, TitleColumn.SHOUHOU_YE_JI,null,null,true)
        ,YE_JI_CHILD_AREA("area","地区",68, TitleColumn.SHOUHOU_YE_JI,null,YE_JI_AREA,true)
        ,JIE_JIAN_LIANG("jie_jian_liang","接件量",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,null,true)
        ,TOTAL_JIE_JIAN("totalJieJian","硬件总接件量",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,true)
        ,ZAI_BAO_JIE_JIAN_LIANG("zaiBaoJieJianLiang","在保量",84, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,true)
        ,BU_ZAI_BAO_JIE_JIAN_LIANG("buZaiBaoJieJianLiang","不在保量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,true)
        ,WAI_XIU_JIE_JIAN_LIANG("waiXiuJieJianLiang","外修量",84, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,true)
        ,JI_JI_FU_WU_JIE_JIAN_LIANG("jiJiFuWuJieJianLiang","{printName}服务量",260, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,true)
        ,FEI_BAO_RATE("feiBaoRate","非保率",84, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,false)
        ,FEI_BAO_JIE_JIAN_RATE("feiBaoJieJianRate","非保接件率",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,true)
        ,RUAN_JIAN_JIE_JIAN_LIANG("ruanJianJieJianLiang","软件接件量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,true)
        ,RUAN_JIAN_JIE_JIAN_RATE("ruanJianJieJianRate","软件接件率",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,false)
        ,YU_YUE_JIE_JIAN_LIANG("yuYueJieJianLiang","预约接件量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIE_JIAN_LIANG,JIE_JIAN_LIANG,true)
        ,TAO_CAN_ZHAN_BI("tao_can_zhan_bi","套餐占比",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.TAO_CAN_ZHAN_BI,null,false)
        ,TAO_CAN_DA_PEI_RATE("taoCanDaPeiRate","套餐搭售率",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.TAO_CAN_ZHAN_BI,TAO_CAN_ZHAN_BI,false)
        ,GAO_RONG_LIANG_DIAN_CHI_BI("gaoRongLiangDianChiBi","高容量电池比",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.TAO_CAN_ZHAN_BI,TAO_CAN_ZHAN_BI,false)
        ,YOU_HUI_SHU_JU("you_hui_shu_ju","优惠数据",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.YOU_HUI_SHU_JU,null,true)
        ,YOU_HUI_MA_SHI_YONG_LIANG("youHuiMaShiYongLiang","优惠码使用量",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.YOU_HUI_SHU_JU,YOU_HUI_SHU_JU,true)
        ,YOU_HUI_MA_SHI_YONG_ZONG_JIN_E("youHuiMaShiYongZongJinE","优惠码金额",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.YOU_HUI_SHU_JU,YOU_HUI_SHU_JU,true)
        ,GAI_JIA_DAN_LIANG("gaiJiaDanLiang","改价单量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.YOU_HUI_SHU_JU,YOU_HUI_SHU_JU,true)
        ,GAI_JIA_JIN_E("gaiJiaJinE","改价金额",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.YOU_HUI_SHU_JU,YOU_HUI_SHU_JU,true)
        ,WEI_XIU_LIANG("wei_xiu_liang","维修量",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,null,true)
        ,ZONG_XIU_HAO_LIANG("zongXiuHaoLiang","总修好量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,false)

        ,ZI_XIU_ZONG_LIANG("ziXiuZongLiang","自修总量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,ZI_XIU_XIU_HAO_LIANG("ziXiuXiuHaoLiang","自修修好量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,ZI_XIU_XIU_HAO_BU_ZAI_BAO_LIANG("ziXiuXiuHaoBuZaiBaoLiang","自修修好不在保量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,ZI_XIU_XIU_HAO_WAI_XIU_LIANG("ziXiuXiuHaoWaiXiuLiang","自修修好外修量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,ZI_XIU_XIU_HAO_LV("ziXiuXiuHaoRate","自修修好率",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,ZI_XIU_BU_HAO_LIANG("ziXiuBuHaoLiang","自修修不好量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,ZI_XIU_XIU_BU_HAO_LV("ziXiuXiuBuHaoRate","自修修不好率",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,ZI_XIU_XIU_HAO_YOU_MAO_LI_LIANG("ziXiuXiuHaoYouMaoLiLiang","自修修好有毛利量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,ZI_XIU_XIU_HAO_WU_MAO_LI_LIANG("ziXiuXiuHaoWuMaoLiLiang","自修修好无毛利量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,WAI_SONG_XIU_HAO_LIANG("waiSongXiuHaoLiang","外送修好量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)

        ,JIU_JI_FU_WU_XIU_HAO_LIANG("jiuJiFuWuXiuHaoLiang","{printName}服务修好量",292, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,XIU_BU_HAO_LIANG("xiuBuHaoLiang","修不好量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,false)
        ,FAN_XIU_ZONG_LIANG("fanXiuZongLiang","返修总量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,true)
        ,ZI_XIU_FAN_XIU_LIANG("ziXiuFanXiuLiang","自修返修量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,false)
        ,WAI_SONG_FAN_XIU_LIANG("waiSongFanXiuLiang","外送返修量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,false)
        ,YU_YUE_XIU_HAO_LIANG("yuYueXiuHaoLiang","预约修好量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.WEI_XIU_LIANG,WEI_XIU_LIANG,false)
        ,LA_XIN_LIANG("la_xin_liang","拉新量",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.LA_XIN_LIANG,null,false)
        ,CHILD_LA_XIN_LIANG("laXinLiang","拉新量",84, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.LA_XIN_LIANG,LA_XIN_LIANG,false)
        ,LA_XIN_LV("laXinLv","拉新率",84, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.LA_XIN_LIANG,LA_XIN_LIANG,false)
        ,SHOU_HOU_XIAN("shou_hou_xian","售后险",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,null,true)
        ,SHOU_HOU_DIAN_CHI_XIAN_LIANG("shouHouDianChiXianLiang","电池险量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,SHOU_HOU_DIAN_CHI_XIAN_JIN_E("shouHouDianChiXianJinE","电池险金额",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        , SHOU_HOU_DIAN_CHI_CHU_KU_LIANG("shouHouDianChiChuKuLiang","电池险可搭售量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,DIAN_CHI_DA_SHOU_LV("dianChiDaShouLv","电池搭售率",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,SHOU_HOU_DIAN_CHI_XIAN_CHU_XIAN_CHENG_BEN("shouHouDianChiXianChuXianChengBen","电池出险成本",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,false)
        ,SHOU_HOU_SUI_PINGXIAN_LIANG("shouHouSuiPingxianLiang","碎屏险量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,SHOU_HOU_SUI_PING_XIAN_JIN_E("shouHouSuiPingXianJinE","碎屏险金额",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        , SHOU_HOU_PING_MU_CHU_KU_LIANG("shouHouPingMuChuKuLiang","碎屏险可搭售量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,PING_MU_DA_SHOU_LV("pingMuDaShouLv","屏幕搭售率",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,SHOU_HOU_SUI_PING_XIAN_CHU_XIAN_CHENG_BEN("shouHouSuiPingXianChuXianChengBen","碎屏出险成本",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,false)
        ,SHOU_HOU_HOU_GAI_XIAN_LIANG("shouHouHouGaiXianLiang","后盖险量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,SHOU_HOU_HOU_GAI_XIAN_JIN_E("shouHouHouGaiXianJinE","后盖险金额",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        , SHOU_HOU_HOU_GAI_CHU_KU_LIANG("shouHouHouGaiChuKuLiang","后盖险可搭售量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,HOU_GAI_DA_SHOU_LV("houGaiDaShouLv","后盖搭售率",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,AN_XIN_BAO_XIAO_LIANG("anXinBaoXiaoLiang","安心保销量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,AN_XIN_BAO_JIN_E("anXinBaoJinE","安心保金额",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        , DIAN_CHI_PEI_JIAN_AN_XIN_BAO_LIANG("dianChiPeiJianAnXinBaoLiang","安心保可搭售量",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,AN_XIN_BAO_DA_SHOU_LV("anXinBaoDaShouLv","安心保搭售率",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,true)
        ,SHOU_HOU_HOU_GAI_XIAN_CHU_XIAN_CHENG_BEN("shouHouHouGaiXianChuXianChengBen","后盖出险成本",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_XIAN,SHOU_HOU_XIAN,false)
        ,SHOU_HOU_MAO_LI("shou_hou_mao_li","售后毛利",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_MAO_LI,null,true)
        ,WEI_XIU_MAO_LI("weiXiuMaoLi","维修毛利",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_MAO_LI,SHOU_HOU_MAO_LI,true)
        ,WAI_XIU_MAO_LI("waiXiuMaoLi","外修毛利",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_MAO_LI,SHOU_HOU_MAO_LI,true)
        ,RUAN_JIAN_MAO_LI("ruanJianMaoLi","软件毛利",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_MAO_LI,SHOU_HOU_MAO_LI,true)
        ,WEI_XIU_DAN_MAO_LI("weiXiuDanMaoLi","维修单毛利",116, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_MAO_LI,SHOU_HOU_MAO_LI,true)
        ,WEI_XIU_MAO_LV_PEI_BI("weiXiuMaoLvPeiBi","维修毛利配比",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_MAO_LI,SHOU_HOU_MAO_LI,false)
        ,MEN_DIAN_ZHI_HUAN_JIU_JIAN_MAO_LI("menDianZhiHuanJiuJianMaoLi","门店置换旧件毛利",164, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.SHOU_HOU_MAO_LI,SHOU_HOU_MAO_LI,false)
        ,YU_YUE_DAN("yu_yue_dan","预约单",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.YU_YU_DAN,null,false)
        ,YU_YUE_DAN_LIANG("yuYueDanLiang","预约单量",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.YU_YU_DAN,YU_YUE_DAN,false)
        ,YU_YUE_DAN_WAN_CHENG_LIANG("yuYueDanWanChengLiang","预约单完成量",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.YU_YU_DAN,YU_YUE_DAN,false)
        ,YU_YUE_DAN_QU_XIAO_LIANG("yuYueDanQuXiaoLiang","预约单取消量",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.YU_YU_DAN,YU_YUE_DAN,false)
        ,JIU_JIAN_MAO_LI("jiu_jian_mao_li","旧件毛利",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIU_JIAN_MAO_LI,null,true)
        ,CHILD_JIU_JIAN_MAO_LI("jiuJianMaoLi","旧件毛利",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.JIU_JIAN_MAO_LI,JIU_JIAN_MAO_LI,true)
        ,HUAN_WAI_PING_YE_WU("huan_wai_ping_ye_wu","换外屏业务",null, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.HUAN_WAI_PIN_YE_WU,null,true)
        ,WAI_SONG_HUAN_WAI_PING_LIANG("waiSongHuanWaiPingLiang","外送换外屏量",132, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.HUAN_WAI_PIN_YE_WU,HUAN_WAI_PING_YE_WU,false)
        ,ZI_YA_LIANG("ziYaLiang","自压量",84, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.HUAN_WAI_PIN_YE_WU,HUAN_WAI_PING_YE_WU,true)
        ,ZI_YA_LIANG_MAO_LI("ziYaLiangMaoLi","自压毛利",100, TitleColumn.SHOUHOU_YE_JI,ShouHouStatisticsEnum.HUAN_WAI_PIN_YE_WU,HUAN_WAI_PING_YE_WU,true)
        /**售后管控统计*/
        ,GUAN_KONG_AREA("area","地区",null, TitleColumn.SHOUHOU_GUAN_KONG,null,null,true)
        ,GUAN_KONG_CHILD_AREA("area","地区",68, TitleColumn.SHOUHOU_GUAN_KONG,null,GUAN_KONG_AREA,true)
        ,YI_SHOU_YOU_PIN("yi_shou_you_pin","一手优品",null, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.YI_SHOU_YOU_PIN,null,true)
        ,YOU_PIN_LV("youPinLv","优品率",84, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.YI_SHOU_YOU_PIN,YI_SHOU_YOU_PIN,true)
        ,TUI_HUAN_JI_LIANG("tui_huan_ji_liang","退换机量",null, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.HUAN_JI_LIANG,null,true)
        ,TUI_KUAN_LIANG("tuiKuanLiang","退款量",84, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.HUAN_JI_LIANG,TUI_HUAN_JI_LIANG,true)
        ,TUI_KUAN_RATE("tuiKuanRate","退款率",84, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.HUAN_JI_LIANG,TUI_HUAN_JI_LIANG,true)
        ,HUAN_JI_LIANG("huanJiLiang","换机量",84, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.HUAN_JI_LIANG,TUI_HUAN_JI_LIANG,true)
        ,HUAN_JI_RATE("huanJiRate","换机率",84, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.HUAN_JI_LIANG,TUI_HUAN_JI_LIANG,true)
        ,SHOU_HOU_ZHOU_QI("shou_hou_zhou_qi","售后周期",null, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHOU_QI,null,true)
        ,PING_JUN_CHU_LI_ZHOU_QI_DAY("pingJunChuLiZhouQiDay","平均处理周期",132, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHOU_QI,SHOU_HOU_ZHOU_QI,true)
        ,PING_JUN_WAI_SONG_ZHOU_QI_DAY("pingJunWaiSongZhouQiDay","平均外送周期",132, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHOU_QI,SHOU_HOU_ZHOU_QI,true)
        ,PING_JUN_ZI_XIU_ZHOU_QI_DAY("pingJunZiXiuZhouQiDay","平均自修周期",132, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHOU_QI,SHOU_HOU_ZHOU_QI,true)
        ,YI_XIAO_SHI_ZHAN_BI("yiXiaoShiZhanBi","1小时占比",116, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHOU_QI,SHOU_HOU_ZHOU_QI,false)
        ,CHAO3_TIAN_ZHAN_BI("chao3TianZhanBi","超3天占比",116, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHOU_QI,SHOU_HOU_ZHOU_QI,false)
        ,BA_FEN_ZHONG_ZHAN_BI("baFenZhongZhanBi","8分钟内占比",132, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHOU_QI,SHOU_HOU_ZHOU_QI,false)
        ,WEI_XIU_DAN_ZHUANG_TAI("wei_xiu_dan_zhuang_tai","维修单状态",null, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_DAN_ZHUANG_TAI,null,true)
        ,CHU_LI_ZHONG_ZI_XIU_CHAO5_TIAN_LIANG("chuLiZhongZiXiuChao5TianLiang","处理中自修超5天量",180, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_DAN_ZHUANG_TAI,WEI_XIU_DAN_ZHUANG_TAI,true)
        ,CHU_LI_ZHONG_WAI_SONG_CHAO30_TIAN_LIANG("chuLiZhongWaiSongChao30TianLiang","处理中外送超30天量",196, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_DAN_ZHUANG_TAI,WEI_XIU_DAN_ZHUANG_TAI,true)
        ,CHU_LI_ZHONG_YI_XIU_HAO_DAN("chuLiZhongYiXiuHaoDan","处理中已修好单",148, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_DAN_ZHUANG_TAI,WEI_XIU_DAN_ZHUANG_TAI,true)
        ,XIU_BU_HAO_CHAO7_TIAN("xiuBuHaoChao7Tian","修不好超7天",132, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_DAN_ZHUANG_TAI,WEI_XIU_DAN_ZHUANG_TAI,true)
        ,YI_XIU_HAO_CHAO5_TIAN("yiXiuHaoChao5Tian","已修好超5天",132, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_DAN_ZHUANG_TAI,WEI_XIU_DAN_ZHUANG_TAI,true)
        ,YI_XIU_HAO_CHAO30_TIAN("yiXiuHaoChao30Tian","已修好超30天",148, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_DAN_ZHUANG_TAI,WEI_XIU_DAN_ZHUANG_TAI,true)
        ,SHOU_HOU_JING_TUI_JIAN("shou_hou_jing_tui_jian","售后净推荐",null, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_JIN_TUI_JIAN,null,false)
        ,CHILD_SHOU_HOU_JING_TUI_JIAN("shouHouJingTuiJian","售后净推荐",116, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_JIN_TUI_JIAN,SHOU_HOU_JING_TUI_JIAN,false)
        ,JI_SHU_JING_TUI_JIAN("jiShuJingTuiJian","技术净推荐",116, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_JIN_TUI_JIAN,SHOU_HOU_JING_TUI_JIAN,false)
        ,ZONG_HE_JING_TUI_JIAN("zongHeJingTuiJian","综合净推荐",116, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_JIN_TUI_JIAN,SHOU_HOU_JING_TUI_JIAN,false)
        ,SHOU_HOU_CHENG_BEN("shou_hou_cheng_ben","售后成本",null, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_CHENG_BEN,null,true)
        ,JIU_JI_FU_WU_CHENG_BEN("jiuJiFuWuChengBen","{printName}服务成本",276, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_CHENG_BEN,SHOU_HOU_CHENG_BEN,true)
        ,TE_SHU_ZHI_BAO_CHENG_BEN("teShuZhiBaoChengBen","在保特殊维修成本",164, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_CHENG_BEN,SHOU_HOU_CHENG_BEN,true)
        ,LIANG_PIN_CHENG_BEN("liangPinChengBen","良品成本",100, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_CHENG_BEN,SHOU_HOU_CHENG_BEN,true)
        ,YOU_PIN_CHENG_BEN("youPinChengBen","优品成本",100, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_CHENG_BEN,SHOU_HOU_CHENG_BEN,true)
        ,WEI_XIU_PEI_JIAN("wei_xiu_pei_jian","维修配件",null, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_PEI_JIAN,null,true)
        ,FAN_XIU_HUAN_HUO_CHENG_BEN("fanXiuHuanHuoChengBen","返修换货成本",132, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_PEI_JIAN,WEI_XIU_PEI_JIAN,false)
        ,WEN_TI_PEI_JIAN_LV("wenTiPeiJianLv","问题配件率",116, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_PEI_JIAN,WEI_XIU_PEI_JIAN,false)
        ,ZHI_XIAO_LV("zhiXiaoLv","滞销率",84, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_PEI_JIAN,WEI_XIU_PEI_JIAN,false)
        ,JIU_JIAN_FAN_HUAI_RATE("jiuJianFanHuaiRate","屏幕旧件返还率",148, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.WEI_XIU_PEI_JIAN,WEI_XIU_PEI_JIAN,true)
        ,SHOU_HOU_ZHUAN_CHU_LIANG("shou_hou_zhuan_chu_liang","售后转出量",null, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHUAN_CHU_LIANG,null,true)
        ,CHILD_SHOU_HOU_ZHUAN_CHU_LIANG("shouHouZhuanChuLiang","售后转出量",116, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHUAN_CHU_LIANG,SHOU_HOU_ZHUAN_CHU_LIANG,true)
        ,SHI_SHI_XIN_JI_KU_CUN("shiShiXinJiKuCun","实时新机库存",132, TitleColumn.SHOUHOU_GUAN_KONG,ShouHouStatisticsEnum.SHOU_HOU_ZHUAN_CHU_LIANG,SHOU_HOU_ZHUAN_CHU_LIANG,true)
        ,ZHOU_QI_AREA("area","地区",null, TitleColumn.SHOUHOU_ZHOU_QI,null,null,true)
        ,ZHOU_QI_CHILD_AREA("area","地区",68, TitleColumn.SHOUHOU_ZHOU_QI,null,ZHOU_QI_AREA,true)
        ,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG("wei_xiu_dan_zhou_qi_guan_kong","维修单周期管控",null, TitleColumn.SHOUHOU_ZHOU_QI,null,null,true)
        ,MEN_DIAN_ZI_XIU_CHAO1_TIAN("menDianZiXiuChao1Tian","门店自修超1天",148, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,MEN_DIAN_ZI_XIU_CHAO3_TIAN("menDianZiXiuChao3Tian","门店自修超3天",148, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,KUA_DIAN_ZI_XIU_CHAO5_TIAN("kuaDianZiXiuChao5Tian","跨店自修超5天",148, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,ZI_XIU_CHAO7_TIAN("ziXiuChao7Tian","自修超7天",116, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,AN_ZHUO_WAI_SONG_CHAO10_TIAN("anZhuoWaiSongChao10Tian","安卓外送超10天",164, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,PING_GUO_WAI_SONG_CHAO25_TIAN("pingGuoWaiSongChao25Tian","苹果外送超25天",164, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,WAI_XIU_WAI_SONG_CHAO15_TIAN("waiXiuWaiSongChao15Tian","外修外送超15天",164, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,WAI_SONG_CHAO30_TIAN("waiSongChao30Tian","外送超30天",132, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,ZHOU_QI_YI_XIU_HAO_CHAO5_TIAN("zhouQiYiXiuHaoChao5Tian","已修好超5天",132, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,ZHOU_QI_YI_XIU_HAO_CHAO30_TIAN("zhouQiYiXiuHaoChao30Tian","已修好超30天",148, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)
        ,ZHOU_QI_YI_XIU_HAO_CHAO60_TIAN("zhouQiYiXiuHaoChao60Tian","已修好超60天",148, TitleColumn.SHOUHOU_ZHOU_QI,null,WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,true)

        ;

        /**
         * 字段名称
         */
        private String code;

        /**
         * 编码对应信息
         */
        private String message;
        /**
         * 宽度
         */
        private Integer width;
        /**
         * 关联的标题(空表示所有都有)
         */
        private TitleColumn titleColumn;
        /**
         *
         */
        private ShouHouStatisticsEnum statisticsEnum;
        /**
         * 父标题
         */
        private FieldColumnEnum parent;
        /**
         * 是否显示
         */
        private Boolean show;

        private FieldColumnEnum(final String code, final String message, final Integer width, final ShouHouStatisticsColumnJiuji.TitleColumn titleColumn
                , final ShouHouStatisticsEnum statisticsEnum, final FieldColumnEnum parent, final Boolean show) {
            this.code = code;
            this.message = message;
            this.width = width;
            this.titleColumn = titleColumn;
            this.statisticsEnum = statisticsEnum;
            this.parent = parent;
            this.show = show;
        }
    }

    /**
     * 字段的其他信息
     */
    @Getter
    @AllArgsConstructor
    public enum FieldOtherInfoEnum implements CodeMessageEnumInterface{
        OTHER_YE_JI_AREA(FieldColumnEnum.YE_JI_AREA,"")
        ,OTHER_YE_JI_CHILD_AREA(FieldColumnEnum.YE_JI_CHILD_AREA,"")
        ,OTHER_JIE_JIAN_LIANG(FieldColumnEnum.JIE_JIAN_LIANG,"")
        ,OTHER_TOTAL_JIE_JIAN(FieldColumnEnum.TOTAL_JIE_JIAN,"1.【硬件】维修单 2.维修单【没有删除】 3.按照【接件时间】进行查询 4.按照【接件地区】维度进行统计 5.【排除现货维修单】的量 单位【单量】")
        ,OTHER_ZAI_BAO_JIE_JIAN_LIANG(FieldColumnEnum.ZAI_BAO_JIE_JIAN_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单【已取机】 4.【排除现货维修单】的量 5.维修单【已取机】 6.维修单选择【在保】 7.【排除出险服务】单量 8.按照【取机时间】进行查询 9.按照【接件地区】维度进行统计  单位【单量】")
        ,OTHER_BU_ZAI_BAO_JIE_JIAN_LIANG(FieldColumnEnum.BU_ZAI_BAO_JIE_JIAN_LIANG,"")
        ,OTHER_WAI_XIU_JIE_JIAN_LIANG(FieldColumnEnum.WAI_XIU_JIE_JIAN_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单【已取机】 4.【排除现货维修单】的量 5.维修单【已取机】 6.维修单选择【外修】 7.维修单【有成本或者有费用】 8.维修单【退完所有维修费】 9.按照【取机时间】进行查询 10.按照【接件地区】维度进行统计  单位【单量】")
        ,OTHER_JI_JI_FU_WU_JIE_JIAN_LIANG(FieldColumnEnum.JI_JI_FU_WU_JIE_JIAN_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单【已取机】 4.【排除现货维修单】的量 5.维修单【已取机】 6.维修单状态【已修好】 7.维修单【出险服务】 8.按照【取机时间】进行查询 9.按照【接件地区】维度进行统计  单位【单量】")
        ,OTHER_FEI_BAO_RATE(FieldColumnEnum.FEI_BAO_RATE,"")
        ,OTHER_FEI_BAO_JIE_JIAN_RATE(FieldColumnEnum.FEI_BAO_JIE_JIAN_RATE,"（不在保量+外修量）/总接件量 （单位：百分比）")
        ,OTHER_RUAN_JIAN_JIE_JIAN_LIANG(FieldColumnEnum.RUAN_JIAN_JIE_JIAN_LIANG,"1.查询【软件安装记录】 2.软件安装记录【有提成】 3.按照软件安装记录的【添加时间查询】 4.按照【软件安装的地区（接件地区）】进行统计  单位【单量】")
        ,OTHER_RUAN_JIAN_JIE_JIAN_RATE(FieldColumnEnum.RUAN_JIAN_JIE_JIAN_RATE,"")
        ,OTHER_YU_YUE_JIE_JIAN_LIANG(FieldColumnEnum.YU_YUE_JIE_JIAN_LIANG,"1.预约单【状态完成】 2.预约单【必须生成维修单】 3.关联的维修单【收件】 4.关联的维修单【没有删除】 5.按照预约单的【加单时间】进行查询 6.按照【预约单地区（生成维修单的接件地区）】维度进行统计")
        ,OTHER_TAO_CAN_ZHAN_BI(FieldColumnEnum.TAO_CAN_ZHAN_BI,"")
        ,OTHER_TAO_CAN_DA_PEI_RATE(FieldColumnEnum.TAO_CAN_DA_PEI_RATE,"")
        ,OTHER_GAO_RONG_LIANG_DIAN_CHI_BI(FieldColumnEnum.GAO_RONG_LIANG_DIAN_CHI_BI,"")
        ,OTHER_YOU_HUI_SHU_JU(FieldColumnEnum.YOU_HUI_SHU_JU,"")
        ,OTHER_YOU_HUI_MA_SHI_YONG_LIANG(FieldColumnEnum.YOU_HUI_MA_SHI_YONG_LIANG,"统计再以下条件下使用过优惠码 1.排除【在保】维修单 2.维修单【已取机】 3.维修单状态为【已修好】 4.优惠码已经使用 5.按照【优惠码使用时间】进行查询 6.统计使用的优惠码的数量  单位【优惠码使用量】")
        ,OTHER_YOU_HUI_MA_SHI_YONG_ZONG_JIN_E(FieldColumnEnum.YOU_HUI_MA_SHI_YONG_ZONG_JIN_E,"统计再以下条件下使用过优惠码 1.排除【在保】维修单 2.维修单【已取机】 3.维修单状态为【已修好】 4.优惠码已经使用 5.按照【优惠码使用时间】进行查询 6.统计使用的优惠码的金额  单位【优惠码使用金额】")
        ,OTHER_GAI_JIA_DAN_LIANG(FieldColumnEnum.GAI_JIA_DAN_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单【没有出服务】 4.维修配件【有优惠金额】 5.维修单状态【已修好】 6.【排除在保】维修单 7.排除【换货的维修配件】 8.按照【取机时间】进行查询 9.按照【维修单接件地区】维度进行统计  单位【维修配件量】")
        ,OTHER_GAI_JIA_JIN_E(FieldColumnEnum.GAI_JIA_JIN_E,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单【没有出服务】 4.维修配件【有优惠金额】 5.【排除人工费用】 6.【排除显示总成置换】 7.维修单状态【已修好】 8.【排除在保】维修单 9.排除退款的 10.排除内部员工的 11.排除总部改价 12.按照【取机时间】进行查询 13.按照【维修单接件地区】维度进行统计  单位【维修金额】")
        ,OTHER_WEI_XIU_LIANG(FieldColumnEnum.WEI_XIU_LIANG,"")
        ,OTHER_ZONG_XIU_HAO_LIANG(FieldColumnEnum.ZONG_XIU_HAO_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3. 维修单状态【已修好】 4.按照【取机时间】进行查询 5.按照【接件地区】维度进行统计   单位【单量】")
        ,OTHER_WAI_SONG_XIU_HAO_LIANG(FieldColumnEnum.WAI_SONG_XIU_HAO_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单【没有退维修费】 5.维修单【存在外送渠道】 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计   单位【单量】")
        ,OTHER_ZI_XIU_XIU_HAO_LIANG(FieldColumnEnum.ZI_XIU_XIU_HAO_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.排除外送，在保订单 5.维修单【不存在外送渠道】 6.按照【取机时间】进行查询 7.维修单的维修费用、维修成本无限制 8.单位【单量】")

        ,OTHER_ZI_XIU_ZONG_LIANG(FieldColumnEnum.ZI_XIU_ZONG_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好,修不好】 4.排除外送，在保订单 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计 7.维修单的维修费用、维修成本无限制 8.单位【单量】")
        ,OTHER_ZI_XIU_XIU_HAO_BU_ZAI_BAO_LIANG(FieldColumnEnum.ZI_XIU_XIU_HAO_BU_ZAI_BAO_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.排除外送，在保订单 5.保修状态为【不在保】 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计 8.维修单的维修费用、维修成本无限制 9.单位【单量】")
        ,OTHER_ZI_XIU_XIU_HAO_WAI_XIU_LIANG(FieldColumnEnum.ZI_XIU_XIU_HAO_WAI_XIU_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.排除外送，在保订单 5.保修状态为【外修】 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计 8.维修单的维修费用、维修成本无限制 9.单位【单量】")
        ,OTHER_ZI_XIU_BU_HAO_LIANG(FieldColumnEnum.ZI_XIU_BU_HAO_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【修不好】 4.排除外送，在保订单 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计 7.维修单的维修费用、维修成本无限制 8.单位【单量】")
        ,OTHER_ZI_XIU_XIU_HAO_YOU_MAO_LI_LIANG(FieldColumnEnum.ZI_XIU_XIU_HAO_YOU_MAO_LI_LIANG, "1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.排除外送，在保订单 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计 7.维修单的【维修费用-维修成本】>0 8.单位【单量】")
        ,OTHER_ZI_XIU_XIU_HAO_WU_MAO_LI_LIANG(FieldColumnEnum.ZI_XIU_XIU_HAO_WU_MAO_LI_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.排除外送，在保订单 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计 7.维修单的【维修费用-维修成本】等于0或者负毛利 8.单位【单量】")

        ,OTHER_JIU_JI_FU_WU_XIU_HAO_LIANG(FieldColumnEnum.JIU_JI_FU_WU_XIU_HAO_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单【使用过九机服务】 5.维修单【不存在外送渠道】 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计 8.维修单【没有退维修费】  单位【单量】")
        ,OTHER_XIU_BU_HAO_LIANG(FieldColumnEnum.XIU_BU_HAO_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【修不好】 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计   单位【单量】")
        ,OTHER_FAN_XIU_ZONG_LIANG(FieldColumnEnum.FAN_XIU_ZONG_LIANG,"1、同一个串号，一个月内再次录单即算一单。 2、每录一次单即算一次返修。")
        ,OTHER_ZI_XIU_FAN_XIU_LIANG(FieldColumnEnum.ZI_XIU_FAN_XIU_LIANG,"")
        ,OTHER_WAI_SONG_FAN_XIU_LIANG(FieldColumnEnum.WAI_SONG_FAN_XIU_LIANG,"")
        ,OTHER_YU_YUE_XIU_HAO_LIANG(FieldColumnEnum.YU_YUE_XIU_HAO_LIANG,"")
        ,OTHER_LA_XIN_LIANG(FieldColumnEnum.LA_XIN_LIANG,"")
        ,OTHER_CHILD_LA_XIN_LIANG(FieldColumnEnum.CHILD_LA_XIN_LIANG,"")
        ,OTHER_LA_XIN_LV(FieldColumnEnum.LA_XIN_LV,"")
        ,OTHER_SHOU_HOU_XIAN(FieldColumnEnum.SHOU_HOU_XIAN,"")
        ,OTHER_SHOU_HOU_DIAN_CHI_XIAN_LIANG(FieldColumnEnum.SHOU_HOU_DIAN_CHI_XIAN_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件ppid为：81683】 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计   单位【配件数量】")
        ,OTHER_SHOU_HOU_DIAN_CHI_XIAN_JIN_E(FieldColumnEnum.SHOU_HOU_DIAN_CHI_XIAN_JIN_E,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件ppid为：81683】 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计 7.金额按照 使用抵扣之后的价格来计算  单位【配件价格】 ")
        ,OTHER_DIAN_CHI_DA_SHOU_LV(FieldColumnEnum.DIAN_CHI_DA_SHOU_LV,"计算逻辑：电池险量/电池配件出库数量  电池险量：页面现有统计字段 电池配件出库数量： 1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件绑定过ppid为：81683 的售后服务】 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计  单位【百分比】")
        ,OTHER_SHOU_HOU_DIAN_CHI_XIAN_CHU_XIAN_CHENG_BEN(FieldColumnEnum.SHOU_HOU_DIAN_CHI_XIAN_CHU_XIAN_CHENG_BEN,"")
        ,OTHER_SHOU_HOU_SUI_PINGXIAN_LIANG(FieldColumnEnum.SHOU_HOU_SUI_PINGXIAN_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件ppid为：81682】 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计   单位【配件数量】")
        ,OTHER_SHOU_HOU_SUI_PING_XIAN_JIN_E(FieldColumnEnum.SHOU_HOU_SUI_PING_XIAN_JIN_E,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件ppid为：81682】 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计 7.金额按照 使用抵扣之后的价格来计算  单位【配件价格】")
        ,OTHER_PING_MU_DA_SHOU_LV(FieldColumnEnum.PING_MU_DA_SHOU_LV," 计算逻辑：碎屏险量/屏幕配件出库数量  碎屏险量：页面现有统计字段 屏幕配件出库数量： 1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件绑定过ppid为：81682 的售后服务】 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计  单位【百分比】")
        ,OTHER_SHOU_HOU_SUI_PING_XIAN_CHU_XIAN_CHENG_BEN(FieldColumnEnum.SHOU_HOU_SUI_PING_XIAN_CHU_XIAN_CHENG_BEN,"")
        ,OTHER_SHOU_HOU_HOU_GAI_XIAN_LIANG(FieldColumnEnum.SHOU_HOU_HOU_GAI_XIAN_LIANG,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件ppid为：110939】 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计   单位【配件数量】")
        ,OTHER_SHOU_HOU_HOU_GAI_XIAN_JIN_E(FieldColumnEnum.SHOU_HOU_HOU_GAI_XIAN_JIN_E,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件ppid为：110939】 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计 7.金额按照 使用抵扣之后的价格来计算  单位【配件价格】")
        ,OTHER_HOU_GAI_DA_SHOU_LV(FieldColumnEnum.HOU_GAI_DA_SHOU_LV," 计算逻辑：后盖险量/后盖配件出库数量  后盖险量：页面现有统计字段 后盖配件出库数量： 1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件绑定过ppid为：110939 的售后服务】 5.按照【取机时间】进行查询 6.按照【接件地区】维度进行统计  单位【百分比】")
        ,OTHER_AN_XIN_BAO_XIAO_LIANG(FieldColumnEnum.AN_XIN_BAO_XIAO_LIANG," 1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】5.维修单的【维修配件ppid为：182901】 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计 8.单位【配件数量】")
        ,OTHER_AN_XIN_BAO_JIN_E(FieldColumnEnum.AN_XIN_BAO_JIN_E," 1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单的【维修配件ppid为：182901】 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计8.金额按照使用抵扣之后的价格来计算 9.单位【配件价格】")
        ,OTHER_AN_XIN_BAO_DA_SHOU_LV(FieldColumnEnum.AN_XIN_BAO_DA_SHOU_LV," 计算逻辑：安心保销量/电池配件出库数量 安心保销量：页面现有统计字段 安心保配件出库数量：1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.维修单的【维修配件没有撤销】 5.维修单出配件时配件可以购买安心保服务 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计 单位【百分比】")
        ,OTHER_SHOU_HOU_HOU_GAI_XIAN_CHU_XIAN_CHENG_BEN(FieldColumnEnum.SHOU_HOU_HOU_GAI_XIAN_CHU_XIAN_CHENG_BEN,"")
        ,OTHER_SHOU_HOU_MAO_LI(FieldColumnEnum.SHOU_HOU_MAO_LI,"")
        ,OTHER_WEI_XIU_MAO_LI(FieldColumnEnum.WEI_XIU_MAO_LI,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.【排除在保】的维修单 5.维修单【排除配件全部撤销退款】 6.维修单【没有出服务】或（【出了服务】且 【维修配件抵扣后的价格-维修配件成本>0】） 7.维修单的【维修配件没有撤销】 8.排除维修单的【换货维修配件】 9.按照【取机时间】进行查询 10.按照【接件地区】维度进行统计 11.最终统计维修配件毛利之和，计算方式：毛利=维修配件价格（排除优惠码等摊销金额）-维修配件成本  单位【金额】")
        ,OTHER_WAI_XIU_MAO_LI(FieldColumnEnum.WAI_XIU_MAO_LI,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.【排除在保】的维修单 5.维修单【排除配件全部撤销退款】 6.维修单【没有出服务】或（【出了服务】且 【维修配件抵扣后的价格-维修配件成本>0】） 7.维修单的【维修配件没有撤销】 8.排除维修单的【换货维修配件】 9.按照【取机时间】进行查询 10.按照【接件地区】维度进行统计 11.最终统计维修配件毛利之和，计算方式：毛利=维修配件价格（排除优惠码等摊销金额）-维修配件成本  单位【金额】")
        ,OTHER_RUAN_JIAN_MAO_LI(FieldColumnEnum.RUAN_JIAN_MAO_LI,"1.【软件】维修单 2.维修单【取机】 3.维修单状态【已修好】 5.维修单【没有删除】 6.按照【取机时间】进行查询 7.按照【接件地区】维度进行统计 8.毛利的计算方式为：【维修单的费用-维修单的成本】 9.增加维修配件的维度进行计算毛利 （1）排除【撤销状态】的维修配件 （2）计算逻辑：毛利=抵扣后配件价格-配件成本 单位【金额】")
        ,OTHER_WEI_XIU_DAN_MAO_LI(FieldColumnEnum.WEI_XIU_DAN_MAO_LI,"计算逻辑：【维修毛利】/【自修修好量】  单位【金额】")
        ,OTHER_WEI_XIU_MAO_LV_PEI_BI(FieldColumnEnum.WEI_XIU_MAO_LV_PEI_BI,"")
        ,OTHER_MEN_DIAN_ZHI_HUAN_JIU_JIAN_MAO_LI(FieldColumnEnum.MEN_DIAN_ZHI_HUAN_JIU_JIAN_MAO_LI,"")
        ,OTHER_YU_YUE_DAN(FieldColumnEnum.YU_YUE_DAN,"")
        ,OTHER_YU_YUE_DAN_LIANG(FieldColumnEnum.YU_YUE_DAN_LIANG,"")
        ,OTHER_YU_YUE_DAN_WAN_CHENG_LIANG(FieldColumnEnum.YU_YUE_DAN_WAN_CHENG_LIANG,"")
        ,OTHER_YU_YUE_DAN_QU_XIAO_LIANG(FieldColumnEnum.YU_YUE_DAN_QU_XIAO_LIANG,"")
        ,OTHER_JIU_JIAN_MAO_LI(FieldColumnEnum.JIU_JIAN_MAO_LI,"")
        ,OTHER_CHILD_JIU_JIAN_MAO_LI(FieldColumnEnum.CHILD_JIU_JIAN_MAO_LI,"1.数据主要查询旧件库存数据 2.旧件关联的维修单【没有删除】 3.【硬件】维修单 4.维修单状态【已修好】 5.按照【旧件出售时间】进行查询 6.按照【对应维修单的接件地区】维度进行统计 7.统计方式：售卖价格-回收价格  单位【金额】")
        ,OTHER_HUAN_WAI_PING_YE_WU(FieldColumnEnum.HUAN_WAI_PING_YE_WU,"")
        ,OTHER_WAI_SONG_HUAN_WAI_PING_LIANG(FieldColumnEnum.WAI_SONG_HUAN_WAI_PING_LIANG,"")
        ,OTHER_ZI_YA_LIANG(FieldColumnEnum.ZI_YA_LIANG,"1.【排除退款的维修单】 2.维修单【配件商品分类属于分类id：603】 3.维修单【取机】 4.维修单的【维修配件没有撤销】 5.维修单状态【已修好】 6.【硬件】维修单 7.维修单【有成本或费用】 8.维修单的【维修配件有售价】 9.【排除在保维修单】 10.按照【取机时间】进行查询 11.按照【维修单接件地区】维度进行统计  单位【维修单量】")
        ,OTHER_ZI_YA_LIANG_MAO_LI(FieldColumnEnum.ZI_YA_LIANG_MAO_LI,"1.【排除退款的维修单】 2.维修单【配件商品分类属于分类id：603】 3.维修单【取机】 4.维修单的【维修配件没有撤销】 5.维修单状态【已修好】 6.【硬件】维修单 7.【排除出险服务维修单】 8.维修单【有成本或费用】 9.维修单的【维修配件有售价】 10.【排除在保维修单】 11.按照【取机时间】进行查询 12.按照【维修单接件地区】维度进行统计 13.毛利计算方式：配件抵扣之后的价格-配件成本  单位【毛利】")
        ,OTHER_GUAN_KONG_AREA(FieldColumnEnum.GUAN_KONG_AREA,"")
        ,OTHER_GUAN_KONG_CHILD_AREA(FieldColumnEnum.GUAN_KONG_CHILD_AREA,"")
        ,OTHER_YI_SHOU_YOU_PIN(FieldColumnEnum.YI_SHOU_YOU_PIN,"")
        ,OTHER_YOU_PIN_LV(FieldColumnEnum.YOU_PIN_LV,"")
        ,OTHER_TUI_HUAN_JI_LIANG(FieldColumnEnum.TUI_HUAN_JI_LIANG,"")
        ,OTHER_TUI_KUAN_LIANG(FieldColumnEnum.TUI_KUAN_LIANG,"")
        ,OTHER_TUI_KUAN_RATE(FieldColumnEnum.TUI_KUAN_RATE,"")
        ,OTHER_HUAN_JI_LIANG(FieldColumnEnum.HUAN_JI_LIANG,"")
        ,OTHER_HUAN_JI_RATE(FieldColumnEnum.HUAN_JI_RATE,"")
        ,OTHER_SHOU_HOU_ZHOU_QI(FieldColumnEnum.SHOU_HOU_ZHOU_QI,"")
        ,OTHER_PING_JUN_CHU_LI_ZHOU_QI_DAY(FieldColumnEnum.PING_JUN_CHU_LI_ZHOU_QI_DAY,"1.【硬件】维修单 2.维修单【没有删除】 3.按照【取机时间进行查询】 4.按照【接件地区】维度进行统计 5.计算方式：每个维修单的【（优先取【修好时间】，没有的话取【取机时间】）-【接件时间】】求平均值   6.维修单【已取机】 单位【时间，计算精确到分钟】")
        ,OTHER_PING_JUN_WAI_SONG_ZHOU_QI_DAY(FieldColumnEnum.PING_JUN_WAI_SONG_ZHOU_QI_DAY,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单【有外送渠道】 4.维修单【取机】 5.维修单状态为【已修好】 6.按照【取机时间进行查询】 7.按照【接件地区】维度进行统计 8.计算方式：每个维修单的【（优先取【修好时间】，没有的话取【取机时间】）-【接件时间】】求平均值     单位【时间，计算精确到分钟】")
        ,OTHER_PING_JUN_ZI_XIU_ZHOU_QI_DAY(FieldColumnEnum.PING_JUN_ZI_XIU_ZHOU_QI_DAY,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单【没有外送渠道】 4.维修单【取机】 5.按照【取机时间进行查询】 6.按照【接件地区】维度进行统计 7.计算方式：每个维修单的【（优先取【修好时间】，没有的话取【取机时间】）-【接件时间】】求平均值   8.维修单【已修好】 单位【时间，计算精确到分钟】")
        ,OTHER_YI_XIAO_SHI_ZHAN_BI(FieldColumnEnum.YI_XIAO_SHI_ZHAN_BI,"")
        ,OTHER_CHAO3_TIAN_ZHAN_BI(FieldColumnEnum.CHAO3_TIAN_ZHAN_BI,"")
        ,OTHER_BA_FEN_ZHONG_ZHAN_BI(FieldColumnEnum.BA_FEN_ZHONG_ZHAN_BI,"")
        ,OTHER_WEI_XIU_DAN_ZHUANG_TAI(FieldColumnEnum.WEI_XIU_DAN_ZHUANG_TAI,"")
        ,OTHER_CHU_LI_ZHONG_ZI_XIU_CHAO5_TIAN_LIANG(FieldColumnEnum.CHU_LI_ZHONG_ZI_XIU_CHAO5_TIAN_LIANG,"")
        ,OTHER_CHU_LI_ZHONG_WAI_SONG_CHAO30_TIAN_LIANG(FieldColumnEnum.CHU_LI_ZHONG_WAI_SONG_CHAO30_TIAN_LIANG,"")
        ,OTHER_CHU_LI_ZHONG_YI_XIU_HAO_DAN(FieldColumnEnum.CHU_LI_ZHONG_YI_XIU_HAO_DAN,"")
        ,OTHER_XIU_BU_HAO_CHAO7_TIAN(FieldColumnEnum.XIU_BU_HAO_CHAO7_TIAN,"")
        ,OTHER_YI_XIU_HAO_CHAO5_TIAN(FieldColumnEnum.YI_XIU_HAO_CHAO5_TIAN,"")
        ,OTHER_YI_XIU_HAO_CHAO30_TIAN(FieldColumnEnum.YI_XIU_HAO_CHAO30_TIAN,"")
        ,OTHER_SHOU_HOU_JING_TUI_JIAN(FieldColumnEnum.SHOU_HOU_JING_TUI_JIAN,"")
        ,OTHER_CHILD_SHOU_HOU_JING_TUI_JIAN(FieldColumnEnum.CHILD_SHOU_HOU_JING_TUI_JIAN,"")
        ,OTHER_JI_SHU_JING_TUI_JIAN(FieldColumnEnum.JI_SHU_JING_TUI_JIAN,"")
        ,OTHER_ZONG_HE_JING_TUI_JIAN(FieldColumnEnum.ZONG_HE_JING_TUI_JIAN,"")
        ,OTHER_SHOU_HOU_CHENG_BEN(FieldColumnEnum.SHOU_HOU_CHENG_BEN,"")
        ,OTHER_JIU_JI_FU_WU_CHENG_BEN(FieldColumnEnum.JIU_JI_FU_WU_CHENG_BEN,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.【排除现货】维修单 5.【排除换货维修配件】 6.维修单状态【已修好】 7.维修单【出险服务】 8.维修单【配件价格为0】 11.维修单【排除手工费】 12.计算方式：维修单维修配件价格-维修配件的成本 13. 按照【取机时间】进行查询 14.按照【维修单接件地区】维度进行统计 单位【金额】")
        ,OTHER_TE_SHU_ZHI_BAO_CHENG_BEN(FieldColumnEnum.TE_SHU_ZHI_BAO_CHENG_BEN,"1.【硬件】维修单 2.维修单【没有删除】 3.维修单状态【已修好】 4.【排除现货】维修单 5.【排除换货维修配件】 6.维修单【在保】 7.维修单【不是显示总成置换】 8.维修单【没有出过服务】 9.【排除良品维修单】 10.【排除优品维修单】 11. 按照【取机时间】进行查询 12.按照【维修单接件地区】维度进行统计 13.最终【统计维修配件成本】  单位【金额】")
        ,OTHER_LIANG_PIN_CHENG_BEN(FieldColumnEnum.LIANG_PIN_CHENG_BEN,"1.【硬件】维修单 2.维修单【没有删除】 3.维修状态【已修好】 4.维修单【在保】 5.维修单【排除手工费】 6.维修单【排除撤销的维修配件】 7.维修单为【良品维修单】 8. 按照【取机时间】进行查询 9.按照【维修单接件地区】维度进行统计 10.最终【统计维修配件成本】  单位【金额】")
        ,OTHER_YOU_PIN_CHENG_BEN(FieldColumnEnum.YOU_PIN_CHENG_BEN,"1.【硬件】维修单 2.维修单【没有删除】 3.维修状态【已修好】 4.维修单【在保】 5.维修单【排除手工费】 6.维修单【排除撤销的维修配件】 7.维修单为【优品维修单】 8. 按照【取机时间】进行查询 9.按照【维修单接件地区】维度进行统计 10.最终【统计维修配件成本】  单位【金额】")
        ,OTHER_WEI_XIU_PEI_JIAN(FieldColumnEnum.WEI_XIU_PEI_JIAN,"")
        ,OTHER_FAN_XIU_HUAN_HUO_CHENG_BEN(FieldColumnEnum.FAN_XIU_HUAN_HUO_CHENG_BEN,"")
        ,OTHER_WEN_TI_PEI_JIAN_LV(FieldColumnEnum.WEN_TI_PEI_JIAN_LV,"")
        ,OTHER_ZHI_XIAO_LV(FieldColumnEnum.ZHI_XIAO_LV,"")
        ,OTHER_JIU_JIAN_FAN_HUAI_RATE(FieldColumnEnum.JIU_JIAN_FAN_HUAI_RATE,"统计逻辑：屏幕旧件返还率=配件返还单量/屏幕更换维修单量 配件返还单量统计逻辑： 1.【硬件】维修单 2.维修单【没有删除】 3.维修单的【费用大于成本】 4.【维修配件属于屏幕分类】 5.维修单【配件已回收】 6. 按照【取机时间】进行查询 7.按照【维修单接件地区】维度进行统计 8.统计【维修配件的数量】  屏幕更换维修单量统计逻辑： 1.【硬件】维修单 2.维修单【没有删除】 3.维修单的【费用大于成本】 4.【维修配件属于屏幕分类】 5. 按照【取机时间】进行查询 6.按照【维修单接件地区】维度进行统计 7.统计【维修配件的数量】  单位【百分比】")
        ,OTHER_SHOU_HOU_ZHUAN_CHU_LIANG(FieldColumnEnum.SHOU_HOU_ZHUAN_CHU_LIANG,"")
        ,OTHER_CHILD_SHOU_HOU_ZHUAN_CHU_LIANG(FieldColumnEnum.CHILD_SHOU_HOU_ZHUAN_CHU_LIANG,"")
        ,OTHER_SHI_SHI_XIN_JI_KU_CUN(FieldColumnEnum.SHI_SHI_XIN_JI_KU_CUN,"")
        ,OTHER_ZHOU_QI_AREA(FieldColumnEnum.ZHOU_QI_AREA,"")
        ,OTHER_ZHOU_QI_CHILD_AREA(FieldColumnEnum.ZHOU_QI_CHILD_AREA,"")
        ,OTHER_WEI_XIU_DAN_ZHOU_QI_GUAN_KONG(FieldColumnEnum.WEI_XIU_DAN_ZHOU_QI_GUAN_KONG,"")
        ,OTHER_MEN_DIAN_ZI_XIU_CHAO1_TIAN(FieldColumnEnum.MEN_DIAN_ZI_XIU_CHAO1_TIAN,"")
        ,OTHER_MEN_DIAN_ZI_XIU_CHAO3_TIAN(FieldColumnEnum.MEN_DIAN_ZI_XIU_CHAO3_TIAN,"")
        ,OTHER_KUA_DIAN_ZI_XIU_CHAO5_TIAN(FieldColumnEnum.KUA_DIAN_ZI_XIU_CHAO5_TIAN,"")
        ,OTHER_ZI_XIU_CHAO7_TIAN(FieldColumnEnum.ZI_XIU_CHAO7_TIAN,"")
        ,OTHER_AN_ZHUO_WAI_SONG_CHAO10_TIAN(FieldColumnEnum.AN_ZHUO_WAI_SONG_CHAO10_TIAN,"")
        ,OTHER_PING_GUO_WAI_SONG_CHAO25_TIAN(FieldColumnEnum.PING_GUO_WAI_SONG_CHAO25_TIAN,"")
        ,OTHER_WAI_XIU_WAI_SONG_CHAO15_TIAN(FieldColumnEnum.WAI_XIU_WAI_SONG_CHAO15_TIAN,"")
        ,OTHER_WAI_SONG_CHAO30_TIAN(FieldColumnEnum.WAI_SONG_CHAO30_TIAN,"")
        ,OTHER_ZHOU_QI_YI_XIU_HAO_CHAO5_TIAN(FieldColumnEnum.ZHOU_QI_YI_XIU_HAO_CHAO5_TIAN,"")
        ,OTHER_ZHOU_QI_YI_XIU_HAO_CHAO30_TIAN(FieldColumnEnum.ZHOU_QI_YI_XIU_HAO_CHAO30_TIAN,"")
        ,OTHER_ZHOU_QI_YI_XIU_HAO_CHAO60_TIAN(FieldColumnEnum.ZHOU_QI_YI_XIU_HAO_CHAO60_TIAN,"")
        ;
        /**
         * 字段名称
         */
        private FieldColumnEnum code;

        /**
         * 编码对应信息
         */
        private String message;
        /**
         * 详细描述
         */
        private String description;

        FieldOtherInfoEnum(FieldColumnEnum code, String description) {
            this.code = code;
            this.description = description;
        }
    }
}
