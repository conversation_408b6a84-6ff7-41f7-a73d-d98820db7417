package com.jiuji.oa.afterservice.smallpro.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.lang.Dict;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ch999.common.util.vo.Result;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.req.ReturnAnnualPackageReq;
import com.jiuji.oa.afterservice.common.aspect.RepeatSubmitCheck;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.config.component.AbstractCurrentRequestComponent;
import com.jiuji.oa.afterservice.common.constant.PpidConstants;
import com.jiuji.oa.afterservice.common.constant.RequestAttrKeys;
import com.jiuji.oa.afterservice.common.constant.SmallproGroupEnum;
import com.jiuji.oa.afterservice.common.enums.*;
import com.jiuji.oa.afterservice.common.exception.CustomizeException;
import com.jiuji.oa.afterservice.common.exception.RRException;
import com.jiuji.oa.afterservice.common.exception.RRExceptionHandler;
import com.jiuji.oa.afterservice.common.source.OaWcfUrlSource;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.util.ExcelUtils;
import com.jiuji.oa.afterservice.common.util.SpringContextUtil;
import com.jiuji.oa.afterservice.common.vo.ResultByList;
import com.jiuji.oa.afterservice.common.vo.res.CommonDataRes;
import com.jiuji.oa.afterservice.common.vo.res.CommonTitleRes;
import com.jiuji.oa.afterservice.other.bo.AlipayAuthBO;
import com.jiuji.oa.afterservice.other.bo.AreaBelongsDcHqD1AreaId;
import com.jiuji.oa.afterservice.other.po.Areainfo;
import com.jiuji.oa.afterservice.other.service.AreainfoService;
import com.jiuji.oa.afterservice.other.vo.res.PrintClientRes;
import com.jiuji.oa.afterservice.refund.bo.DetailParamBo;
import com.jiuji.oa.afterservice.refund.service.RefundMoneyService;
import com.jiuji.oa.afterservice.refund.service.kind.BaseTuiHuanKindService;
import com.jiuji.oa.afterservice.refund.vo.req.GroupTuihuanFormVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundMoneyDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.RefundWayDetailVo;
import com.jiuji.oa.afterservice.refund.vo.res.TuiHuanCheckVo;
import com.jiuji.oa.afterservice.smallpro.bo.CashOrScrapRes;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproOrderInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproProductInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.SmallproReturnFactoryInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmAccessoriesBo;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmAccessoriesV2Item;
import com.jiuji.oa.afterservice.smallpro.bo.filmCard.FilmCardInfomationBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproRefundExceptionItemBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.NotSoldBackBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproInfo.SmallproInfoProductBO;
import com.jiuji.oa.afterservice.smallpro.bo.smallproSendMsgCode.DIYTimoCardBO;
import com.jiuji.oa.afterservice.smallpro.constant.SmallProRelativePathConstant;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproMapper;
import com.jiuji.oa.afterservice.smallpro.dao.SmallproWxMapper;
import com.jiuji.oa.afterservice.smallpro.enums.*;
import com.jiuji.oa.afterservice.smallpro.mapstruct.SmallproMapStruct;
import com.jiuji.oa.afterservice.smallpro.po.Smallpro;
import com.jiuji.oa.afterservice.smallpro.po.SmallproBill;
import com.jiuji.oa.afterservice.smallpro.service.*;
import com.jiuji.oa.afterservice.smallpro.util.FilmLossUtil;
import com.jiuji.oa.afterservice.smallpro.vo.ShowPrintingEnumVO;
import com.jiuji.oa.afterservice.smallpro.vo.req.*;
import com.jiuji.oa.afterservice.smallpro.vo.res.*;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.dto.YearPackageTransferDetailDto;
import com.jiuji.oa.afterservice.smallpro.yearcardtransfer.service.IYearPackageTransferService;
import com.jiuji.oa.afterservice.statistics.vo.req.CashOrScrapBatchReq;
import com.jiuji.oa.afterservice.statistics.vo.req.SmallproAddLogBatchReq;
import com.jiuji.oa.afterservice.sub.po.Category;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.CategoryService;
import com.jiuji.oa.afterservice.sub.service.SubService;
import com.jiuji.oa.afterservice.sys.enums.ValidMemberType;
import com.jiuji.oa.afterservice.sys.service.Password2ValidService;
import com.jiuji.oa.afterservice.sys.service.SysConfigService;
import com.jiuji.oa.loginfo.order.service.SubLogsCloud;
import com.jiuji.oa.loginfo.order.vo.req.SubLogsNewReq;
import com.jiuji.oa.loginfo.smallprobill.client.vo.SmallproBillLogVo;
import com.jiuji.oa.oacore.csharp.cloud.CsharpCloud;
import com.jiuji.oa.oacore.oaorder.res.MiniFileRes;
import com.jiuji.oa.orginfo.sysconfig.client.SysConfigClient;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import com.jiuji.tc.utils.common.CommonUtils;
import com.jiuji.tc.utils.common.DecideUtil;
import com.jiuji.tc.utils.common.build.LambdaBuild;
import com.jiuji.tc.utils.constants.NumberConstant;
import com.jiuji.tc.utils.constants.SysConfigConstant;
import com.jiuji.tc.utils.xtenant.Namespaces;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Param;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2019-11-13
 */
@RestController
@RequestMapping("/api/smallpro")
@Api(tags = "小件接口")
@Slf4j
public class SmallproController {

    // region autowried
    @Autowired
    private SubService subService;
    @Autowired
    private SmallproService smallproService;
    @Autowired
    private SmallproLogService smallproLogService;
    @Autowired
    private SmallproExchangePurchaseService smallproExchangePurchaseService;
    @Autowired
    private SmallproRefundExService smallproRefundExService;
    @Autowired
    private SmallproDetailsExService smallproDetailsExService;
    @Autowired
    private SmallproOldProductExService smallproOldProductExService;
    @Autowired
    private SmallproForwardExService smallproForwardExService;
    @Autowired
    private AreainfoService areainfoService;
    @Autowired
    private AbstractCurrentRequestComponent abstractCurrentRequestComponent;
    @Autowired
    private SmallproFilmCardService smallproFilmCardService;
    @Resource
    private SmallproMapper smallproMapper;
    @Resource
    private OaWcfUrlSource oaWcfUrlSource;
    @Autowired
    private SysConfigService sysConfigService;
    @Autowired
    private SmallProAdapterService smallProAdapterService;
    @Autowired
    private SmsService smsService;

    @Resource
    private RefundMoneyService refundMoneyService;
    @Resource
    private SubLogsCloud subLogsCloud;
    @Resource
    private ExchangeGoodsService exchangeGoodsService;
    @Resource
    private ShouHouPjService shouHouPjService;
    @Resource
    private Executor pushMessageExecutor;
    @Resource
    private ProductinfoService productinfoService;
    @Resource
    private SmallproMapStruct smallproMapStruct;
    @Resource
    private SysConfigClient sysConfigClient;
    @Resource
    private SmallproWxMapper smallproWxMapper;
    @Resource
    private Password2ValidService password2ValidService;
    @Resource
    private ShouhouService shouhouService;
    @Resource
    private CategoryService categoryService;

    // endregion

    // region smallproDetails

    // region 获取小件接件详情信息 get /getSmallproInfo/{smallproId}

    /**
     * description: <获取小件接件详情信息>
     * translation: <Get detailed information on small parts>
     *
     * @param smallproId 小件接件Id
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:34 2019/12/13
     * @see SmallproDetailsExService#getSmallproInfo(Integer, OaUserBO)
     * @since 1.0.0
     **/
    @GetMapping("/getSmallproInfo/{smallproId}")
    @ApiOperation(value = "获取小件退货订单详情", response = SmallproInformationRes.class
            , notes = "传入Id为小件退货Id", httpMethod = "GET")
    public R<SmallproInformationRes> getSmallproInformation(@PathVariable Integer smallproId,
                                                            @RequestParam(required = false, name = "returnFactoryId") Integer returnFactoryId,
                                                            @RequestParam(required = false, name = "smallproBillId") Integer smallproBillId
    ) {
        if ((smallproId == null || smallproId <= 0)
                && (returnFactoryId == null || returnFactoryId <= 0)
                && ObjectUtil.defaultIfNull(smallproBillId, 0) <=0) {
            return R.error("获取订单详情失败！订单ID和售后返厂ID必须有一个！");
        }
        if (smallproId == null || smallproId <= 0 && ObjectUtil.defaultIfNull(returnFactoryId, 0) >0) {
            smallproId = smallproService.getSmallproIdByReturnFactoryId(returnFactoryId);
            if (smallproId == null || smallproId <= 0) {
                return R.error("售后返厂单关联的小件接件单号错误！");
            }
        }else if (ObjectUtil.defaultIfNull(smallproId, 0) <= 0 && ObjectUtil.defaultIfNull(smallproBillId, 0) >0) {
            smallproId = SpringUtil.getBean(SmallproBillService.class).lambdaQuery().eq(SmallproBill::getId, smallproBillId)
                    .select(SmallproBill::getId, SmallproBill::getSmallproID).list().stream().findFirst()
                    .map(SmallproBill::getSmallproID).orElse(null);
            if (ObjectUtil.defaultIfNull(smallproId, 0) <= 0) {
                return R.error("售后明细关联的小件接件单号错误！");
            }
        }
        if (smallproId == null || smallproId <= 0) {
            return R.error("小件接件单号错误！");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        R<SmallproInformationRes> result = R.success(smallproDetailsExService.getSmallproInfo(smallproId, oaUserBO));
        //计算分页
        Integer finalSmallproId = smallproId;
        CompletableFuture future1 = CompletableFuture.runAsync(() -> shouHouPjService.insertSearchHistory(oaUserBO.getUserId(), finalSmallproId + "|5", "oaSearch"), this.pushMessageExecutor);
        result.put("smallproWeChatId", SpringUtil.getBean(TuiHuanOpenIdService.class).getWeiXinTuple(oaUserBO).get(1));
        return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }

    @RepeatSubmitCheck
    @PostMapping("/autGeneratedCash/v1")
    public R<AutGeneratedCashRes> autGeneratedCash(@RequestBody @Validated AutGeneratedCashReq req ) {
        return R.success(smallproService.autGeneratedCash(req));
    }


    @GetMapping("/selectProductInfoListByImei/v1")
    public R<ProductInfoListByImeiReq> selectProductInfoListByImei(@RequestParam String imei) {
        ProductInfoListByImeiReq productInfoListByImeiReq = smallproService.selectProductInfoListByImei(imei);
        log.warn("查询商品信息传入参数：{},返回结果：{}",imei,JSONUtil.toJsonStr(productInfoListByImeiReq));
        return R.success(productInfoListByImeiReq);
    }

    /**
     * 通过basketId自动生成现货单
     * @param req
     * @return
     */
    @RepeatSubmitCheck
    @PostMapping("/autGeneratedCashByBasketId/v1")
    public R<AutGeneratedCashRes> autGeneratedCashByBasketId(@RequestBody @Validated AutGeneratedCashByBasketIdReq req ) {
        log.warn("前端提交参数：{}", JSONUtil.toJsonStr(req));
        //只有九机做这个判断
        if(XtenantEnum.isJiujiXtenant() && CollectionUtils.isEmpty(req.getFileResReqList())){
            //判断如果是图片没有上传
            RRExceptionHandler.logError("现货单生成没有提交串号图片", req, null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        }
        Integer basketId = req.getBasketId();
        Integer smallProId = req.getSmallProId();
        //判断basketId 和 smallProId 不能同时有值或者没有值
        AutGeneratedCashRes autGeneratedCashRes = new AutGeneratedCashRes();
        if (ObjectUtil.isNull(basketId) && ObjectUtil.isNotNull(smallProId) ) {
            AutGeneratedCashReq autGeneratedCashReq = new AutGeneratedCashReq();
            BeanUtils.copyProperties(req,autGeneratedCashReq);
            autGeneratedCashRes = smallproService.autGeneratedCash(autGeneratedCashReq);
        } else if (ObjectUtil.isNotNull(basketId) && ObjectUtil.isNull(smallProId)){
            autGeneratedCashRes = smallproService.autGeneratedCashByBasketId(req);
        } else {
            return R.error("basketId 和 smallProId 不能同时有值或者没有值");
        }
        return R.success(autGeneratedCashRes);
    }


    @GetMapping("/pushMsgTest/v1")
    public R<String> pushMsgTest() {
        Smallpro smallpro = new Smallpro();
        smallpro.setKind(SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode())
                .setUserId(76783)
                .setOldId(33308679)
                .setOldIdType(1)
                .setAreaId(742)
                .setQujianDate(LocalDateTime.now())
                .setId(123123)
                .setName("乐物 iPhone 15 丝印康宁 3D全屏钢化膜");
        FilmScrapReq filmScrapReq = new FilmScrapReq();
        filmScrapReq.setLossCount(5)
                        .setPpid(405920);
        smallproExchangePurchaseService.pushCashMsg(smallpro,filmScrapReq);
        return R.success("推送成功");
    }


    /**
     * 小件库存查询
     * @param req
     * @return
     */
    @PostMapping("/selectKcCount/v1")
    public R<SelectKcCountRes> selectKcCount(@RequestBody @Validated SelectKcCountReq req ) {
        return R.success(smallproService.selectKcCount(req));
    }


    /**
     * 查询损耗详情
     * @param req
     * @return
     */
    @PostMapping("/selectLossInfo/v1")
    public R<SelectKcCountRes> selectLossInfo(@RequestBody @Validated SelectLossInfoReq req ) {
        SelectKcCountRes selectKcCountRes = new SelectKcCountRes();
        long startTime = System.currentTimeMillis(); // 记录开始时间
        try {
            //串号进行去空格操作
            req.setImei(req.getImei().trim());
            log.warn("查询损耗详情传入参数：{}", JSONUtil.toJsonStr(req));
            selectKcCountRes = smallproService.selectLossInfo(req);
            log.warn("查询损耗详情返回结果：{}", JSONUtil.toJsonStr(selectKcCountRes));
        } catch (CustomizeException e){
            throw new CustomizeException(e.getMsg());
        } catch (Exception e) {
            RRExceptionHandler.logError("查询损耗详情异常", req, e, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
        } finally {
            long endTime = System.currentTimeMillis(); // 记录结束时间
            long duration = endTime - startTime; // 计算执行时间
            log.warn("查询损耗详情消耗时间 {} ms", duration); // 记录执行时间
            if (duration > 6000) {
                RRExceptionHandler.logError("查询损耗详情耗时"+duration, req, null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
            }
        }
        return R.success(selectKcCountRes);
    }


    /**
     * 查询损耗详情
     * @param req
     * @return
     */
    @PostMapping("/selectBindImei/v1")
    public R<BindImeiRes> selectBindImei(@RequestBody @Validated BindImeiReq req ) {
        return R.success(smallproService.selectBindImei(req));
    }




    /**
     * 取机并且完成现货单
     * @param req
     * @return
     */
    @RepeatSubmitCheck
    @PostMapping("/autGeneratedCashAndPickUp/v1")
    public R<Boolean> autGeneratedCashAndPickUp(@RequestBody @Validated AutGeneratedCashReq req ) {
        log.warn("取机并且完成现货单传入参数：{}",req);
        return smallproService.autGeneratedCashAndPickUp(req);
    }
    @RepeatSubmitCheck
    @PostMapping("/returnAnnualPackage/v1")
    public R<String> returnAnnualPackage(@RequestBody @Validated ReturnAnnualPackageReq req ) {
        smallproDetailsExService.returnAnnualPackage(req);
        return R.success("操作成功");
    }


    /**
     * 保护膜接件列表
     * @param req
     * @return
     */
    @PostMapping("/selectResistFilm/v1")
    public R<Page<SelectResistFilmRes>> selectResistFilm(@RequestBody SelectResistFilmReq req) {
        Page<SelectResistFilmRes> selectResistFilmResPage = smallproDetailsExService.selectResistFilm(req);
        return R.success(selectResistFilmResPage);
    }

    /**
     * 贴膜列表导出
     * @param req
     * @param response
     */
    @PostMapping("/exportResistFilm/v1")
    public void exportResistFilm(@RequestBody SelectResistFilmReq req, HttpServletResponse response) {
        smallproDetailsExService.exportResistFilm(req,response);
    }

    /**
     * 好耗损详情查询
     * @param req
     */
    @PostMapping("/selectLossDetails/v1")
    public R<LossDetailsRes> selectLossDetails(@RequestBody LossDetailsReq req) {
      return R.success(smallproDetailsExService.selectLossDetails(req));
    }

    /**
     * 获取枚举
     *
     * @return
     */
    @GetMapping("/getEnumList/v1")
    public R<Map<String, List<ShowPrintingEnumVO>>> getEnumList() {
        HashMap<String, List<ShowPrintingEnumVO>> map = new HashMap<>(4);
        List<ShowPrintingEnumVO> selectResistFilmEnum  = SelectResistFilmEnum.getAllPrintingEnum();
        List<ShowPrintingEnumVO> orderStatsEnum = SelectResistFilmOrderStatsEnum.getAllPrintingEnum();
        List<ShowPrintingEnumVO> serviceTypeEnum = SelectResistFilmServiceTypeEnum.getAllPrintingEnum();
        List<ShowPrintingEnumVO> orderTypeEnum = SelectResistFilmOrderTypeEnum.getAllPrintingEnum();
        List<Category> list = categoryService.lambdaQuery().in(Category::getId, categoryService.tieMoCids()).list();
        List<ShowPrintingEnumVO> tieMoEnum = list.stream().map(item -> {
            ShowPrintingEnumVO showPrintingEnumVO = new ShowPrintingEnumVO();
            showPrintingEnumVO.setValue(item.getId());
            showPrintingEnumVO.setLabel(item.getName());
            return showPrintingEnumVO;
        }).collect(Collectors.toList());
        map.put("selectResistFilmEnum", selectResistFilmEnum);
        map.put("tieMoEnum", tieMoEnum);
        map.put("orderStatsEnum", orderStatsEnum);
        map.put("serviceTypeEnum", serviceTypeEnum);
        map.put("orderTypeEnum", orderTypeEnum);
        return R.success(map);
    }

    @GetMapping("/getReturnFactoryInfoBOPage/{smallproId}")
    @ApiOperation(value = "获取小件接件单返厂商品信息", httpMethod = "GET")
    public R<Page<SmallproReturnFactoryInfoBO>> getReturnFactoryInfoBOPage(@PathVariable(name = "smallproId") String smallproIdParam,
                                                                           @RequestParam(required = false) Integer returnFactoryId,
                                                                           @RequestParam(required = false) Integer size,
                                                                           @RequestParam(required = false) Integer current) {
        Integer smallproId = Convert.toInt(smallproIdParam);
        if (CommenUtil.isNullOrZero(size)) {
            size = 10;
        }
        if (CommenUtil.isNullOrZero(current)) {
            current = 1;
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        return smallproDetailsExService.getReturnFactoryInfoBOPage(smallproId, returnFactoryId, oaUserBO, size, current);
    }

    // endregion

    // region 获取当前小件是否可以接件 get /getReceivableSmallpro

    /**
     * description: <获取当前小件是否可以接件>
     * translation: <Gets whether the current smallpro can be picked up>
     *
     * @param type key类别
     * @param keys key数组
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:39 2019/12/13
     * @since 1.0.0
     **/
    @GetMapping("/getReceivableSmallpro")
    @ApiOperation(value = "获取当前小件是否可以接件，type:[1:subId|2:ppid|3:barCode|4:keys 传为subId 然后再加上ppid]",
            response = SmallproReceivableRes.class, httpMethod = "GET")
    public R<SmallproReceivableRes> getReceivableSmallpro(@RequestParam(value = "type") Integer type,
                                                          @RequestParam(value = "keys") String keys,
                                                          @RequestParam(value = "imei", required = false) String imei,
                                                          @RequestParam(value = "basketId", required = false) String basketId,
                                                          @RequestParam(value = "subId", required = false) Integer subId,
                                                          @RequestParam(value = "ppid", required = false) Integer ppid,
                                                          @RequestParam(value = "selectBasketId", required = false) Integer selectBasketId) {
        List<String> keyArray = StrUtil.splitTrim(keys,",");
        ArrayList<String> realKeyList = new ArrayList<>(keyArray.size());
        AtomicBoolean errorFlag = new AtomicBoolean(false);
        keyArray.forEach(key -> {
            try {
                realKeyList.add(key);
            } catch (Exception e) {
                log.error("参数转换错误，参数值：{}", key);
                log.error("错误信息：{}", e);
                errorFlag.set(true);
                return;
            }
        });
        if (errorFlag.get()) {
            return R.error("ID错误！");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        ReceivableSmallProReq req = new ReceivableSmallProReq();
        req.setSubId(subId);
        req.setPpid(ppid);
        req.setSelectBasketId(selectBasketId);
        SmallproReceivableRes result = smallproDetailsExService.getReceivableSmallpro(type, realKeyList,
                oaUserBO.getAreaId(), imei,req);

        if (result == null) {
            return R.error("未知错误！");
        } else if (result.getCode() == 500) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            //切膜现货接件basketid修改
            List<SmallproOrderInfoBO> orderInfoList = result.getOrderInfoList();
            if (CollectionUtils.isNotEmpty(orderInfoList)) {
                for (SmallproOrderInfoBO smallproOrderInfoBO : orderInfoList) {
                    if (XtenantEnum.isJiujiXtenant()
                            && Objects.nonNull(basketId)
                            && NumberConstant.ZERO.equals(smallproOrderInfoBO.getBasketId())) {
                        smallproOrderInfoBO.setBasketId(Convert.toInt(basketId));
                    }
                }
            }
            return R.success(result).addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        } else {
            return R.error("未知错误！");
        }
    }

    @GetMapping("/getReceivableSmallpro/v2")
    @ApiOperation(value = "获取当前小件是否可以接件V2",
            response = SmallproReceivableRes.class, httpMethod = "GET")
    public R<SmallproReceivableRes> getReceivableSmallProV2(@RequestParam(value = "subId") Integer subId,
                                                            @RequestParam(value = "imei", required = false) String imei,
                                                        @RequestParam(value = "selectBasketId", required = false) Integer selectBasketId) {
        return smallproDetailsExService.getReceivableSmallProV2(subId, imei,selectBasketId);
    }


    /**
     * description: 获取分销订单批量生成小件单退货列表
     * translation: <Gets whether the current smallpro can be picked up>
     **/
    @GetMapping("/getSmallproBasketList")
    public R<SmallproBasketRes> getSmallproBasketList(@RequestParam(value = "subId") String subId) {
        SmallproBasketRes result = smallproService.getSmallproBasketList(subId);
        return R.success(result);
    }


    /**
     * description: <添加小件接件单>
     * translation: <Add Small Pickup Order>
     *
     * @param req 小件接件Req
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 15:52 2020/4/20
     * @since 1.0.0
     **/
    @PostMapping("/saveSmallproByBasketList")
    @ApiOperation(value = "添加小件接件单", notes = "smallpro为json数据", httpMethod = "POST")
    public R<SaveSmallproBasketRes> saveSmallproByBasketList(@RequestBody SmallproBasketReq req) {
        SaveSmallproBasketRes res = new SaveSmallproBasketRes();
        List<SaveSmallproBasketRes.SmallproBasketResult> smallproBasketList = new ArrayList<>();
        res.setSmallproBasketResultList(smallproBasketList);

        List<SmallproBasketReq.SmallproBasket> smallproBaskeList = req.getSmallproBasketList();
        Map<Integer, List<SmallproBasketReq.SmallproBasket>> subMap =
                smallproBaskeList.stream().collect(Collectors.groupingBy(SmallproBasketReq.SmallproBasket::getSubId));
        for (Map.Entry<Integer, List<SmallproBasketReq.SmallproBasket>> entry : subMap.entrySet()) {
            SaveSmallproBasketRes.SmallproBasketResult temp = new SaveSmallproBasketRes.SmallproBasketResult();
            smallproBasketList.add(temp);
            try {
                Integer subId = entry.getKey();
                temp.setResultFlag(false);
                temp.setSubId(subId);
                //获取当前小件是否可以接件
                R<SmallproReceivableRes> receivableSmallpro = this.getReceivableSmallpro(1, Convert.toStr(subId), null, null,subId,null,null);
                if (receivableSmallpro.getCode() != 0) {
                    temp.setResultString(receivableSmallpro.getUserMsg());
                    continue;
                }
                List<SmallproBasketReq.SmallproBasket> smallproBaskets = subMap.get(subId);
                smallproBaskets = smallproBaskets.stream().filter(x -> x.getCount() > 0).collect(Collectors.toList());
                BigDecimal returnPriceSum = BigDecimal.ZERO;
                for (SmallproBasketReq.SmallproBasket smallproBasket : smallproBaskets) {
                    returnPriceSum = returnPriceSum.add(BigDecimal.valueOf(smallproBasket.getCount()).multiply(smallproBasket.getReturnPrice()));
                }
                List<SmallproBill> smallproBillList = smallproMapStruct.toSmallproBillList(smallproBaskets);

                //构造接件退货参数
                SmallproReceivableRes smallproReceivableResRes = receivableSmallpro.getData();
                if (smallproReceivableResRes.getCode() != 0 || (Objects.nonNull(smallproReceivableResRes.getSmallproId())
                        && smallproReceivableResRes.getSmallproId() != 0)) {
                    temp.setResultString(smallproReceivableResRes.getMessage() + smallproReceivableResRes.getSmallproId());
                    temp.setSmallProId(smallproReceivableResRes.getSmallproId());
                    continue;
                }
                SmallproReq vo = new SmallproReq();
                vo.setSubId(subId);
                vo.setBuyDate(LocalDateTime.parse(smallproReceivableResRes.getBuyDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                vo.setIsBaoxiu(false);
                vo.setGroupId(SmallproGroupEnum.MOBILE_PHONE_PARTS.getGroupId());
                vo.setKind(SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode());
                Integer userId = smallproReceivableResRes.getUserId();
                String userName = smallproReceivableResRes.getUserName();
                String mobile = smallproReceivableResRes.getMobile();
                //查询订单信息
                Sub sub = subService.getByIdSqlServer(subId);
                if (StringUtils.isEmpty(userName)) {
                    userName = sub.getSubTo();
                }
                if (StringUtils.isEmpty(mobile)) {
                    mobile = sub.getSubMobile();
                }
                vo.setUserId(userId);
                vo.setUserName(userName);
                vo.setMobile(mobile);
                vo.setStats(SmallProStatsEnum.SMALL_PRO_STATS_PROCESSING.getCode());
                vo.setProblem("分销批量退货");
                vo.setSmallProKinds(Arrays.asList(SmallProKindEnum.SMALL_PRO_KIND_RETURN.getCode()));
                vo.setSmallproBillList(smallproBillList);
                List<Integer> ppriceid = smallproBillList.stream().map(smallproBill -> Convert.toInt(smallproBill.getPpriceid())).collect(Collectors.toList());
                Map<Integer, Productinfo> productMapByPpids = productinfoService.getProductMapByPpids(ppriceid);
                String name = StringUtils.join(productMapByPpids.values().stream().map(Productinfo::getProductName).collect(Collectors.toList()), StringPool.COMMA);
                vo.setName(name);
                //接件退货
                R r = this.saveSmallpro(vo);
                SmallproReq data = (SmallproReq) r.getData();
                Integer smallProId = Objects.nonNull(data) ? data.getId() : null;
                if (r.getCode() != 0 || Objects.isNull(smallProId)) {
                    temp.setResultString(r.getUserMsg());
                    temp.setSmallProId(smallProId);
                    continue;
                }

                temp.setSmallProId(smallProId);
                R<Boolean> checkCodeMessageResult = this.checkCodeMessage(smallProId, "授权", 2, null);
                if (checkCodeMessageResult.getCode() != 0) {
                    temp.setResultString(checkCodeMessageResult.getUserMsg());
                    continue;
                }
                List<JSONObject> refundWayDetails = new ArrayList<>();
                for (SmallproBasketReq.SmallproBasket smallproBill : smallproBaskets) {
                    JSONObject json = new JSONObject();
                    json.put("groupCode", 6);
                    json.put("refundBusinessType", 0);
                    json.put("returnWayName", "余额");
                    json.put("shouyingId", 0);
                    BigDecimal refundPrice = BigDecimal.valueOf(smallproBill.getCount())
                            .multiply(smallproBill.getReturnPrice());
                    json.put("refundPrice", refundPrice);
                    refundWayDetails.add(json);
                }

                GroupTuihuanFormVo tuihuanFormVo = new GroupTuihuanFormVo();
                tuihuanFormVo.setShouhouId(smallProId).setSubId(subId).setTuihuanKind(TuihuanKindEnum.TPJ.getCode())
                        .setComment("分销批量退货").setValidtWay(ValidMemberType.MEMBER_CODE.getCode())
                        .setRefundPrice(returnPriceSum)
                        .setRefundWayDetails(refundWayDetails);
                R<Integer> save = refundMoneyService.save(tuihuanFormVo, tuihuanForm ->
                        BaseTuiHuanKindService.getBean(tuihuanForm.getTuihuanKind())
                                .getSuInfoWithMaxRefundPrice(ObjectUtil.defaultIfNull(tuihuanForm.getShouhouId(), tuihuanForm.getSubId()), tuihuanForm.getTuihuanKind()));
                if (!save.isSuccess()) {
                    temp.setResultString(save.getUserMsg());
                    continue;
                }
                TuihuanKindEnum tuihuanKindEnum = TuihuanKindEnum.TPJ;
                R<RefundMoneyDetailVo> RefundMoneyDetailVoDetail = refundMoneyService.detail(new DetailParamBo().setOrderId(smallProId).setSupportSeconds(1)
                                .setTuihuanId(null).setTuihuanKindEnum(tuihuanKindEnum),
                        detail -> BaseTuiHuanKindService.getBean(tuihuanKindEnum).getSuInfoWithMaxRefundPrice(smallProId, tuihuanKindEnum.getCode()));
                RefundMoneyDetailVo refundMoneyDetailVoData = RefundMoneyDetailVoDetail.getData();
                OaUserBO currentStaffId = abstractCurrentRequestComponent.getCurrentStaffId();

                String validStr = password2ValidService.getPassword2ByUserId(currentStaffId.getUserId());
                TuiHuanCheckVo tuiHuanCheckVo = new TuiHuanCheckVo();
                tuiHuanCheckVo.setBasketIds(smallproBillList.stream().map(SmallproBill::getBasketId).collect(Collectors.toList()));
                tuiHuanCheckVo.setTuihuanId(refundMoneyDetailVoData.getTuihuanId());
                tuiHuanCheckVo.setProcessStatus(refundMoneyDetailVoData.getProcessStatus());
                tuiHuanCheckVo.setPassword2(validStr);
                List<JSONObject> refundWayDetailsList = new ArrayList<>();
                for (RefundWayDetailVo refundWayDetail : refundMoneyDetailVoData.getRefundWayDetails()) {
                    refundWayDetailsList.add(JSON.parseObject(JSON.toJSONString(refundWayDetail)));
                }
                tuiHuanCheckVo.setRefundWayDetails(refundWayDetailsList);
                tuiHuanCheckVo.setTuihuanKindEnum(tuihuanKindEnum);
                tuiHuanCheckVo.setCurrUser(currentStaffId);
                R<Integer> submitCheckResult = refundMoneyService.submitCheck(tuiHuanCheckVo);
                if (submitCheckResult.isSuccess()) {
                    Optional.ofNullable(refundMoneyService.tryAutoCheck3(tuiHuanCheckVo, refundMoneyService::submitCheck))
                            // 自动转售后不影响主流程
                            .map(rr -> LambdaBuild.create(rr).set(R::setCode, ResultCode.SUCCESS).build())
                            .orElse(submitCheckResult);
                    temp.setResultFlag(true);
                } else {
                    temp.setResultString(submitCheckResult.getUserMsg());
                }
            } catch (CustomizeException e) {
                temp.setResultString(e.getMessage());
            }
        }

        return R.success(res);
    }


    // endregion

    // region 更新小件接件页面详情内可更改字段 patch /updateSmallproInfo/{smallproId}

    /**
     * description: <更新小件接件页面详情内可更改字段>
     * translation: <Update fields that can be changed in the smallpro page details>
     *
     * @param smallproId            小件接件Id
     * @param smallproInfoUpdateReq 小件接件更新Req
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:37 2019/12/13
     * @see SmallproDetailsExService#updateSmallproInfo(Integer, OaUserBO, SmallproInfoUpdateReq)
     * @see SmallproDetailsExService#updateSmallproInfo(Integer smallproId, OaUserBO oaUserBO, SmallproInfoUpdateReq smallproInfoUpdateReq)
     * @since 1.0.0
     **/
    @PatchMapping("/updateSmallproInfo/{smallproId}")
    @ApiOperation(value = "更新小件接件页面详情内可更改字段",
            response = SmallproInfoUpdateRes.class, httpMethod = "PATCH")
    public R<SmallproInfoUpdateRes> updateSmallproInfo(@PathVariable Integer smallproId,
                                                       @RequestBody SmallproInfoUpdateReq smallproInfoUpdateReq) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        SmallproInfoUpdateRes res = smallproDetailsExService.updateSmallproInfo(smallproId, oaUserBO,
                smallproInfoUpdateReq);
        if (res == null || res.getSmallproId() == null || res.getSmallproId() == 0) {
            if (smallproInfoUpdateReq.getIsUpdateByPrice() != null) {
                return R.error("您无权进行更新操作！");
            }
            return R.error("更新失败,小件单已删除或单号错误！");
        }
        return R.success(res);
    }

    @PatchMapping("/saveAppendix/{smallproId}")
    @ApiOperation(value = "当完成时也可以保存附件操作", httpMethod = "PATCH")
    public R<SmallproInfoUpdateRes> saveAppendix(@PathVariable Integer smallproId,
                                                 @RequestBody List<MiniFileRes> filesList) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        smallproService.saveSmallproAttachments(filesList, oaUserBO, smallproId);
        return R.success("保存成功！");
    }


    // endregion

    // region 校验置换商品 get /check/changeProduct/{ppriceId}

    /**
     * description: <校验置换商品>
     * translation: <Verify replacement products>
     *
     * @param ppriceId    ppid
     * @param price       价格
     * @param serviceType 服务类别
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.smallpro.bo.SmallproProductInfoBO>
     * <AUTHOR>
     * @date 13:48 2020/4/16
     * @since 1.0.0
     **/
    @GetMapping("/check/changeProduct/{ppriceId}")
    @ApiOperation(value = "校验置换商品", httpMethod = "GET")
    public R<SmallproProductInfoBO> checkChangePpriceId(@PathVariable("ppriceId") Integer ppriceId,
                                                        @RequestParam("price") Double price,
                                                        @RequestParam("serviceType") Integer serviceType) {
        BigDecimal priceDecimal = BigDecimal.valueOf(price);
        SmallproProductInfoBO result = smallproDetailsExService.checkChangeProduct(ppriceId, priceDecimal, serviceType);
        if (result.getIsCanChange()) {
            return R.success(result);
        } else {
            return R.error(result.getMessage());
        }
    }

    // endregion

    // region 获取上一次置换商品信息 get /get/changeProduct/last

    /**
     * description: <获取上一次置换商品信息>
     * translation: <Get the last replacement product information>
     *
     * @param subId    原订单Id
     * @param basketId 条目Id
     * @param ppriceId PpId
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.smallpro.vo.res.SmallproLastChangePpidRes>
     * <AUTHOR>
     * @date 16:30 2020/4/17
     * @since 1.0.0
     **/
    @GetMapping("/get/changeProduct/last")
    @ApiOperation(value = "获取上一次置换商品信息", httpMethod = "GET")
    public R<SmallproLastChangePpidRes> getLastChangePpid(@RequestParam("subId") Integer subId,
                                                          @RequestParam("basketId") Integer basketId,
                                                          @RequestParam("ppriceId") Integer ppriceId,
                                                          @RequestParam("changePpriceId") Integer changePpriceId) {
        if (subId == null || subId <= 0 || basketId == null || basketId <= 0) {
            return R.error("参数错误！请核对所选商品的订单Id和条目Id！");
        }
        SmallproLastChangePpidRes result = smallproDetailsExService.getLastChangePpid(subId, basketId, ppriceId,
                changePpriceId);
        if (result != null && result.getCode() == 500) {
            return R.error(result.getMessage());
        } else if (result == null) {
            return R.error("未知错误！");
        }
        return R.success(result);
    }

    // endregion


    @GetMapping("/check/tempered/film/imei/{basketId}")
    public R<SmallproNormalCodeMessageRes> checkTemperedFilm(@PathVariable("basketId") Integer basketId) {
        if (basketId == null || basketId <= 0) {
            return R.error("参数错误！");
        }
        SmallproNormalCodeMessageRes result = smallproService.checkTemperedFilm(basketId);
        if (result.getCode() == null) {
            return R.error("未知错误");
        } else if (result.getCode() == 500) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            return R.success(result.getMessage());
        } else {
            return R.error("未知错误");
        }
    }

    @DeleteMapping("/file/{smallproId}")
    @ApiModelProperty()
    public R<SmallproNormalCodeMessageRes> deleteFile(@PathVariable Integer smallproId,
                                                      @RequestParam("fileId") Integer fileId) {
        if (smallproId == null || smallproId <= 0) {
            return R.error("小件单号错误！");
        }
        if (fileId == null || fileId <= 0) {
            return R.error("附件Id错误！");
        }
        SmallproNormalCodeMessageRes result = smallproDetailsExService.deleteSmallproFile(smallproId, fileId);
        if (result == null) {
            return R.error("未知错误！");
        } else if (result.getCode() == 500) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            return R.success(result.getMessage());
        } else {
            return R.error("未知错误！");
        }

    }


    /**
     * 判断是否换其他型号
     * @param smallProId
     * @return
     */
    @GetMapping("/isHqtxh")
    public R<Boolean> isHqtxh(@RequestParam("smallProId") Integer smallProId) {
        return R.success(smallproService.isHqtxh(smallProId));
    }

    /**
     * 小件单生成销售单
     * @param smallProId
     * @return
     */
    @GetMapping("/createSubBySmallProId")
    public R<Integer> createSubBySmallProId(@RequestParam("smallProId") Integer smallProId) {
        Integer subBySmallProId = SpringUtil.getBean(SubService.class).createSubBySmallProId(smallProId);
        log.warn("通过接口小件单生成销售单传入参数：{}，返回结果：{}",smallProId,subBySmallProId);
        return R.success(subBySmallProId);
    }

    /**
     * 小件单提交二次确认
     * @return
     */

    @PostMapping("/secondaryConfirmation")
    public R<String> secondaryConfirmation(@RequestBody SmallproReq req) {
       return R.success(smallproService.secondaryConfirmation(req));
    }

    /**
     * 小件单提交二次确认
     * @return
     */

    @PostMapping("/selectHistoricalProcessing")
    public R<List<HistoricalProcessingRes>> selectHistoricalProcessing(@RequestBody HistoricalProcessingReq req) {
        return R.success(smallproService.selectHistoricalProcessing(req));
    }


    /**
     * 修复 OldUserId 字段
     * @return
     */

    @GetMapping("/fixOldUserId")
    public R<String> fixOldUserId() {
        smallproService.fixOldUserId();
        return R.success("操作成功");
    }

    /**
     * description: <添加小件接件单>
     * translation: <Add Small Pickup Order>
     *
     * @param smallpro 小件接件Req
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 15:52 2020/4/20
     * @since 1.0.0
     **/
    @PostMapping("/saveSmallpro")
    @ApiOperation(value = "添加小件接件单", notes = "smallpro为json数据", httpMethod = "POST")
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{smallpro.subId != null && smallpro.subId>0 ? smallpro.subId : smallpro.toMd5Hex()}")
    public R saveSmallpro(@RequestBody SmallproReq smallpro) {
        log.warn("添加小件接件单：" + JSONObject.toJSONString(smallpro));
        //isbaoxiu 数据修正 前端用的是 warrantyStatus
       if (smallpro.getWarrantyStatus() != null) {
            smallpro.setIsBaoxiu(Convert.toBool(smallpro.getWarrantyStatus()));
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        smallpro.setInUser(oaUserBO.getUserName());
        smallpro.setInDate(LocalDateTime.now());
        smallpro.setArea(oaUserBO.getArea());
        smallpro.setAreaId(oaUserBO.getAreaId());
        // 用户信息跳店了,进行通知,通过是否有该门店的权限
        if (!SpringUtil.getBean(UserSmallproService.class).existsAreaRanks(oaUserBO.getUserId(), smallpro.getAreaId())) {
            AtomicReference<String> erMsg = new AtomicReference<>("");
            RRExceptionHandler.logError("添加小件接件单", Dict.create().set("smallpro", smallpro).set("oaUserBO", oaUserBO)
                            .set("oaUserFrom", SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.OA_USER_FROM_SOURCE)),
                    new CustomizeException(StrUtil.format("没有门店{}的权限", oaUserBO.getArea())),
                    msg -> {
                        erMsg.set(msg);
                    });
            return R.error(erMsg.get());
        }
        //如果过connectionFid 不为空但是fig为空的情况那就把connectionFid设置为connectionFid
        String connectionFid = smallpro.getConnectionFid();
        String fid = smallpro.getFid();
        if("undefined".equals(connectionFid) || "undefined".equals(fid)){
            return R.error("fid和connectionFid不能为undefined");
        }
        if(StringUtils.isNotEmpty(connectionFid) && StringUtils.isEmpty(fid)){
            smallpro.setFid(connectionFid);
        }
        // 参数校验
        if (smallpro.getName() == null || smallpro.getName().equals("")) {
            return R.error("商品名称信息错误！");
        }
        if (smallpro.getUserId() == null || smallpro.getUserId() <= 0) {
            return R.error("联系人用户ID错误！");
        }
        if (smallpro.getSubId() == null || smallpro.getSubId() < 0) {
            return R.error("原订单ID错误！");
        }
        if(Optional.ofNullable(smallpro.getSubId()).orElse(NumberConstant.ZERO)>0 && !SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode().equals(smallpro.getKind())){
            Integer processingId = smallproMapper.selectProcessingIdBySubId(smallpro.getSubId());
            if(ObjectUtil.isNotNull(processingId)){
                String msg = String.format("订单：%s存在正在进行中的小件单：%s", smallpro.getSubId(), processingId);
               // RRExceptionHandler.logError(msg, smallpro, null, SpringUtil.getBean(SmsService.class)::sendOaMsgTo9JiMan);
                return R.error(msg);
            }
        }
        if (smallpro.getBuyDate() == null) {
            return R.error("订单购买时间错误！");
        }
        if (smallpro.getIsBaoxiu() == null) {
            return R.error("接件是否保修未填写！");
        }
        if (smallpro.getGroupId() == null || smallpro.getGroupId() < 0) {
            return R.error("接件分组错误！");
        }
        if (smallpro.getKind() == null || smallpro.getKind() < 0) {
            return R.error("接件类型错误！");
        }
        if (smallpro.getStats() == null || smallpro.getStats() < 0) {
            return R.error("接件状态错误！");
        }
        if (smallpro.getUserName() == null || smallpro.getUserName().equals("")) {
            return R.error("接件用户姓名错误！");
        }
        if (smallpro.getMobile() == null || smallpro.getMobile().equals("")) {
            return R.error("接件用户联系方式错误！");
        }
        if (StringUtils.isBlank(smallpro.getProblem())) {
            if (smallpro.getSmallProKinds().contains(SmallproSituationKindEnum.SMALL_PRO_SITUATION_KIND_BEFORE_OTHER.getCode())) {
                return R.error("接件故障描述错误！");
            } else {
                smallpro.setProblem("");
            }
        }
        if (smallpro.getIsSpecialTreatment() == null) {
            smallpro.setIsSpecialTreatment(Boolean.FALSE);
        }
        List<Integer> kindList = Arrays.asList( SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode());
        //如果有单号的时候不判断，没有单号的判断不可以接膜商品
        if(XtenantEnum.isJiujiXtenant()){
            //串号文件相关信息封装
            List<MiniFileResReq> fileResReqList = smallpro.getFileResReqImeiList();
            if(CollectionUtils.isNotEmpty(fileResReqList)){
                //小图存入fid
                Optional.of(fileResReqList.stream().filter(item -> ImageTypeEnum.SMALL.getCode().equals(item.getImageType())).findFirst().get()).ifPresent(file->{
                    smallpro.setFid(file.getFid());
                    smallpro.setConnectionFid(file.getFid());
                });
                //大图存入附件
                Optional.of(fileResReqList.stream().filter(item -> ImageTypeEnum.BIG.getCode().equals(item.getImageType())).findFirst().get()).ifPresent(file->{
                    MiniFileRes miniFileRes = new MiniFileRes();
                    BeanUtils.copyProperties(file,miniFileRes);
                    smallpro.setBigImeiFile(miniFileRes);
                });
            }
            Integer changePpriceid = smallpro.getChangePpriceid();
            if(ObjectUtil.isNotNull(changePpriceid) && categoryService.determineFilmByPpid(changePpriceid)){
                if(ObjectUtil.isNull(smallpro.getOldId()) && ObjectUtil.isNull(smallpro.getSubId())){
                    return R.error("无关联订单，不能提交“保护膜”品类商品。");
                }
            }
        }
        List<SmallproBill> smallproBillList = smallpro.getSmallproBillList();
        // DIY只支持小件接件退货处理，不支持维修和换货
        if (CollectionUtils.isNotEmpty(smallproBillList)) {
            List<Integer> basketIds =
                    smallproBillList.stream().map(SmallproBill::getBasketId).filter(e -> e != null && !e.equals(0)).collect(Collectors.toList());
            List<DIYTimoCardBO> diyTimoCardBOS = null;
            if (CollectionUtils.isNotEmpty(basketIds)) {
                diyTimoCardBOS = smallproMapper.getDIYYearCardByBasketIds(basketIds);
            }
            if (CollectionUtils.isNotEmpty(diyTimoCardBOS)) {
                if (smallpro.getKind() != 3) {
                    return R.error("DIY年包服务只允许退款");
                }
                boolean b =
                        diyTimoCardBOS.stream().anyMatch(e -> (e.getIsdel() == null || e.getIsdel().equals(0)) && e.getBasketId() != null);
                if (b) {
                    return R.error("DIY年包服务已经兑换过，不允许接件！");
                }
            }
            //九机不需要这个校验
            if(XtenantEnum.isSaasXtenant()){
                //当商品未返销的时候不能进行接件
                List<Integer> operatorBasketByStatus = smallproService.getOperatorBasketByStatus(basketIds);
                if (CollUtil.isNotEmpty(operatorBasketByStatus)) {
                    return R.error("存在未返销数据，请检查！");
                }
            }

            List<Long> ppids =
                    smallproBillList.stream().map(SmallproBill::getPpriceid).collect(Collectors.toList());
            Integer ppid = null;
            if (CollectionUtils.isNotEmpty(ppids)) {
                ppid = smallproMapper.getproductIdByPPid(ppids);
            }
            // 是DIY保护壳
            if (null != ppid) {
                if (smallpro.getKind() != 3) {
                    return R.error("DIY保护壳只允许退款");
                }
                LocalDateTime tradeDate = smallproMapper.getTradeDateTime(smallpro.getSubId());
                if (LocalDateTime.now().isAfter(CommonUtils.getEndOfDay(tradeDate).plusDays(31))) {
                    return R.error("DIY保护壳只支持7天内退货处理！");
                }
            }
        }
        // 如果换货已选中的年包服务已失效或次数已使用完，则保存接件提示“年包次数已使用完或已失效，请核对
        if (null != smallpro.getServiceType() && smallpro.getServiceType() == 4
                && smallpro.getKind().equals(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode())
                && CollectionUtils.isNotEmpty(smallproBillList) && smallproBillList.size() == 1) {
            IYearPackageTransferService yearPackageTransferService = SpringUtil.getBean(IYearPackageTransferService.class);
            Optional<YearPackageTransferDetailDto> transferDetailOpt = Optional.ofNullable(smallpro.getImei())
                    .filter(StrUtil::isNotBlank).map(yearPackageTransferService::getTransferDetail);

            FilmCardInfomationBO filmCardInfomationBO = transferDetailOpt.isPresent() ? null :
                    smallproFilmCardService.getFilmCardInfoByBasketIdByWrite(smallproBillList.get(0).getBasketId());
            if(transferDetailOpt.isPresent()){
                YearPackageTransferDetailDto transferDetailDto = transferDetailOpt.get();
                R<?> checkTransferR = yearPackageTransferService.validateYearPackageTransfer(transferDetailDto, false);
                if(!checkTransferR.isSuccess()){
                    return R.error(checkTransferR.getUserMsg());
                }
                // 如果有在进行中的小件单也不允许接件
                Optional<Smallpro> existGoingSmallOpt = smallproService.lambdaQuery().eq(Smallpro::getImei, smallpro.getImei())
                        .and(CommenUtil.isNullOrEq(Smallpro::getIsDel, false))
                        .eq(Smallpro::getStats, SmallProStatsEnum.SMALL_PRO_STATS_PROCESSING.getCode()).list().stream().findFirst();
                if(existGoingSmallOpt.isPresent()){
                    return R.error(StrUtil.format("已存在进行中的小件单[{}]，不允许接件", existGoingSmallOpt.get().getId()));
                }
            }else if (null != filmCardInfomationBO && null != filmCardInfomationBO.getIsNotExpired()
                    && null != filmCardInfomationBO.getIsUseCount() && (!filmCardInfomationBO.getIsNotExpired() || !filmCardInfomationBO.getIsUseCount())) {
                return R.error("年包次数已使用完或已失效，请核对！");
            }
        }
        //1.小件单接件方式为现货，商品分类为壳、膜时（膜已做限制，需增加壳的分类id为43,609,614），需填写销售单号（必填项）
        //2.销售单号校验机制：单号内有该ppid的商品（不含已删除），订单号下单时间与小件单接件时间小于7天；校验不通过则在保存接件信息提示：销售单号无效，请重新输入。
        // 3.接件地区为d1时，售前接件销售单号可不填
        AreaBelongsDcHqD1AreaId areaBelongsDcHqD1AreaId = areainfoService.getAreaBelongsDcHqD1AreaId(smallpro.getAreaId());
        Integer d1AreaId = areaBelongsDcHqD1AreaId.getD1AreaId();
        if (smallpro.getKind() != null && smallpro.getKind().equals(SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode())
                && !Objects.equals(d1AreaId, smallpro.getAreaId())) {
            Integer oldId = smallpro.getOldId();
            if (SpotTypeEnum.TYPE2.getCode().equals(smallpro.getSpotType())) {
                List<SmallproBill> billList = smallpro.getSmallproBillList();
                List<Long> ppIds =
                        Optional.ofNullable(billList).map(e -> e.stream().map(SmallproBill::getPpriceid).collect(Collectors.toList())).orElse(null);
                //校验订单是否存在
                if(ObjectUtil.defaultIfNull(oldId, 0) >0){
                    boolean subExist = CommenUtil.autoQueryHist(() -> subService.lambdaQuery().eq(Sub::getSubId, oldId).count(), MTableInfoEnum.SUB, oldId) >0;
                    if(!subExist){
                        return R.error("销售单号不存在，请重新输入");
                    }
                }
                if (XtenantEnum.isSaasXtenant() && CollectionUtils.isNotEmpty(ppIds)) {
                    List<SmallproInfoProductBO> smallproShopInfo = smallproMapper.getSmallproShopInfo(ppIds);
                    if (CollectionUtils.isNotEmpty(smallproShopInfo)) {
                        List<Integer> cids =
                                smallproShopInfo.stream().map(SmallproInfoProductBO::getCid).collect(Collectors.toList());
                        List<Integer> list = new ArrayList<>();
                        list.addAll(smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_TEMPERED_FILM_CID));
//                        list.addAll(smallProAdapterService.getSmallRelativeConfigList(SysConfigConstant.JIUJI_PROTECTIVE_SHELL_CID));
                        boolean b = cids.stream().anyMatch(e -> list.contains(e));
                        if (b) {
                            if (CommenUtil.isNullOrZero(smallpro.getOldId())) {
                                return R.error("商品分类为壳、膜时需填写销售单号/小件单号。");
                            }
                            Integer subId = smallproService.getSpotSubData(smallpro.getOldId(), ppIds);
                            if (null == subId) {
                                String msg = Objects.equals(SpotTypeEnum.TYPE2.getCode(), smallpro.getSpotType()) ? "销售单号无效，请重新输入。" : "小件单号无效，请重新输入。";
                                return R.error(msg);
                            }
                        }
                    }
                }
            } else if (SpotTypeEnum.TYPE1.getCode().equals(smallpro.getSpotType())) {
                Long ppId =
                        Optional.ofNullable(smallpro).map(e -> e.getSmallproBillList()).map(e -> e.get(0))
                                .map(e -> e.getPpriceid()).orElse(null);
                if (null == ppId) {
                    return R.error("接件商品ppid为空");
                }
                if (null == oldId) {
                    return R.error("小件单号为空");
                }
                List<Integer> smallProIds = smallproMapper.checkSmallProId(oldId, ppId);
                if (CollectionUtils.isEmpty(smallProIds)) {
                    return R.error("小件单号无效，请重新输入。");
                }

            }
        }
        R r = smallproService.saveSmallpro(smallpro, oaUserBO.getAreaId(), oaUserBO);
        if(r.isSuccess() && smallpro.getBeihuoAutoReq() != null){
            R autoBeiHuoR = SpringUtil.getBean(CsharpCloud.class).smallProBeihuoAuto(smallpro.getBeihuoAutoReq());
            log.warn("自动备货参数: {}, 结果: {}, 业务日志: {}", smallpro.getBeihuoAutoReq(), JSON.toJSONString(autoBeiHuoR),
                    SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        }
        log.warn("小件单号: {} ,接件人: {} ,接件门店: {} , 接件门店id: {}", smallpro.getId(), oaUserBO.getUserName(), oaUserBO.getArea(), oaUserBO.getAreaId());
        boolean b = smallproBillList.stream().anyMatch(e -> e.getPpriceid().equals(PpidConstants.TEST_PRODUCT.longValue())
                || e.getPpriceid().equals(PpidConstants.LEWU_QC.longValue()) || (Boolean.TRUE.equals(smallpro.getIsAuthority())
                && XtenantEnum.isJiujiXtenant()));
        if (b && r.getCode() == Result.SUCCESS) {
            String codeMsg;
            String imei = smallpro.getImei();
            if (StringUtils.isNotEmpty(imei) && XtenantEnum.isJiujiXtenant()) {
                codeMsg = MessageFormat.format("扫码验证[app 扫码验证],串号：{0} 【{1}】 {2}", imei, oaUserBO.getUserName(), LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            } else {
                codeMsg = MessageFormat.format("扫码验证[app 扫码验证] 【{0}】 {1}", oaUserBO.getUserName(), LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            }
            if(StringUtils.isNotEmpty(imei)){
                Optional.ofNullable(smallpro.getId()).ifPresent(item -> smallproService.lambdaUpdate().eq(Smallpro::getId, item).set(Smallpro::getImei, imei).update());
            }
            smallproExchangePurchaseService.checkCodeMessage(smallpro.getId(),
                    codeMsg, 1, 3);
        }
        if (XtenantEnum.isJiujiXtenant() && smallpro.getIsCutScreenSpotFlag() && !NumberConstant.ZERO.equals(smallproBillList.get(0).getBasketId())) {
            SubLogsNewReq subLogsNewReq = new SubLogsNewReq();
            String comment = "生成现货小件单【<a href='/staticpc/#/small-refund/" + smallpro.getId() + "' title='点击查看现货小件单详细' >" +
                    smallpro.getId() + "</a>】，数量：" + smallproBillList.get(0).getCount();
            subLogsNewReq.setComment(comment);
            subLogsNewReq.setSubId(Convert.toInt(smallproBillList.get(0).getBasketId()));
            subLogsNewReq.setInUser(oaUserBO.getUserName());
            subLogsNewReq.setShowType(Boolean.TRUE);
            subLogsNewReq.setType(2);
            subLogsNewReq.setDTime(LocalDateTime.now());
            subLogsCloud.addSubLog(subLogsNewReq);

            smallproLogService.addLogs(smallpro.getId(),
                    "来源销售订单 【<a href='/addOrder/editOrder?SubID=" + smallpro.getSubId() + "' title='点击查看订单详细' >"
                            + smallpro.getSubId() + "</a>】，原因：售前切膜出库商品库存处理",
                    oaUserBO.getUserName(), 0);
        }
        // 九机 且 非现货单 且 换货 且 接件商品为贴膜
        if(FilmLossUtil.isFilmLossEnabled(smallpro) && r.isSuccess()
                && CollectionUtils.isNotEmpty(smallproBillList)
                 && categoryService.determineFilmByPpid(Convert.toInt(smallproBillList.get(0).getPpriceid()))
                && Arrays.asList(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode(), SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode()).contains(smallpro.getKind())){
             //如果过是换其他型号那就不进行自动办理
             SmallproReq data = (SmallproReq) r.getData();
             if(smallproService.isHqtxh(data.getId())){
                 return r;
             }
             Integer oldId = smallpro.getOldId();
             Integer oldIdType = smallpro.getOldIdType();
             Integer smallProId = smallpro.getId();
             if(ObjectUtil.isNotNull(oldId) && ObjectUtil.isNotNull(oldIdType)){
                 if(OldIdTypeEnum.SALE_TYPE.getCode().equals(oldIdType)){
                     String orderUrl = String.format("<a href= /staticpc/#/small-refund/%s>%s</a>",smallProId,smallProId);
                     String msg = String.format("生成小件现货单：%s", orderUrl);
                     //现货单日志生成记录
                     SubLogsNewReq subLogsNewReqMsg = new SubLogsNewReq();
                     subLogsNewReqMsg.setComment(msg);
                     subLogsNewReqMsg.setSubId(oldId);
                     subLogsNewReqMsg.setInUser(oaUserBO.getUserName());
                     subLogsNewReqMsg.setShowType(Boolean.FALSE);
                     subLogsNewReqMsg.setType(1);
                     subLogsCloud.addSubLog(subLogsNewReqMsg);
                 } else if (OldIdTypeEnum.SMALL_PRO_TYPE.getCode().equals(oldIdType)){
                     //在原来的小件单上面添加生成现货单日志
                     String orderUrl = String.format("<a href= /staticpc/#/small-refund/%s>%s</a>",smallProId,smallProId);
                     String msg = String.format("生成小件现货单：%s", orderUrl);
                     smallproLogService.addLogs(oldId, msg, oaUserBO.getUserName(), 0);
                     //在生成的现货单上记录日志
                     String url = "/staticpc/#/small-refund/"+oldId;
                     String message = String.format("小件售前接件，单号"+"<a href="+url+">"+oldId +"</a>");
                     smallproLogService.addLogs(smallProId, message, oaUserBO.getUserName(), 0);
                 }
             }
             //换货自动完成
             PickUpExtendReq pickUpExtendReq = new PickUpExtendReq();
             if(SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode().equals(smallpro.getKind())){
                 SmallproNormalCodeMessageRes smallproCheckRes = smallproExchangePurchaseService.checkExchange(smallProId, oaUserBO.getUserName());
                 if (!smallproCheckRes.getCode().equals(0)) {
                     log.warn("小件接件自动换货审核失败：{}，小件单信息：{}",smallproCheckRes.getMessage(),JSONUtil.toJsonStr(smallpro));
                 } else {
                     //取机操作
                     SmallproNormalCodeMessageRes smallproNormalCodeMessageRes =
                             smallproExchangePurchaseService.pickup(smallProId, SmallProKindEnum.SMALL_PRO_KIND_EXCHANGE.getCode(), oaUserBO,pickUpExtendReq);
                     if (!smallproNormalCodeMessageRes.getCode().equals(0)) {
                         log.warn("小件接件自动取机失败：{}，小件单信息：{}",smallproNormalCodeMessageRes.getMessage(),JSONUtil.toJsonStr(smallpro));
                     }
                 }
             }
             //现货自动完成
             if(SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode().equals(smallpro.getKind())){
                 SmallproExchangePurchaseService exchangePurchaseService = SpringUtil.getBean(SmallproExchangePurchaseService.class);
                 Integer lossPpid = smallpro.getLossPpid();
                 Integer lossCount = smallpro.getLossCount();
                 if(OldIdTypeEnum.SALE_TYPE.getCode().equals(oldIdType)){
                     pickUpExtendReq.setLossCount(lossCount);
                     pickUpExtendReq.setOperationKind("销售单生成现货单自动完成");
                     pickUpExtendReq.setPpid(lossPpid);
                     SmallproNormalCodeMessageRes smallproNormalCodeMessageRes = exchangePurchaseService.pickup(smallProId,SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode() , oaUserBO,pickUpExtendReq);
                     if (smallproNormalCodeMessageRes.getCode().equals(500)) {
                         log.warn("销售单生成的现货单取件失败" + smallProId + ":" + smallproNormalCodeMessageRes.getMessage());
                       //  throw new CustomizeException(smallproNormalCodeMessageRes.getMessage());
                     }
                 } else if (OldIdTypeEnum.SMALL_PRO_TYPE.getCode().equals(oldIdType)){
                     pickUpExtendReq.setLossCount(lossCount);
                     pickUpExtendReq.setOperationKind("小件单生成现货单自动完成");
                     pickUpExtendReq.setPpid(lossPpid);
                     SmallproNormalCodeMessageRes smallproNormalCodeMessageRes = exchangePurchaseService.pickup(smallProId,SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode() , oaUserBO,pickUpExtendReq);
                     if (smallproNormalCodeMessageRes.getCode().equals(500)) {
                         log.warn("小件单生成的现货单取件失败" + smallProId + ":" + smallproNormalCodeMessageRes.getMessage());
                       //  throw new CustomizeException(smallproNormalCodeMessageRes.getMessage());
                     }
                 } else {
                     pickUpExtendReq.setOperationKind("现货单自动完成");
                     SmallproNormalCodeMessageRes smallproNormalCodeMessageRes = exchangePurchaseService.pickup(smallProId,SmallProKindEnum.SMALL_PRO_KIND_SPOT.getCode() , oaUserBO,pickUpExtendReq);
                     if (smallproNormalCodeMessageRes.getCode().equals(500)) {
                         log.warn("现货单自动完成失败:" + smallproNormalCodeMessageRes.getMessage());
                     }
                 }
             }
         }
        CompletableFuture.runAsync(() -> {
            //跳转连接
            String host = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                    .filter(x -> ResultCode.SUCCESS == x.getCode())
                    .map(R::getData)
                    .orElseThrow(() -> new RRException("获取域名出错"));
            String inuser = smallpro.getInUser();
            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(inuser)) {
                List<TransferUserRes> transferUserRes = smallproWxMapper.selectUserNameList(Collections.singletonList(inuser));
                if (CollectionUtils.isNotEmpty(transferUserRes)) {
                    Integer areaId = transferUserRes.get(0).getAreaId();
                    Areainfo areainfo = areainfoService.getById(areaId);
                    String message = "你关注的员工【" + inuser + "（" + areainfo.getArea() + "）】添加了小件接件单（单号：" + smallpro.getId() + "），请做好跟进~";
                    String link = host + "/new/#/small-refund/" + smallpro.getId();
                    shouhouService.sendSubCollectionMessage(transferUserRes.get(0).getCh999UserId(), link, message);
                }
            }
            String weixiuren = smallpro.getWxUser();
            if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(weixiuren)) {
                List<TransferUserRes> transferUserRes = smallproWxMapper.selectUserNameList(Collections.singletonList(weixiuren));
                if (CollectionUtils.isNotEmpty(transferUserRes)) {
                    Integer areaId = transferUserRes.get(0).getAreaId();
                    Areainfo areainfo = areainfoService.getById(areaId);
                    String message = "你关注的员工【" + weixiuren + "（" + areainfo.getArea() + "）】添加了小件接件单（单号：" + smallpro.getId() + "），请做好跟进~";
                    String link = host + "/new/#/small-refund/" + smallpro.getId();
                    shouhouService.sendSubCollectionMessage(transferUserRes.get(0).getCh999UserId(), link, message);
                }
            }
        });
        return r;
    }



    /**
     * 校验是否返销的接口
     */
    @PostMapping("/checkResultOperatorBasket")
    @ApiOperation(value = "校验是否需要返销", httpMethod = "POST")
    public R<List<NotSoldBackBO>> checkResultOperatorBasket(@RequestBody List<SmallproBill> smallproBillList) {
        return smallproService.checkResultOperatorBasket(smallproBillList);
    }


    /**
     * 批量返销接口
     */
    @PostMapping("/batchReturnOperatorBasket")
    @ApiOperation(value = "批量返销接口", httpMethod = "POST")
    public R<Boolean> batchReturnOperatorBasket(@RequestBody List<NotSoldBackBO> notSoldBackList) {
        return smallproService.batchReturnOperatorBasket(notSoldBackList);
    }


    /**
     * 1.按照规则需要生成销售单展示：换其他型号 （返回 true）
     * 2.按照规则不需要生成销售单展示：换同型号/服务换货/大件附件（返回false）
     * 判断是否生成销售单
     */
    @PostMapping("/generateSalesOrder")
    public R<String> generateSalesOrder(@RequestBody HqtxhReq req){
        HqtxhRes hqtxhRes = smallproService.generateSalesOrder(req);
        return R.success("成功", ExchangeCopyWritingEnum.getExchangeCopyWriting(hqtxhRes));
    }

    // endregion

    // region 删除小件接件单 delete /delSmallpro/{smallproId}

    /**
     * description: <删除小件接件单>
     * translation: <Delete small order>
     *
     * @param smallproId 小件接件Id
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 15:40 2020/4/10
     * @see SmallproService#delSmallpro(Integer)
     * @since 1.0.0
     **/
    @DeleteMapping("/delSmallpro/{smallproId}")
    @ApiOperation(value = "删除接件信息", notes = "id为列表中的id", httpMethod = "DELETE")
    public R delSmallpro(@PathVariable Integer smallproId) {
        if (smallproId == null || smallproId <= 0) {
            return R.error("小件接件单号错误！");
        }
        SmallproNormalCodeMessageRes result = smallproService.delSmallpro(smallproId);
        if (result.getCode() == 500) {
            return R.error(result.getMessage());
        } else {
            return R.success(result.getMessage());
        }
    }

    // endregion

    // region 获取小件接件筛选下拉框选项 get /getSmallproQueryCondition

    /**
     * description: <获取小件接件筛选下拉框选项>
     * translation: <Get smallpro selection drop-down box options>
     *
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:35 2019/12/13
     * @sincwe 1.0.0
     * @see SmallproService#getSmallproQueryCondition()
     **/
    @GetMapping("/getSmallproQueryCondition")
    @ApiOperation(value = "获取小件接件筛选下拉框选项", httpMethod = "GET")
    public R<SmallproQueryConditionRes> getSmallproListQueryCondition() {
        R<SmallproQueryConditionRes> r = new R<>();
        r.setData((smallproService.getSmallproQueryCondition()));
        Map<String, Object> exData = new HashMap<>(NumberConstant.ONE);
        Boolean showSelectAllArea = false;
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
        if (Arrays.asList(backEndInfo.getD1AreaId(), backEndInfo.getDcAreaId(), backEndInfo.getH1AreaId()).contains(oaUserBO.getAreaId())
                || CollUtil.contains(backEndInfo.getHqAreaIds(), oaUserBO.getAreaId())) {
            showSelectAllArea = true;
        }
        exData.put("showSelectAllArea", showSelectAllArea);
        r.setExData(exData);
        return r;
    }

    // endregion

    // region 获取小件接件页面详情内的所有选择列表 get /getSmallproInfoSelection

    /**
     * description: <获取小件接件页面详情内的所有选择列表>
     * translation: <Get a list of all choices in the widget page details>
     *
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:36 2019/12/13
     * @see SmallproService#getSmallproInfoSelection()
     * @since 1.0.0
     **/
    @GetMapping("/getSmallproInfoSelection")
    @ApiOperation(value = "获取小件接件页面详情内的所有选择列表",
            response = SmallproInfoSelectionRes.class, httpMethod = "GET")
    public R<SmallproInfoSelectionRes> getSmallproInfoSelectionList() {
        return R.success(smallproService.getSmallproInfoSelection());
    }

    // endregion

    // region 获取小件接件单列表 post /getSmallproReturnGoodsList

    /**
     * description: <获取小件接件单列表>
     * translation: <Get a list of smallpro>
     *
     * @param query       筛选条件
     * @param pageSize    页面尺寸
     * @param currentPage 当前页
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 17:00 2020/4/10
     * @see SmallproService# getSmallproPage(SmallproReturnGoodsReq, Integer, Integer, Boolean)
     * @since 1.0.0
     **/
    @PostMapping("/getSmallproReturnGoodsList")
    @ApiOperation(value = "获取小件退货管理列表", notes = "query传相应的json数据", httpMethod = "POST")
    public R getSmallproReturnGoodsList(@RequestBody SmallproReturnGoodsReq query,
                                        @RequestParam(value = "pageSize") Integer pageSize,
                                        @RequestParam(value = "currentPage") Integer currentPage) {
        query = query.uniqueClearOtherVariable(query);
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        AreaBelongsDcHqD1AreaId backEndInfo = areainfoService.getAreaBelongsDcHqD1AreaId(oaUserBO.getAreaId());
        if (CollectionUtils.isNotEmpty(query.getAreaIdStrings())) {
            query.setAreaIds(areainfoService.getAreaIdByDepartInfo(query.getAreaIdStrings().stream().filter(e -> !e.contains("a")).collect(Collectors.toList())));
        }
        if (CollectionUtils.isEmpty(query.getToAreaIdStrings()) && CollUtil.isEmpty(query.getAreaIdStrings())) {
            if (!Arrays.asList(backEndInfo.getD1AreaId(), backEndInfo.getDcAreaId(), backEndInfo.getH1AreaId()).contains(oaUserBO.getAreaId())
                    && !CollUtil.contains(backEndInfo.getHqAreaIds(), oaUserBO.getAreaId())) {
                query.setToAreaIdStrings(Collections.singletonList(oaUserBO.getAreaId().toString()));
            }
        }

        if (CollectionUtils.isNotEmpty(query.getToAreaIdStrings())) {
            query.setToAreaIds(areainfoService.getAreaIdByDepartInfo(query.getToAreaIdStrings().stream().filter(e -> !e.contains("a")).collect(Collectors.toList())));
        }
        if (CollectionUtils.isNotEmpty(query.getCids())) {
            query.setCategoryCharSeq(org.springframework.util.StringUtils.arrayToCommaDelimitedString(query.getCids().toArray()));
        }
        //判断是进行地区限制
        if(Optional.ofNullable(query.getNotLimitArea()).orElse(Boolean.FALSE)){
            query.setAreaIds(Collections.emptyList());
            query.setAreaIdStrings(Collections.emptyList());
            query.setToAreaIdStrings(Collections.emptyList());
            query.setToAreaIds(Collections.emptyList());
        }
        return smallproService.getSmallproPage(query, pageSize, currentPage, true);
    }

    /**
     * 导出报表
     *
     * @return
     */
    @PostMapping(value = "/export")
    @ApiOperation(value = "导出excel报表", httpMethod = "POST")
    public void export(@RequestBody SmallproReturnGoodsReq query,
                       HttpServletResponse response) {
        log.warn("小件导出条件: {}", JSON.toJSONString(query));
        //String fileName = "小件退货管理列表" + System.currentTimeMillis() + ".xls"
        String fileName = ExcelUtils.getExportFileName("小件接件列表");
        query = query.uniqueClearOtherVariable(query);
        query.setAreaIds(areainfoService.getAreaIdByDepartInfo(query.getAreaIdStrings()));
        query.setToAreaIds(areainfoService.getAreaIdByDepartInfo(query.getToAreaIdStrings()));
        SXSSFWorkbook wb = smallproService.getWorkBook(query, fileName);
        // 响应到客户端
        try (OutputStream os = response.getOutputStream()) {
            CommenUtil.setResponseHeader(response, fileName);
            wb.write(os);
            os.flush();
        } catch (IOException e) {
            String msg = String.format("导出excel报表异常, 入参：[%s]", query);
            log.error(msg, e);
        }
    }

    @GetMapping(value = "/cancelSmallTuifeiDiscount")
    @ApiOperation(value = "取消小件退费折件", httpMethod = "GET")
    public R cancelSmallTuifeiDiscount(@RequestParam String smallproID) {
        return smallproService.cancelSmallTuifeiDiscount(smallproID);
    }


    // endregion

    // region 添加小件接件进程 post /addSmallproLog

    /**
     * description: <添加小件接件进程>
     * translation: <Adding smallpro logs>
     *
     * @param smallproAddLogReq 小件接件进程Req
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:44 2019/12/13
     * @see SmallproService#addSmallproLogWithPush(SmallproAddLogReq, String)
     * @since 1.0.0
     **/
    @PostMapping("/addSmallproLog")
    @ApiOperation(value = "添加小件接件进程", httpMethod = "POST", response = SmallproLogRes.class)
    public R<SmallproLogRes> addSmallproLog(@RequestBody SmallproAddLogReq smallproAddLogReq) {
        if (smallproAddLogReq.getSmallproId() == null ||
                smallproAddLogReq.getUserName() == null ||
                smallproAddLogReq.getComment() == null ||
                smallproAddLogReq.getProductName() == null ||
                smallproAddLogReq.getToEmail() == null ||
                smallproAddLogReq.getToSms() == null ||
                smallproAddLogReq.getToWeixin() == null ||
                smallproAddLogReq.getShowType() == null) {
            return R.error("添加小件进程参数不全！");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        return R.success(smallproService.addSmallproLogWithPush(smallproAddLogReq, oaUserBO.getUserName()));
    }

    @PostMapping("/addSmallProLogBatch")
    @ApiOperation(value = "批量推送通知", httpMethod = "POST", response = SmallproLogRes.class)
    public R<Boolean> addSmallproLogBatch(@RequestBody SmallproAddLogBatchReq req) {
        String comment = req.getComment();
        List<Integer> idList = req.getIdList();
        if (StringUtils.isEmpty(comment) ||
                CollectionUtils.isEmpty(idList)) {
            return R.error("添加小件进程参数不全！");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        return R.success(smallproService.addSmallproLogBatch(req, oaUserBO.getUserName()));
    }

    // endregion

    // region 小件接件设置渠道并送修 patch /repair/{smallproId}

    /**
     * description: <小件接件设置渠道并送修>
     * translation: <Set up channels for small pieces and send them for repair>
     *
     * @param smallproId      小件接件Id
     * @param maintainChannel 渠道名
     * @return com.jiuji.tc.common.vo.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 18:32 2020/4/10
     * @see SmallproService#repair(Integer, String)
     * @since 1.0.0
     **/
    @PatchMapping("/repair/{smallproId}")
    @ApiOperation(value = "小件接件设置渠道并送修", httpMethod = "PATCH")
    public R<Boolean> repair(@PathVariable Integer smallproId,
                             @RequestParam String maintainChannel) {
        if (smallproId == null || maintainChannel == null) {
            return R.error("参数错误！");
        }
        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes = smallproService.repair(smallproId, maintainChannel);
        if (smallproNormalCodeMessageRes.getCode() == 500) {
            return R.error(smallproNormalCodeMessageRes.getMessage());
        } else if (smallproNormalCodeMessageRes.getCode() == 0) {
            return R.success(true);
        } else {
            return R.error("未知错误！");
        }
    }

    // endregion

    // region 小件接件维修状态更新 patch /update/repair/status/{smallproId}

    /**
     * description: <小件接件维修状态更新>
     * translation: <Update smallpro repair status>
     *
     * @param smallproId    小件接件Id
     * @param maintainState 维修状态
     * @param userName      维修人名称
     * @return com.jiuji.tc.common.vo.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 15:54 2020/1/3
     * @see SmallproService#updateSmallproRepairStatus(Integer, String, Integer)
     * @since 1.0.0
     **/
    @PatchMapping("/update/repair/status/{smallproId}")
    @ApiOperation(value = "小件维修状态更新操作", httpMethod = "PATCH", notes = "maintainStats：维修状态[1已修好|2修不好];userName:维修人名称")
    public R<Boolean> updateRepairStatus(@PathVariable Integer smallproId, @RequestParam Integer maintainState,
                                         @RequestParam String userName) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes =
                smallproService.updateSmallproRepairStatus(smallproId, oaUserBO.getUserName(), maintainState);
        if (smallproNormalCodeMessageRes.getCode().equals(500)) {
            return R.error(smallproNormalCodeMessageRes.getMessage());
        }
        return R.success(true);
    }

    // endregion

    // region 转地区操作 post /toArea/{smallproId}

    /**
     * description: <转地区操作>
     * translation: <Transfer area operation>
     *
     * @param smallproId 小件接件单
     * @param areaId     地区Id
     * @param toAreaId   转地区Id
     * @param type       类型[1提交|2接收]
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes>
     * <AUTHOR>
     * @date 17:19 2020/4/13
     * @see SmallproService#toArea(Integer, Integer, Integer, Integer, OaUserBO, Boolean, Smallpro)
     * @since 1.0.0
     **/
    @PostMapping("/toArea/{smallproId}")
    @ApiOperation(value = "转地区操作;type[1提交|2接收]", httpMethod = "POST")
    public R<SmallproNormalCodeMessageRes> toArea(@PathVariable Integer smallproId,
                                                  @RequestParam Integer areaId,
                                                  @RequestParam Integer toAreaId,
                                                  @RequestParam Integer type) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (Objects.equals(null, toAreaId)) {
            return R.error("目标地区参数为空");
        }
//        if (areaId.equals(toAreaId)) {
//            return R.error("同地区不可操作");
//        }
        if (type == 1 && !oaUserBO.getAreaId().equals(areaId)) {
            return R.error("当前地区错误！");
        }
        if (type == 2 && !oaUserBO.getAreaId().equals(toAreaId)) {
            return R.error("转到地区错误");
        }
        if (CommenUtil.isNullOrZero(smallproId)) {
            return R.error("小件id不能为空");
        }

        Smallpro smallpro = smallproService.getByIdSqlServer(smallproId);
        if (smallpro == null) {
            return R.error(StrUtil.format("小件[]不存在", smallproId));
        }
        // 维修小件转地区到d1时限制提示，提示“维修小件请转地区到h3”
        AreaBelongsDcHqD1AreaId areaBelongsDcHqD1AreaId = areainfoService.getAreaBelongsDcHqD1AreaId(smallpro.getAreaId());

        if (oaUserBO.getXTenant() < 1000 && smallpro.getKind().equals(1) && toAreaId.equals(areaBelongsDcHqD1AreaId.getD1AreaId())) {
            return R.error("维修小件请转地区到h3");
        }
        SmallproNormalCodeMessageRes result = smallproService.toArea(smallproId, areaId, toAreaId, type,
                oaUserBO, true, smallpro);
        if (result.getCode() == 500) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            return R.success(result);
        } else {
            return R.error("未知错误！");
        }
    }

    // endregion

    // region 小件接件预约确认 patch /check/reserve/{smallproId}

    /**
     * description: <小件接件预约确认>
     * translation: <Confirmation of Small Piece Pickup Reservation>
     *
     * @param smallproId 小件接件ID
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes>
     * <AUTHOR>
     * @date 21:22 2020/4/13
     * @since 1.0.0
     **/
    @ApiOperation(value = "小件接件预约确认", httpMethod = "PATCH")
    @PatchMapping("/check/reserve/{smallproId}")
    public R<SmallproNormalCodeMessageRes> checkReserve(@PathVariable Integer smallproId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        SmallproNormalCodeMessageRes result = smallproService.checkReserve(smallproId, oaUserBO);
        if (result.getCode() == 500) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            return R.success(result.getMessage());
        }
        return R.error("未知错误");
    }

    // endregion

    // region 一键备货 get /stocking/{smallproId}

    /**
     * description: <一键备货>
     * translation: <One-click stocking>
     *
     * @param smallproId 小件接件Id
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes>
     * <AUTHOR>
     * @date 16:23 2020/4/26
     * @since 1.0.0
     **/
    @GetMapping("/stocking/{smallproId}")
    @ApiOperation(value = "一键备货", httpMethod = "GET")
    public R<SmallproNormalCodeMessageRes> oneClickStocking(@PathVariable Integer smallproId) {
        if (smallproId == null || smallproId < 0) {
            return R.error("小件接件单号错误！");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        SmallproNormalCodeMessageRes result = smallproService.oneClickStocking(smallproId, oaUserBO);
        if (result == null) {
            return R.error("未知错误！");
        } else if (result.getCode() == 500) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            return R.success(result.getMessage());
        } else {
            return R.error("未知错误！");
        }
    }

    // endregion

    // region 根据条形码获取对应的PpriceId get /PpriceId/{barCode}

    /**
     * description: <根据条形码获取对应的PpriceId>
     * translation: <Obtain the corresponding PpriceId according to the barcode>
     *
     * @param barCode 条形码
     * @return com.jiuji.tc.common.vo.R<java.lang.Integer>
     * <AUTHOR>
     * @date 14:17 2020/5/11
     * @since 1.0.0
     **/
    @GetMapping("/PpriceId/{barCode}")
    @ApiOperation(value = "根据条形码获取对应的PpriceId", httpMethod = "GET")
    public R<Integer> getPpriceIdByBarcodeWithSmallpro(@PathVariable String barCode) {
        Integer result = smallproService.getPpriceIdByBarcodeWithSmallpro(barCode);
        if (result == null) {
            return R.error("无对应的PpriceId！");
        } else {
            return R.success(result);
        }

    }

    // endregion

    // endregion

    // region smallproOldProduct

    // region 获取小件接件旧货列表 post /getSmallproOldPartList

    /**
     * description: <获取小件接件旧货列表>
     * translation: <Get the smallpro pick-up list>
     *
     * @param query       筛选条件
     * @param pageSize    页面尺寸
     * @param currentPage 当前页
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 17:11 2020/4/10
     * @see SmallproOldProductExService#getSmallproOldPartPage(SmallproOldPartReq, Integer, Integer)
     * @since 1.0.0
     **/
    @PostMapping("/getSmallproOldPartList")
    @ApiOperation(value = "获取小件退换货旧件处理", notes = "query传相应的json数据", httpMethod = "POST")
    public R getSmallproOldPartList(@RequestBody SmallproOldPartReq query,
                                    @RequestParam(value = "pageSize") Integer pageSize,
                                    @RequestParam(value = "currentPage") Integer currentPage) {
        query = query.uniqueClearOtherVariable(query);
        query.setAreaIds(areainfoService.getAreaIdByDepartInfo(query.getAreaIdStrings()));
        query.setToAreaIds(areainfoService.getAreaIdByDepartInfo(query.getToAreaIdStrings()));
        if (CollectionUtils.isNotEmpty(query.getCids())) {
            query.setCategoryCharSeq(org.springframework.util.StringUtils.arrayToCommaDelimitedString(query.getCids().toArray()));
        }
        return R.success(smallproOldProductExService.getSmallproOldPartPage(query, pageSize, currentPage));
    }

    /**
     * 导出报表
     *
     * @return
     */
    @PostMapping(value = "/exportOldPart")
    @ApiOperation(value = "导出小件退换货旧件excel报表", httpMethod = "POST")
    public void exportOldPart(@RequestBody SmallproOldPartReq query,
                              HttpServletResponse response) {
        //String fileName = "小件退换货旧件列表" + System.currentTimeMillis() + ".xls"
        String fileName = ExcelUtils.getExportFileName("退换小件旧件");
        query = query.uniqueClearOtherVariable(query);
        query.setAreaIds(areainfoService.getAreaIdByDepartInfo(query.getAreaIdStrings()));
        query.setToAreaIds(areainfoService.getAreaIdByDepartInfo(query.getToAreaIdStrings()));
        log.warn("退换小件旧件导出条件: {}", JSON.toJSONString(query));
        SXSSFWorkbook wb = smallproOldProductExService.getWorkBookOldPart(query, fileName);
        // 响应到客户端
        try (OutputStream os = response.getOutputStream()) {
            CommenUtil.setResponseHeader(response, fileName);
            wb.write(os);
            os.flush();
        } catch (IOException e) {
            String msg = String.format("导出excel报表异常, 入参：[%s]", query);
            log.error(msg, e);
        }
    }

    // endregion

    // region 小件接件转现操作 post /returnFactory/intoStock/{returnFactoryId}

    /**
     * description: <小件接件转现操作>
     * translation: <Smallpro transfer operation>
     *
     * @param returnFactoryId shouhouFanchangId
     * @param smallproId      小件接件单Id
     * @return com.jiuji.tc.common.vo.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 17:25 2020/4/10
     * @see SmallproOldProductExService# incomingSmallpro(Integer, String, Integer)
     * @since 1.0.0
     **/
    @PostMapping("/returnFactory/intoStock/{returnFactoryId}")
    @ApiOperation(value = "小件接件转现", httpMethod = "POST")
    @RepeatSubmitCheck(expression = "#{packageFullName}:intoStock:#{smallproId}")
    public R<Boolean> intoStock(@PathVariable Integer returnFactoryId,
                                @RequestParam Integer smallproId,
                                @RequestParam(required = false) Boolean transformXc,
                                //现货：1 瑕疵 2 默认为现货库存：3 报损
                                @RequestParam(required = false) Integer transform) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (returnFactoryId == null || returnFactoryId <= 0) {
            return R.error("售后返厂ID错误！");
        }
        if (!oaUserBO.getRank().contains("6f2")) {
            return R.error("您没有权限！权值: 6f2");
        }
        if (transformXc == null) {
            transformXc = false;
        }
        //当转现货或者瑕疵状态值为空时
        if (CommenUtil.isNullOrZero(transform)) {
            transform = DecideUtil.iif(transformXc, 1, 2);
        }
        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes =
                smallproOldProductExService.incomingSmallpro(returnFactoryId, oaUserBO.getUserName(), smallproId, transform, true);
        if (smallproNormalCodeMessageRes.getCode() == 500) {
            return R.error(smallproNormalCodeMessageRes.getMessage());
        } else if (smallproNormalCodeMessageRes.getCode() == 0) {
            return R.success(true);
        } else {
            return R.error("未知错误");
        }
    }


    /**
     * <AUTHOR>
     * @Date 2021/6/29 14:59
     * @Description 小件批量接件转现
     */
    @PostMapping("/returnFactory/batchIntoStock")
    @ApiOperation(value = "小件批量接件转现", httpMethod = "POST")
    @RepeatSubmitCheck(expression = "#{packageFullName}:intoStock:#{smallBatchIntoStockReq.smallproId}")
    public R<Boolean> batchIntoStock(@Validated @RequestBody SmallBatchIntoStockReq smallBatchIntoStockReq) {
        //获取当前登陆用户信息
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();

        if (!RankEnum.hasAuthority(oaUserBO.getRank(), RankEnum.SMALL_PRO_OLD_ZX)) {
            return R.error("您没有权限！权值:6f2");
        }
        if (CollectionUtils.isEmpty(smallBatchIntoStockReq.getPpidList())) {
            return R.error("无数据");
        }
        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes =
                smallproOldProductExService.batchIncomingSmallpro(smallBatchIntoStockReq.getPpidList(), oaUserBO, smallBatchIntoStockReq.getSmallproId());

        if (smallproNormalCodeMessageRes.getCode() == 500) {
            return R.error(smallproNormalCodeMessageRes.getMessage());
        } else if (smallproNormalCodeMessageRes.getCode() == 0) {
            return R.success(true);
        } else {
            return R.error("批量转现未知异常:" + smallproNormalCodeMessageRes.getMessage());
        }
    }

    /**
     * <AUTHOR>
     * @Date 2021/6/29 14:59
     * @Description 小件接件批量报废
     */
    @PostMapping("/returnFactory/batchScrap")
    @ApiOperation(value = "小件接件批量报废", httpMethod = "POST")
    @RepeatSubmitCheck(expression = "#{packageFullName}:intoStock:#{smallBatchIntoStockReq.smallproId}")
    public R<Boolean> batchScrap(@Validated @RequestBody SmallBatchIntoStockReq smallBatchIntoStockReq) {
        //获取当前登陆用户信息
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();

        if (!RankEnum.hasAuthority(oaUserBO.getRank(), RankEnum.SMALL_PRO_OLD_SCRAPPED)) {
            return R.error(StrUtil.format("您没有权限！权值:{}", RankEnum.SMALL_PRO_OLD_SCRAPPED.getCode()));
        }
        if (CollectionUtils.isEmpty(smallBatchIntoStockReq.getPpidList())) {
            return R.error("无数据");
        }
        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes =
                smallproOldProductExService.batchScrapSmallpro(smallBatchIntoStockReq.getPpidList(), oaUserBO, smallBatchIntoStockReq.getSmallproId());
        if (smallproNormalCodeMessageRes.getCode() == 500) {
            return R.error(smallproNormalCodeMessageRes.getMessage());
        } else if (smallproNormalCodeMessageRes.getCode() == 0) {
            return R.success(true);
        } else {
            return R.error("批量报废未知异常:" + smallproNormalCodeMessageRes.getMessage());
        }
    }
    // endregion

    // region 小件接件报废 post /returnFactory/scrap/{returnFactoryId}

    /**
     * description: <小件接件报废>
     * translation: <Smallpro received scrap>
     *
     * @param returnFactoryId shouhouFanchangId
     * @param smallproId      小件接件单Id
     * @return com.jiuji.tc.common.vo.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 17:56 2020/4/10
     * @see SmallproOldProductExService#scrapSmallpro(Integer, String, Integer, Integer)
     * @since 1.0.0
     **/
    @PostMapping("/returnFactory/scrap/{returnFactoryId}")
    @ApiOperation(value = "小件接件报废", httpMethod = "POST")
    @RepeatSubmitCheck(expression = "#{packageFullName}:intoStock:#{smallproId}")
    public R<Boolean> scrap(@PathVariable Integer returnFactoryId,
                            @RequestParam Integer smallproId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (!oaUserBO.getRank().contains("6f1")) {
            return R.error("您没有权限！权值: 6f1");
        }
        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes =
                smallproOldProductExService.scrapSmallpro(returnFactoryId, oaUserBO.getUserName(), smallproId,
                        oaUserBO.getAreaId());
        if (smallproNormalCodeMessageRes.getCode() == 500) {
            return R.error(smallproNormalCodeMessageRes.getMessage());
        } else if (smallproNormalCodeMessageRes.getCode() == 0) {
            return R.success(true);
        } else {
            return R.error("未知错误");
        }
    }
    // endregion

    // region 生成物流单 post /logisticsOrder/generate

    /**
     * description: <生成物流单>
     * translation: <Generate logistics order>
     *
     * @param req 生成物流单请求参数
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 13:56 2020/4/14
     * @see SmallproOldProductExService#addSmallproReturnToFactoryLogistics(List, OaUserBO)
     * @since 1.0.0
     **/
    @PostMapping("/logisticsOrder/generate")
    @ApiOperation(value = "生成物流单", httpMethod = "POST")
    public R generateLogisticsOrder(@RequestBody SmallproAddLogisticsOrderReq req) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (CollectionUtils.isEmpty(req.getSmallproIdList())) {
            return R.error("数据为空");
        }
        SmallproNormalCodeMessageRes result =
                smallproOldProductExService.addSmallproReturnToFactoryLogistics(req.getSmallproIdList(), oaUserBO);
        if (result.getCode() == 500) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            return R.success(result.getMessage(), result.getMessage());
        } else {
            return R.error("未知错误！");
        }
    }

    // endregion

    // endregion

    // region smallproForward

    // region 获取用户所在地区的打印机客户端列表 get /getPrintClient

    /**
     * description: <获取用户所在地区的打印机客户端列表>
     * translation: <Get the list of printer clients in the user's area>
     *
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:41 2019/12/13
     * @see SmallproForwardExService#getPrintClientInfo(String)
     * @since 1.0.0
     **/
    @GetMapping("/getPrintClient")
    @ApiOperation(value = "获取用户所在地区的打印机客户端列表", response = PrintClientRes.class,
            httpMethod = "GET")
    public R getPrintClient() {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        return R.success(smallproForwardExService.getPrintClientInfo(oaUserBO.getArea()));
    }

    // endregion

    // region 打印小件接件单相关的票据 get /printSmallproTicket

    /**
     * description: <打印小件接件单相关的票据>
     * translation: <Printing receipts related to small pickup orders>
     *
     * @param type       票据类型
     * @param smallproId 小件Id
     * @param clientNo   打印机Id
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:42 2019/12/13
     * @since 1.0.0
     **/
    @GetMapping("/printSmallproTicket")
    @ApiOperation(value = "打印小件接件单相关的票据,type:[24接件单小票|25打印条码]", httpMethod = "GET")
    public R printSmallproTicket(@RequestParam("type") Integer type,
                                 @RequestParam("smallproId") Integer smallproId,
                                 @RequestParam("clientNo") String clientNo) {
        if (CommenUtil.isNullOrZero(type)) {
            return R.error("打印类别错误！");
        }
        OaUserBO oaUser = abstractCurrentRequestComponent.getCurrentStaffId();
        boolean flag = smallproForwardExService.printSmallproTicket(type, smallproId, clientNo, oaUser.getArea(),
                oaUser.getXTenant(), oaUser.getUserId(), oaUser.getUserName(), 1);
        if (flag) {
            return R.success("已通知客户端打印，请等待客户端打印！");
        } else {
            return R.error("通知客户端打印失败！");
        }
    }

    // endregion

    // region 推送消息给接件用户 get /pushMessageToUser

    /**
     * description: <推送消息给接件用户>
     * translation: <Push message to receiver user>
     *
     * @param userNames  用户名称数组
     * @param userIds    用户ID数组
     * @param type       通知类别
     * @param smallproId 小件ID
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 14:43 2019/12/13
     * @see SmallproForwardExService#pushInfoToUsers(List, List, Integer, Integer)
     * @since 1.0.0
     **/
    @GetMapping("/pushMessageToUser")
    @ApiOperation(value = "推送消息给接件用户,type:[1通知发货|2通知取件],userNames/userIds用逗号隔开,不用顺序相同,只有一个直接传",
            httpMethod = "GET")
    public R pushMessageToUser(@RequestParam("userNames") String userNames,
                               @RequestParam("userIds") String userIds,
                               @RequestParam("type") Integer type,
                               @RequestParam("smallproId") Integer smallproId) {
        String[] userIdArray = userIds.split(",");
        List<Integer> userIdList = new ArrayList<>(userIdArray.length);
        for (String userId : userIdArray) {
            try {
                userIdList.add(Integer.parseInt(userId));
            } catch (Exception e) {
                return R.error("用户Id错误！");
            }
        }
        String[] userNameArray = userNames.split(",");
        List<String> userNameList = new ArrayList<>(userNameArray.length);
        Collections.addAll(userNameList, userNameArray);
        if (smallproForwardExService.pushInfoToUsers(userNameList, userIdList, type, smallproId)) {
            return R.success("发送信息成功！");
        } else {
            return R.error("发送信息失败！");
        }
    }

    // endregion

    // endregion

    // region smallproRefund

    // region 获取微信验证二维码链接 get /pay/wxCode/{afterSaleREId}

    /**
     * description: <获取微信验证二维码链接>
     * translation: <Get WeChat verification QR code link>
     *
     * @param afterSaleREId shouhouTuihuanId
     * @return com.jiuji.tc.common.vo.R<java.lang.String>
     * <AUTHOR>
     * @date 18:26 2020/4/10
     * @see SmallproRefundExService#getReturnWxCode(Integer, Integer, Integer)
     * @since 1.0.0
     **/
    @GetMapping("/pay/wxCode/{afterSaleREId}")
    @ApiOperation(value = "获取微信验证二维码链接", httpMethod = "GET")
    public R<String> getReturnWxCode(@PathVariable Integer afterSaleREId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        String url = smallproRefundExService.getReturnWxCode(afterSaleREId, oaUserBO.getAreaId(), oaUserBO.getUserId());
        if (url != null && !"".equals(url)) {
            return R.success(url);
        } else {
            return R.error("获取微信验证二维码失败！");
        }
    }

    // endregion

    // region 判断是否有支付宝原路返回 get /refund/isReturnAlipay/{returnFactoryId}

    /**
     * description: <判断是否有支付宝原路返回>
     * translation: <Determine if there is Alipay original way back>
     *
     * @param returnFactoryId shouhouFanchangId
     * @return com.jiuji.tc.common.vo.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 18:28 2020/4/10
     * @since 1.0.0
     **/
    @GetMapping("/refund/isReturnAlipay/{returnFactoryId}")
    @ApiOperation(value = "判断是否有支付宝原路返回", httpMethod = "GET")
    public R<Boolean> isReturnAlipay(@PathVariable Integer returnFactoryId) {
        if (returnFactoryId == null || returnFactoryId == 0) {
            return R.error("参数错误！");
        }
        SmallproNormalCodeMessageRes result = smallproRefundExService.isReturnAlipay(returnFactoryId);
        if (result.getCode() == 500) {
            return R.success(false);
        }
        return R.success(true);
    }

    // endregion

    // region 退货提交 post /refund/submit/{smallproId}

    // #point

    /**
     * description: <提交退款>
     * translation: <Submit a refund>
     *
     * @param smallproId 小件接件Id
     * @param req        退货提交Req
     * @return com.jiuji.tc.common.vo.R
     * <AUTHOR>
     * @date 19:47 2020/4/10
     * @see SmallproRefundExService#refundSubmit(OaUserBO, Integer, List, SmallproRefundSubmitReq)
     * @since 1.0.0
     **/
    @PostMapping("/refund/submit/{smallproId}")
    @ApiOperation(value = "退货提交", httpMethod = "POST")
    @RepeatSubmitCheck(seconds = 30, argIndexs = {0})
    public R refundSubmit(@PathVariable Integer smallproId,
                          @RequestBody SmallproRefundSubmitReq req) {
        if (smallproId <= 0) {
            return R.error("小件接件ID错误！");
        }

        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        SmallproNormalCodeMessageRes result = new SmallproNormalCodeMessageRes();
        result = smallproRefundExService.refundSubmit(oaUserBO,
                smallproId, req.getBasketInfoList(), req);
        if (result.getCode() != 0) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            return R.success("退款提交成功！");
        } else {
            return R.error("未知错误");
        }
    }

    // endregion

    // region 退货二次审核 post /refund/check2/{shouhouTuihuanId}

    /**
     * description: <退货二次审核>
     * translation: <Return audit>
     *
     * @param afterSaleREId shouhouTuihuanId
     * @param pwd2          二级密码
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes>
     * <AUTHOR>
     * @date 20:00 2020/4/10
     * @see SmallproRefundExService#refundCheck2(OaUserBO, Integer)
     * @since 1.0.0
     **/
    @PostMapping("/refund/check2/{afterSaleREId}")
    @ApiOperation(value = "退货二次审核", httpMethod = "POST")
    public R<SmallproNormalCodeMessageRes> refundCheck2(@PathVariable("afterSaleREId") Integer afterSaleREId,
                                                        @RequestParam("pwd2") String pwd2) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        // 校验二级密码
        try {
            SmallproNormalCodeMessageRes checkPwd2Result =
                    smallproExchangePurchaseService.checkPwd2(oaUserBO.getUserId(), pwd2);
            if (checkPwd2Result == null || checkPwd2Result.getCode() == 500) {
                return R.error("二级密码校验失败！");
            }
        } catch (UnsupportedEncodingException e) {
            log.error("二级密码转译异常{}:");
//            e.printStackTrace();
            return R.error("二级密码转译失败！");
        }
        if (!oaUserBO.getRank().contains("777") && !oaUserBO.getRank().contains("52")) {
            return R.error("您没有对退款的二次审核权限！需要权值52或777");
        }
        SmallproNormalCodeMessageRes result = smallproRefundExService.refundCheck2(oaUserBO, afterSaleREId);
        if (result.getCode() != 0) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            return R.success("审核成功！");
        } else {
            return R.error("未知错误！");
        }
    }

    // endregion

    // region 撤销退货提交 post /refund/cancel/check/{afterSaleREId}

    /**
     * description: <撤销退货提交>
     * translation: <Unsubmit return submission>
     *
     * @param afterSaleREId shouhouTuihuanId
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.smallpro.vo.res.SmallproNormalCodeMessageRes>
     * <AUTHOR>
     * @date 20:31 2020/4/10
     * @see SmallproRefundExService#cancelRefundCheck(Integer, OaUserBO)
     * @since 1.0.0
     **/
    @PostMapping("/refund/cancel/check/{afterSaleREId}")
    @ApiOperation(value = "撤销退货提交", httpMethod = "POST")
    public R<SmallproNormalCodeMessageRes> cancelRefundCheck(@PathVariable Integer afterSaleREId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        SmallproNormalCodeMessageRes result = smallproRefundExService.cancelRefundCheck(afterSaleREId,
                oaUserBO);
        if (result.getCode() != 0) {
            return R.error(result.getMessage());
        } else if (result.getCode() == 0) {
            return R.success("撤销退货成功！");
        } else {
            return R.error("未知错误");
        }
    }

    // endregion

    // region 获取支付数据信息 get /pay/info

    /**
     * description: <获取支付数据信息>
     * translation: <Get payment data information>
     *
     * @param subId         原订单Id
     * @param afterSaleREId shouhouTuihuanId
     * @param tuiWay        退款方式
     * @param tuihuanKind   退款类型
     * @return com.jiuji.tc.common.vo.R<java.util.List < com.jiuji.oa.afterservice.smallpro.bo.smallproExchange.SmallproPayInfoBO>>
     * <AUTHOR>
     * @date 20:20 2020/4/10
     * @see SmallproRefundExService#getPayInfo(Integer, Integer, String, Integer)
     * @since 1.0.0
     **/
    @GetMapping("/pay/info")
    @ApiOperation(value = "获取支付数据信息", httpMethod = "GET")
    public R<List<SmallproPayInfoBO>> getPayInfo(@RequestParam(required = false) Integer subId,
                                                 @RequestParam(required = false) Integer afterSaleREId,
                                                 @RequestParam(required = false) String tuiWay,
                                                 @RequestParam(required = false) Integer tuihuanKind) {
        if (tuihuanKind == 7 && subId == null && afterSaleREId == null) {
            return R.error("原订单Id和售后退换Id都为空！");
        }
        if (subId != null && (tuiWay == null || tuiWay.isEmpty())) {
            return R.error("退款方式错误！");
        }
        List<SmallproPayInfoBO> result = smallproRefundExService.getPayInfo(subId, afterSaleREId, tuiWay,
                tuihuanKind);
        if (CollectionUtils.isEmpty(result)) {
            return R.error("无此记录！");
        }
        return R.success(result);
    }

    // endregion

    // region 获取订单相关支付宝验证信息 get /alipay/auth/info

    /**
     * description: <获取订单相关支付宝验证信息>
     * translation: <Get Alipay verification information related to the order>
     *
     * @param id   redisKey
     * @param type redisType
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.other.bo.AlipayAuthBO>
     * <AUTHOR>
     * @date 20:35 2020/4/10
     * @see SmallproRefundExService#getAlipayAuthInfo(String, String)
     * @since 1.0.0
     **/
    @GetMapping("/alipay/auth/info")
    @ApiOperation(value = "获取订单相关支付宝验证信息", httpMethod = "GET")
    public R<AlipayAuthBO> getAlipayAuthInfo(@RequestParam String id, @RequestParam String type) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (id == null || id.equals("") || type == null || type.equals("")) {
            return R.error("参数id或type错误！");
        }
        AlipayAuthBO alipayAuthBO = smallproRefundExService.getAlipayAuthInfo(id, type);
        if (alipayAuthBO == null) {
            return R.error("无此记录！");
        } else {
            log.warn(alipayAuthBO.toString());
            return R.success(alipayAuthBO);
        }
    }


    /**
     * description: <验证是否可以进行退款办理>
     * translation: <Verify if refund processing is possible>
     *
     * @param afterSaleREId shouhouTuihuanId
     * @return com.jiuji.tc.common.vo.R<com.jiuji.oa.afterservice.smallpro.vo.res.SmallproRefundExceptionCheckRes>
     * <AUTHOR>
     * @date 13:49 2020/4/16
     * @see SmallproRefundExService#getRefundException(Integer)
     * @since 1.0.0
     **/
    @ApiOperation(value = "验证是否可以进行退款办理", httpMethod = "POST")
    @PostMapping("/refund/exceptionCheck/{afterSaleREId}")
    public R<SmallproRefundExceptionCheckRes> getRefundExceptionResult(@PathVariable Integer afterSaleREId) {
        if (afterSaleREId == null || afterSaleREId <= 0) {
            return R.error("售后退换ID错误！");
        }
        SmallproRefundExceptionCheckRes result = new SmallproRefundExceptionCheckRes();
        SmallproNormalCodeMessageRes checkResult = smallproRefundExService.checkRefundException(afterSaleREId);
        if (checkResult != null && checkResult.getCode() == 500) {
            result.setType(2);
            result.setMessage(checkResult.getMessage());
        } else if (checkResult != null && checkResult.getCode() == 501) {
            result.setType(1);
            result.setMessage(checkResult.getMessage());
        } else if (checkResult != null && checkResult.getCode() == 0) {
            result.setType(0);
            result.setMessage(checkResult.getMessage());
        }
        if (result.getType() == 2) {
            List<SmallproRefundExceptionItemBO> items = smallproRefundExService.getRefundException(afterSaleREId);
            result.setList(items);
        }
        return R.success(result);
    }

    // endregion


    // endregion

    // region smallproExchangePurchase

    // region 小件接件取件操作 post /pickup/{smallproId}

    /**
     * description: <小件接件取件操作>
     * translation: <Smallpro pick-up>
     *
     * @param smallproId 小件接件单Id
     * @return com.jiuji.tc.common.vo.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 16:03 2020/1/3
     * @since 1.0.0
     **/
    @PostMapping("/pickup/{smallproId}")
    @ApiOperation(value = "小件接件取件操作", httpMethod = "POST")
    public R<Boolean> pickup(@PathVariable Integer smallproId,
                             @RequestParam(name = "kind") Integer kindParam,
                             @RequestParam(required = false) Integer transferAreaId) {
        Smallpro smallpro = smallproService.getByIdSqlServer(smallproId);
        Integer kind = smallpro.getKind();
        if (smallproId == null || smallproId.equals(0) || kind == null || kind < 1 || kind > 4) {
            R.error("参数错误，请检查参数！");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (kind == 4 && !oaUserBO.getRank().contains("6f5")) {
            return R.error("您没有权限！权值:6f5");
        }
        PickUpExtendReq pickUpExtendReq = new PickUpExtendReq();
        pickUpExtendReq.setTransferAreaId(transferAreaId);
        SmallproNormalCodeMessageRes smallproNormalCodeMessageRes =
                smallproExchangePurchaseService.pickup(smallproId, kind, oaUserBO,pickUpExtendReq);
        if (smallproNormalCodeMessageRes.getCode().equals(500)) {
            log.warn("小件取件操作失败！" + smallproId + ":" + smallproNormalCodeMessageRes.getMessage());
            return R.error(smallproNormalCodeMessageRes.getMessage());
        } else if (smallproNormalCodeMessageRes.getCode() == 501) {
            R<Boolean> returnR = R.error(smallproNormalCodeMessageRes.getMessage());
            returnR.setCode(5001);
            return returnR;
        } else if (smallproNormalCodeMessageRes.getCode() == 0) {
            log.debug("小件取件操作成功！" + smallproId);
            R<Boolean> result = R.success(smallproNormalCodeMessageRes.getMessage());
            CompletableFuture.runAsync(() -> {
                //跳转连接
                String host = Optional.of(sysConfigClient.getValueByCode(SysConfigConstant.MOA_URL))
                        .filter(x -> ResultCode.SUCCESS == x.getCode())
                        .map(R::getData)
                        .orElseThrow(() -> new RRException("获取域名出错"));
                String inuser = smallpro.getInUser();
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(inuser)) {
                    List<TransferUserRes> transferUserRes = smallproWxMapper.selectUserNameList(Collections.singletonList(inuser));
                    if (CollectionUtils.isNotEmpty(transferUserRes)) {
                        Integer areaId = transferUserRes.get(0).getAreaId();
                        Areainfo areainfo = areainfoService.getById(areaId);
                        String message = "你关注的员工【" + inuser + "（" + areainfo.getArea() + "）】添加的小件接件单（单号：" + smallpro.getId() + "）处理完成，请做好跟进~";
                        String link = host + "/new/#/small-refund/" + smallpro.getId();
                        shouhouService.sendSubCollectionMessage(transferUserRes.get(0).getCh999UserId(), link, message);
                    }
                }
                String weixiuren = smallpro.getWxUser();
                if (com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotEmpty(weixiuren)) {
                    List<TransferUserRes> transferUserRes = smallproWxMapper.selectUserNameList(Collections.singletonList(weixiuren));
                    if (CollectionUtils.isNotEmpty(transferUserRes)) {
                        Integer areaId = transferUserRes.get(0).getAreaId();
                        Areainfo areainfo = areainfoService.getById(areaId);
                        String message = "你关注的员工【" + weixiuren + "（" + areainfo.getArea() + "）】添加的小件接件单（单号：" + smallpro.getId() + "）处理完成，请做好跟进~";
                        String link = host + "/new/#/small-refund/" + smallpro.getId();
                        shouhouService.sendSubCollectionMessage(transferUserRes.get(0).getCh999UserId(), link, message);
                    }
                }
            });
            return result.addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
        }
        return R.error("未知错误");
    }


    /**
     * 取机校验
     * @param pickUpCheckReq
     * @return
     */
    @PostMapping("pickUpCheck")
    public R<PickUpCheckRes> pickUpCheck(@RequestBody @Validated PickUpCheckReq pickUpCheckReq) {
        PickUpCheckRes pickUpCheckRes = smallproService.pickUpCheck(pickUpCheckReq);
        return R.success(pickUpCheckRes);
    }

    // endregion

    // region 换货审核/撤销换货审核 post /check/exchange/{smallproId}

    /**
     * description: <换货审核/撤销换货审核>
     * translation: <Exchange audit / cancel exchange audit>
     *
     * @param smallproId 小件接件单Id
     * @param type       审核类别
     * @return com.jiuji.tc.common.vo.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 20:23 2020/4/10
     * @see SmallproExchangePurchaseService#checkExchange(Integer, String)
     * @see SmallproExchangePurchaseService#cancelCheckExchange(Integer, String)
     * @since 1.0.0
     **/
    @PostMapping("/check/exchange/{smallproId}")
    @ApiOperation(value = "换货审核type[0换货审核|1撤销审核]", httpMethod = "POST")
    @Transactional(rollbackFor = Exception.class)
    public R<Boolean> checkExchange(@PathVariable Integer smallproId, @RequestParam Integer type) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        SmallproNormalCodeMessageRes smallproCheckRes = new SmallproNormalCodeMessageRes().setCode(500).setMessage(
                "type错误！");
        switch (type) {
            case 0:
                smallproCheckRes = smallproExchangePurchaseService.checkExchange(
                        smallproId, oaUserBO.getUserName());
                break;
            case 1:
                smallproCheckRes = smallproExchangePurchaseService.cancelCheckExchange(
                        smallproId, oaUserBO.getUserName());
                break;
            default:
                break;
        }
        if (smallproCheckRes.getCode().equals(0)) {
            return R.success(smallproCheckRes.getMessage());
        } else if (smallproCheckRes.getCode().equals(500)) {
            return R.error(smallproCheckRes.getMessage());
        } else {
            return R.error("Service未知错误!");
        }
    }

    // endregion

    // region 保存验证码 post /check/codeMessage/{smallproId}

    /**
     * description: <保存验证码>
     * translation: <Save verification code>
     *
     * @param smallproId 小件接件Id
     * @param code       验证码
     * @param type       验证码类型
     * @return com.jiuji.tc.common.vo.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 18:34 2020/4/10
     * @since 1.0.0
     **/
    @PostMapping("/check/codeMessage/{smallproId}")
    @ApiOperation(value = "保存验证码", httpMethod = "POST", notes = "type:验证码类型[1换货验证码|2退货验证码]")
    public R<Boolean> checkCodeMessage(@PathVariable Integer smallproId,
                                       @RequestParam(value = "code", required = false) String code,
                                       @RequestParam(value = "type") Integer type,
                                       @RequestParam(value = "validType", required = false) Integer validType) {
        if (CommenUtil.isNullOrZero(validType)) {
            validType = 3;
        }
        if (StringUtils.isBlank(code)) {
            return R.error("请输入验证码!");
        }
        if (smallproId == null || smallproId <= 0) {
            return R.error("小件id不能为空!");
        }
        if (type == null || type < NumberConstant.ONE || type > NumberConstant.TWO) {
            return R.error("验证类型只能为1或2");
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        String tempCode = code;
        if (code.equals("授权")) {
            if (!oaUserBO.getRank().contains("6f7")) {
                return R.error("您没有权限！权值:6f7");
            }

            tempCode = MessageFormat.format("授权验证 【{0}】 {1}", oaUserBO.getUserName()
                    , LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        }
        SmallproNormalCodeMessageRes result = smallproExchangePurchaseService.checkCodeMessage(smallproId, tempCode, type, validType);
        if (result.getCode() != 0) {
            return R.error(result.getMessage());
        }
        return R.success(true);
    }

    // endregion

    // region 发送验证码给用户 post /push/codeMessage/{smallproId}

    /**
     * description: <发送验证码给用户>
     * translation: <Send verification code to user>
     *
     * @param smallproId 小件接件Id
     * @param areaId     门店Id
     * @param subId      原订单Id
     * @param type       验证码类别
     * @return com.jiuji.tc.common.vo.R<java.lang.Boolean>
     * <AUTHOR>
     * @date 13:54 2020/4/16
     * @since 1.0.0
     **/
    @PostMapping("/push/codeMessage/{smallproId}")
    @ApiOperation(value = "发送验证码给用户", httpMethod = "POST", notes = "type:验证码类型[1换货验证码|2退货验证码]")
    public R<Boolean> pushCodeMessageToUser(@PathVariable Integer smallproId,
                                            @RequestParam(required = false, value = "areaId") Integer areaId,
                                            @RequestParam(required = false, value = "subId") Integer subId,
                                            @RequestParam(required = false, value = "type") Integer type) {
        R<Boolean> error = checkPushToUser(smallproId, areaId, subId, type);
        if (error != null) {
            return error;
        }
        SmallproNormalCodeMessageRes result = smallproExchangePurchaseService.pushCodeMessageToUser(smallproId,
                areaId, subId,
                type);
        if (result.getCode() != 0) {
            return R.error(result.getMessage());
        }
        return R.success(true);
    }

    private static R<Boolean> checkPushToUser(Integer smallproId, Integer areaId, Integer subId, Integer type) {
        if (smallproId == null || smallproId <= 0) {
            return R.error("小件id不能为空!");
        }
        if (areaId == null || areaId <= 0) {
            return R.error("门店id不能为空!");
        }
        if (subId == null || subId <= 0) {
            return R.error("原订单id不能为空!");
        }
        if (type == null || type < 1 || type > NumberConstant.TWO) {
            return R.error("验证码类型只能为1或2!");
        }
        return null;
    }

    // region 钢化膜绑定验证

    /**
     * description: <钢化膜绑定验证>
     *
     * @since 1.0.0
     **/
    @PostMapping("/check/TemperedFilmBinding")
    @ApiOperation(value = "钢化膜绑定验证", httpMethod = "POST")
    public R<Boolean> checkTemperedFilmBinding(@RequestParam String imei,
                                               @RequestParam int smallproId,
                                               @RequestParam long basketId,
                                               @RequestParam String fid,
                                               @RequestParam int kind) {
        //处理fid
        fid = Optional.ofNullable(fid)
                .map(item->Arrays.asList("undefined","null").contains(item.trim()) ? "" : item)
                .orElse("");
        SmallproNormalCodeMessageRes result = smallproExchangePurchaseService.checkTemperedFilmBinding(imei,
                smallproId, basketId, fid, kind);
        if (result.getCode() == 1) {
            return R.success(result.getMessage());
        } else {
            return R.error(result.getMessage());
        }
    }

    // endregion
    // region 校验该订单是否能转现，报废

    /**
     * description: <校验该订单是否能转现，报废>
     *
     * @since 1.0.0
     **/
    @GetMapping("/check/CashOrScrap")
    @ApiOperation(value = "校验该订单是否能转现，报废", httpMethod = "GET")
    public R<CashOrScrapRes> CashOrScrap(@RequestParam Integer smallproId) {
        if (null == smallproId) {
            return R.error("请检查单号");
        }
        return smallproExchangePurchaseService.CashOrScrap(smallproId);
    }


    /**
     * 添加物流或者小件单
     *
     * @param orderIdParam
     * @return
     */
    @GetMapping("/check/CashOrScrap/V2")
    @ApiOperation(value = "添加物流或者小件单", httpMethod = "GET")
    public R<ResultByList> CashOrScrapV2(@RequestParam("orderId") String orderIdParam) {
        Integer orderId = Convert.toInt(orderIdParam);
        if (CommenUtil.isNullOrZero(orderId)) {
            return R.error("单号错误，请检查！");
        }
        R<List<CashOrScrapV2Res>> listR = smallproExchangePurchaseService.cashOrScrapV2(orderId);
        int orderType = 0;
        if (listR.getExData() != null) {
            Map<String, Object> exData = listR.getExData();
            for (Map.Entry<String, Object> stringObjectEntry : exData.entrySet()) {
                orderType = (int) stringObjectEntry.getValue();
            }
        }
        if (listR.getCode() == NumberConstant.ZERO) {
            ResultByList result = new ResultByList();
            result.setList(listR.getData());
            result.setOrderType(orderType);
            return R.success(result);
        }
        return R.error(listR.getUserMsg());
    }

    /**
     * 批量异常提交
     *
     * @param smallproIds 批量小件id
     * @return
     */
    @PostMapping("/check/abnormalBatch")
    @ApiOperation(value = "批量异常提交", httpMethod = "POST")
    public R<Boolean> abnormalBatch(@RequestBody List<Integer> smallproIds) {
        if (CollectionUtils.isEmpty(smallproIds)) {
            return R.error("数据为空，无法提交！");
        }
        return smallproExchangePurchaseService.abnormalBatch(smallproIds);
    }

    /**
     * description: <批量转现报废>
     *
     * @since 1.0.0
     **/
    @PostMapping("/CashOrScrapBatch")
    @ApiOperation(value = "批量转现报废", httpMethod = "POST")
    public R<Map<String, List<Integer>>> CashOrScrapBatch(@RequestBody CashOrScrapBatchReq cashOrScrapBatchReq) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        if (cashOrScrapBatchReq.getType() == 1 && !oaUserBO.getRank().contains("6f2")) {
            return R.error("您没有权限！权值:6f2");
        }
        if (cashOrScrapBatchReq.getType() == 2 && !oaUserBO.getRank().contains("6f1")) {
            return R.error("您没有权限！权值:6f1");
        }
        if (CollectionUtils.isEmpty(cashOrScrapBatchReq.getReturnFactoryIds())) {
            return R.error("无数据");
        }
        List<Integer> integers = smallproExchangePurchaseService.CashOrScrapBatch(cashOrScrapBatchReq, oaUserBO);
        Map<String, List<Integer>> map = new HashMap<>();
        map.put("returnFactoryIds", integers);
        if (CollectionUtils.isEmpty(integers)) {
            return R.success("操作成功", map);
        } else {
            R r = new R();
            r.setUserMsg("部分商品操作失败，请检查");
            r.setData(map);
            r.setCode(0);
            return r;
        }
    }

    /**
     * description: <修改状态不对的>
     *
     * @since 1.0.0
     **/
    @GetMapping("/correctStatus")
    @ApiOperation(value = "修改状态不对的", httpMethod = "GET")
    public R<Integer> correctStatus() {
        return R.success(smallproExchangePurchaseService.correctStatus());
    }

    // endregion

    /**
     * description: <现货需要校验ppid或条码是否为钢化膜及其子分类下面的，判断是否需要填关联订单号>
     *
     * @since 1.0.0
     **/
    @GetMapping("/checkPpid")
    @ApiOperation(value = "现货需要校验ppid或条码是否为钢化膜及其子分类下面的，判断是否需要填关联订单号",
            notes = "type:2 按ppid  3  按条码", httpMethod = "GET")
    public R<Boolean> checkPpid(@RequestParam List<String> ppId,
                                @RequestParam(required = false, defaultValue = "1") Integer type) {
        return R.success(smallproExchangePurchaseService.checkPpid(ppId, type));
    }


    @GetMapping("/getUrl")
    public R<String> getUrl() {
        String host = sysConfigService.getValueByCode(SysConfigConstant.IN_WCF_HOST);
        String url = host + SmallProRelativePathConstant.MONEY_SAVE;
        log.error("url是:" + url);
        return R.success(Namespaces.get() + ":" + url);
    }

    /**
     * 校验小件单是否能生成物流单
     *
     * @param smallproId
     * @return
     */
    @GetMapping("/checkSmallProWuLiu")
    public R<CheckSmallProWuLiuRes> checkSmallProWuLiu(@RequestParam Integer smallproId) {
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        return smallproService.checkSmallProWuLiu(smallproId, oaUserBO);
    }

    /**
     * 打印物流单
     *
     * @return
     */
    @GetMapping("/printWuLiu")
    public R<Boolean> printWuLiu(@RequestParam Integer wuLiuId,
                                 @RequestParam String clientNo,
                                 @RequestParam String source) {
        String url = oaWcfUrlSource.getBasicUrl() + "/kcApi/printPailiangjiWuliu";
        String param = "?wuLiuId=" + wuLiuId + "&clientNo=" + clientNo + "&source=" + source + "&type=" + 33;
        try {
            String s = HttpUtil.get(url + param);
            System.out.println(s);
        } catch (Exception e) {
            return R.success(false);
        }
        return R.success(true);
    }

    /**
     * 小件品类统计查询参数下拉数据
     *
     * @return R<CommonTitleRes>
     */
    @GetMapping("/title")
    public R<CommonTitleRes> title() {
        return R.success(smallproService.getTitle());
    }

    /**
     * 小件品类统计数据
     *
     * @return
     */
    @PostMapping("/data")
    public R<CommonDataRes> data(@RequestBody SmallCategoryReq req) {
        return R.success(smallproService.data(req));
    }

    /**
     * 退换小件旧件列表新增批量推送功能
     *
     * @return
     */
    @PostMapping("/batchPush")
    public R<Boolean> batchPush(@RequestBody @Validated SmallBatchPushReq req) {
        OaUserBO staffId = abstractCurrentRequestComponent.getCurrentStaffId();
        return R.success(smallproService.batchPush(req, staffId));
    }

    @GetMapping("getYuyueInfoByMobile")
    public R<SmallProYuyueInfo> getYuyueInfoByMobile(@Param(value = "mobile") String mobile,@RequestParam(required = false) Integer yuYueId) {
        return smallproService.getYuyueInfoByMobile(mobile,yuYueId);
    }

    /**
     * 测试无故障接口
     *
     * @param status
     * @return
     */
    @GetMapping("testWithoutProblem")
    @ApiOperation(value = "测试无故障接口", httpMethod = "GET", notes = "status状态值，默认0-有故障，1无故障")
    public R<Boolean> testWithoutProblem(@Param(value = "status") Integer status, @Param(value = "fcId") Integer fcId) {
        return smallproService.testWithoutProblem(status, fcId);
    }

    @GetMapping("testShortUrl")
    @ApiOperation(value = "测试短链生成", httpMethod = "GET", notes = "后端测试，请忽略")
    public R<Boolean> testShortUrl() {
        String shortUrl = smsService.getShortUrl(0L, "https://m.9ji.com/member/comment?type=KT&commentCode=aFW&from=dx", "测试");
        smsService.sendSms("15969576182", "这是一段短信文本内容", "2021-03-24 11:42:00", "", SysConfigConstant.MARKETING_CHANNEL);

//        System.out.println(shortUrl);
        return R.success("操作成功");
    }

    @GetMapping("getFilmAccessories")
    @ApiOperation(value = "获取兑换商品膜信息", httpMethod = "GET", notes = "前端请求存在跨域访问，后端转发处理")
    public R<List<FilmAccessoriesBo>> getFilmAccessories(@Param(value = "ppid") Integer ppid, HttpServletRequest request) {
        return smallproService.getFilmAccessories(ppid, request);
    }

    /**
     * 小件质保换新接口
     *
     * @param ppid
     * @param type
     * @return
     * @see SmallProServiceTypeEnum
     */
    @GetMapping("getFilmAccessories/v2")
    @ApiOperation(value = "小件质保换新接口", httpMethod = "GET", notes = "小件质保换新最新接口")
    public R<List<FilmAccessoriesV2Item>> getFilmAccessoriesV2(@RequestParam(value = "ppid") Integer ppid,
                                                               @RequestParam(value = "type", required = false) Integer type,
                                                               @RequestParam(value = "basketId", required = false) String basketId) {
        return smallproService.getFilmAccessoriesV2(ppid, type, Convert.toInt(basketId));
    }

    /**
     * 小件质保换新接口
     *
     * @param ppid
     * @param type
     * @return
     * @see SmallProServiceTypeEnum
     */
    @GetMapping("getFilmAccessories/v3")
    @ApiOperation(value = "小件质保换新接口", httpMethod = "GET", notes = "小件质保换新最新接口")
    public R<List<FilmAccessoriesV2Item>> getFilmAccessoriesV3(@RequestParam(value = "ppid") Integer ppid,
                                                               @RequestParam(value = "type", required = false) Integer type,
                                                               @RequestParam(value = "basketId", required = false) Integer basketId,
                                                               @RequestParam(value = "key", required = false) String key,
                                                               @RequestParam(value = "mobileExchangeFlag", required = false) Integer mobileExchangeFlag,
                                                               @RequestParam(value = "imei", required = false) String imei
    ) {
        return smallproService.getFilmAccessoriesV3(ppid, type, basketId, StrUtil.trim(key), mobileExchangeFlag, imei)
                .addAllBusinessLog(SpringContextUtil.getRequestKeyListMsg(RequestAttrKeys.REQUEST_ATTR_BUSINESS_LOG));
    }


    @GetMapping("/getSafeShellByPpid")
    @ApiOperation(value = "获取壳膜分类下所有上架的壳商品", httpMethod = "GET")
    public R<List<FilmAccessoriesV2Item>> getSafeShellByPpid(@RequestParam(value = "basketId") Integer basketId,
                                                             @RequestParam(value = "key", required = false) String key,
                                                             @RequestParam(value = "size", required = false) Integer size) {
        if (StrUtil.isBlank(key)) {
            return R.success(new ArrayList<>());
        }
        OaUserBO oaUserBO = abstractCurrentRequestComponent.getCurrentStaffId();
        List<FilmAccessoriesV2Item> filmAccessoriesV2Items = smallproDetailsExService.getSafeShellByPpid(basketId, oaUserBO.getAreaId());
        if (Boolean.TRUE.equals(CommenUtil.isNullOrZero(size))) {
            size = NumberConstant.FIFTY;
        }
        if (StringUtils.isNotEmpty(key)) {
            //判断字符串是否是数值
            List<FilmAccessoriesV2Item> collect;
            if (NumberUtil.isNumber(key)) {
                collect = filmAccessoriesV2Items.stream().filter(ma -> String.valueOf(ma.getPpid()).contains(key)).collect(Collectors.toList());
            } else {
                collect = filmAccessoriesV2Items.stream().filter(ma -> ma.getProductName().contains(key)).collect(Collectors.toList());
            }
            return R.success(CollUtil.sub(collect, 0, size));
        }
        return R.success(CollUtil.sub(filmAccessoriesV2Items, 0, size));
    }

    @GetMapping("testTuiWayList")
    @ApiOperation(value = "测试短链生成", httpMethod = "GET", notes = "后端测试，请忽略")
    public R<List<String>> testTuiWayList() {
//
        List<String> allOnlinePayWayList = smallProAdapterService.getAllOnlinePayWayList();
        return R.success(allOnlinePayWayList);
    }

    @GetMapping("/getSmallProOldGoodsWaitingForSelect")
    @ApiOperation(value = "旧件发货待选取列表")
    public R<List<SmallProOldGoodsWaitingForSelectRes>> getSmallProOldGoodsWaitingForSelect(@RequestParam(value = "area", required = false) String area) {
        return smallproService.getSmallProOldGoodsWaitingForSelect(area);
    }

    @GetMapping("/virtualProductAutoScrap")
    @ApiOperation(value = "虚拟商品自动报废", notes = "小件退款办理的时候调用")
    public R<String> virtualProductAutoScrap(@RequestParam(value = "smallProId") Integer smallProId) {
        smallproExchangePurchaseService.virtualProductAutoScrap(smallProId);
        return R.success("操作成功");
    }

    @GetMapping("/receiptPrinting")
    @ApiOperation(value = "回执单打印提供小件信息", httpMethod = "GET")
    public R<SmallReceiptPrintingRes> receiptPrinting(@RequestParam("smallProId") Integer smallProId) {
        if (smallProId == null) {
            return R.error("小件ID错误！");
        }
        return R.success(smallproService.receiptPrinting(smallProId));
    }

    @PostMapping("/exchangeGoodsSplitOrder")
    public R<SplitBasketRes> exchangeGoodsSplitOrder(@RequestBody SplitBasketConditionReq splitBasketConditionReq) {
        return R.success(exchangeGoodsService.exchangeSplitBasket(splitBasketConditionReq));
    }

    /**
     * 小件备货接口
     * @param req
     * @return
     */
    @PostMapping("/stockUp")
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{req.smallproBillId}")
    public R stockUp(@RequestBody @Validated SmallStockUpReq req) {
        return smallproService.stockUp(req);
    }

    /**
     * 保存订单进程日志
     * @param req
     * @return
     */
    @PostMapping("/addStockLog")
    @ApiOperation(value = "保存备货进程日志", notes = "req传相应的json数据", httpMethod = "POST",response = R.class)
    public R addStockLog(@RequestBody SmallproBillLogVo req){
        return R.success(smallproService.addStockLog(req));
    }

    /**
     * 查询小件备货日志
     */
    @GetMapping("/listStockLog")
    @ApiOperation(value = "查询小件备货日志", notes = "req传相应的json数据", httpMethod = "GET",response = R.class)
    public R<List<SmallproBillLogVo>> listStockLog(@RequestParam("billId") Integer billId,
                                                   @RequestParam(value = "type", required = false, defaultValue = "1") Integer type){
        return smallproService.listStockLog(billId, type);
    }
    /**
     *
     * 定时处理保护壳半价复购提醒推送
     * @return
     */
    @GetMapping("/handlePhoneCasePurchaseBuy/v1")
    @ApiOperation(value = "定时处理保护壳半价复购提醒推送", httpMethod = "GET")
    public R<Boolean> handlePhoneCasePurchaseBuy () {
        smallproService.handlePhoneCasePurchaseBuy();
        return R.success(true);
    }
    @PostMapping("/submitNoDiscount")
    @ApiOperation(value = "不折价退款申请", notes = "不折价退款申请")
    @RepeatSubmitCheck(expression = "#{packageFullName}:#{methodSignName}:#{req.smallproId}",message = "请稍后再重试")
    public R<Boolean> submitNoDiscount(@RequestBody @Valid SmallproNoDiscountReqVo req){
        return smallproService.submitNoDiscount(req);
    }

    @GetMapping("/getSubmitNoDiscount")
    @ApiOperation(value = "不折价退款申请", notes = "不折价退款申请")
    public R<SmallproNoDiscountResVo> getSubmitNoDiscount(@RequestParam("smallProId") Integer smallProId){
        return smallproService.getSubmitNoDiscount(smallProId);
    }

    @PostMapping("/checkNoDiscount")
    @ApiOperation(value = "不折价退款审核", notes = "不折价退款审核")
    public R<Boolean> checkNoDiscount(@RequestBody @Valid SmallproNoDiscountCheckReqVo req){
        return smallproService.checkNoDiscount(req);
    }

    @GetMapping("/submitNotDiscountById")
    @ApiOperation(value = "不折价退款申请", notes = "不折价退款申请")
    @RepeatSubmitCheck(argIndexs = {0}, message = "请稍后再重试")
    public R<Boolean> submitNotDiscountById(@RequestParam("id") Integer smallProId){
        SmallproNoDiscountReqVo req = new SmallproNoDiscountReqVo();
        req.setSmallproId(smallProId);
        req.setReason("服务不折价退款");
        return smallproService.submitNoDiscount(req);
    }
}

