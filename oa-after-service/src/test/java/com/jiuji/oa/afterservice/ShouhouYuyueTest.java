package com.jiuji.oa.afterservice;

import com.jiuji.oa.afterservice.bigpro.bo.ShouhouMsgPushMessageBo;
import com.jiuji.oa.afterservice.bigpro.po.Attachments;
import com.jiuji.oa.afterservice.bigpro.service.*;
import com.jiuji.oa.afterservice.bigpro.vo.res.SmsReceiverRes;
import com.jiuji.oa.afterservice.common.source.ConstantsSource;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @author: gengjiaping
 * @date: 2020/3/13
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class ShouhouYuyueTest {
    @Autowired
    private ShouhouYuyueService shouhouYuyueService;
    @Autowired
    private WXSmsReceiverService wxSmsReceiverService;
    @Resource
    private ConstantsSource wxEnginerAreaIdsSource;
    @Autowired
    private ShouhouService shouhouService;
    @Autowired
    private AttachmentsService attachmentsService;
    @Resource
    private MemberClient memberClient;
    @Autowired
    private RepairService repairService;
    @Autowired
    private SendMessageRecordService sendMessageRecordService;

    @Test
    public void getRepairTypeTest(){
        shouhouYuyueService.getRepairTypes();
    }

    @Test
    public void getServiceInfoTest(){
        shouhouYuyueService.getServiceInfo("869695047106254",false);
    }

    @Test
    public void wxSmsReceiverTest(){
        List<SmsReceiverRes>  list = wxSmsReceiverService.getWxSmsReceiversByClassify(String.valueOf(15));
        log.info("");
    }

    @Test
    public void getAreaidsTest(){
        Integer week = LocalDateTime.now().getDayOfWeek().getValue();
        log.info("");
    }

    @Test
    public void yuyueconfirmSmDdEnter(){
      shouhouYuyueService.yuyueconfirmSmDdEnter(242362,0,false);
      log.info("");
    }

    @Test
    public void getAttachmentsTest(){
        List<Attachments> attachments = attachmentsService.getAttachmentsByLinkId(1,34,null);
        log.info("");
    }

    @Test
    public void shFeiyongTest(){
        repairService.updateShouhouFeiyong(10001);
    }

    @Test
    public void test(){
        sendMessageRecordService.getSendMessageRecordById(1);
    }

    @Test
    public void pushMessageTest(){
        ShouhouMsgPushMessageBo shouhouMsgPushMessageBo = new ShouhouMsgPushMessageBo();
        shouhouMsgPushMessageBo.setMsgId(1);
        shouhouMsgPushMessageBo.setShouhouId(2117543);
        shouhouMsgPushMessageBo.setAreaId(10);
        shouhouMsgPushMessageBo.setUserId(9302);
        shouhouMsgPushMessageBo.setOptUser("耿加平");
        shouhouService.pushMessage(shouhouMsgPushMessageBo,true);
    }

    @Test
    public void upShFeiyongTest(){
        repairService.updateShouhouFeiyong(2117850);
    }



}
