package com.jiuji.oa.afterservice.bigpro.service;

import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> <EMAIL>
 * @date 2020/9/28 18:38
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class ShouHouPjServiceTest {
    @Autowired
    private ShouHouPjService shouHouPjService;

    @Test
    public void insertSearchHistory() {
        Boolean result=shouHouPjService.insertSearchHistory(11288,"2255377|2","oaSearch");
        Assert.assertTrue(result);
    }
}