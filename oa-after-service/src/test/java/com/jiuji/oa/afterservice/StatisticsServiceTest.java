package com.jiuji.oa.afterservice;

import com.jiuji.oa.afterservice.batchreturn.vo.req.ShouHouStatisticsHttpReq;
import com.jiuji.oa.afterservice.batchreturn.vo.res.ShouHouStatisticsRes;
import com.jiuji.oa.afterservice.bigpro.statistics.enums.ShouHouStatisticsEnum;
import com.jiuji.oa.afterservice.bigpro.statistics.service.ShouhouStatisticsService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.IntStream;

/**
 * <AUTHOR> leee41
 * @date : 2020-09-10 14:12
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class StatisticsServiceTest {
    @Autowired
    private ShouhouStatisticsService shouhouStatisticsService;

//    @Test
//    public void testSpeed() {
//        long t1 = IntStream.range(0, 9).boxed().mapToLong(operand -> {
//            long start = System.currentTimeMillis();
//            list1();
//            return System.currentTimeMillis() - start;
//        }).sum();
//        long t2 = IntStream.range(0, 9).boxed().mapToLong(operand -> {
//            long start = System.currentTimeMillis();
//            list2();
//            return System.currentTimeMillis() - start;
//        }).sum();
//        System.err.println(t1);
//        System.err.println(t2);
//        Assert.assertTrue(t1 < t2);
//    }

    @Test
    public void testHuanJiTuiKuanLv() {
        Assert.assertTrue(
                IntStream.range(0, 99).mapToObj(operand -> check()).reduce((aBoolean, aBoolean2) -> aBoolean && aBoolean2).orElseThrow(RuntimeException::new)
        );
    }

    private boolean check() {
        Set<ShouHouStatisticsEnum> enumsSet = new HashSet<>();
        enumsSet.add(ShouHouStatisticsEnum.HUAN_JI_LIANG);
        List<ShouHouStatisticsRes> result = shouhouStatisticsService.multiStatisticsList(ShouHouStatisticsHttpReq.builder()
                .areaCodes(
                        Arrays.asList("27", "282", "294", "603", "696", "293", "51", "85", "642", "27", "282", "294", "603", "696", "293", "51", "85", "642")
                )
                .areaKind(0)
                .start(LocalDateTime.of(2020, 9, 1, 0, 0, 0))
                .end(LocalDateTime.of(2020, 9, 11, 23, 59, 59))
                .shouHouStatisticsEnums(enumsSet)
                .build(), new StringBuffer());
        return result.stream().noneMatch(res -> res.getTotalJieJian() != 0 && ((res.getHuanJiLiang() != 0 && res.getHuanJiLv().equals(BigDecimal.ZERO)) || (res.getTuiKuanLiang() != 0 && res.getTuiKuanLv().equals(BigDecimal.ZERO))));
    }


    private List<ShouHouStatisticsRes> list1() {
        return shouhouStatisticsService.multiStatisticsList(ShouHouStatisticsHttpReq.builder()
                .areaCodes(new ArrayList<>())
                .areaKind(0)
                .start(LocalDateTime.of(2020, 6, 10, 0, 0, 0))
                .end(LocalDateTime.of(2020, 9, 10, 0, 0, 0))
                .shouHouStatisticsEnums(new HashSet<>())
                .build(), new StringBuffer());
    }

//    private List<ShouHouStatisticsRes> list2() {
//        return shouhouStatisticsService.futureTask(ShouHouStatisticsHttpReq.builder()
//                .areaCodes(new ArrayList<>())
//                .areaKind(0)
//                .start(LocalDateTime.of(2020, 6, 10, 0, 0, 0))
//                .end(LocalDateTime.of(2020, 9, 10, 0, 0, 0))
//                .shouHouStatisticsEnums(new HashSet<>())
//                .build());
//    }
}
