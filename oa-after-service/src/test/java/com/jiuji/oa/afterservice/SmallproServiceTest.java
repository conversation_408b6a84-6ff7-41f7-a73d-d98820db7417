package com.jiuji.oa.afterservice;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.jiuji.oa.afterservice.common.bo.OaUserBO;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.source.MoaUrlSource;
import com.jiuji.oa.afterservice.common.util.HttpClientUtil;
import com.jiuji.oa.afterservice.common.util.OaVerifyUtil;
import com.jiuji.oa.afterservice.other.bo.SaveMoneyInfoBO;
import com.jiuji.oa.afterservice.other.bo.SaveMoneyInfoReqBO;
import com.jiuji.oa.afterservice.other.po.ShouhouTuihuan;
import com.jiuji.oa.afterservice.smallpro.service.SmallproBillService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproForwardExService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.afterservice.smallpro.vo.req.oaApiReq.JiujiCoinItemReq;
import com.jiuji.oa.afterservice.smallpro.vo.req.oaApiReq.OaApiReq;
import com.jiuji.oa.orginfo.member.client.MemberClient;
import com.jiuji.oa.orginfo.member.res.BlackAndSpecialTypeRes;
import com.jiuji.tc.common.vo.R;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.client.RedisException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * @author: gengjiaping
 * @date: 2019/11/14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SmallproServiceTest {
    @Resource
    private SmallproService smallproService;
    @Autowired
    private SmallproForwardExService smallproForwardExService;
    @Autowired
    private MoaUrlSource moaUrlSource;

    @Resource
    private SmallproBillService smallproBillService;
    @Resource
    private InwcfUrlSource inwcfUrlSource;

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private RedissonClient redisson;
    @Autowired
    private MemberClient memberClient;
    @Test
    public void getSmallproPage() {
        RLock fairLock = redisson.getLock("javaoa:refundSubmitWrite:942675");
        boolean flagSu = false;
        try {
            boolean flag = fairLock.tryLock(2, 100 , TimeUnit.SECONDS);
            if(flag) {
                System.out.println("success");
            }
        }catch (Exception e){
            log.error("退款提交错误",e);
        }finally {
            try {
                //解锁
                if (fairLock.isHeldByCurrentThread()) {
                    fairLock.unlock();
                }
            } catch (RedisException e) {
                log.error("refundSubmitWrite:->fairLock.unlock error", e);
            }
        }
    }


    @Test
    public void md5Test() throws UnsupportedEncodingException {
        R<BlackAndSpecialTypeRes> blackAndSpecialType = memberClient.getBlackAndSpecialType(5284410, 0);
        System.out.println(blackAndSpecialType.getData());
    }

    @Test
    public void kcTest() {
        String pwd = "";
        // 非补偿操作请勿通过
        pwd = "pass";
        if (pwd.equals("pass")) {
            for (int topi = 0; topi < 2; topi++) {
                smallproForwardExService.stockOperations(
                        68191, -1,
                        BigDecimal.valueOf(0.0),
                        294, "戚魏清", "",
                        "事务性补偿操作：售后小件换货取件出库，数量：-1",
                        887038,
                        1, 1, null, false, false);
            }
        }
    }

    @Test
    public void saveMoneyTest() {
        String url = inwcfUrlSource.getMoneySave();
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        SaveMoneyInfoBO saveMoneyInfoBO = new SaveMoneyInfoBO();
        saveMoneyInfoBO.setUserid(5288749).setAmount(0.0).setEkind(8).setSubid(7568350).setMemo("OA接口测试");
        SaveMoneyInfoReqBO saveMoneyInfoReqBO = new SaveMoneyInfoReqBO().setItem(saveMoneyInfoBO);
        String params = JSONObject.toJSONString(saveMoneyInfoReqBO);
        String kcResult = HttpClientUtil.post(url, params, headers);
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.parse(kcResult).toString());
        System.out.println(kcResult);
//        "{\"code\":0,\"msg\":\"操作成功\",\"userMsg\":\"操作成功\",\"data\":\"\"}"
    }

    @Test
    public void jsonTest() {
        OaUserBO oaUserBO = new OaUserBO();
        ShouhouTuihuan shouhouTuihuan = new ShouhouTuihuan();
        shouhouTuihuan.setSubId(15533595);
        oaUserBO.setAreaId(22);
        oaUserBO.setUserName("谢伊达");
        oaUserBO.setUserId(10237);
        oaUserBO.setToken("F1234545B5804E5E954F839E98972ADE");
        OaApiReq req = new OaApiReq().setTimeStamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
        BigDecimal subCoinM = BigDecimal.ZERO;
        JiujiCoinItemReq jiujiCoinItemReq = new JiujiCoinItemReq();
        jiujiCoinItemReq.setUserid(oaUserBO.getUserId()).setSub_id(shouhouTuihuan.getSubId().intValue())
                .setAreaid(oaUserBO.getAreaId()).setInuser(oaUserBO.getUserName())
                .setKinds((byte)3).setType(1).setCoins(subCoinM.doubleValue()).setPrices(0.0);
        req.setData(jiujiCoinItemReq);
        req.setSign(OaVerifyUtil.createSign(
                "MHP3iJchhmpulW9MV4JkOv75O9DMnfCQKLxTwyILQksLi3hbD0e3Y3VBueYvq", req));
        String url = moaUrlSource.getPayJiujiCoin();
        Map<String, String> headMap = new HashMap<>(1);
        headMap.put("Authorization", oaUserBO.getToken());
        System.out.println(JSONObject.toJSONString(req));
        String jiujiCoinResult = HttpClientUtil.post(url, JSONObject.toJSONString(req),
                headMap);
        JSONObject result = JSONObject.parseObject(jiujiCoinResult);
    }

    @Test
    public void javaTest() {
        ArrayList<Integer> temp = null;
        System.out.println(CollectionUtils.isNotEmpty(temp));
    }
}

