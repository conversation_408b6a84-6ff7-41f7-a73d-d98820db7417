package com.jiuji.oa.afterservice;

import com.jiuji.oa.afterservice.bigpro.po.ShouhouYuyue;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouMsgconfigService;
import com.jiuji.oa.afterservice.common.source.InwcfUrlSource;
import com.jiuji.oa.afterservice.common.util.ReflectUtil;
import com.jiuji.oa.afterservice.other.document.SmallproLogDocument;
import com.jiuji.oa.afterservice.other.po.LogParamDocument;
import com.jiuji.oa.afterservice.other.repository.SmallproFcLogRepository;
import com.jiuji.oa.afterservice.other.repository.SmallproLogRepository;
import com.jiuji.oa.afterservice.smallpro.service.SmallproFcLogService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproLogService;
import com.jiuji.oa.afterservice.smallpro.service.SmallproService;
import com.jiuji.oa.oacore.oaorder.SubCloud;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: gengjiaping
 * @date: 2019/11/14
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@Slf4j
public class SmallproLogServiceTest {

    @Resource
    private SmallproLogService smallproLogService;

    @Autowired
    private InwcfUrlSource inwcfUrlSource;
    @Autowired
    private SmallproService smallproService;
    @Resource
    private SmallproFcLogService smallproFcLogService;

    @Resource
    private SmallproFcLogRepository smallproFcLogRepository;
    @Resource
    private ShouhouMsgconfigService shouhouMsgconfigService;
    @Resource
    private SmallproLogRepository smallproLogRepository;
    @Autowired
    SubCloud subCloud;

    @Test
    public void testSmallProFcLog() {
        /*ShouhouMsgconfig shouhouMsgconfig = shouhouMsgconfigService.getShouhouMsgconfigByLogType(ShouHouMsgConfigEnum.TYPE6.getCode());
        String format = MessageFormat.format(shouhouMsgconfig.getMsgcontent(), "1", "2",
                "3", "4","5","6");
        String wxMsg = format;
        String url = "/after-service/detail/518773";
        String handleType = shouhouMsgconfig.getFuwutxt();
        String status = shouhouMsgconfig.getChulitxt();
        Integer areaId = 22;
        Integer userId = 5244413;
        String logs = shouhouMsgconfig.getJindutxt();
        Integer pushType = shouhouMsgconfig.getPushtype();
        boolean b = smallproService.sendWeixinMsg(wxMsg, url, handleType, status, areaId, userId, logs, pushType,
                "19188793525");
        System.out.println(b);*/
    }

    @Test
    public void test4() {
        String url = inwcfUrlSource.getMoneySave();
        System.out.println(url);
    }

    @Test
    public void getSmallproLogByIdTest() {
        SmallproLogDocument smallproLogDocument = smallproLogService.getSmallproLogById("5dba99c8d5c1601384be5cb0");
        Map<String, LogParamDocument> params = new HashMap<>();
        params.put("orderId", new LogParamDocument("123"));
        smallproLogDocument.setParams(params);
        smallproLogDocument.setCommentType(1);

        log.info("");
    }

    @Test
    public void cLogTest() {
        ShouhouYuyue shouhouYuyue = new ShouhouYuyue();
        shouhouYuyue.setId(1);
        shouhouYuyue.setCheckUser("李权");
        ShouhouYuyue shouhouYuyue1 = new ShouhouYuyue();
        shouhouYuyue1.setId(3);
        shouhouYuyue1.setCheckUser("李权2");
        List<String> list = ReflectUtil.getFieldModifiedLog(ShouhouYuyue.class, shouhouYuyue, shouhouYuyue1);
        System.out.println("");
    }

}
