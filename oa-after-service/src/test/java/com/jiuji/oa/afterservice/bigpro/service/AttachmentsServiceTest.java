package com.jiuji.oa.afterservice.bigpro.service;

import com.jiuji.oa.afterservice.bigpro.vo.req.FileReq;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * @<NAME_EMAIL>
 * @date 2020/9/30 14:11
 **/
@RunWith(SpringRunner.class)
@SpringBootTest
public class AttachmentsServiceTest {
    @Autowired
    private AttachmentsService attachmentsService;
    @Test
    public void saveAttachemnts() {
        List<FileReq> attList=attachmentsService.getAttachmentsList(2256261,6,0,null);
        Assert.assertNotNull(attList);
    }
}