package com.jiuji.oa.afterservice;

import com.jiuji.oa.afterservice.bigpro.bo.LockWxpjBo;
import com.jiuji.oa.afterservice.bigpro.service.ShouhouYuyueService;
import com.jiuji.oa.afterservice.bigpro.service.impl.ShouhouPpidDictServiceImpl;
import com.jiuji.oa.afterservice.sub.po.Sub;
import com.jiuji.oa.afterservice.sub.service.SubService;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * @Description TODO
 * <AUTHOR>
 * @Date 2020/10/15 15:24
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class MybatisPlusParseTest {

    @Resource
    private ShouhouPpidDictServiceImpl ppidDictService;
    @Resource
    ShouhouYuyueService yuyueService;
    @Resource
    SubService subService;

    @Test
    public void testMySqlQuery(){
        String result = ppidDictService.getPpidsByPpId(1);
        Assert.assertTrue(StringUtils.isEmpty(result));
    }

    @Test
    public void testSqlServer(){
        List<Integer> ids = new ArrayList<>();
        ids.add(1);
        List<LockWxpjBo> result = yuyueService.getLockWxpj(1,ids);
        Assert.assertTrue(result == null || result.size()==0);
    }

    @Test
    public void testSqlServerDefault(){
        Sub result = subService.getByIdSqlServer(1);
        Assert.assertTrue(result == null);
    }
}
