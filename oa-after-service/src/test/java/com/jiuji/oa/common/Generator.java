package com.jiuji.oa.common;

import com.baomidou.mybatisplus.generator.AutoGenerator;
import com.baomidou.mybatisplus.generator.config.DataSourceConfig;
import com.baomidou.mybatisplus.generator.config.GlobalConfig;
import com.baomidou.mybatisplus.generator.config.PackageConfig;
import com.baomidou.mybatisplus.generator.config.StrategyConfig;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import org.junit.Test;

import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneOffset;

/**
 * <AUTHOR>
 * @description
 * @since 2019/11/19
 */
public class Generator {

    @Test
    public void toDateByToken() {
        //"\/Date(1638862348467+0800)\/"
        //"\/Date(1638862348467+0800)\/"

        long l = LocalDateTime.now().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        System.out.println("\\/Data(\""+l+"\"+0800)\\/");
    }

    /**
     * 需要生成的表名
     */
    private static final String[] TABLE_NAME = {"EvaluateScore"};

    /**
     * 使用的数据源
     */
    private static final DataSourceConfig dataSourceConfig = getOaNew();

    /**
     * 表前缀
     */
    private static final String TABLE_PREFIX = "";

    private static final String PACKAGE_NAME = "com.jiuji.oa.afterservcie.evaluate";

    private static final String MODEL_NAME="evaluate";

    @Test
    public void generater() {
        generateByTables();
    }


    private static void generateByTables() {
        GlobalConfig globalConfig = new GlobalConfig();
        StrategyConfig strategyConfig = new StrategyConfig();
        strategyConfig
                .setCapitalMode(true)
                .setEntityLombokModel(true)
                .setRestControllerStyle(true)
                .setEntityColumnConstant(true)
                .setTablePrefix(TABLE_PREFIX)
                .setNaming(NamingStrategy.underline_to_camel)
//                .setLogicDeleteFieldName("deleted")
//                .setTableFillList(new ArrayList<TableFill>() {{
//                    add(new TableFill("create_time", FieldFill.INSERT));
//                    add(new TableFill("update_time", FieldFill.INSERT_UPDATE));
//                }})
                .setInclude(TABLE_NAME);
        String srcPath = "src" + File.separator + "main" + File.separator + "java";
        globalConfig.setActiveRecord(true)
                .setAuthor(System.getProperty("user.name"))
                .setOutputDir(srcPath)
                .setEnableCache(false)
                .setBaseResultMap(true)
                .setBaseColumnList(false)
                .setKotlin(false)
                .setActiveRecord(true)
                .setFileOverride(true)
                .setServiceName("%sService");
        new AutoGenerator().setGlobalConfig(globalConfig)
                .setDataSource(dataSourceConfig)
                .setStrategy(strategyConfig)
                .setPackageInfo(new PackageConfig()
                        .setParent(PACKAGE_NAME)
                        .setController("controller")
                        .setEntity("po")
                        .setModuleName(MODEL_NAME)
                        .setMapper("dao")
                        .setService("service")
                ).execute();
    }

    private static DataSourceConfig getOaNew() {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dsc.setUsername("ch999oanew");
        dsc.setPassword("oa9#*#($#*didkoeawd");
        dsc.setUrl("********************************************************");
        return dsc;
    }

    private static DataSourceConfig getProdWeb() {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dsc.setUsername("2017web999");
        dsc.setPassword("2017fensiyongfu)_999_@)!#**");
        dsc.setUrl("*******************************************************************************");
        return dsc;
    }

    private static DataSourceConfig getProdWebOther() {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dsc.setUsername("web999_other");
        dsc.setPassword("#*@(@aoe8@)!#**");
        dsc.setUrl("*******************************************************************************_other");
        return dsc;
    }


    private static DataSourceConfig getDevWebOther() {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dsc.setUsername("webother999");
        dsc.setPassword("webother999");
        dsc.setUrl("***********************************************************");
        return dsc;
    }

    private static DataSourceConfig getDevWeb() {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dsc.setUsername("web999");
        dsc.setPassword("web999");
        dsc.setUrl("*****************************************************");
        return dsc;
    }


    private static DataSourceConfig getTenantDevWebOther() {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dsc.setUsername("simulaction");
        dsc.setPassword("simulaction");
        dsc.setUrl("***************************************************************************");
        return dsc;
    }

    private static DataSourceConfig getTenantDevWeb() {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dsc.setUsername("simulaction");
        dsc.setPassword("simulaction2018");
        dsc.setUrl("********************************************************************");
        return dsc;
    }

    private static DataSourceConfig getProdErshou() {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.microsoft.sqlserver.jdbc.SQLServerDriver");
        dsc.setUsername("ch999hsData");
        dsc.setPassword("2019huishou_999_@)!@#");
        dsc.setUrl("*******************************************************");
        return dsc;
    }

    private static DataSourceConfig getDevIteng() {
        DataSourceConfig dsc = new DataSourceConfig();
        dsc.setDriverName("com.mysql.cj.jdbc.Driver");
        dsc.setUsername("iteng_chengyi");
        dsc.setPassword("Iteng2019chengyi#@%");
        dsc.setUrl("*************************************************************************************************************");
        return dsc;
    }

}
