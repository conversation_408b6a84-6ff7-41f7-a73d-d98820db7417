package com.jiuji.oa.productkc;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.jiuji.oa.afterservice.bigpro.bo.OperateProductKcPara;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.OperateProductKcRes;
import com.jiuji.oa.afterservice.bigpro.bo.productkc.ProductLeftKcInfoBo;
import com.jiuji.oa.afterservice.bigpro.po.BasketOther;
import com.jiuji.oa.afterservice.bigpro.po.Productinfo;
import com.jiuji.oa.afterservice.common.constant.ShouhouConstants;
import com.jiuji.oa.afterservice.common.util.CommenUtil;
import com.jiuji.oa.afterservice.common.vo.res.OaQuequRes;
import com.jiuji.oa.afterservice.stock.po.ProductKc;
import com.jiuji.oa.afterservice.stock.po.ProductKclogs;
import com.jiuji.oa.orginfo.areainfo.vo.AreaInfo;
import com.jiuji.tc.common.vo.R;
import com.jiuji.tc.common.vo.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;

/**
 * 模拟库存操作沙盒
 * <AUTHOR>
 * @description
 * @since 2021/4/23 11:18
 */
@Slf4j
public class ModelOperateProductKcTest {
    @Test
    public void operateProductKc() {
        OperateProductKcPara para = new OperateProductKcPara().setComment("调拨").setInuser("xxk").setCount(1).setInprice(new BigDecimal("14.63"))
                .setPpid(59787);
        R<OperateProductKcRes> operateProductKcResR = operateProductKc(para);
        log.info(operateProductKcResR.getUserMsg());
    }

    public R<OperateProductKcRes> operateProductKc(OperateProductKcPara para) {
        //默认值处理
        Boolean transFlag = para.getTransFlag() == null || para.getTransFlag();
        Boolean isLp = para.getIsLp() != null && para.getIsLp();
        Boolean diaoboFlag = para.getDiaoboFlag() != null && para.getDiaoboFlag();

        Boolean transferFlag = ("调拨".equals(para.getComment()) && para.getCount() > 0 && transFlag);

        if (StringUtils.isEmpty(para.getInuser())) {
            return R.error("库存操作人不能为空");
        }
        if (CommenUtil.isNullOrZero(para.getCount())) {
            return R.error("库存操作数量不能为空");
        }
        if (CommenUtil.isNullOrZero(para.getPpid())) {
            return R.error("ppid为空");
        }

        log.info("配件库存出入库信息：" + JSONObject.toJSONString(para));
        Integer cid = 0;
        Integer vendor = 0;
        Integer orderCount = 0;
        Boolean isDone = false;
        Integer ppidOther = 0;
        Integer areaOther = 0;

        if (para.getCount() < 0) {
            //出库
            List<Productinfo> pDt = new JSONArray().fluentAdd(new Productinfo().setCid(29).setVendor(0))
                    .toJavaObject(new TypeReference<List<Productinfo>>(){});
            //productinfoService.list(new LambdaQueryWrapper<Productinfo>().eq(Productinfo::getPpriceid, para.getPpid()));
            if (CollectionUtils.isNotEmpty(pDt)) {
                cid = pDt.get(0).getCid();
                vendor = pDt.get(0).getVendor();
                cid = cid == null ? 0 : cid;
                vendor = vendor == null ? 0 : vendor;
            }
        }
        if (("欠款".equals(para.getComment()) || "欠款转出".equals(para.getComment())) && !isLp && !Arrays.asList(16386, 59830, 65752, 16387).contains(para.getPpid())) {

            BasketOther bo = null;//basketOtherService.getBasketOtherInfoByIdAndIsDone(para.getBasketId(), "欠款".equals(para.getComment()) ? 0 : 1);
            if (bo != null) {
                orderCount = bo.getLcount() == null ? 0 : bo.getLcount();
                isDone = bo.getIsDone() != null && bo.getIsDone();
                ppidOther = bo.getPpriceid();
                areaOther = bo.getAreaid();
            }
        }

        List<ProductKc> productKcList = new JSONArray().fluentAdd(new ProductKc().setId(645333L).setLeftCount(0).setPanCount(null)
                .setInprice(new BigDecimal("14.7742")).setLcount(-1))
                .toJavaObject(new TypeReference<List<ProductKc>>(){});;//super.list(new LambdaQueryWrapper<ProductKc>().eq(ProductKc::getAreaid, para.getAreaId()).eq(ProductKc::getPpriceid, para.getPpid()));
        Integer oldCount = 0;
        Integer countE = 0;
        Integer oLcount = 0;
        BigDecimal inpriceE = BigDecimal.valueOf(-1);
        BigDecimal oldInprice = BigDecimal.ZERO;
        Integer panCount = 0;

        if (CollectionUtils.isNotEmpty(productKcList)) {
            ProductKc pk = productKcList.get(0);
            oldCount = pk.getLeftCount() == null ? 0 : pk.getLeftCount();
            inpriceE = pk.getInprice() == null ? BigDecimal.ZERO : pk.getInprice();
            countE = pk.getLcount() == null ? 0 : pk.getLcount();
//            if (pk.getOrderCount().equals(0) && para.getCount() < 0){
//                return R.error("当前地区备货锁定库存量已为0，不可再撤销");
//            }
            oLcount = countE;
            if (CommenUtil.isNotNullZero(pk.getPanCount())) {
                panCount = pk.getPanCount();
            }
            oldInprice = inpriceE;
        }

        //其他
        if (para.getPpid().equals(10754)) {
            oldInprice = para.getInprice();
        }

        ShouhouConstants shouhouConstants = new ShouhouConstants();

        //调拨 负库存不允许调拨
        if (para.getCount() < 0 && (oldCount + orderCount + para.getCount()) < 0 &&
                ((!shouhouConstants.getOtherPPid().contains(para.getPpid()) && !shouhouConstants.getOtherCid().contains(cid)
                        && !shouhouConstants.getMaskPPriceids().contains(para.getPpid()) && !shouhouConstants.getInstallServicesPpriceid().contains(para.getPpid()))
                        || "调拨".equals(para.getComment())) && vendor.equals(0)) {

            //负库存不允许出库
            //vendor为0为九机商品，否则为第三方发货产品，第三方发货商品允许负库存出库
            return R.error("负库存不允许出库");
        }

        Boolean dcFlag = false;

        if (para.getInprice().compareTo(BigDecimal.ZERO) >= 0 && para.getCount() > 0) {
            if (transferFlag) {
                inpriceE = para.getInprice();
            } else {
                R<AreaInfo> areaInfoR = R.success(new JSONObject().fluentPut("kind1",1)
                        .fluentPut("companyName","贵阳市观山湖区利宏通讯器材经营部").toJavaObject(AreaInfo.class));//areaInfoClient.getAreaInfoById(para.getAreaId());
                if (areaInfoR.getCode() != ResultCode.SUCCESS || areaInfoR.getData() == null) {
                    return R.error("库存操作地区信息不存在");
                }

                AreaInfo areaInfo = areaInfoR.getData();
                //自营（共享大仓成本）
                Integer count1 = 0;
                Boolean inpriceFlag = true;
                if (areaInfo.getKind1().equals(1)) {
                    ProductLeftKcInfoBo leftKcInfo = new JSONObject().fluentPut("inprices",new BigDecimal("945.5488"))
                            .fluentPut("lcounts",64).toJavaObject(ProductLeftKcInfoBo.class); //baseMapper.getProductLeftKcInfoByPpid(para.getPpid());
                    if (leftKcInfo != null) {
                        dcFlag = true;
                        inpriceE = leftKcInfo.getInprices() == null ? BigDecimal.ZERO : leftKcInfo.getInprices();
                        count1 = leftKcInfo.getLcounts() == null ? 0 : leftKcInfo.getLcounts();
                    }

                    if (para.getComment().contains("采购单")) {
                        ProductLeftKcInfoBo diaoBoLeftKcInfo = null;//baseMapper.getDiaoBoBasketLeftKcInfoByPpid(para.getPpid());
                        if (diaoBoLeftKcInfo != null) {
                            dcFlag = true;
                            inpriceE = inpriceE.add(diaoBoLeftKcInfo.getInprices() == null ? BigDecimal.ZERO : diaoBoLeftKcInfo.getInprices());
                            count1 = count1 + (diaoBoLeftKcInfo.getLcounts() == null ? 0 : diaoBoLeftKcInfo.getLcounts());
                        }
                    }

                }

                if (inpriceE.compareTo(BigDecimal.valueOf(-1)) == 0) {
                    //如果库存成本为0 以当前采购价为成本价
                    inpriceE = para.getInprice();
                } else if (inpriceE.compareTo(BigDecimal.ZERO) >= 0 && ((countE > 0 && !dcFlag) || (count1 > 0 && dcFlag))) {
                    //还有库存 库存 加权平均计算成本
                    if (dcFlag) {
                        inpriceE = (inpriceE.add(para.getInprice().multiply(BigDecimal.valueOf(para.getCount())))).divide(BigDecimal.valueOf(Long.valueOf(count1) + Long.valueOf(para.getCount())), 4);
                    } else {
                        inpriceE = ((inpriceE.multiply(BigDecimal.valueOf(countE)).add(para.getInprice().multiply(BigDecimal.valueOf(para.getCount()))))).divide(BigDecimal.valueOf(Long.valueOf(countE) + Long.valueOf(para.getCount())), 4);
                    }
                } else {
                    inpriceE = para.getInprice();
                }

            }
        }

        if (inpriceE.compareTo(BigDecimal.valueOf(-1)) == 0) {
            inpriceE = para.getInprice();
        }

        countE = countE + para.getCount();

        Integer pCount = panCount + para.getCount();
        Integer kcCount = 0;
        Integer kcLogCount = 0;

        Boolean flag = false;

        if (CollectionUtils.isEmpty(productKcList)) {
            //不存在记录
            ProductKc pk = new ProductKc();
            pk.setAreaid(para.getAreaId());
            pk.setLcount(countE);
            pk.setInprice(inpriceE);
            pk.setPpriceid(para.getPpid());
            log.info("入库出库后ProductKc记录:{}",JSON.toJSONString(pk));
            flag = true;//super.save(pk);
        } else {
            flag = true;
            ProductKc pk = new ProductKc();
            pk.setAreaid(para.getAreaId());
            pk.setLcount(countE);
            pk.setInprice(inpriceE);
            pk.setPanCount(panCount);
            pk.setPpriceid(para.getPpid());
            log.info("入库出库后ProductKc记录:{}",JSON.toJSONString(pk));
            /*super.update(new LambdaUpdateWrapper<ProductKc>()
                    .set(ProductKc::getInprice, inpriceE)
                    .set(ProductKc::getLcount, countE)
                    .set(panCount != null, ProductKc::getPanCount, panCount)
                    .eq(ProductKc::getAreaid, para.getAreaId()).eq(ProductKc::getPpriceid, para.getPpid()).eq(ProductKc::getLcount, oLcount));*/
        }

        if (!flag) {
            return R.error("更新库存失败");
        }

        kcCount = oLcount;

        if (orderCount > 0) {
            //ppriceid发生变化 不能执行
            if (!ppidOther.equals(para.getPpid()) || !areaOther.equals(para.getAreaId())) {
                return R.error("操作失败,ppid发生改变");
            }
            log.info("orderCount={}",(isDone ? "+" : "-") + orderCount);
            /*flag = super.update(new LambdaUpdateWrapper<ProductKc>().setSql("orderCount=orderCount" + (isDone ? "+" : "-") + orderCount)
                    .eq(ProductKc::getAreaid, para.getAreaId()).eq(ProductKc::getPpriceid, para.getPpid()));*/

            if (!flag) {
                return R.error("更新订单数量失败");
            }
            log.info("更新订单关联信息isDone:{}",!isDone);
            /*flag = basketOtherService.update(new LambdaUpdateWrapper<BasketOther>()
                    .set(BasketOther::getIsDone, !isDone).eq(BasketOther::getBasketId, para.getBasketId()).eq(BasketOther::getIsDone, isDone).eq(BasketOther::getPpriceid, para.getPpid()));*/
            if (!flag) {
                return R.error("更新订单关联信息表失败");
            }

        }

        //更新自营大仓平均成本
        if (dcFlag && !transferFlag) {
            log.info("更新自营大仓平均成本(inpriceE):{} by {}",inpriceE, para.getPpid());
            Integer count = 1;//baseMapper.updateDcCostPrice(inpriceE, para.getPpid());
            if (CommenUtil.isNullOrZero(count)) {
                ///smsService.sendOaMsg("大仓平均成本更新失败：ppriceid[" + para.getPpid() + "],area[" + para.getAreaId() + "]", null, "10722", OaMesTypeEnum.YCTZ.getCode().toString());
                return R.error("大盘平均成本更新失败！");
            }
        }

        ProductKclogs kclogs = new ProductKclogs();
        kclogs.setPpriceid(para.getPpid());
        kclogs.setCount(para.getCount());
        kclogs.setLastcount(countE);
        kclogs.setInprice(para.getInprice());
        kclogs.setAreaid(para.getAreaId());
        kclogs.setInuser(para.getInuser());
        kclogs.setInsource(para.getInsource());
        kclogs.setComment(para.getComment());
        kclogs.setBasketId(para.getBasketId());
        kclogs.setShouhouId(para.getShouhouId());

        Boolean check1 = para.getCheck1() != null && para.getCheck1();
        Boolean check2 = para.getCheck2() != null && para.getCheck2();
        kclogs.setCheck1(check1);
        kclogs.setCheck2(check2);
        kclogs.setCheck1dtime(check1 ? LocalDateTime.now() : null);
        kclogs.setCheck2dtime(check2 ? LocalDateTime.now() : null);
        kclogs.setDtime(LocalDateTime.now());

        //productKclogsService.save(kclogs);
        log.info("入库日志记录:{}",JSON.toJSONString(kclogs));
        kcLogCount = countE;
//        if (kcCount != kcLogCount){
//            log.error("库存与日志不匹配");
//            smsService.sendOaMsg("库存与日志不匹配：ppriceid["+para.getPpid()+"],area["+para.getAreaId()+"]",null,"10722", OaMesTypeEnum.YCTZ.getCode().toString());
//            return R.error("库存与日志不匹配！");
//        }


        //出入库记录检测 自动备货
        //正式环境，rabbitMQ推送(要放在出其他配件逻辑前面，不然ppid会被改掉
        Map<String, String> data = new HashMap<>();
        data.put("areaid", String.valueOf(para.getAreaId()));
        data.put("ppriceid", String.valueOf(para.getPpid()));
        OaQuequRes oaQuequRes = new OaQuequRes("autoKcPush", data);


        Integer[] s1 = {1, 2, 3, 4, 5, 6, 7, 8, 9};
        int expire = s1[new Random().nextInt(s1.length)] * 300;
        /*rabbitTemplate.convertAndSend("", RabbitMqConfig.autoKcPushQueue, JSON.toJSONString(oaQuequRes), message -> {
            message.getMessageProperties().setExpiration(String.valueOf(expire));
            return message;
        });*/
//            smsService.oaRabbitMQWorkQueue(JSON.toJSONString(oaQuequRes), RabbitMqConfig.autoKcPushQueue);

        OperateProductKcRes res = new OperateProductKcRes();
        res.setCount(countE);
        res.setInprice(oldInprice);

        return R.success("出库成功", res);
    }
}
