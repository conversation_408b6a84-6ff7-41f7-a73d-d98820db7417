#!/bin/bash

# 获取当前分支名
current_branch=$(git rev-parse --abbrev-ref HEAD)

# 新建分支名称
new_branch="${current_branch}\$\$9ji"

# 切换到当前分支
git checkout "${current_branch}"

# 新建并切换到新分支
git checkout -b "${new_branch}"

# 拉取远程分支[release_9ji]
git fetch origin release_9ji:release_9ji
# 合并[release_9ji]分支到新分支
git merge --no-edit release_9ji
git add .
# 提交合并结果
git commit -m "Merge [release_9ji] into ${new_branch}"

# 推送新分支到远程仓库
git push origin "${new_branch}"

echo "新分支 ${new_branch} 已成功创建并合并[release_9ji]分支。"
